# java代码开发规范

## 1 restful标准

URI 表示资源，资源一般对应服务器端领域模型中的实体类。

### 1-1 URI规范

1.  URI 设计首先，我们知道在 REST API 中，URI 代表的是一种资源，它的设计要满足两个基本要求，第一名词而非动词，第二要能清晰表达出资源的含义。
2. 不用大写；
3. URI用中杠-不用下杠_；
4. 参数用杠_，不用中划线-
5. URI中的名词表示资源集合，使用复数形式。
6. URL结尾不应该包含斜杠"/""

```
/api-server/horizon-dev/api/nova/servers
 
/api-server/horizon-dev/api/nova/servers/{server_id}/live-migration
 
这里用live-migration而不是动词live-migrate
```

### 1-2 资源集合vs 单个资源

资源集合

```
/api-server/horizon-dev/api/nova/servers
/api-server/horizon-dev/api/nova/servers/{server_id}/ports
```

单个资源

```
/api-server/horizon-dev/api/nova/servers/{server_id}
/api-server/horizon-dev/api/nova/servers/{server_id}/ports/{port_id}
```

### 1-3  避免层级过深的URI

/在url中表达层级，用于按实体关联关系进行对象导航，一般根据id导航。

过深的导航容易导致url膨胀，不易维护，如 GET /zoos/1/areas/3/animals/4，尽量使用查询参数代替路径中的实体导航，如GET /animals?zoo=1&area=3；

### 1-4 post:创建

创建单个资源。POST一般向“资源集合”型uri发起

1） 创建第一级资源

```
POST /api-server/horizon-dev/api/nova/servers
```

 path以外的参数用body或是param

2）创建第二级资源（比如创建某一虚拟机的备份）

```
POST /api-server/horizon-dev/api/nova/servers/{server_id}/backups
```

path以外的参数用body或是param

### 1-5 GET:查询

1）查询第一级资源列表

```
GET /api-server/horizon-dev/api/nova/servers
```

path以外的参数param

2）查询第二级资源列表（比如查询某一虚拟机的备份）

```
GET /api-server/horizon-dev/api/nova/servers/{server_id}/backups
```

path以外的参数用param

3）查询第一级资源单个 

```
GET /api-server/horizon-dev/api/nova/servers/{server_id} 
```

path以外的参数用param

4）查询第二级资源单个 

```
GET /api-server/horizon-dev/api/nova/servers/{server_id}/backups/{backup_id} 
```

path以外的参数用param

### 1-6 PUT：更新

1）更新第一级资源

```
PUT /api-server/horizon-dev/api/nova/servers/{server_id}
```

更新的属性放在param参数或是body参数

2）更新第二级资源

```
PUT /api-server/horizon-dev/api/nova/servers/{server_id}/backups/{backup_id}
```

 更新的属性放在param参数或是body参数

3）执行某一个关联操作（虚拟机绑定网卡）

```
PUT /api-server/horizon-dev/api/nova/servers/{server_id}/networks/{network_id}/connection
```

 注意：使用名词connection而不是动词connect

 

## 1-7  DELETE：删除

1）删除第一级资源

```
DELETE /api-server/horizon-dev/api/nova/servers/{server_id}
```

2）删除第二级资源

```
DELETE /api-server/horizon-dev/api/nova/servers/{server_id}/backups/{backup_id}
```

 

 

## 2 swagger要求

### 2-1 controller的类上要加上@Api

**![img](https://gitee.com/liyao0312/blogImage/raw/master/img/clip_image002.png)**

注意，description必须要有，而且是有一个空格的字符串

### 2-2 方法上必须写有@ApiOperation

**![img](https://gitee.com/liyao0312/blogImage/raw/master/img/clip_image004.png)**

### 2-3 每个参数上都加上说明，包括中文意思，类型，是否必填，限制要求。

 1）param参数

**![img](https://gitee.com/liyao0312/blogImage/raw/master/img/clip_image006.png)**

 value上要写上中文描述，限制说明

 2）path参数

**![img](https://gitee.com/liyao0312/blogImage/raw/master/img/clip_image008.png)**

 value上要写上中文描述，限制说明

  3）@RequestBody参数，要加上@ApiModelProperty

**![img](https://gitee.com/liyao0312/blogImage/raw/master/img/clip_image010.png)**

value上要写上中文意思，是否必填（这点是区别于path和query的），限制说明

 

## 3 关于操作日志的加法

### 3-1 取path和param参数作为操作日志的资源的加法

**![img](https://gitee.com/liyao0312/blogImage/raw/master/img/clip_image012.png)**

1）nameIndex和idIndex不要同时使用，用其一即可。如果参数里面既有id，又有名称，尽量用名字作为操作日志记录的资源

2) nameIndex="1"，表示取第二个参数，0表示取第一个参数据，依次类推

### 3-2 @RequestBody的某一个字段作为操作日志的资源的加法

![img](https://gitee.com/liyao0312/blogImage/raw/master/img/clip_image014.png)

1）其中entityIndex表示，实体类在方法的第几个参数，0表示第一个

2）entityName上和参数类名称一致

3）该点是最重要的一点，需要在下面的类里面添加一段if语句，目的就是要通过判断方法上传过来类名字符串来将参数实体类转换成实例，然后取相应的参数给resource

![img](https://gitee.com/liyao0312/blogImage/raw/master/img/clip_image016.png)

## 4 代码书写要求

- **类名**

必须有意义，从类名能够判断出是哪一模块，必须大写开头，如果是多个单词的组合，第二个单词开头也是大写，如：ConfigGroupController

- **方法名**

必须有意义，从方法名能够判断出是哪一功能，必须小写开头，如果是多个单词的组合，第二个单词开头是大写，如：createBlueprintVersion

-  **字段参数**

json展示用下划线，java变量用驼峰

**![img](https://gitee.com/liyao0312/blogImage/raw/master/img/clip_image018.png)**

![img](https://gitee.com/liyao0312/blogImage/raw/master/img/clip_image020.png)

-  **如果不是有不得不的需，禁止循环里面调用方法**
- **字符串比较方式**

```
尽量用
 
"admin".equals(o.getTenant())       
 
不要用
 
o.getTenant.equals("admin")
 
第二种方法，如果o没有tenant，就会出现空指针错误
```

- **异常日志打印**

用log.error，log.warn等来打印信息

![img](https://gitee.com/liyao0312/blogImage/raw/master/img/clip_image022.png)

注意，是logger.error(“xxxxx”,e)不是logger.error(“xxxxx”,e.getMessage())

不用下面的方式：

![img](https://gitee.com/liyao0312/blogImage/raw/master/img/clip_image024.png)

##  5 Git提交规范

### 5-1 修改说明的格式约定

需求提交请填写：

```
issue_id: req_2181
描述: 存储池相关功能开发
```

Bug修改的提交请填写：

```
issue_id: bug_ 6807

描述: 超出配额创建云主机，提示不友好，应该提示进程配额已经超出设定
```

合并提交请填写：

```
issue_id: req_2181****，bug_6260，其他

描述: 修改了部分时候监控获取不到数据会报错的问题，修改了CPUTOP5的公式，对网卡进行了改造
```

其他的提交：

```
其他

描述: 修改用户记录日志时增加记录组织角色与项目角色
```





 

 

 

 

 