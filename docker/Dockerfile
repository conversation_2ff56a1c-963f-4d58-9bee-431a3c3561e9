FROM registry-harbor:5000/full-stack-cloud/alpine-java:8_server-jre_unlimited
MAINTAINER sugoncloud "<EMAIL>"

RUN mkdir -p /apps

RUN mkdir -p /apps/libs

RUN mkdir -p /apps/keys

# Copy the .so file into the image
COPY libftauthng.so libftauthng.so.1.1 libftauthng.so.1.1.1 /apps/libs/

# Create a symbolic link for the default version
RUN ln -sf /apps/libs/libftauthng.so.1.1.1 /apps/libs/libftauthng.so

COPY sugoncloud-iam-api.jar /apps

COPY sugoncloud-iam-api.conf /apps

ENV LANG en_US.utf8

ENV LD_LIBRARY_PATH=/apps/libs

CMD ["/apps/sugoncloud-iam-api.jar", "start"]

EXPOSE 8080