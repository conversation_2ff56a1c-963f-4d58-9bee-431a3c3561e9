package model

type Globalsettings struct {
	Uuid              string `xorm:"not null pk comment('唯一标识') VARCHAR(64)" json:"uuid"`
	PolicyName        string `xorm:"not null comment('策略名称') VARCHAR(255)" json:"policy_name"`
	PolicyDocument    string `xorm:"not null comment('策略内容') MEDIUMTEXT" json:"policy_document"`
	PolicyType        string `xorm:"not null comment('策略类型') VARCHAR(255)" json:"policy_type"`
	RetainedField     string `xorm:"comment('保留字段') VARCHAR(255)" json:"retained_field"`
	PolicyDisplayName string `xorm:"comment('显示名称') VARCHAR(255)" json:"policy_display_name"`
}
