package model

import "time"

type MicroService struct {
	Id                string    `xorm:"not null pk comment('主键ID') VARCHAR(64)" json:"id"`
	Name              string    `xorm:"not null comment('微服务名称') VARCHAR(255)" json:"name"`
	Description       string    `xorm:"comment('描述') VARCHAR(255)" json:"description"`
	CreateTime        time.Time `xorm:"comment('创建时间') DATETIME" json:"create_time"`
	ModifyTime        time.Time `xorm:"comment('修改时间') DATETIME" json:"modify_time"`
	Link              string    `xorm:"comment('连接地址') VARCHAR(255)" json:"link"`
	CategoryId        string    `xorm:"comment('分类') VARCHAR(64)" json:"category_id"`
	NavHidden         bool      `xorm:"not null default 0 comment('导航栏是否隐藏') TINYINT(1)" json:"nav_hidden"`
	LatestRequestTime time.Time `xorm:"comment('最近一次的访问') DATETIME" json:"latest_request_time"`
	CollectTime       time.Time `xorm:"comment('收藏时间：为空表示未被收藏') DATETIME" json:"collect_time"`
	Order             float64   `xorm:"DOUBLE" json:"order"`
}

type MicroServiceCategory struct {
	Id          string          `xorm:"not null pk VARCHAR(64)" json:"id"`                      // 唯一标识
	Name        string          `xorm:"not null VARCHAR(255)" json:"name"`                      // 名称
	Description string          `xorm:"VARCHAR(255)" json:"description"`                        // 描述
	CreateTime  time.Time       `xorm:"DATETIME" json:"create_time"`                            // 创建时间
	ModifyTime  time.Time       `xorm:"DATETIME" json:"modify_time"`                            // 修改时间
	Order       float64         `xorm:"DOUBLE" json:"order"`                                    // 排序
	NavHidden   bool            `xorm:"default 0 comment('隐藏分类') TINYINT(1)" json:"nav_hidden"` // 是否隐藏
	Children    []*MicroService `xorm:"-" json:"children"`                                      // 子分类
}

type MicroServiceCategoryListArgs struct {
	List
	Name string `query:"name" json:"name"` //微服务类别名称
}

type MicroServiceCategoryUpdateArgs struct {
	Id        string   `json:"id" binding:"required"` // 主键
	Order     *float64 `json:"order"`                 // 排序
	NavHidden *bool    `json:"nav_hidden"`            // 是否隐藏分类
}

type MicroServiceUpdateArgs struct {
	Id        string   `json:"id" binding:"required"` // 主键
	Order     *float64 `json:"order"`                 // 排序
	NavHidden *bool    `json:"nav_hidden"`            // 是否隐藏分类
}
