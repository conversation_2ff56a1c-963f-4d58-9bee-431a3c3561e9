package model

type List struct {
	PageNum  int `query:"page_num" form:"page_num" json:"page_num"`    //分页索引
	PageSize int `query:"page_size" form:"page_size" json:"page_size"` //分页大小
}

func (l *List) GetLimit() int {
	if l.PageSize < 1 {
		l.PageSize = 10
	}
	return l.PageSize
}

func (l *List) GetOffset() int {
	if l.PageSize < 1 {
		l.PageSize = 10
	}
	if l.PageSize > 200 {
		l.PageSize = 200
	}
	if l.PageNum < 1 {
		l.PageNum = 1
	}
	return (l.PageNum - 1) * l.PageSize
}

func (l *List) GetPageNum() int {
	if l.PageNum < 1 {
		l.PageNum = 1
	}
	return l.PageNum
}

// ResultModel仅文档使用，勿用
type ResultModel struct {
	StatusCode int    `json:"status_code"` // 状态码 0：失败  1：成功
	StatusMes  string `json:"status_mes"`  // 执行结果消息
	Content    any    `json:"content"`     // 返回内容
	Resource   string `json:"resource"`    // 资源
	DebugMsg   string `json:"debug_msg"`   // debug消息
}

type PageCL[T any] struct {
	PageNum   int `json:"page_num"`   //分页索引
	PageSize  int `json:"page_size"`  //分页大小
	Size      int `json:"size"`       //当前页条数
	Total     int `json:"total"`      //总条数
	PageCount int `json:"page_count"` // 页数
	List      []T `json:"list"`       //数据
}
