package model

import (
	"time"
)

type User struct {
	Id                  string    `xorm:"not null pk VARCHAR(64)" json:"id"`         // 主键
	Name                string    `xorm:"default '' VARCHAR(64)" json:"name"`        // 用户名
	<PERSON>as               string    `xorm:"comment('用户名别名') VARCHAR(64)" json:"alias"` // 别名
	Extra               string    `xorm:"TEXT" json:"extra"`                         // 描述
	Enabled             bool      `xorm:"TINYINT(1)" json:"enabled"`
	CreatedAt           time.Time `xorm:"DATETIME" json:"created_at"`
	ModifyTime          time.Time `xorm:"DATETIME" json:"modify_time"`
	LastActiveAt        time.Time `xorm:"DATETIME" json:"last_active_at"`
	PasswordExpiresAt   time.Time `xorm:"DATETIME" json:"password_expires_at"`
	Password            string    `xorm:"VARCHAR(128)" json:"password"`
	Email               string    `xorm:"VARCHAR(255)" json:"email"`
	Phone               string    `xorm:"VARCHAR(255)" json:"phone"`
	Type                string    `xorm:"VARCHAR(32)" json:"type"`
	DeptId              string    `xorm:"VARCHAR(64)" json:"dept_id"`
	Expired             time.Time `xorm:"DATETIME" json:"expired"`
	AllowIp             string    `xorm:"VARCHAR(32)" json:"allow_ip"`
	LastLoginIp         string    `xorm:"VARCHAR(128)" json:"last_login_ip"`
	LastPasswordTime    time.Time `xorm:"DATETIME" json:"last_password_time"`
	DeptPath            string    `xorm:"-" json:"dept_path"`
	LastLoginErrorCount int       `xorm:"-" json:"last_login_error_count"`
}

type UserAccess struct {
	Id         string    `xorm:"not null pk comment('主键ID') VARCHAR(64)" json:"id"`
	UserId     string    `xorm:"not null comment('用户id') VARCHAR(64)" json:"user_id"`
	StartDate  time.Time `xorm:"comment('开始时间') DATETIME" json:"start_date"`       // 开始日期
	EndDate    time.Time `xorm:"comment('结束时间') DATETIME" json:"end_date"`         // 结束日期
	LimitFlag  bool      `xorm:"comment('限制时间点标识') TINYINT(1)" json:"limit_flag"`  // 是否开启时间限制
	LimitTime  string    `xorm:"comment('限制时间点') VARCHAR(2000)" json:"limit_time"` // 限制时间
	CreateTime time.Time `xorm:"comment('创建时间') DATETIME" json:"create_time"`
	ModifyTime time.Time `xorm:"comment('修改时间') DATETIME" json:"modify_time"`
	Ip         string    `xorm:"-" json:"ip"` // ip白名单
}

type UserAccessSetArgs struct {
	Ip        string `json:"ip"`         // ip白名单
	StartDate string `json:"start_date"` // 开始日期 yyyy-MM-dd
	EndDate   string `json:"end_date"`   // 结束日期 yyyy-MM-dd
	LimitFlag bool   `json:"limit_flag"` // 是否开启时间限制
	LimitTime string `json:"limit_time"` // 限制时间
}

type LoginUserArgs struct {
	Username    string `json:"username"`   // 用户名
	Password    string `json:"password"`   // 密码
	PublicKey   string `json:"public_key"` // 加密公钥
	Code        string `json:"code"`       // 验证码
	IpAddr      string `json:"_"`          // 请求ip地址，内部调用不需要
	OriginLogin bool   `json:"-"`          // 是否原生登录
}

type UserCreateArgs struct {
	Name                string           `json:"name" binding:"required"`     // 用户名
	Alias               string           `json:"alias"`                       // 用户别名
	DeptId              string           `json:"dept_id"`                     // 部门id
	Email               string           `json:"email" binding:"required"`    // 邮箱
	Extra               string           `json:"extra"`                       // 描述
	IsDepartmentManager bool             `json:"is_department_manager"`       // 是否为部门管理员
	Password            string           `json:"password" binding:"required"` // 密码
	Phone               string           `json:"phone" binding:"required"`    // 电话
	Publickey           string           `json:"publickey"`                   // 公钥
	DepartmentName      string           `json:"department_name"`             // 部门名称
	QuotaValueMap       map[string]int64 `json:"quota_value_map"`             // 配额配置
	RegionId            string           `json:"-"`                           // 区域id
}

type UserProjectsArgs struct {
	List
	ProjectName string `query:"project_name" json:"project_name"` // 项目名
}

type UserUpdateArgs struct {
	Id    string `json:"id" binding:"required"`    // 用户id
	Alias string `json:"alias" binding:"required"` // 用户别名
	Phone string `json:"phone" binding:"required"` // 电话
	Email string `json:"email" binding:"required"` // 邮箱
	Extra string `json:"extra"`                    // 描述
}

type UserRoleListArgs struct {
	List
	RoleName  string `query:"role_name" json:"role_name"`   // 角色名称
	ProjectId string `query:"project_id" json:"project_id"` // 项目ID
	UserId    string `json:"-"`                             // 用户id
}

type UserBindRolesArgs struct {
	UserId    string   `json:"-"`                             // 用户id
	ProjectId string   `query:"project_id" json:"project_id"` // 项目ID
	RoleIds   []string `query:"role_ids" json:"role_ids"`     // 角色id集合
}

type UserUnBindRolesArgs struct {
	UserId  string   `json:"-"`                         // 用户id
	RoleIds []string `query:"role_ids" json:"role_ids"` // 角色id集合
}

type UserBindProjectsArgs struct {
	UserId     string   `json:"-"`                               // 用户id
	ProjectIds []string `query:"project_ids" json:"project_ids"` // 项目ID集合
}

type UserResetPasswordArgs struct {
	UserId     string `json:"-"`         // 重置密码用户id
	CurrUserId string `json:"-"`         // 当前登录用户id
	Password   string `json:"password"`  // 密码
	Publickey  string `json:"publickey"` // 密码公钥匙
}

type UserPageByProjectIdArgs struct {
	List
	Name      string `query:"name" json:"name"` // 用户名
	ProjectId string `json:"-"`                 // 项目ID
}

type UserTypeReoly struct {
	UserType     string `json:"user_type"`      // 用户类型标识
	UserTypeDesc string `json:"user_type_desc"` // 用户类型描述
}

type OverviewCountReoly struct {
	DeptCount    int `json:"dept_count"`    // 组织数量
	ProjectCount int `json:"project_count"` // 项目数量
	RoleCount    int `json:"role_count"`    // 角色数量
	UserCount    int `json:"user_count"`    // 用户数量
}

type UserListArgs struct {
	List
	Name          string `query:"name" json:"name"`           // 用户名
	QueryUserType string `query:"user_type" json:"user_type"` // 用户类型
	DeptId        string `query:"dept_id" json:"dept_ids"`    // 组织id
	CurrUserId    string `json:"-"`                           // 当前登录用户id
}
