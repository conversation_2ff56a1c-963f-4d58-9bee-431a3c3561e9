package model

import "time"

type RamPolicy struct {
	Uuid               string       `xorm:"not null pk default '1' comment('唯一标识') VARCHAR(64)" json:"uuid"`               // 唯一标识
	PolicyName         string       `xorm:"not null comment('策略名称') VARCHAR(255)" json:"policy_name"`                      // 策略名称
	PolicyDocument     string       `xorm:"not null comment('策略内容') MEDIUMTEXT" json:"policy_document"`                    // 策略内容
	Action             string       `xorm:"comment('方法') VARCHAR(16)" json:"action"`                                       // 方法
	PolicyType         string       `xorm:"comment('策略类型: 0: 菜单类型 | 1：非菜单类型') VARCHAR(255)" json:"policy_type"`            // 策略类型: 0: 菜单类型 | 1：非菜单类型
	Comments           string       `xorm:"comment('描述') VARCHAR(255)" json:"comments"`                                    // 描述
	OwnerId            string       `xorm:"comment('主账号ID') VARCHAR(64)" json:"owner_id"`                                  // 主账号ID
	Effect             string       `xorm:"not null comment('用户系统：0，云平台：1， 大数据：2') VARCHAR(16)" json:"effect"`             // 用户系统：0，云平台：1， 大数据：2
	ParentId           string       `xorm:"not null comment('父ID') VARCHAR(64)" json:"parent_id"`                          // 父ID
	MethodUrl          string       `xorm:"comment('方法请求路径') VARCHAR(255)" json:"method_url"`                              // 方法请求路径
	Sort               int          `xorm:"comment('排序') INT(11)" json:"sort"`                                             // 排序
	CreateBy           string       `xorm:"comment('创建人') VARCHAR(64)" json:"create_by"`                                   // 创建人
	CreateAt           time.Time    `xorm:"created comment('创建时间') TIMESTAMP" json:"create_at"`                            // 创建时间
	Index              string       `xorm:"comment('前端菜单路由') VARCHAR(64)" json:"index"`                                    // 前端菜单路由
	Icon               string       `xorm:"comment('前端菜单图标') VARCHAR(255)" json:"icon"`                                    // 前端菜单图标
	LinkId             string       `xorm:"comment('前端权限联动') VARCHAR(64)" json:"link_id"`                                  // 前端权限联动
	ResourceLinkId     string       `xorm:"comment('资源策略关联到策略的id') VARCHAR(64)" json:"resource_link_id"`                   // 资源策略关联到策略的id
	ResourcePolicyFlag string       `xorm:"comment('是否为资源，1为资源，0非资源，null为用户不能查看') VARCHAR(4)" json:"resource_policy_flag"` // 是否为资源，1为资源，0非资源，null为用户不能查看
	CanDelete          string       `xorm:"default '1' comment('能否删除（0：不能删除，1：能删除）') VARCHAR(4)" json:"can_delete"`        // 能否删除（0：不能删除，1：能删除）
	ServiceId          string       `xorm:"not null comment('服务ID') VARCHAR(64)" json:"service_id"`                        // 服务ID
	Children           []*RamPolicy `xorm:"-" json:"children"`                                                             // 子策略
}

type RamPolicyRole struct {
	Uuid      string `xorm:"not null pk comment('唯一标识') VARCHAR(64)"`
	PolicyId  string `xorm:"not null comment('策略ID') VARCHAR(64)"`
	RamRoleId string `xorm:"not null comment('角色ID') VARCHAR(64)"`
	ServiceId string `xorm:"not null comment('服务ID') VARCHAR(64)"`
}

type PolicyBindArgs struct {
	RoleId    string   `json:"role_id" binding:"required"`    // 角色id
	PolicyIds []string `json:"policy_ids"`                    // 策略id集合
	ServiceId string   `json:"service_id" binding:"required"` // 服务id
	UserType  string   `json:"-"`                             // 用户类型
}
