package model

import "time"

type Project struct {
	Id          string    `xorm:"not null pk VARCHAR(64)" json:"id"`      // 唯一标识
	Name        string    `xorm:"not null VARCHAR(128)" json:"name"`      // 名称
	Extra       string    `xorm:"TEXT" json:"extra"`                      // 扩展信息
	Description string    `xorm:"TEXT" json:"description"`                // 描述
	Enabled     bool      `xorm:"TINYINT(1)" json:"enabled"`              // 是否有效
	DeptId      string    `xorm:"VARCHAR(64)" json:"dept_id"`             // 组织id
	Alias       string    `xorm:"comment('别名') VARCHAR(96)" json:"alias"` // 别名
	CreateTime  time.Time `xorm:"DATETIME created" json:"create_time"`    // 创建时间
	ModifyTime  time.Time `xorm:"DATETIME" json:"modify_time"`            // 修改时间
	DeptName    string    `xorm:"-" json:"dept_name"`                     // 部门名称
	DeptPath    string    `xorm:"-" json:"dept_path"`                     // 部门层级
}

type ProjectCreateOrUpdateArgs struct {
	DeptId        string           `json:"dept_id" binding:"required"` // 部门id
	Name          string           `json:"name" binding:"required"`    // 名称
	Description   string           `json:"description"`                // 描述
	QuotaValueMap map[string]int64 `json:"quota_value_map"`            // 配额配置
	ProjectId     string           `json:"-"`                          // 项目id
	RegionId      string           `json:"-"`                          // 区域id
}

type ProjectPageAllArgs struct {
	List
	ProjectName     string   `json:"project_name"` // 项目名称
	DeptId          string   `json:"dept_id"`      // 组织id
	CurrUserId      string   `json:"-"`            // 当前登录用户id
	DataBaseDeptIds []string `json:"-"`            // 数据库查询组织id集合
	ProjectIds      []string `json:"-"`            // 数据库查询项目id集合
}

type ProjectQuotaUpdateArgs struct {
	QuotaValueMap map[string]int64 `json:"quota_value_map"` // 配额配置
	ProjectId     string           `json:"-"`               // 项目id
	RegionId      string           `json:"-"`               // 区域id
}

type ProjectBindUserArgs struct {
	ProjectId string   `json:"-"`                         // 项目id
	UserIds   []string `query:"user_ids" json:"user_ids"` // 用户id集合
}
