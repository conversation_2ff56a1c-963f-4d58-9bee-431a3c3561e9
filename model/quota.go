package model

type QuotaType struct {
	Name        string `xorm:"not null pk VARCHAR(64)" json:"name"`      // 分类名
	Description string `xorm:"not null VARCHAR(255)" json:"description"` // 描述
	Sequence    int    `xorm:"INT(4)" json:"sequence"`                   // 排序
}

type QuotaMetric struct {
	Name              string `xorm:"not null pk VARCHAR(64)" json:"name"`       // 配额指标名称
	TypeName          string `xorm:"not null VARCHAR(64)" json:"type_name"`     // 配额类型名称
	TotalName         string `xorm:"not null VARCHAR(64)" json:"total_name"`    // 配额总量名称
	UsedName          string `xorm:"not null VARCHAR(64)" json:"used_name"`     // 使用量名称
	Unit              string `xorm:"VARCHAR(255)" json:"unit"`                  // 单位
	TypeDesc          string `xorm:"not null VARCHAR(255)" json:"type_desc"`    // 配额类型描述
	Sequence          int    `xorm:"INT(4)" json:"sequence"`                    // 排序
	Alert             string `xorm:"comment('提示信息') VARCHAR(500)" json:"alert"` // 提示信息
	TotalValue        int64  `xorm:"-" json:"total_value"`                      // 总量
	UsedValue         int64  `xorm:"-" json:"used_value"`                       // 使用量
	TopAvailableValue int64  `xorm:"-" json:"available_value"`                  // 上级可用量
}

type QuotaDepartmentValue struct {
	MetricName   string `xorm:"not null pk VARCHAR(64)" json:"metric_name"`
	DepartmentId string `xorm:"not null pk VARCHAR(64)" json:"department_id"`
	TotalValue   int64  `xorm:"BIGINT(20)" json:"total_value"`
	UsedValue    int64  `xorm:"BIGINT(20)" json:"used_value"`
	RegionId     string `xorm:"not null pk default 'RegionOne' VARCHAR(64)" json:"region_id"`
}

type QuotaProjectValue struct {
	MetricName string `xorm:"not null pk VARCHAR(64)"`
	ProjectId  string `xorm:"not null pk VARCHAR(64)"`
	TotalValue int64  `xorm:"BIGINT(20)"`
	UsedValue  int64  `xorm:"BIGINT(20)"`
	RegionId   string `xorm:"not null pk default 'RegionOne' VARCHAR(64)"`
}

type QuotaByDeparmentIdArgs struct {
	DepartmentId string `query:"department_id" json:"department_id"` // 组织部门ID
	RegionId     string `json:"-"`                                   // 区域Id
	UserId       string `json:"-"`                                   // 用户id
	UserType     string `json:"-"`                                   // 用户类型
}

type QuotaTypeDepartmentReoly struct {
	QuotaType
	QuotaDepartments []QuotaDepartmentReoly `xorm:"-" json:"quota_departments"`
}

type QuotaDepartmentReoly struct {
	Id           string        `json:"id"`            // 部门id
	Name         string        `json:"name"`          // 部门名称
	Description  string        `json:"description"`   // 描述
	QuotaMetrics []QuotaMetric `json:"quota_metrics"` // 指标
}

type QuotaProjectArgs struct {
	DeptId       string `query:"dept_id" json:"dept_id"`           // 组织部门ID
	ProjectName  string `query:"project_name" json:"project_name"` // 项目名称
	CurrUserId   string `json:"-"`                                 // 用户id
	CurrUserType string `json:"-"`                                 // 用户类型
	RegionId     string `json:"-"`                                 // 区域Id
}

type QuotaTypeProjectReoly struct {
	QuotaType
	QuotaProjects []QuotaProjectReoly `xorm:"-" json:"quota_projects"`
}

type QuotaProjectReoly struct {
	Id           string        `json:"id"`            // 项目id
	Name         string        `json:"name"`          // 项目名称
	DeptPath     string        `json:"dept_path"`     // 所属组织
	Description  string        `json:"description"`   // 描述
	QuotaMetrics []QuotaMetric `json:"quota_metrics"` // 指标
}

type QuotaDefaultReoly struct {
	QuotaType
	QuotaMetrics []QuotaMetric `json:"quota_metrics"` // 指标
}

type QuotaMetricReoly struct {
	Name       string `json:"name"`        // 配额指标名称
	TypeName   string `json:"type_name"`   // 配额类型名称
	TotalValue int64  `json:"total_value"` // 总量
	UsedValue  int64  `json:"used_value"`  // 使用量
}
