package model

import "time"

type IamRole struct {
	Id          string    `xorm:"not null pk VARCHAR(64)" json:"id"`                   // 角色ID
	Name        string    `xorm:"not null VARCHAR(64)" json:"name"`                    // 角色名称
	DeptId      string    `xorm:"VARCHAR(64)" json:"dept_id"`                          // 部门ID
	Description string    `xorm:"VARCHAR(64)" json:"description"`                      // 描述
	Type        string    `xorm:"VARCHAR(64)" json:"type"`                             // 角色类型
	CreateTime  time.Time `xorm:"comment('创建时间') DATETIME created" json:"create_time"` // 创建时间
	ModifyTime  time.Time `xorm:"comment('修改时间') DATETIME" json:"modify_time"`         // 修改时间
	DeptName    string    `xorm:"-" json:"dept_name"`                                  // 部门名称
	DeptPath    string    `xorm:"-" json:"dept_path"`                                  // 部门路径
}

type RoleListByDeptArgs struct {
	List
	Name   string `query:"name" json:"name"` // 模糊查询名称
	DeptId string `jsong:"_"`                // 部门id
}

type RoleCreateArgs struct {
	Name        string `json:"name" binding:"required"`    // 角色名称
	DeptId      string `json:"dept_id" binding:"required"` // 部门id
	Description string `json:"description"`                // 描述
}

type RoleUpdateArgs struct {
	RoleCreateArgs
	Id string `json:"id" binding:"required"` // 角色id
}

type RolePageAllArgs struct {
	List
	Name            string   `query:"name" json:"name"`       // 角色名称
	DeptId          string   `query:"dept_id" json:"dept_id"` // 组织id
	DataBaseDeptIds []string `json:"-"`                       // 数据库组织id集合
	CurrUserId      string   `json:"-"`                       // 当前登录用户id
	RoleTypeIsNull  bool     `json:"-"`                       // 组织类型是否为空
}

type RolesInnerArgs struct {
	List
	Name       string `query:"name" json:"name"` // 角色名称
	CurrUserId string `json:"-"`                 // 当前登录用户id
}
