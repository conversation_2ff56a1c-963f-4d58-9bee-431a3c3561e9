package model

import "time"

type Department struct {
	Id          string        `xorm:"not null pk comment('主键ID') VARCHAR(64)" json:"id"`     // 部门id
	Name        string        `xorm:"not null comment('名称') VARCHAR(255)" json:"name"`       // 部门名称
	Description string        `xorm:"comment('描述') VARCHAR(255)" json:"description"`         // 描述
	CreateTime  time.Time     `xorm:"comment('创建时间') DATETIME created" json:"create_time"`   // 创建时间
	ModifyTime  time.Time     `xorm:"comment('修改时间') DATETIME" json:"modify_time"`           // 修改时间
	ParentId    string        `xorm:"comment('父级部门id') VARCHAR(64)" json:"parent_id"`        // 父级部门ID
	Level       int           `xorm:"not null default 0 comment('层级') INT(11)" json:"level"` // 层级
	Children    []*Department `xorm:"-" json:"children"`                                     // 子部门
}

type CreateOrUpdateArgs struct {
	Name        string `json:"name" binding:"required"`      // 名称
	Description string `json:"description"`                  // 描述
	ParentId    string `json:"parent_id" binding:"required"` // 父级部门ID
}

type DepartmentQuotaUpdateArgs struct {
	QuotaValueMap map[string]int64 `json:"quota_value_map"` // 配额全量配置
	DeptId        string           `json:"-"`               // 组织id
	RegionId      string           `json:"-"`               // 区域id
}

type DeparmentUsersArgs struct {
	List
	Name          string `query:"name" json:"name"`           // 用户名
	QueryUserType string `query:"user_type" json:"user_type"` // 用户类型
	DeptId        string `json:"-"`                           // 部门id集合
	CurrUserId    string `json:"-"`                           // 当前登录用户id
	IsDeptMaster  bool   `json:"-"`                           // 是否是组织管理员
}

type DeparmentProjectsArgs struct {
	List
	ProjectName string `query:"project_name" json:"project_name"` // 项目名
	DeptId      string `json:"-"`                                 //组织id
}
