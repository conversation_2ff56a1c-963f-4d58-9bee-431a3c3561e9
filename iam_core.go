package iam

import (
	"context"

	"code.mysugoncloud.com/hci/iam/internal/service"
	"code.mysugoncloud.com/hci/iam/model"
)

// GetPulicKey 获取公钥
func GetPulicKey(ctx context.Context) string {
	return service.GetPulicKey(ctx)
}

// CreateToken 获取token
func CreateToken(ctx context.Context, loginUser *model.LoginUserArgs) (string, error) {
	return service.CreateToken(ctx, loginUser)
}

// ValidateToken 验证token
func ValidateToken(ctx context.Context, token string) (*model.User, error) {
	return service.ValidateToken(ctx, token)
}

// PrivateKeyGet 根据公钥获取私钥
func PrivateKeyGet(ctx context.Context, publicKey string) string {
	return service.PrivateKeyGet(ctx, publicKey)
}

// PrivateKeyDeletec 根据公钥删除
func PrivateKeyDelete(ctx context.Context, publicKey string) {
	service.PrivateKeyDelete(ctx, publicKey)
}

// QuotaByProjectId 获取项目配额
func QuotaByProjectId(ctx context.Context, projectId, regionId string, qmNames []string) ([]model.QuotaMetricReoly, error) {
	return service.QuotaByProjectId(ctx, projectId, regionId, qmNames)
}

// ProjectQuotaUpdate 项目配额修改
func ProjectQuotaUpdate(ctx context.Context, args *model.ProjectQuotaUpdateArgs) error {
	return service.ProjectQuotaUpdate(ctx, args)
}
