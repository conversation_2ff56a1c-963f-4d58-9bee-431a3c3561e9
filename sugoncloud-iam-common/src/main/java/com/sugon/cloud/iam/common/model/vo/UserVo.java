package com.sugon.cloud.iam.common.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("用户信息")
public class UserVo {
    @ApiModelProperty("用户名")
    private String name;
    @ApiModelProperty("用户别名")
    private String alias;
    @ApiModelProperty("密码")
    private String password;
    @JsonProperty(value = "default_project_id")
    @ApiModelProperty("默认项目id")
    private String defaultProjectId;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("扩展信息")
    private String extra;
}
