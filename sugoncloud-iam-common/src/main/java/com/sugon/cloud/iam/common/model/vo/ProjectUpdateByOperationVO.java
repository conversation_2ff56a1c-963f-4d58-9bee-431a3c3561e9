package com.sugon.cloud.iam.common.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/12/19 16:57
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProjectUpdateByOperationVO {
    /**
     * 延期申请：com.sugon.cloud.iam.common.constants.ProjectType.STATUS_EXTENSION_APPLY
     * 转正申请：com.sugon.cloud.iam.common.constants.ProjectType.STATUS_FORMAL_APPLY
     * 结束申请：com.sugon.cloud.iam.common.constants.ProjectType.STATUS_STOP_APPLY
     * 延期申请成功：com.sugon.cloud.iam.common.constants.ProjectType.STATUS_EXTENSION_APPLY_COMMIT
     * 转正申请成功：com.sugon.cloud.iam.common.constants.ProjectType.STATUS_FORMAL_APPLY_COMMIT
     * 结束申请成功：com.sugon.cloud.iam.common.constants.ProjectType.STATUS_STOP_APPLY_COMMIT
     */
    @NotEmpty
    private String action;
    private Date endTime;
}
