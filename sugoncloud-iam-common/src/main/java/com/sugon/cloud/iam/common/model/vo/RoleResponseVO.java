package com.sugon.cloud.iam.common.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("角色视图")
public class RoleResponseVO {

    @ApiModelProperty("角色ID")
    private String id;
    @ApiModelProperty("角色名称")
    private String name;
    @JsonProperty("dept_id")
    @ApiModelProperty("组织ID")
    private String deptId;
    @ApiModelProperty("描述")
    private String description;
    @ApiModelProperty("角色类型")
    private String type;
    @JsonProperty("create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;
    @JsonProperty("modify_time")
    @ApiModelProperty("修改时间")
    private Date modifyTime;
    @JsonProperty("dept_path")
    @ApiModelProperty("组织路径")
    private String deptPath;
}
