package com.sugon.cloud.iam.common.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "认证实体FORM")
public class LoginUserVO {

    @ApiModelProperty(value = "账号")
    public String username;
    @ApiModelProperty(value = "密码")
    public String password;
    @ApiModelProperty(value = "密钥Key")
    public String publickey;
    @JsonProperty("login_info_sign")
    @ApiModelProperty(value = "登录信息签名")
    public String loginInfoSign;
    @JsonProperty("origin_login")
    @ApiModelProperty(value = "是否原生登录")
    public boolean originLogin;
    @JsonProperty("mfa_code")
    @ApiModelProperty(value = "MFA验证码")
    public String mfaCode;
    @ApiModelProperty(value = "挑战值")
    public String challenge;
    //随机向量
    @ApiModelProperty(value = "随机向量")
    public String iv;
}
