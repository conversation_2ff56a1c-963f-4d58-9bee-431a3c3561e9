package com.sugon.cloud.iam.common.model.dto;

import lombok.Data;


/**
 * @Author: yangdingshan
 * @Date: 2025/4/18 15:39
 * @Description:
 */
@Data
public class UserLoginAlarmMetricDTO {

    /**
     * 指标名称, user_lock
     */
    private String metric;

    /**
     * 当前时间戳 秒级
     * 1692396000
     */
    private Long timestamp;

    /**
     * 锁定状态，0：解锁，1：锁定
     */
    private Integer value;

    private Tags tags;

    @Data
    public static class Tags {

        private String userId;

        private String userName;

        private String regionId;

        /**
         * 原因
         */
        private String reason;
    }

    public static UserLoginAlarmMetricDTO build(Integer value, String userId, String userName, String reason, String regionId) {
        UserLoginAlarmMetricDTO dto = new UserLoginAlarmMetricDTO();
        dto.setMetric("user_lock");
        dto.setTimestamp(System.currentTimeMillis() / 1000);
        dto.setValue(value);
        Tags tags = new Tags();
        tags.setUserId(userId);
        tags.setUserName(userName);
        tags.setReason(reason);
        tags.setRegionId(regionId);
        dto.setTags(tags);
        return dto;
    }
}

