package com.sugon.cloud.iam.common.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
@ApiModel("配额指标")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QuotaMetric {
    @ApiModelProperty(value = "配额指标名称")
    @NotEmpty(message = "配额指标名称不能为空")
    private String name;
    @ApiModelProperty(value = "配额类型名称")
    @NotEmpty(message = "配额类型名称不能为空")
    @JsonProperty("type_name")
    private String typeName;
    @ApiModelProperty(value = "配额总量名称")
    @NotEmpty(message = "配额总量不能为空")
    @JsonProperty("total_name")
    private String totalName;
    @ApiModelProperty(value = "使用量名称")
    @NotEmpty(message = "使用量不能为空")
    @JsonProperty("used_name")
    private String usedName;
    @ApiModelProperty(value = "配额总量值")
    @NotNull(message = "配额总量值不能为空")
    @JsonProperty("total_value")
    private Long totalValue;
    @ApiModelProperty(value = "配额总量值")
    @NotNull(message = "配额使用量值不能为空")
    @JsonProperty("used_value")
    private Long usedValue;
    @ApiModelProperty(value = "组织id")
    @JsonProperty("department_id")
    private String departmentId;
    @ApiModelProperty(value = "组织名称")
    @JsonProperty("department_name")
    private String departmentName;
    @ApiModelProperty(value = "项目id")
    @JsonProperty("project_id")
    private String projectId;
    @ApiModelProperty(value = "父级可用量")
    @JsonProperty("parent_available")
    private Long parentAvailable;
    @ApiModelProperty(value = "区域id")
    @JsonProperty("region_id")
    private String regionId;
}
