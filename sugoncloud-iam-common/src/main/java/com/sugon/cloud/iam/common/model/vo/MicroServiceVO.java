package com.sugon.cloud.iam.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * (MicroService)实体类
 *
 * <AUTHOR>
 * @since 2021-04-15 09:35:33
 */
@Data
@ApiModel("产品微服务")
public class MicroServiceVO {
    @ApiModelProperty(value = "主键")
    private String id;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "描述")
    private String description;
    @ApiModelProperty(value = "微服务ID")
    private String serviceId;
    @ApiModelProperty(value = "web链接")
    private String link;
    @ApiModelProperty(value = "分类id")
    private String categoryId;
    @ApiModelProperty(value = "隐藏服务")
    private Boolean navHidden;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;
    @ApiModelProperty(value = "最近一次访问时间")
    private Date latestRequestTime;
    @ApiModelProperty(value = "收藏时间,为空表示未被收藏")
    private Date collectTime;
    @ApiModelProperty(value = "微服务版本")
    private String version;
    @ApiModelProperty(value = "微服务排序")
    private Double order;
    @ApiModelProperty(value = "是否为第三方接入")
    private Boolean thirdPartAccess;
    @ApiModelProperty(value = "公开链接")
    private String publicLink;
    @ApiModelProperty(value = "是否需要策略")
    private Boolean needPolicy;
    @ApiModelProperty(value = "许可信息")
    private String licenseKey;
}
