package com.sugon.cloud.iam.common.model.dto;

import com.sugon.cloud.iam.common.model.vo.ProjectDetailVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/16 10:56
 **/
@Data
@ApiModel("用户拥有组织和项目")
public class UserOwnDeptAndProjectDTO {
    @ApiModelProperty("拥有组织")
    private List<String> departments;
    @ApiModelProperty("拥有项目")
    private List<String> projects;
    @ApiModelProperty("拥有项目详情")
    private List<ProjectDetailVO> projectInfos;
}
