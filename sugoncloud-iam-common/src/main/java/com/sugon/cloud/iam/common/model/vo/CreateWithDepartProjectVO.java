package com.sugon.cloud.iam.common.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
@ApiModel("创建无组织项目")
public class CreateWithDepartProjectVO {
    @ApiModelProperty(value = "id", hidden = true)
    private String id;
    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "项目名称不能为空")
    private String name;
    @ApiModelProperty(value = "描述")
    private String description;
    @ApiModelProperty(value = "组织ID")
    @JsonProperty("dept_id")
    private String deptId;
}