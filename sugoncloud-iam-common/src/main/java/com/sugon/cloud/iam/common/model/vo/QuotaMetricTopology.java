package com.sugon.cloud.iam.common.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel("配额指标")
public class QuotaMetricTopology {
    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "描述")
    private String description;
    @ApiModelProperty(value = "子级组织")
    private List<QuotaMetricTopology> children;
    @ApiModelProperty(value = "创建时间")
    @JsonProperty("create_time")
    private Date createTime;
    @ApiModelProperty(value = "修改时间")
    @JsonProperty("modify_time")
    private Date modifyTime;
    @ApiModelProperty(value = "domainId")
    @JsonProperty("domain_id")
    private String domainId;
    @ApiModelProperty(value = "parentId")
    @JsonProperty("parent_id")
    private String parentId;
    @ApiModelProperty(value = "组织级别")
    private Integer level;
    @JsonProperty("user_id")
    private String userId;
    @JsonProperty("user_name")
    private String userName;
    @JsonProperty("user_type")
    private String userType;
    @JsonProperty("quota_metrics")
    private List<QuotaMetric> quotaMetrics;
    @JsonProperty("type_name")
    private String typeName;
    private List<QuotaMetricTopology> projects;
    @JsonProperty("project_id")
    private String projectId;
}
