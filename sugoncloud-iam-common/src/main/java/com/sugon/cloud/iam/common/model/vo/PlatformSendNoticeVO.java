package com.sugon.cloud.iam.common.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Set;

/**
 * @Author: yangdingshan
 * @Date: 2025/1/25 11:28
 * @Description:
 */
@Data
@ApiModel("平台通知消息")
public class PlatformSendNoticeVO {

    @ApiModelProperty(value = "电话集合")
    private Set<String> phones;

    @ApiModelProperty(value = "邮箱集合")
    private Set<String> emails;

    @ApiModelProperty(value = "模板参数, 与模板顺序保持一致", required = true)
    @NotEmpty(message = "模板参数为空")
    @JsonProperty("template_params")
    private List<String> templateParams;

    @ApiModelProperty(value = "业务类型, 告警：alarm", required = true)
    @NotBlank(message = "业务类型不能为空")
    @JsonProperty("business_type")
    private String businessType;
}
