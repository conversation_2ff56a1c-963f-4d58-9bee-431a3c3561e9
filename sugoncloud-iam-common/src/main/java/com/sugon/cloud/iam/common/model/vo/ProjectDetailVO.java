package com.sugon.cloud.iam.common.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sugon.cloud.common.annotations.DocumentIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Data
@ApiModel("项目")
@AllArgsConstructor
@NoArgsConstructor
public class ProjectDetailVO {
    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "描述")
    private String description;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;
    @ApiModelProperty(value = "组织id")
    @JsonProperty("dept_id")
    private String deptId;
    @JsonProperty("dept_path")
    @ApiModelProperty("组织路径")
    private String deptPath;
    @JsonProperty("dept_name")
    @ApiModelProperty("组织名称")
    private String deptName;
    @JsonProperty("current_user_default")
    @ApiModelProperty("当前用户默认")
    private Boolean currentUserDefault = false;

    @ApiModelProperty("项目类型：正式项目(formal)、试用项目(test)")
    private String type;


    @JsonProperty("meter_types")
    @ApiModelProperty("计费类型（统一计费：contract_meter_unification , 合同计费：contract_meter_contract）")
    @NotNull(message = "计费类型不能为空")
    private List<String> meterTypes;

    @JsonProperty(value = "start_time")
    @ApiModelProperty("(试用开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    @JsonProperty(value = "end_time")
    @ApiModelProperty("试用结束时间")
    @DateTimeFormat(pattern="yyyy-MM-dd")//页面写入数据库时格式化
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    @ApiModelProperty("任务状态（转正申请中:FORMAL_APPLY,延期申请中:EXTENSION_APPLY）")
    private String taskStatus;

    @ApiModelProperty("自服务计费审批状态")
    @JsonProperty("operation_approve_type")
    private String operationApproveType;

    @DocumentIgnore
    public String getTypeName() {
        if ("formal".equals(type)) {
            return "正式项目";
        } else if ("test".equals(type)) {
            return "试用项目";
        } else {
            return "未知";
        }
    }

    @DocumentIgnore
    public List<String> getMeterTypesName() {
        if (meterTypes == null) {
            return Collections.emptyList();
        }
        return meterTypes.stream().map(meterType -> {
            if (meterType.equals("contract_meter_unification")) {
                return "统一计费";
            } else if (meterType.equals("contract_meter_contract")) {
                return "合同计费";
            } else {
                return "未知";
            }
        }).collect(Collectors.toList());
    }

    public void convertApproveType(boolean operationsEnabled) {
        if (!operationsEnabled) {
            this.setOperationApproveType("NO_REQUIRE");
        }
    }

}
