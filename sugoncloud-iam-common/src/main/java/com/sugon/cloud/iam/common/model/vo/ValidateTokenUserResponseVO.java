package com.sugon.cloud.iam.common.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "token验证实体")
public class ValidateTokenUserResponseVO {
    @JsonProperty("user_id")
    @ApiModelProperty(value = "用户id")
    private String userId;
    @ApiModelProperty(value = "用户名称")
    private String username;
    @JsonProperty("user_type")
    @ApiModelProperty(value = "用户类型")
    private String userType;
    @ApiModelProperty(value = "刷新token")
    private String refreshedToken;
    @ApiModelProperty(value = "是否允许多人登录")
    private boolean loginElsewhere = false;

}