package com.sugon.cloud.iam.common.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class QuotaRootUserEntity {
    @ApiModelProperty(value = "组织ID")
    @JsonProperty("department_id")
    private String departmentId;
    @ApiModelProperty(value = "组织名称")
    @JsonProperty("department_name")
    private String departmentName;
    @ApiModelProperty(value = "根账号名")
    @JsonProperty("user_name")
    private String userName;
    @ApiModelProperty(value = "手机号")
    @JsonProperty("user_phone")
    private String userPhone;
    @ApiModelProperty(value = "邮箱")
    @JsonProperty("user_email")
    private String userEmail;
    private String description;
    @ApiModelProperty(value = "配额类型名称")
    @JsonProperty("type_name")
    private String typeName;
    @JsonProperty("quota_metrics")
    private List<QuotaMetric> quotaMetrics;

    public String getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(String departmentId) {
        this.departmentId = departmentId;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public List<QuotaMetric> getQuotaMetrics() {
        return quotaMetrics;
    }

    public void setQuotaMetrics(List<QuotaMetric> quotaMetrics) {
        this.quotaMetrics = quotaMetrics;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserPhone() {
        return userPhone;
    }

    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }
}
