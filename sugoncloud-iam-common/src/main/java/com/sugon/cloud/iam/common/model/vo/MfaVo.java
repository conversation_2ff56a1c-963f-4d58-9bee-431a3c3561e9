package com.sugon.cloud.iam.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "MFA")
public class MfaVo {
    @ApiModelProperty("描述")
    private String desc;    //描述
    @ApiModelProperty("签名算法")
    private Integer signAlg;    //签名算法
    @ApiModelProperty("签名")
    private String signNature;  //签名
    @ApiModelProperty("原文")
    private String inData;  //原文
    @ApiModelProperty("证书")
    private String cert;    //证书
}
