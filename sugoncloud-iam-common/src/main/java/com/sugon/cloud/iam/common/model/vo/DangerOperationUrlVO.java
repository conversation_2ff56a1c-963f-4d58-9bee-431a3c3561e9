package com.sugon.cloud.iam.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: yangdingshan
 * @Date: 2025/2/9 14:44
 * @Description:
 */
@Data
@ApiModel("危险操作url")
public class DangerOperationUrlVO {

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("接口地址")
    @NotBlank(message = "接口地址不能为空")
    private String url;

    @ApiModelProperty("接口请求方式")
    @NotBlank(message = "接口请求方式不能为空")
    private String method;

    @ApiModelProperty("接口描述")
    private String description;

    @ApiModelProperty("是否使验证码失效")
    private Boolean expired;
}
