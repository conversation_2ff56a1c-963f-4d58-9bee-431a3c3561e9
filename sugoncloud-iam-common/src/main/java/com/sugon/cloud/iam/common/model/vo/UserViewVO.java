package com.sugon.cloud.iam.common.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class UserViewVO {
    @ApiModelProperty("用户Id")
    private String id;
    @ApiModelProperty("用户名")
    private String name;
    @ApiModelProperty("用户别名")
    private String alias;
    @ApiModelProperty("邮箱")
    private String email;
    @ApiModelProperty("手机号码")
    private String phone;
    @ApiModelProperty("描述")
    private String extra;
    @ApiModelProperty("用户类型")
    private String type;
    @JsonProperty("created_at")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createdAt;
    @ApiModelProperty("是否启用")
    private boolean enabled;
    @JsonFormat(pattern="yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("过期时间")
    private Date expired;
    @JsonProperty("last_login_ip")
    @ApiModelProperty("最后登录ip")
    private String lastLoginIp;
    @JsonProperty("last_active_at")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("最后登录时间")
    private Date lastActiveAt;
    @JsonProperty("allow_ip")
    @ApiModelProperty("允许登录ip")
    private String allowIp;
    @JsonProperty(value = "dept_id")
    @ApiModelProperty("所属组织Id")
    private String deptId;
    @JsonProperty("dept_path")
    @ApiModelProperty("所属组织路径")
    private String deptPath;
    @JsonProperty("hash_role")
    @ApiModelProperty("hash角色")
    private String hashRole;
    @ApiModelProperty("向量")
    private String iv;
    @JsonProperty("hash")
    @ApiModelProperty("hash")
    private String hash;
    @JsonProperty("mfa_enabled")
    @ApiModelProperty("是否开启MFA认证")
    private boolean mfaEnabled;

    @ApiModelProperty(value = "上次累计登录错误次数")
    private int lastLoginErrorCount;
    public boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
}
