package com.sugon.cloud.iam.common.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("组织")
public class DepartmentTreeDetailVO {
    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "描述")
    private String description;
    @ApiModelProperty(value = "子级组织")
    private List<DepartmentTreeDetailVO> children;
    @ApiModelProperty(value = "创建时间")
    @JsonProperty("create_time")
    private Date createTime;
    @ApiModelProperty(value = "修改时间")
    @JsonProperty("modify_time")
    private Date modifyTime;
    @ApiModelProperty(value = "domainId")
    @JsonProperty("domain_id")
    private String domainId;
    @ApiModelProperty(value = "parentId")
    @JsonProperty("parent_id")
    private String parentId;
    @ApiModelProperty(value = "组织级别")
    private Integer level;
    @JsonProperty("user_id")
    @ApiModelProperty(value = "用户ID")
    private String userId;
    @JsonProperty("user_name")
    @ApiModelProperty(value = "用户名称")
    private String userName;
    @JsonProperty("user_type")
    @ApiModelProperty(value = "用户类型")
    private String userType;
}
