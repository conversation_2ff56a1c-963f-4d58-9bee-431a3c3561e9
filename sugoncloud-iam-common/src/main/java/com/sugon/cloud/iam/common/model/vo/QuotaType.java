package com.sugon.cloud.iam.common.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sugon.cloud.common.utils.I18nUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@ApiModel("配额类型")
public class QuotaType {
    @ApiModelProperty(value = "配额类型名称")
    @NotEmpty(message = "配额类型名称不能为空")
    private String name;
    @ApiModelProperty(value = "配额类型描述")
    @NotEmpty(message = "配额类型描述不能为空")
    private String description;
    @ApiModelProperty(value = "配额所属分类")
    @JsonProperty("category_id")
    private String categoryId;
    @ApiModelProperty(value = "微服务ID")
    private String microId;
    @ApiModelProperty(value = "配额指标列表")
    private List<QuotaMetric> quotaMetricList;

    public String getDescription() {
        return I18nUtil.getMessageByKey(description, description);
    }
}
