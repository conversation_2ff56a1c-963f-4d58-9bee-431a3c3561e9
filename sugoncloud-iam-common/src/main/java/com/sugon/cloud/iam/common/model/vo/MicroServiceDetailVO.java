package com.sugon.cloud.iam.common.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * (MicroService)实体类
 *
 * <AUTHOR>
 * @since 2021-04-15 09:35:33
 */
@Data
@ApiModel("产品微服务")
@NoArgsConstructor
@AllArgsConstructor
public class MicroServiceDetailVO {
    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("微服务类别id")
    @JsonProperty("category_id")
    private String categoryId;
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("描述")
    private String description;
    @ApiModelProperty("微服务id")
    @JsonProperty("service_id")
    private String serviceId;
    @ApiModelProperty("web链接")
    private String link;
    @ApiModelProperty("创建时间")
    @JsonProperty("create_time")
    private Date createTime;
    @ApiModelProperty("修改时间")
    @JsonProperty("modify_time")
    private Date modifyTime;
    @ApiModelProperty("最近一次访问时间")
    @JsonProperty("latest_request_time")
    private Date latestRequestTime;
    @ApiModelProperty("收藏时间,为空表示未被收藏")
    @JsonProperty("collect_time")
    private Date collectTime;
    @ApiModelProperty("版本")
    private String version;
    @ApiModelProperty("隐藏服务")
    @JsonProperty("nav_hidden")
    private Boolean navHidden;
    @ApiModelProperty("排序")
    private Double order;
    @ApiModelProperty("是否为第三方接入")
    @JsonProperty("third_part_access")
    private Boolean thirdPartAccess;
    @ApiModelProperty("license_key")
    @JsonProperty("license_key")
    private String licenseKey;
    @ApiModelProperty("是否需要策略")
    private Boolean needPolicy;
    @ApiModelProperty("类型inner:内部,costom:自定义")
    private String type;
    @ApiModelProperty("打开方式Internal:内部加载,external:外部跳转")
    @JsonProperty("open_mode")
    private String openMode;
}
