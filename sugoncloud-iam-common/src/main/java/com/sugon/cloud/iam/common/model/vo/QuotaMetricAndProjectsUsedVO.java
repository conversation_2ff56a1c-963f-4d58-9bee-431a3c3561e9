package com.sugon.cloud.iam.common.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Getter
@Setter
@Builder
@AllArgsConstructor
@ApiModel("租户配额指标")
public class QuotaMetricAndProjectsUsedVO {

    @ApiModelProperty("指标名称")
    @JsonProperty("metric_name")
    @NotNull
    private String metricName;

    @JsonProperty("quota_projects_useds")
    @NotEmpty
    List<QuotaProjectsUsed> quotaProjectsUseds;

    @NotNull
    @JsonProperty("region_id")
    private String regionId;


    @Getter
    @Setter
    @Builder
    @AllArgsConstructor
    public static class QuotaProjectsUsed {

        @JsonProperty("project_id")
        private String projectId;

        @JsonProperty("use_value")
        private Long useValue;
    }

}
