package com.sugon.cloud.iam.common.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
@ApiModel("项目配额")
public class QuotaProjectValue {
    @ApiModelProperty(value = "配额指标名称")
    @NotEmpty(message = "配额指标名称不能为空")
    private String name;

    @ApiModelProperty(value = "项目名称")
    @NotEmpty(message = "项目名称不能为空")
    @JsonProperty("project_id")
    private String projectId;

    @ApiModelProperty(value = "配额总量值")
    @JsonProperty("total_value")
    private Long totalValue;

    @ApiModelProperty(value = "配额总量值")
    @NotNull(message = "配额使用量值不能为空")
    @JsonProperty("used_value")
    private Long usedValue;

    @ApiModelProperty(value = "区域id")
    @JsonProperty("region_id")
    private String regionId;

}
