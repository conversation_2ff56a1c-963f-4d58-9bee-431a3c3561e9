package com.sugon.cloud.iam.common.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/16 14:09
 **/
@Data
@ApiModel("域信息")
public class RegionInfoDTO {
    @ApiModelProperty(value = "区域管理描述")
    @JsonProperty("description")
    private String description;
    @ApiModelProperty(value = "区域ID")
    private String id;
    @ApiModelProperty(value = "区域管理类型:sugoncloud(曙光云)|vmware")
    private String type;
    @JsonProperty("vmware_param")
    private VmwareParamDTO vmwareParam;
}
