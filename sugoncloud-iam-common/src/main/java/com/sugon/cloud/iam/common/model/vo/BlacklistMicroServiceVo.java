package com.sugon.cloud.iam.common.model.vo;

import com.sugon.cloud.iam.common.enums.BlacklistTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName blacklist_micro_service
 */
@Data
public class BlacklistMicroServiceVo implements Serializable {
    /**
     * 主键，菜单主键，或者微服务micro主键，微服务类型category主键
     */
    private String id;

    /**
     * 菜单代表menu，微服务micro，微服务类型category, 微服务自定义
     */
    private BlacklistTypeEnum type;

    /**
     * 名称
     */
    private String alias;

    private String extra;

    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        BlacklistMicroServiceVo other = (BlacklistMicroServiceVo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", type=").append(type);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}