package com.sugon.cloud.iam.common.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "创建endpoint响应参数")
public class CreateEndpointResponseParam {
    @JsonProperty("region_id")
    @ApiModelProperty("区域id")
    private String regionId;
    @ApiModelProperty("区域")
    private String region;
    @JsonProperty("interface")
    @ApiModelProperty("接口")
    private String interfaceParam;
    @JsonProperty("service_id")
    @ApiModelProperty("服务id")
    private String serviceId;
    @ApiModelProperty("url")
    private String url;
    @ApiModelProperty("endpoint id")
    private String id;
    @ApiModelProperty("是否启用")
    private Boolean enabled;
    @ApiModelProperty("链接")
    private CreateResourceResponseLinksParam links;
    @JsonProperty("service_name")
    @ApiModelProperty("服务名称")
    private String serviceName;
}
