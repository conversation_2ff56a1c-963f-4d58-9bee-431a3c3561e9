package com.sugon.cloud.iam.common.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("组织")
public class DepartmentDetailVO {
    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "描述")
    private String description;
    @ApiModelProperty(value = "父级组织ID")
    @JsonProperty("parent_id")
    private String parentId;
    @ApiModelProperty(value = "创建时间")
    @JsonProperty("create_time")
    private Date createTime;
    @ApiModelProperty(value = "修改时间")
    @JsonProperty("modify_time")
    private Date modifyTime;
    @ApiModelProperty(value = "domainId")
    @JsonProperty("domain_id")
    private String domainId;
}
