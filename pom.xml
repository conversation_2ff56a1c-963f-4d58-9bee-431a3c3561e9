<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.sugon.cloud</groupId>
        <artifactId>sugoncloud-parent</artifactId>
        <version>20250930-STABLE-SNAPSHOT</version>
    </parent>
    <artifactId>sugoncloud-iam</artifactId>
    <name>sugoncloud-iam</name>
    <description>sugoncloud-iam</description>
    <packaging>pom</packaging>

    <modules>
        <module>sugoncloud-iam-common</module>
        <module>sugoncloud-iam-client</module>
        <module>sugoncloud-iam-api</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.sugon.cloud</groupId>
                <artifactId>sugoncloud-iam-common</artifactId>
                <version>${sugoncloud-common.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>2.4.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>2.8.1</version>
            </dependency>
            <dependency>
                <groupId>io.lettuce</groupId>
                <artifactId>lettuce-core</artifactId>
                <version>6.0.1.RELEASE</version>
            </dependency>
        </dependencies>

    </dependencyManagement>
    <repositories>
        <repository>
            <id>sugoncloud-public</id>
            <url>http://172.22.5.34:9996/repository/sugoncloud-public/</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </snapshots>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </releases>
        </repository>
    </repositories>
    <distributionManagement>
        <repository>
            <id>sugoncloud-releases</id>
            <name>Sugoncloud Release Repository</name>
            <url>http://172.22.5.34:9996/repository/sugoncloud-releases/</url>
        </repository>
        <snapshotRepository>
            <id>sugoncloud-snapshots</id>
            <name>Sugoncloud Snapshot Repository</name>
            <url>http://172.22.5.34:9996/repository/sugoncloud-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
