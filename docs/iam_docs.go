// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplateiam = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/iam/departments": {
            "post": {
                "tags": [
                    "组织部门API"
                ],
                "summary": "创建子组织",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.CreateOrUpdateArgs"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "$ref": "#/definitions/model.CreateOrUpdateArgs"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/departments/alls": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "组织部门API"
                ],
                "summary": "查询所有部门及各个部门子节点",
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "$ref": "#/definitions/model.Department"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/departments/quota/{id}": {
            "put": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "组织部门API"
                ],
                "summary": "修改一级组织配额",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.DepartmentQuotaUpdateArgs"
                        }
                    },
                    {
                        "type": "string",
                        "description": "组织id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "$ref": "#/definitions/model.ResultModel"
                        }
                    }
                }
            }
        },
        "/iam/departments/{id}": {
            "get": {
                "tags": [
                    "组织部门API"
                ],
                "summary": "根据id获取部门详情",
                "parameters": [
                    {
                        "type": "string",
                        "description": "部门id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "$ref": "#/definitions/model.Department"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "put": {
                "tags": [
                    "组织部门API"
                ],
                "summary": "修改组织",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.CreateOrUpdateArgs"
                        }
                    },
                    {
                        "type": "string",
                        "description": "组织id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "$ref": "#/definitions/model.CreateOrUpdateArgs"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "delete": {
                "tags": [
                    "组织部门API"
                ],
                "summary": "删除组织",
                "parameters": [
                    {
                        "type": "string",
                        "description": "组织id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "$ref": "#/definitions/model.ResultModel"
                        }
                    }
                }
            }
        },
        "/iam/departments/{id}/projects": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "组织部门API"
                ],
                "summary": "组织项目列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "分页索引",
                        "name": "page_num",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "分页大小",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "项目名",
                        "name": "project_name",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "组织id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "$ref": "#/definitions/model.PageCL-model_Project"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/departments/{id}/roles": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "组织部门API"
                ],
                "summary": "通过部门ID查询角色列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "组织id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "部门id",
                        "name": "deptId",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "模糊查询名称",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "分页索引",
                        "name": "page_num",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "分页大小",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/model.IamRole"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/departments/{id}/users": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "组织部门API"
                ],
                "summary": "通过部门ID查询用户列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户名",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "分页索引",
                        "name": "page_num",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "分页大小",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "用户类型",
                        "name": "user_type",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "组织id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "$ref": "#/definitions/model.PageCL-model_User"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/login/code": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "登录API"
                ],
                "summary": "获取验证码",
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/login/error": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "登录API"
                ],
                "summary": "获取用户登录错误次数",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户名",
                        "name": "username",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "type": "integer"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/login/out": {
            "delete": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "登录API"
                ],
                "summary": "登出",
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/micro-service-categories": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "菜单API"
                ],
                "summary": "查询菜单类别信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "微服务类别名称",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "分页索引",
                        "name": "page_num",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "分页大小",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "$ref": "#/definitions/model.PageCL-model_MicroServiceCategory"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "菜单API"
                ],
                "summary": "更新菜单类别信息",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.MicroServiceCategoryUpdateArgs"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "$ref": "#/definitions/model.ResultModel"
                        }
                    }
                }
            }
        },
        "/iam/micro-services": {
            "put": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "菜单API"
                ],
                "summary": "修改菜单信息",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.MicroServiceUpdateArgs"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "$ref": "#/definitions/model.ResultModel"
                        }
                    }
                }
            }
        },
        "/iam/oauth/token": {
            "post": {
                "tags": [
                    "登录API"
                ],
                "summary": "获取token",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.LoginUserArgs"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/overview/count": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "概览API"
                ],
                "summary": "组织/项目/角色/用户数量",
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "$ref": "#/definitions/model.OverviewCountReoly"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/projects": {
            "post": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "项目API"
                ],
                "summary": "创建项目",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.ProjectCreateOrUpdateArgs"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "$ref": "#/definitions/model.ResultModel"
                        }
                    }
                }
            }
        },
        "/iam/projects/all": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "项目API"
                ],
                "summary": "根据用户类型查询所有项目列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "组织id",
                        "name": "dept_id",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "分页索引",
                        "name": "page_num",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "分页大小",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "项目名称",
                        "name": "project_name",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "$ref": "#/definitions/model.PageCL-model_Project"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/projects/quota/{id}": {
            "put": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "项目API"
                ],
                "summary": "修改项目配额",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.ProjectQuotaUpdateArgs"
                        }
                    },
                    {
                        "type": "string",
                        "description": "项目id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "$ref": "#/definitions/model.ResultModel"
                        }
                    }
                }
            }
        },
        "/iam/projects/{id}": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "项目API"
                ],
                "summary": "项目详情",
                "parameters": [
                    {
                        "type": "string",
                        "description": "项目id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "$ref": "#/definitions/model.Project"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "项目API"
                ],
                "summary": "修改项目",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.ProjectCreateOrUpdateArgs"
                        }
                    },
                    {
                        "type": "string",
                        "description": "项目id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "$ref": "#/definitions/model.ResultModel"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "项目API"
                ],
                "summary": "删除项目",
                "parameters": [
                    {
                        "type": "string",
                        "description": "项目id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "$ref": "#/definitions/model.ResultModel"
                        }
                    }
                }
            }
        },
        "/iam/projects/{id}/bind-users": {
            "post": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "项目API"
                ],
                "summary": "项目关联用户",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.ProjectBindUserArgs"
                        }
                    },
                    {
                        "type": "string",
                        "description": "项目id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "$ref": "#/definitions/model.ResultModel"
                        }
                    }
                }
            }
        },
        "/iam/projects/{id}/unbind-users": {
            "post": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "项目API"
                ],
                "summary": "项目批量解绑用户",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.ProjectBindUserArgs"
                        }
                    },
                    {
                        "type": "string",
                        "description": "项目id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "$ref": "#/definitions/model.ResultModel"
                        }
                    }
                }
            }
        },
        "/iam/projects/{id}/users": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "项目API"
                ],
                "summary": "查询项目所有用户",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户名",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "分页索引",
                        "name": "page_num",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "分页大小",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "$ref": "#/definitions/model.PageCL-model_User"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/publickey": {
            "get": {
                "tags": [
                    "密钥API"
                ],
                "summary": "获取公钥",
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/quotas/department": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "配额API"
                ],
                "summary": "查询部门配额",
                "parameters": [
                    {
                        "type": "string",
                        "description": "组织id，创建一级组织时，不传获取默认配额项",
                        "name": "dept_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/model.QuotaDefaultReoly"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/quotas/department/overview": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "配额API"
                ],
                "summary": "概览查询部门配额",
                "parameters": [
                    {
                        "type": "string",
                        "description": "组织部门ID",
                        "name": "department_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/model.QuotaTypeDepartmentReoly"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/quotas/project": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "配额API"
                ],
                "summary": "查询项目配额",
                "parameters": [
                    {
                        "type": "string",
                        "description": "组织id",
                        "name": "dept_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "项目id",
                        "name": "project_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/model.QuotaDefaultReoly"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/quotas/project/overview": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "配额API"
                ],
                "summary": "概览查询项目配额",
                "parameters": [
                    {
                        "type": "string",
                        "description": "组织部门ID",
                        "name": "dept_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "项目名称",
                        "name": "project_name",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/model.QuotaTypeProjectReoly"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/ram/policy": {
            "post": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "ram策略管理"
                ],
                "summary": "创建策略",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.RamPolicy"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "$ref": "#/definitions/model.ResultModel"
                        }
                    }
                }
            }
        },
        "/iam/ram/policy/bind-policy": {
            "put": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "ram策略管理"
                ],
                "summary": "角色绑定策略",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.PolicyBindArgs"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "$ref": "#/definitions/model.ResultModel"
                        }
                    }
                }
            }
        },
        "/iam/ram/policy/generate-sql": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "ram策略管理"
                ],
                "summary": "生成策略的sql",
                "responses": {}
            }
        },
        "/iam/ram/policy/menu": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "ram策略管理"
                ],
                "summary": "获取菜单列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "服务id",
                        "name": "service_id",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "$ref": "#/definitions/model.RamPolicy"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/ram/policy/name": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "ram策略管理"
                ],
                "summary": "根据名称查询策略",
                "parameters": [
                    {
                        "type": "string",
                        "description": "服务id",
                        "name": "service_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "策略名称",
                        "name": "name",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "$ref": "#/definitions/model.RamPolicy"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/ram/policy/tree": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "ram策略管理"
                ],
                "summary": "获取策略树形列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "服务id",
                        "name": "service_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "角色id",
                        "name": "role_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/model.RamPolicy"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/ram/policy/{id}": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "ram策略管理"
                ],
                "summary": "根据id查询策略",
                "parameters": [
                    {
                        "type": "string",
                        "description": "id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "$ref": "#/definitions/model.RamPolicy"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "ram策略管理"
                ],
                "summary": "修改策略",
                "parameters": [
                    {
                        "type": "string",
                        "description": "id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "请求参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.RamPolicy"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "$ref": "#/definitions/model.ResultModel"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "ram策略管理"
                ],
                "summary": "根据id删除策略",
                "parameters": [
                    {
                        "type": "string",
                        "description": "id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "$ref": "#/definitions/model.ResultModel"
                        }
                    }
                }
            }
        },
        "/iam/roles": {
            "put": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "角色API"
                ],
                "summary": "更新角色",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.RoleUpdateArgs"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "$ref": "#/definitions/model.ResultModel"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "角色API"
                ],
                "summary": "添加角色",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.RoleCreateArgs"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "$ref": "#/definitions/model.ResultModel"
                        }
                    }
                }
            }
        },
        "/iam/roles/all": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "角色API"
                ],
                "summary": "查询所有角色",
                "parameters": [
                    {
                        "type": "string",
                        "description": "组织id",
                        "name": "dept_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "角色名称",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "分页索引",
                        "name": "page_num",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "分页大小",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "$ref": "#/definitions/model.PageCL-model_IamRole"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/roles/inner-role-list": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "角色API"
                ],
                "summary": "查询内置角色",
                "parameters": [
                    {
                        "type": "string",
                        "description": "角色名称",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "分页索引",
                        "name": "page_num",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "分页大小",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "$ref": "#/definitions/model.PageCL-model_IamRole"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/roles/user/{user_id}": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "角色API"
                ],
                "summary": "通过用户Id查询角色列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "项目id",
                        "name": "project_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/model.IamRole"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/roles/{role_id}": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "角色API"
                ],
                "summary": "角色详情",
                "parameters": [
                    {
                        "type": "string",
                        "description": "角色id",
                        "name": "role_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "$ref": "#/definitions/model.IamRole"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "角色API"
                ],
                "summary": "删除角色",
                "parameters": [
                    {
                        "type": "string",
                        "description": "角色id",
                        "name": "role_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "$ref": "#/definitions/model.ResultModel"
                        }
                    }
                }
            }
        },
        "/iam/users": {
            "put": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "用户API"
                ],
                "summary": "修改用户",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.UserUpdateArgs"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "$ref": "#/definitions/model.ResultModel"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "用户API"
                ],
                "summary": "注册用户",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.UserCreateArgs"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/users/all": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "用户API"
                ],
                "summary": "查询所有用户列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "组织id",
                        "name": "dept_ids",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "用户名",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "分页索引",
                        "name": "page_num",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "分页大小",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "用户类型",
                        "name": "user_type",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "$ref": "#/definitions/model.PageCL-model_User"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/users/sub": {
            "post": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "用户API"
                ],
                "summary": "创建用户",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.UserCreateArgs"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/users/type": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "用户API"
                ],
                "summary": "获取用户类型标签",
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/model.UserTypeReoly"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/users/unlock/{user_id}": {
            "put": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "用户API"
                ],
                "summary": "解锁用户",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "$ref": "#/definitions/model.ResultModel"
                        }
                    }
                }
            }
        },
        "/iam/users/user_id/{user_id}/status/{status}": {
            "put": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "用户API"
                ],
                "summary": "设置用户状态",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "boolean",
                        "description": "用户状态",
                        "name": "status",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "$ref": "#/definitions/model.ResultModel"
                        }
                    }
                }
            }
        },
        "/iam/users/{user_id}": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "用户API"
                ],
                "summary": "用户详情",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "$ref": "#/definitions/model.User"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "用户API"
                ],
                "summary": "删除用户",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "$ref": "#/definitions/model.ResultModel"
                        }
                    }
                }
            }
        },
        "/iam/users/{user_id}/access": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "用户API"
                ],
                "summary": "获取用户访问权限",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "$ref": "#/definitions/model.UserAccess"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/users/{user_id}/bind-projects": {
            "post": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "用户API"
                ],
                "summary": "用户绑定项目",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.UserBindProjectsArgs"
                        }
                    },
                    {
                        "type": "string",
                        "description": "用户id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "$ref": "#/definitions/model.ResultModel"
                        }
                    }
                }
            }
        },
        "/iam/users/{user_id}/bind-roles": {
            "post": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "用户API"
                ],
                "summary": "用户批量绑定部门角色",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.UserBindRolesArgs"
                        }
                    },
                    {
                        "type": "string",
                        "description": "用户id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "$ref": "#/definitions/model.ResultModel"
                        }
                    }
                }
            }
        },
        "/iam/users/{user_id}/expired": {
            "post": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "用户API"
                ],
                "summary": "设置用户过期时间",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "过期时间(yyyy-MM-dd)",
                        "name": "expired",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "$ref": "#/definitions/model.ResultModel"
                        }
                    }
                }
            }
        },
        "/iam/users/{user_id}/password": {
            "put": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "用户API"
                ],
                "summary": "重置密码",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "请求参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.UserResetPasswordArgs"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "$ref": "#/definitions/model.ResultModel"
                        }
                    }
                }
            }
        },
        "/iam/users/{user_id}/projects": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "用户API"
                ],
                "summary": "用户所拥有的项目列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "分页索引",
                        "name": "page_num",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "分页大小",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "项目名",
                        "name": "project_name",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "用户id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "$ref": "#/definitions/model.PageCL-model_Project"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/users/{user_id}/roles": {
            "get": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "用户API"
                ],
                "summary": "用户已绑定的角色列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "分页索引",
                        "name": "page_num",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "分页大小",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "项目ID",
                        "name": "project_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "角色名称",
                        "name": "role_name",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "用户id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/model.ResultModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "$ref": "#/definitions/model.PageCL-model_IamRole"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/iam/users/{user_id}/set-access": {
            "post": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "用户API"
                ],
                "summary": "设置用户访问权限",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "请求参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.UserAccessSetArgs"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "$ref": "#/definitions/model.ResultModel"
                        }
                    }
                }
            }
        },
        "/iam/users/{user_id}/unbind-projects": {
            "post": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "用户API"
                ],
                "summary": "用户批量解绑项目",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.UserBindProjectsArgs"
                        }
                    },
                    {
                        "type": "string",
                        "description": "用户id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "$ref": "#/definitions/model.ResultModel"
                        }
                    }
                }
            }
        },
        "/iam/users/{user_id}/unbind-roles": {
            "post": {
                "security": [
                    {
                        "Authorization": []
                    },
                    {
                        "Current-Project-Id": []
                    },
                    {
                        "Projectid": []
                    }
                ],
                "tags": [
                    "用户API"
                ],
                "summary": "用户批量解绑角色",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.UserUnBindRolesArgs"
                        }
                    },
                    {
                        "type": "string",
                        "description": "用户id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回数据",
                        "schema": {
                            "$ref": "#/definitions/model.ResultModel"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "model.CreateOrUpdateArgs": {
            "type": "object",
            "required": [
                "name",
                "parent_id"
            ],
            "properties": {
                "description": {
                    "description": "描述",
                    "type": "string"
                },
                "name": {
                    "description": "名称",
                    "type": "string"
                },
                "parent_id": {
                    "description": "父级部门ID",
                    "type": "string"
                }
            }
        },
        "model.Department": {
            "type": "object",
            "properties": {
                "children": {
                    "description": "子部门",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.Department"
                    }
                },
                "create_time": {
                    "description": "创建时间",
                    "type": "string"
                },
                "description": {
                    "description": "描述",
                    "type": "string"
                },
                "id": {
                    "description": "部门id",
                    "type": "string"
                },
                "level": {
                    "description": "层级",
                    "type": "integer"
                },
                "modify_time": {
                    "description": "修改时间",
                    "type": "string"
                },
                "name": {
                    "description": "部门名称",
                    "type": "string"
                },
                "parent_id": {
                    "description": "父级部门ID",
                    "type": "string"
                }
            }
        },
        "model.DepartmentQuotaUpdateArgs": {
            "type": "object",
            "properties": {
                "quota_value_map": {
                    "description": "配额全量配置",
                    "type": "object",
                    "additionalProperties": {
                        "type": "integer"
                    }
                }
            }
        },
        "model.IamRole": {
            "type": "object",
            "properties": {
                "create_time": {
                    "description": "创建时间",
                    "type": "string"
                },
                "dept_id": {
                    "description": "部门ID",
                    "type": "string"
                },
                "dept_name": {
                    "description": "部门名称",
                    "type": "string"
                },
                "dept_path": {
                    "description": "部门路径",
                    "type": "string"
                },
                "description": {
                    "description": "描述",
                    "type": "string"
                },
                "id": {
                    "description": "角色ID",
                    "type": "string"
                },
                "modify_time": {
                    "description": "修改时间",
                    "type": "string"
                },
                "name": {
                    "description": "角色名称",
                    "type": "string"
                },
                "type": {
                    "description": "角色类型",
                    "type": "string"
                }
            }
        },
        "model.LoginUserArgs": {
            "type": "object",
            "properties": {
                "_": {
                    "description": "请求ip地址，内部调用不需要",
                    "type": "string"
                },
                "code": {
                    "description": "验证码",
                    "type": "string"
                },
                "password": {
                    "description": "密码",
                    "type": "string"
                },
                "public_key": {
                    "description": "加密公钥",
                    "type": "string"
                },
                "username": {
                    "description": "用户名",
                    "type": "string"
                }
            }
        },
        "model.MicroService": {
            "type": "object",
            "properties": {
                "category_id": {
                    "type": "string"
                },
                "collect_time": {
                    "type": "string"
                },
                "create_time": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "latest_request_time": {
                    "type": "string"
                },
                "link": {
                    "type": "string"
                },
                "modify_time": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "nav_hidden": {
                    "type": "boolean"
                },
                "order": {
                    "type": "number"
                }
            }
        },
        "model.MicroServiceCategory": {
            "type": "object",
            "properties": {
                "children": {
                    "description": "子分类",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.MicroService"
                    }
                },
                "create_time": {
                    "description": "创建时间",
                    "type": "string"
                },
                "description": {
                    "description": "描述",
                    "type": "string"
                },
                "id": {
                    "description": "唯一标识",
                    "type": "string"
                },
                "modify_time": {
                    "description": "修改时间",
                    "type": "string"
                },
                "name": {
                    "description": "名称",
                    "type": "string"
                },
                "nav_hidden": {
                    "description": "是否隐藏",
                    "type": "boolean"
                },
                "order": {
                    "description": "排序",
                    "type": "number"
                }
            }
        },
        "model.MicroServiceCategoryUpdateArgs": {
            "type": "object",
            "required": [
                "id"
            ],
            "properties": {
                "id": {
                    "description": "主键",
                    "type": "string"
                },
                "nav_hidden": {
                    "description": "是否隐藏分类",
                    "type": "boolean"
                },
                "order": {
                    "description": "排序",
                    "type": "number"
                }
            }
        },
        "model.MicroServiceUpdateArgs": {
            "type": "object",
            "required": [
                "id"
            ],
            "properties": {
                "id": {
                    "description": "主键",
                    "type": "string"
                },
                "nav_hidden": {
                    "description": "是否隐藏分类",
                    "type": "boolean"
                },
                "order": {
                    "description": "排序",
                    "type": "number"
                }
            }
        },
        "model.OverviewCountReoly": {
            "type": "object",
            "properties": {
                "dept_count": {
                    "description": "组织数量",
                    "type": "integer"
                },
                "project_count": {
                    "description": "项目数量",
                    "type": "integer"
                },
                "role_count": {
                    "description": "角色数量",
                    "type": "integer"
                },
                "user_count": {
                    "description": "用户数量",
                    "type": "integer"
                }
            }
        },
        "model.PageCL-model_IamRole": {
            "type": "object",
            "properties": {
                "list": {
                    "description": "数据",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.IamRole"
                    }
                },
                "page_count": {
                    "description": "页数",
                    "type": "integer"
                },
                "page_num": {
                    "description": "分页索引",
                    "type": "integer"
                },
                "page_size": {
                    "description": "分页大小",
                    "type": "integer"
                },
                "size": {
                    "description": "当前页条数",
                    "type": "integer"
                },
                "total": {
                    "description": "总条数",
                    "type": "integer"
                }
            }
        },
        "model.PageCL-model_MicroServiceCategory": {
            "type": "object",
            "properties": {
                "list": {
                    "description": "数据",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.MicroServiceCategory"
                    }
                },
                "page_count": {
                    "description": "页数",
                    "type": "integer"
                },
                "page_num": {
                    "description": "分页索引",
                    "type": "integer"
                },
                "page_size": {
                    "description": "分页大小",
                    "type": "integer"
                },
                "size": {
                    "description": "当前页条数",
                    "type": "integer"
                },
                "total": {
                    "description": "总条数",
                    "type": "integer"
                }
            }
        },
        "model.PageCL-model_Project": {
            "type": "object",
            "properties": {
                "list": {
                    "description": "数据",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.Project"
                    }
                },
                "page_count": {
                    "description": "页数",
                    "type": "integer"
                },
                "page_num": {
                    "description": "分页索引",
                    "type": "integer"
                },
                "page_size": {
                    "description": "分页大小",
                    "type": "integer"
                },
                "size": {
                    "description": "当前页条数",
                    "type": "integer"
                },
                "total": {
                    "description": "总条数",
                    "type": "integer"
                }
            }
        },
        "model.PageCL-model_User": {
            "type": "object",
            "properties": {
                "list": {
                    "description": "数据",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.User"
                    }
                },
                "page_count": {
                    "description": "页数",
                    "type": "integer"
                },
                "page_num": {
                    "description": "分页索引",
                    "type": "integer"
                },
                "page_size": {
                    "description": "分页大小",
                    "type": "integer"
                },
                "size": {
                    "description": "当前页条数",
                    "type": "integer"
                },
                "total": {
                    "description": "总条数",
                    "type": "integer"
                }
            }
        },
        "model.PolicyBindArgs": {
            "type": "object",
            "required": [
                "role_id",
                "service_id"
            ],
            "properties": {
                "policy_ids": {
                    "description": "策略id集合",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "role_id": {
                    "description": "角色id",
                    "type": "string"
                },
                "service_id": {
                    "description": "服务id",
                    "type": "string"
                }
            }
        },
        "model.Project": {
            "type": "object",
            "properties": {
                "alias": {
                    "description": "别名",
                    "type": "string"
                },
                "create_time": {
                    "description": "创建时间",
                    "type": "string"
                },
                "dept_id": {
                    "description": "组织id",
                    "type": "string"
                },
                "dept_name": {
                    "description": "部门名称",
                    "type": "string"
                },
                "dept_path": {
                    "description": "部门层级",
                    "type": "string"
                },
                "description": {
                    "description": "描述",
                    "type": "string"
                },
                "enabled": {
                    "description": "是否有效",
                    "type": "boolean"
                },
                "extra": {
                    "description": "扩展信息",
                    "type": "string"
                },
                "id": {
                    "description": "唯一标识",
                    "type": "string"
                },
                "modify_time": {
                    "description": "修改时间",
                    "type": "string"
                },
                "name": {
                    "description": "名称",
                    "type": "string"
                }
            }
        },
        "model.ProjectBindUserArgs": {
            "type": "object",
            "properties": {
                "user_ids": {
                    "description": "用户id集合",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "model.ProjectCreateOrUpdateArgs": {
            "type": "object",
            "required": [
                "dept_id",
                "name"
            ],
            "properties": {
                "dept_id": {
                    "description": "部门id",
                    "type": "string"
                },
                "description": {
                    "description": "描述",
                    "type": "string"
                },
                "name": {
                    "description": "名称",
                    "type": "string"
                },
                "quota_value_map": {
                    "description": "配额配置",
                    "type": "object",
                    "additionalProperties": {
                        "type": "integer"
                    }
                }
            }
        },
        "model.ProjectQuotaUpdateArgs": {
            "type": "object",
            "properties": {
                "quota_value_map": {
                    "description": "配额配置",
                    "type": "object",
                    "additionalProperties": {
                        "type": "integer"
                    }
                }
            }
        },
        "model.QuotaDefaultReoly": {
            "type": "object",
            "properties": {
                "description": {
                    "description": "描述",
                    "type": "string"
                },
                "name": {
                    "description": "分类名",
                    "type": "string"
                },
                "quota_metrics": {
                    "description": "指标",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.QuotaMetric"
                    }
                },
                "sequence": {
                    "description": "排序",
                    "type": "integer"
                }
            }
        },
        "model.QuotaDepartmentReoly": {
            "type": "object",
            "properties": {
                "description": {
                    "description": "描述",
                    "type": "string"
                },
                "id": {
                    "description": "部门id",
                    "type": "string"
                },
                "name": {
                    "description": "部门名称",
                    "type": "string"
                },
                "quota_metrics": {
                    "description": "指标",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.QuotaMetric"
                    }
                }
            }
        },
        "model.QuotaMetric": {
            "type": "object",
            "properties": {
                "alert": {
                    "description": "提示信息",
                    "type": "string"
                },
                "available_value": {
                    "description": "上级可用量",
                    "type": "integer"
                },
                "name": {
                    "description": "配额指标名称",
                    "type": "string"
                },
                "sequence": {
                    "description": "排序",
                    "type": "integer"
                },
                "total_name": {
                    "description": "配额总量名称",
                    "type": "string"
                },
                "total_value": {
                    "description": "总量",
                    "type": "integer"
                },
                "type_desc": {
                    "description": "配额类型描述",
                    "type": "string"
                },
                "type_name": {
                    "description": "配额类型名称",
                    "type": "string"
                },
                "unit": {
                    "description": "单位",
                    "type": "string"
                },
                "used_name": {
                    "description": "使用量名称",
                    "type": "string"
                },
                "used_value": {
                    "description": "使用量",
                    "type": "integer"
                }
            }
        },
        "model.QuotaProjectReoly": {
            "type": "object",
            "properties": {
                "dept_path": {
                    "description": "所属组织",
                    "type": "string"
                },
                "description": {
                    "description": "描述",
                    "type": "string"
                },
                "id": {
                    "description": "项目id",
                    "type": "string"
                },
                "name": {
                    "description": "项目名称",
                    "type": "string"
                },
                "quota_metrics": {
                    "description": "指标",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.QuotaMetric"
                    }
                }
            }
        },
        "model.QuotaTypeDepartmentReoly": {
            "type": "object",
            "properties": {
                "description": {
                    "description": "描述",
                    "type": "string"
                },
                "name": {
                    "description": "分类名",
                    "type": "string"
                },
                "quota_departments": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.QuotaDepartmentReoly"
                    }
                },
                "sequence": {
                    "description": "排序",
                    "type": "integer"
                }
            }
        },
        "model.QuotaTypeProjectReoly": {
            "type": "object",
            "properties": {
                "description": {
                    "description": "描述",
                    "type": "string"
                },
                "name": {
                    "description": "分类名",
                    "type": "string"
                },
                "quota_projects": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.QuotaProjectReoly"
                    }
                },
                "sequence": {
                    "description": "排序",
                    "type": "integer"
                }
            }
        },
        "model.RamPolicy": {
            "type": "object",
            "properties": {
                "action": {
                    "description": "方法",
                    "type": "string"
                },
                "can_delete": {
                    "description": "能否删除（0：不能删除，1：能删除）",
                    "type": "string"
                },
                "children": {
                    "description": "子策略",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.RamPolicy"
                    }
                },
                "comments": {
                    "description": "描述",
                    "type": "string"
                },
                "create_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "create_by": {
                    "description": "创建人",
                    "type": "string"
                },
                "effect": {
                    "description": "用户系统：0，云平台：1， 大数据：2",
                    "type": "string"
                },
                "icon": {
                    "description": "前端菜单图标",
                    "type": "string"
                },
                "index": {
                    "description": "前端菜单路由",
                    "type": "string"
                },
                "link_id": {
                    "description": "前端权限联动",
                    "type": "string"
                },
                "method_url": {
                    "description": "方法请求路径",
                    "type": "string"
                },
                "owner_id": {
                    "description": "主账号ID",
                    "type": "string"
                },
                "parent_id": {
                    "description": "父ID",
                    "type": "string"
                },
                "policy_document": {
                    "description": "策略内容",
                    "type": "string"
                },
                "policy_name": {
                    "description": "策略名称",
                    "type": "string"
                },
                "policy_type": {
                    "description": "策略类型: 0: 菜单类型 | 1：非菜单类型",
                    "type": "string"
                },
                "resource_link_id": {
                    "description": "资源策略关联到策略的id",
                    "type": "string"
                },
                "resource_policy_flag": {
                    "description": "是否为资源，1为资源，0非资源，null为用户不能查看",
                    "type": "string"
                },
                "service_id": {
                    "description": "服务ID",
                    "type": "string"
                },
                "sort": {
                    "description": "排序",
                    "type": "integer"
                },
                "uuid": {
                    "description": "唯一标识",
                    "type": "string"
                }
            }
        },
        "model.ResultModel": {
            "type": "object",
            "properties": {
                "content": {
                    "description": "返回内容"
                },
                "debug_msg": {
                    "description": "debug消息",
                    "type": "string"
                },
                "resource": {
                    "description": "资源",
                    "type": "string"
                },
                "status_code": {
                    "description": "状态码 0：失败  1：成功",
                    "type": "integer"
                },
                "status_mes": {
                    "description": "执行结果消息",
                    "type": "string"
                }
            }
        },
        "model.RoleCreateArgs": {
            "type": "object",
            "required": [
                "dept_id",
                "name"
            ],
            "properties": {
                "dept_id": {
                    "description": "部门id",
                    "type": "string"
                },
                "description": {
                    "description": "描述",
                    "type": "string"
                },
                "name": {
                    "description": "角色名称",
                    "type": "string"
                }
            }
        },
        "model.RoleUpdateArgs": {
            "type": "object",
            "required": [
                "dept_id",
                "id",
                "name"
            ],
            "properties": {
                "dept_id": {
                    "description": "部门id",
                    "type": "string"
                },
                "description": {
                    "description": "描述",
                    "type": "string"
                },
                "id": {
                    "description": "角色id",
                    "type": "string"
                },
                "name": {
                    "description": "角色名称",
                    "type": "string"
                }
            }
        },
        "model.User": {
            "type": "object",
            "properties": {
                "alias": {
                    "description": "别名",
                    "type": "string"
                },
                "allow_ip": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "dept_id": {
                    "type": "string"
                },
                "dept_path": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "enabled": {
                    "type": "boolean"
                },
                "expired": {
                    "type": "string"
                },
                "extra": {
                    "description": "描述",
                    "type": "string"
                },
                "id": {
                    "description": "主键",
                    "type": "string"
                },
                "last_active_at": {
                    "type": "string"
                },
                "last_login_error_count": {
                    "type": "integer"
                },
                "last_login_ip": {
                    "type": "string"
                },
                "last_password_time": {
                    "type": "string"
                },
                "modify_time": {
                    "type": "string"
                },
                "name": {
                    "description": "用户名",
                    "type": "string"
                },
                "password": {
                    "type": "string"
                },
                "password_expires_at": {
                    "type": "string"
                },
                "phone": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "model.UserAccess": {
            "type": "object",
            "properties": {
                "create_time": {
                    "type": "string"
                },
                "end_date": {
                    "description": "结束日期",
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "ip": {
                    "description": "ip白名单",
                    "type": "string"
                },
                "limit_flag": {
                    "description": "是否开启时间限制",
                    "type": "boolean"
                },
                "limit_time": {
                    "description": "限制时间",
                    "type": "string"
                },
                "modify_time": {
                    "type": "string"
                },
                "start_date": {
                    "description": "开始日期",
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "model.UserAccessSetArgs": {
            "type": "object",
            "properties": {
                "end_date": {
                    "description": "结束日期 yyyy-MM-dd",
                    "type": "string"
                },
                "ip": {
                    "description": "ip白名单",
                    "type": "string"
                },
                "limit_flag": {
                    "description": "是否开启时间限制",
                    "type": "boolean"
                },
                "limit_time": {
                    "description": "限制时间",
                    "type": "string"
                },
                "start_date": {
                    "description": "开始日期 yyyy-MM-dd",
                    "type": "string"
                }
            }
        },
        "model.UserBindProjectsArgs": {
            "type": "object",
            "properties": {
                "project_ids": {
                    "description": "项目ID集合",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "model.UserBindRolesArgs": {
            "type": "object",
            "properties": {
                "project_id": {
                    "description": "项目ID",
                    "type": "string"
                },
                "role_ids": {
                    "description": "角色id集合",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "model.UserCreateArgs": {
            "type": "object",
            "required": [
                "email",
                "name",
                "password",
                "phone"
            ],
            "properties": {
                "alias": {
                    "description": "用户别名",
                    "type": "string"
                },
                "department_name": {
                    "description": "部门名称",
                    "type": "string"
                },
                "dept_id": {
                    "description": "部门id",
                    "type": "string"
                },
                "email": {
                    "description": "邮箱",
                    "type": "string"
                },
                "extra": {
                    "description": "描述",
                    "type": "string"
                },
                "is_department_manager": {
                    "description": "是否为部门管理员",
                    "type": "boolean"
                },
                "name": {
                    "description": "用户名",
                    "type": "string"
                },
                "password": {
                    "description": "密码",
                    "type": "string"
                },
                "phone": {
                    "description": "电话",
                    "type": "string"
                },
                "publickey": {
                    "description": "公钥",
                    "type": "string"
                },
                "quota_value_map": {
                    "description": "配额配置",
                    "type": "object",
                    "additionalProperties": {
                        "type": "integer"
                    }
                }
            }
        },
        "model.UserResetPasswordArgs": {
            "type": "object",
            "properties": {
                "password": {
                    "description": "密码",
                    "type": "string"
                },
                "publickey": {
                    "description": "密码公钥匙",
                    "type": "string"
                }
            }
        },
        "model.UserTypeReoly": {
            "type": "object",
            "properties": {
                "user_type": {
                    "description": "用户类型标识",
                    "type": "string"
                },
                "user_type_desc": {
                    "description": "用户类型描述",
                    "type": "string"
                }
            }
        },
        "model.UserUnBindRolesArgs": {
            "type": "object",
            "properties": {
                "role_ids": {
                    "description": "角色id集合",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "model.UserUpdateArgs": {
            "type": "object",
            "required": [
                "alias",
                "email",
                "id",
                "phone"
            ],
            "properties": {
                "alias": {
                    "description": "用户别名",
                    "type": "string"
                },
                "email": {
                    "description": "邮箱",
                    "type": "string"
                },
                "extra": {
                    "description": "描述",
                    "type": "string"
                },
                "id": {
                    "description": "用户id",
                    "type": "string"
                },
                "phone": {
                    "description": "电话",
                    "type": "string"
                }
            }
        }
    },
    "securityDefinitions": {
        "Authorization": {
            "description": "Description Authorization",
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        },
        "Current-Project-Id": {
            "description": "Description Current-Project-Id",
            "type": "apiKey",
            "name": "Current-Project-Id",
            "in": "header"
        },
        "Projectid": {
            "description": "Description Projectid",
            "type": "apiKey",
            "name": "Projectid",
            "in": "header"
        }
    }
}`

// SwaggerInfoiam holds exported Swagger Info so clients can modify it
var SwaggerInfoiam = &swag.Spec{
	Version:          "",
	Host:             "",
	BasePath:         "",
	Schemes:          []string{},
	Title:            "iam’s Api文档",
	Description:      "",
	InfoInstanceName: "iam",
	SwaggerTemplate:  docTemplateiam,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfoiam.InstanceName(), SwaggerInfoiam)
}
