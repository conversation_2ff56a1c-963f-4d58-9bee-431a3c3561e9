package service

import (
	"context"
	"errors"
	"fmt"
	"slices"

	"code.mysugoncloud.com/hci/assist/topx"
	"code.mysugoncloud.com/hci/iam/internal/repo"
	"code.mysugoncloud.com/hci/iam/model"
)

// ResourceAuthDept 验证组织资源
func ResourceAuthDept(ctx context.Context, operateUserId, verifyDeptId string) error {
	if operateUserId == "" {
		return errors.New("用户id不能为空")
	}
	if verifyDeptId == "" {
		return errors.New("组织id不能为空")
	}
	currUser := &model.User{Id: operateUserId}
	if err := repo.UserGet(ctx, currUser); err != nil {
		return errors.New("用户不存在")
	}
	// admin/sysadmin/secadmin直接跳过验证
	if currUser.Type == topx.TYPE_ADMIN || currUser.Type == topx.TYPE_SYS_ADMIN || currUser.Type == topx.TYPE_SECURITY {
		return nil
	}

	// 其他类型用户
	dept := &model.Department{Id: verifyDeptId}
	if err := repo.DepartmentGet(ctx, dept); err != nil {
		return errors.New("组织不存在")
	}
	errMsg := fmt.Errorf("您没有操作该组织【%s】的权限", dept.Name)

	if currUser.DeptId == "" {
		return errMsg
	}
	deptIds, _ := repo.DepartmentUnderAllIds(ctx, currUser.DeptId)
	if !slices.Contains(deptIds, verifyDeptId) {
		return errMsg
	}
	return nil
}

// ResourceAuthDept 验证用户资源
func ResourceAuthUser(ctx context.Context, operateUserId, verifyUserId string) error {
	if operateUserId == "" {
		return errors.New("用户id不能为空")
	}
	if verifyUserId == "" {
		return errors.New("验证用户id不能为空")
	}
	currUser := &model.User{Id: operateUserId}
	if err := repo.UserGet(ctx, currUser); err != nil {
		return errors.New("用户不存在")
	}
	// admin/sysadmin/secadmin直接跳过验证
	if currUser.Type == topx.TYPE_ADMIN || currUser.Type == topx.TYPE_SYS_ADMIN || currUser.Type == topx.TYPE_SECURITY {
		return nil
	}

	filter := &model.UserListArgs{
		List:       model.List{PageNum: 1, PageSize: 9999},
		CurrUserId: operateUserId,
	}
	// 当前用户所拥有的用户
	users, _, _ := repo.UserListPageByUserType(ctx, filter)
	userIds := make([]string, 0, len(users)+1)
	userIds = append(userIds, operateUserId)
	for _, user := range users {
		userIds = append(userIds, user.Id)
	}
	if !slices.Contains(userIds, verifyUserId) {
		verifyUser := &model.User{Id: verifyUserId}
		repo.UserGet(ctx, verifyUser)
		return fmt.Errorf("您没有操作该用户【%s】的权限", verifyUser.Name)
	}
	return nil
}
