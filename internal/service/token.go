package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"code.mysugoncloud.com/hci/assist/logs"
	"code.mysugoncloud.com/hci/iam/internal/repo"
	"code.mysugoncloud.com/hci/iam/internal/util"
	"code.mysugoncloud.com/hci/iam/model"
)

func CreateToken(ctx context.Context, loginUser *model.LoginUserArgs) (string, error) {
	if loginUser.Username == "" {
		return "", errors.New("用户名不能为空")
	}
	// 验证码验证
	if loginUser.Code != "" {
		code := strings.ToLower(loginUser.Code)
		if _, err := util.CacheGet(ctx, code); err != nil {
			return "", errors.New("验证码错误，请重新输入")
		}
	}

	lockUser := UNAME_ERROR_LOCK + loginUser.Username
	value, _ := util.CacheGet(ctx, lockUser)
	if value != "" {
		// 用户已锁定
		lockTime := getPasswordErrorLockTime(ctx)
		return "", fmt.Errorf("用户名/密码错误次数达到上限，锁定%d分钟", lockTime)
	}
	user := &model.User{Name: loginUser.Username}
	err := repo.UserGet(ctx, user)
	if err != nil {
		return createTokenFailHandler(ctx, loginUser.Username)
	}
	err = checkUser(ctx, user, loginUser)
	if err != nil {
		return "", err
	}

	// 验证用户名/密码
	var password string
	if loginUser.OriginLogin {
		password = loginUser.Password
	} else {
		password, err = util.DecryptByPublicKey(ctx, loginUser.PublicKey, loginUser.Password)
		if err != nil {
			return "", err
		}
	}
	// 校验密码
	if isMatch := util.PasswordVerify(password, user.Password); !isMatch {
		return createTokenFailHandler(ctx, loginUser.Username)
	}
	// 获取token
	token, err := util.GenToken(user)
	if err != nil {
		return createTokenFailHandler(ctx, loginUser.Username)
	}
	// 获取token缓存时间
	tokenExpired := getTokenExpiredTime(ctx)
	// 设置用户是否只能在一个地方登录
	setOnePlaceLogin(ctx, loginUser.Username, tokenExpired)

	lastLoginErr, _ := util.CacheGet(ctx, UNAME_LAST_ERROR_COUNT+loginUser.Username)
	// 设置缓存
	util.CacheAdd(ctx, loginUser.Username+":"+token, lastLoginErr, time.Duration(tokenExpired)*time.Second)
	// 更新用户信息
	user.LastActiveAt = time.Now()

	if loginUser.IpAddr != "" {
		user.LastLoginIp = loginUser.IpAddr
	}
	err = repo.UserEdit(ctx, user, "last_active_at")
	if err != nil {
		logs.Ctx(ctx).Error(err)
	}
	// 成功获取token，删除错误缓存
	createTokenSucccess(ctx, loginUser.Username)
	return token, nil
}

func ValidateToken(ctx context.Context, token string) (*model.User, error) {
	if token == "" {
		return nil, errors.New("token不能为空")
	}
	claims, err := util.ParseToken(token)
	if err != nil {
		return nil, err
	}
	// 判断token是否失效
	if util.CacheHasKey(ctx, claims.UserName+":"+token) {
		// 延长token时效
		util.CacheResetExpiredTime(ctx, claims.UserName+":"+token, time.Duration(getTokenExpiredTime(ctx))*time.Second)
	} else {
		return nil, errors.New("token已失效")
	}

	user := &model.User{Id: claims.UserID, Name: claims.UserName, Type: claims.UserType, Alias: claims.Alias, DeptId: claims.DeptId}
	return user, err
}

func checkUser(ctx context.Context, user *model.User, loginUser *model.LoginUserArgs) error {
	if !user.Enabled {
		return errors.New("用户未激活或已冻结，请联系管理员激活")
	}

	if !user.Expired.IsZero() && user.Expired.Before(time.Now()) {
		return errors.New("用户已过期")
	}
	// 账号冻结
	noLoginDays := getUserLongNologin(ctx)
	if noLoginDays != 0 && user.Name != "inner" {
		// 判断
		if !user.LastActiveAt.IsZero() && util.CalculateTotalDay(user.LastActiveAt) >= noLoginDays {
			user.Enabled = false
			repo.UserEdit(ctx, user, "enabled")
			return errors.New("用户长时间未登录，账号被冻结，请联系管理员。")
		}
	}
	// 检测用户访问限制
	if err := checkUserAccess(ctx, user, loginUser); err != nil {
		return err
	}
	return nil
}

// checkUserAccess 检测用户访问限制
func checkUserAccess(ctx context.Context, user *model.User, loginUser *model.LoginUserArgs) error {
	// 检测ip
	ipAddr := loginUser.IpAddr
	if ipAddr != "" && user.AllowIp != "" && ipAddr != user.AllowIp {
		return errors.New("当前IP不允许登录")
	}
	// 获取时间访问限制
	userAccess := &model.UserAccess{UserId: user.Id}
	if err := repo.UserAccessGet(ctx, userAccess); err != nil {
		// 如果userAccess为空，则证明未设置登录时间限制，直接返回
		return nil
	}
	// 验证是否在允许登录时间范围内
	nowTime := time.Now()
	if (!userAccess.StartDate.IsZero() && nowTime.Before(userAccess.StartDate)) || (!userAccess.EndDate.IsZero() && nowTime.After(userAccess.EndDate)) {
		return errors.New("当前用户不在允许登录时间范围")
	}
	// 不进行具体时间点限制
	if !userAccess.LimitFlag {
		return nil
	}
	// 所有时间点均无法登录
	if userAccess.LimitTime == "" {
		return errors.New("当前用户不在允许登录时间范围")
	}
	var limitTimeMap map[string]string
	if err := json.Unmarshal([]byte(userAccess.LimitTime), &limitTimeMap); err != nil {
		return errors.New("登录时间点范围解析失败")
	}
	// 获取今天是星期几
	week := util.GetWeekByDate(nowTime)
	if value, ok := limitTimeMap[week]; ok {
		// 今天没有允许登录的时间点
		if value == "" {
			return errors.New("当前用户不在允许登录时间范围")
		}
		hour := util.GetTimeHourStr(nowTime)
		parts := strings.Split(value, ",")
		var flag bool
		for _, part := range parts {
			if part == hour {
				flag = true
				break
			}
		}
		if !flag {
			return errors.New("当前用户不在允许登录时间范围")
		}
	} else {
		return errors.New("当前用户不在允许登录时间范围")
	}

	return nil
}

func createTokenFailHandler(ctx context.Context, username string) (string, error) {
	// 获取最大错误次数配置
	passwordErrorTimes := getPasswordErrorCount(ctx)
	// 锁定时间
	lockTime := getPasswordErrorLockTime(ctx)
	// 锁定前缀
	lockUser := UNAME_ERROR_LOCK + username
	// 用户上次错误次数key,锁定后不会清零
	lastLoginError := UNAME_LAST_ERROR_COUNT + username
	// 用户已错误次数key,锁定后会清零
	username = UNAME_ERROR_TIMES + username

	// 用户错误次数
	userErrorCountMsg := 1
	// 多少分钟内连续输密码错误次数达到设定值，触发锁定
	errorTimeInterval := getErrorTimeInterval(ctx)

	// 设置上次错误次数，缓存一个月
	if util.CacheHasKey(ctx, lastLoginError) {
		lastLoginErrorCount, err := util.CacheGet(ctx, lastLoginError)
		if err != nil {
			lastLoginErrorCount = "1"
		}
		tempCount, _ := strconv.Atoi(lastLoginErrorCount)
		util.CacheEditValueResetExpired(ctx, lastLoginError, strconv.Itoa(tempCount+1), time.Duration(30*24)*time.Hour)
	} else {
		util.CacheAdd(ctx, lastLoginError, "1", time.Duration(30*24)*time.Hour)
	}
	if passwordErrorTimes == 1 {
		// 锁定用户
		util.CacheAdd(ctx, lockUser, "true", time.Duration(lockTime)*time.Minute)
		return "", errors.New("用户名/密码错误次数达上限，锁定" + strconv.Itoa(lockTime) + "分钟")
	}

	userErrorTimes, _ := util.CacheGet(ctx, username)
	if userErrorTimes != "" {
		userErrorCount, _ := strconv.Atoi(userErrorTimes)
		if userErrorCount+1 >= passwordErrorTimes {
			// 锁定用户
			util.CacheAdd(ctx, lockUser, "true", time.Duration(lockTime)*time.Minute)
			util.CacheDrop(ctx, username)
			return "", errors.New("用户名/密码错误次数达上限，锁定" + strconv.Itoa(lockTime) + "分钟")
		}
		// 更新错误次数
		userErrorCountMsg = userErrorCount + 1
		userErrorTimes = strconv.Itoa(userErrorCountMsg)
		util.CacheEditValue(ctx, username, userErrorTimes)

	} else {
		// 第一次错误
		util.CacheAdd(ctx, username, strconv.Itoa(1), time.Duration(errorTimeInterval)*time.Minute)
	}
	return "", fmt.Errorf("%d分钟内,用户名/密码连续错误%d次将被锁定%d分钟,已错误%d次", errorTimeInterval, passwordErrorTimes, lockTime, userErrorCountMsg)
}

func createTokenSucccess(ctx context.Context, username string) {
	repo.CacheDrop(ctx, &model.IamCache{Key: UNAME_ERROR_LOCK + username})
	repo.CacheDrop(ctx, &model.IamCache{Key: UNAME_ERROR_TIMES + username})
	util.CacheDrop(ctx, UNAME_LAST_ERROR_COUNT+username)
}

func getPasswordErrorCount(ctx context.Context) int {
	settings := repo.GlobalsettingGetByType(ctx, CLOBAL_SETTING_PASSWORD, CLOBAL_SETTING_PASSWORD_ERROR_TIMES)
	if settings.PolicyDocument == "" {
		return 10086
	}
	result, _ := strconv.Atoi(settings.PolicyDocument)
	return result
}

func getPasswordErrorLockTime(ctx context.Context) int {
	settings := repo.GlobalsettingGetByType(ctx, CLOBAL_SETTING_PASSWORD, CLOBAL_SETTING_PASSWORD_LOCK_MINUTE)
	if settings.PolicyDocument == "" {
		return 1
	}
	result, _ := strconv.Atoi(settings.PolicyDocument)
	return result
}

func getErrorTimeInterval(ctx context.Context) int {
	settings := repo.GlobalsettingGetByType(ctx, CLOBAL_SETTING_PASSWORD, CLOBAL_SETTING_PASSWORD_ERROR_TIME_INTERVAL)
	if settings.PolicyDocument == "" {
		return 1
	}
	result, _ := strconv.Atoi(settings.PolicyDocument)
	return result
}

func getTokenExpiredTime(ctx context.Context) int64 {
	var result int64
	// 默认1800秒
	result = 1800
	settings := repo.GlobalsettingGetByType(ctx, TOKEN, TOKEN_EXPIRED_KEY)
	if settings.PolicyDocument == "" {
		return 1
	}
	result, _ = strconv.ParseInt(settings.PolicyDocument, 10, 64)
	return result
}

func getUserLongNologin(ctx context.Context) int {
	settings := repo.GlobalsettingGetByType(ctx, POLICY_TYPE, "long_time_no_login")
	if settings.PolicyDocument == "" {
		return 0
	}
	result, _ := strconv.Atoi(settings.PolicyDocument)
	return result
}

func getOnePlaceLogin(ctx context.Context) bool {
	settings := repo.GlobalsettingGetByType(ctx, POLICY_TYPE, ONE_PLACE_LOGIN)
	if settings.PolicyDocument == "" {
		return false
	}
	result, err := strconv.ParseBool(settings.PolicyDocument)
	if err != nil {
		return false
	}
	return result

}

// setOnePlaceLogin 设置用户在第一个地方登录
func setOnePlaceLogin(ctx context.Context, username string, tokenExpired int64) {
	if getOnePlaceLogin(ctx) {
		otherLoginUsers, _ := repo.CacheKeyPrefixMatch(ctx, &username)
		for _, mod := range otherLoginUsers {
			expireKey := strings.Replace(mod.Key, username+":", EXPIRE_TOKEN, 1)
			util.CacheAdd(ctx, expireKey, mod.Value, time.Duration(tokenExpired)*time.Second)
		}
	}
}

func CheckOnePlaceLogin(ctx context.Context, token string) bool {
	if getOnePlaceLogin(ctx) {
		return util.CacheHasKey(ctx, EXPIRE_TOKEN+token)
	}
	return false
}
