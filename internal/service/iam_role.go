package service

import (
	"context"
	"errors"
	"time"

	"code.mysugoncloud.com/hci/assist/logs"
	"code.mysugoncloud.com/hci/assist/rndm"
	"code.mysugoncloud.com/hci/assist/topx"
	"code.mysugoncloud.com/hci/iam/internal/repo"
	"code.mysugoncloud.com/hci/iam/model"
)

// RoleListByUserId 根据用户id查询用户角色列表
func RoleListByUserId(ctx context.Context, userId string, projectId string) ([]model.IamRole, error) {
	filter := &model.UserRoleListArgs{
		List:      model.List{PageNum: 1, PageSize: 999},
		UserId:    userId,
		ProjectId: projectId,
	}
	roles, _, err := RolePageByUserId(ctx, filter)
	if err != nil {
		return nil, err
	}
	return roles, nil
}

// RolePageByUserId 用户所拥有角色列表
func RolePageByUserId(ctx context.Context, filter *model.UserRoleListArgs) ([]model.IamRole, int, error) {
	if filter.UserId == "" {
		return nil, 0, errors.New("用户id不能为空")
	}
	user := &model.User{Id: filter.UserId}
	if err := repo.UserGet(ctx, user); err != nil {
		return nil, 0, errors.New("用户不存在")
	}
	userType := user.Type
	// 内置管理员类型（audit/security）
	if userType == topx.TYPE_AUDIT || userType == topx.TYPE_SECURITY || userType == topx.TYPE_SYS_ADMIN || topx.TYPE_ADMIN == userType {
		return repo.IamRoleListByType(ctx, filter, userType)
	}
	// 根账号/部门管理员
	if topx.TYPE_DEPT_MASTER == userType || topx.TYPE_MASTER == userType {
		department := &model.Department{Id: user.DeptId}
		err := repo.DepartmentGet(ctx, department)
		if err != nil {
			return nil, 0, errors.New("用户所在部门不存在，用户ID:" + filter.UserId)
		}
		// 查询所有部门id
		deptIds, err := repo.DepartmentUnderAllIds(ctx, user.DeptId)
		if err != nil {
			return nil, 0, err
		}
		return repo.IamRoleListByMaster(ctx, filter, userType, deptIds)
	}

	if topx.TYPE_NORMAL == userType || userType == "" {
		return repo.IamRoleByUserIdPage(ctx, filter)
	}
	return nil, 0, nil
}

// RoleListByDept 根据组织查询角色
func RoleListByDept(ctx context.Context, filter *model.RoleListByDeptArgs) ([]model.IamRole, int, error) {
	if filter.DeptId == "" {
		return nil, 0, errors.New("组织id不能为空")
	}
	return repo.IamRoleListByDept(ctx, filter)
}

// RoleCreate 角色创建
func RoleCreate(ctx context.Context, body *model.RoleCreateArgs) error {
	if body.DeptId == "" {
		return errors.New("组织id不能为空")
	}
	if body.Name == "" {
		return errors.New("角色名称不能为空")
	}
	if !repo.DepartmentExist(ctx, &model.Department{Id: body.DeptId}) {
		return errors.New("组织不存在")
	}
	role := &model.IamRole{Name: body.Name, DeptId: body.DeptId}
	if repo.IamRoleExist(ctx, role) {
		return errors.New("角色名已存在:" + body.Name)
	}
	role.Id = rndm.GUID()
	role.Description = body.Description
	role.CreateTime = time.Now()
	role.ModifyTime = time.Now()
	return repo.IamRoleAdd(ctx, role)
}

// RoleUpdate 角色更新
func RoleUpdate(ctx context.Context, body *model.RoleUpdateArgs) error {
	if body.Id == "" {
		return errors.New("角色id不能为空")
	}
	if body.DeptId == "" {
		return errors.New("角色部门id不能为空")
	}
	if body.Name == "" {
		return errors.New("角色名称不能为空")
	}
	role := &model.IamRole{Id: body.Id}
	if err := repo.IamRoleGet(ctx, role); err != nil {
		return errors.New("角色不存在: " + body.Id)
	}
	if role.DeptId == "" {
		return errors.New("内置角色不能被修改")
	}
	if repo.IamRoleExistNotId(ctx, body.Id, &model.IamRole{Name: body.Name, DeptId: body.DeptId}) {
		return errors.New("角色名已存在:" + body.Name)
	}
	role.Name = body.Name
	role.Description = body.Description
	role.ModifyTime = time.Now()
	return repo.IamRoleEdit(ctx, role, "name", "description", "modify_time")
}

// RoleDelete 角色删除
func RoleDelete(ctx context.Context, roleId string) error {
	if roleId == "" {
		return errors.New("角色id不能为空")
	}
	role := &model.IamRole{Id: roleId}
	if err := repo.IamRoleGet(ctx, role); err != nil {
		return errors.New("角色不存在")
	}
	if role.DeptId == "" {
		return errors.New("内置角色不能被删除")
	}
	repo.UserRoleMappingDrop(ctx, &model.UserRoleMapping{RoleId: roleId})
	repo.IamRoleDrop(ctx, role)
	return nil
}

// RoleDetail 角色详情
func RoleDetail(ctx context.Context, roleId string) (*model.IamRole, error) {
	if roleId == "" {
		return nil, errors.New("角色id不能为空")
	}
	role := &model.IamRole{Id: roleId}
	if err := repo.IamRoleGet(ctx, role); err != nil {
		return nil, errors.New("角色不存在")
	}
	tree, err := DepartmentMap(ctx)
	if err != nil {
		return nil, err
	}
	if role.DeptId != "" {
		role.DeptName = tree[role.DeptId].Name
		role.DeptPath, _ = DepartmentPath(ctx, role.DeptId, tree)
	}
	return role, nil
}

// RolePageAll 查询所有角色
func RolePageAll(ctx context.Context, filter *model.RolePageAllArgs) ([]model.IamRole, int, error) {
	if filter.CurrUserId == "" {
		return nil, 0, errors.New("用户id不能为空")
	}
	user := &model.User{Id: filter.CurrUserId}
	if err := repo.UserGet(ctx, user); err != nil {
		return nil, 0, errors.New("用户不存在")
	}
	roles := make([]model.IamRole, 0)
	total := 0
	var err error
	// 根账号或是部门管理员
	if topx.TYPE_MASTER == user.Type || topx.TYPE_DEPT_MASTER == user.Type {
		// 查询条件有组织
		if filter.DeptId != "" {
			filter.DataBaseDeptIds = append(filter.DataBaseDeptIds, filter.DeptId)
		} else {
			deptIds, err := repo.DepartmentUnderAllIds(ctx, user.DeptId)
			if err != nil {
				return nil, 0, errors.New("查询所有部门id失败")
			}
			filter.DataBaseDeptIds = append(filter.DataBaseDeptIds, deptIds...)
		}
		if roles, total, err = repo.IamRoleAllPage(ctx, filter); err != nil {
			logs.Ctx(ctx).Err(err).Any("filter", filter).Error("IamRoleAllPage error")
			return nil, 0, errors.New("查询用户角色失败")
		}
	} else if topx.TYPE_SECURITY == user.Type || topx.TYPE_SYS_ADMIN == user.Type || topx.TYPE_ADMIN == user.Type {
		// 安全管理员/系统管理员
		filter.RoleTypeIsNull = true
		if filter.DeptId != "" {
			filter.DataBaseDeptIds = append(filter.DataBaseDeptIds, filter.DeptId)
		}
		if roles, total, err = repo.IamRoleAllPage(ctx, filter); err != nil {
			logs.Ctx(ctx).Err(err).Any("filter", filter).Error("IamRoleAllPage error")
			return nil, 0, errors.New("查询用户角色失败")
		}
	}
	// 数据转换
	if len(roles) > 0 {
		tree, err := DepartmentMap(ctx)
		if err != nil {
			return nil, 0, err
		}
		for i := range roles {
			roles[i].DeptPath, _ = DepartmentPath(ctx, roles[i].DeptId, tree)
			if dept, ok := tree[roles[i].DeptId]; ok {
				roles[i].DeptName = dept.Name
			}
		}
	}

	return roles, total, nil
}

// RoleInnerList 内置角色列表
func RoleInnerList(ctx context.Context, filter *model.RolesInnerArgs) ([]model.IamRole, int, error) {
	if filter.CurrUserId == "" {
		return nil, 0, errors.New("用户id不能为空")
	}
	user := &model.User{Id: filter.CurrUserId}
	if err := repo.UserGet(ctx, user); err != nil {
		return nil, 0, errors.New("用户不存在")
	}
	if user.Type != topx.TYPE_SECURITY && user.Type != topx.TYPE_ADMIN {
		return nil, 0, errors.New("该用户不能查询内置用户")
	}
	roles, total, err := repo.IamRoleInnerPage(ctx, filter)
	if err != nil {
		logs.Ctx(ctx).Err(err).Any("filter", filter).Error("IamRoleAllPage error")
		return nil, 0, errors.New("查询内置角色失败")
	}
	return roles, total, nil
}
