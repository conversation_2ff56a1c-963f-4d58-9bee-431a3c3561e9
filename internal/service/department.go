package service

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"code.mysugoncloud.com/hci/assist/config/distlock"
	"code.mysugoncloud.com/hci/assist/logs"
	"code.mysugoncloud.com/hci/assist/rndm"
	"code.mysugoncloud.com/hci/assist/topx"
	"code.mysugoncloud.com/hci/iam/internal/conf"
	"code.mysugoncloud.com/hci/iam/internal/repo"
	"code.mysugoncloud.com/hci/iam/internal/util"
	"code.mysugoncloud.com/hci/iam/model"
)

// DepartmentDetailById 部门详情
func DepartmentDetailById(ctx context.Context, id string) (*model.Department, error) {
	department := &model.Department{Id: id}
	err := repo.DepartmentGet(ctx, department)
	if err != nil {
		return nil, err
	}
	return department, nil
}

// DepartmentCreate 创建部门
func DepartmentCreate(ctx context.Context, department *model.Department) (*model.Department, error) {
	if department.Name == "" {
		return nil, errors.New("部门名称不能为空")
	}
	parentDepartment, err := getParentDepartment(ctx, department)
	if err != nil {
		return nil, err
	}
	department.Id = rndm.GUID()
	department.Level = parentDepartment.Level + 1
	err = repo.DepartmentAdd(ctx, department)
	if err != nil {
		return nil, err
	}
	return department, nil
}

// DepartmentUpdate 修改部门
func DepartmentUpdate(ctx context.Context, department *model.Department) (*model.Department, error) {
	if department.Name == "" {
		return nil, errors.New("部门名称不能为空")
	}
	originDept := &model.Department{Id: department.Id}
	err := repo.DepartmentGet(ctx, originDept)
	if err != nil {
		return nil, errors.New("修改失败，该id对应的组织/部门不存在")
	}
	// 重名校验
	if MASTER_DEPARTMENT_LEVEL == originDept.Level {
		// 顶级部门只需要验证顶级部门间的部门名称
		if repo.DepartmentExistNotId(ctx, department.Id, &model.Department{Level: MASTER_DEPARTMENT_LEVEL, Name: department.Name}) {
			return nil, errors.New("组织名称已存在")
		}
	} else {
		// 子级部门只需要验证同一顶级部门下的部门名称
		topDeptId, err := DepartmentTopId(ctx, originDept.Id)
		if err != nil {
			return nil, err
		}
		deptAllIds, err := repo.DepartmentUnderAllIds(ctx, topDeptId)
		if err != nil {
			return nil, errors.New("查询所有组织失败")
		}
		deptAllIds = util.RemoveElementInPlaceOptimized(deptAllIds, originDept.Id)
		if repo.DepartmentExistByDeptIds(ctx, deptAllIds, department.Name) {
			return nil, errors.New("组织名称已存在")
		}
	}
	repo.DepartmentEdit(ctx, department, "name", "description")
	return department, nil
}

// DepartmentDelete 删除部门
func DepartmentDelete(ctx context.Context, id string) error {
	if id == "" {
		return errors.New("部门id不能为空")
	}
	if !repo.DepartmentExist(ctx, &model.Department{Id: id}) {
		return errors.New("删除失败，该部门对应的部门不存在")
	}
	// 校验是否有子部门存在
	if repo.DepartmentExist(ctx, &model.Department{ParentId: id}) {
		return errors.New("删除失败，该部门对应有子部门存在")
	}
	// 校验是否有人员存在
	if repo.UserExist(ctx, &model.User{DeptId: id}) {
		return errors.New("删除失败，该部门对应有用户存在")
	}
	// 校验是否有项目存在
	if repo.ProjectExist(ctx, &model.Project{DeptId: id, Enabled: true}) {
		return errors.New("删除失败，该部门对应有项目存在")
	}
	// 校验是否有角色存在
	if repo.IamRoleExist(ctx, &model.IamRole{DeptId: id}) {
		return errors.New("删除失败，该部门对应有角色存在")
	}
	err := repo.DepartmentDrop(ctx, &model.Department{Id: id})
	if err != nil {
		logs.Ctx(ctx).Err(err).Str("id", id).Error("delete department error")
		return errors.New("删除部门失败")
	}
	return nil
}

func getParentDepartment(ctx context.Context, department *model.Department) (*model.Department, error) {
	parentDepartment := &model.Department{Id: department.ParentId}
	err := repo.DepartmentGet(ctx, parentDepartment)
	if err != nil {
		return nil, errors.New("父级部门不存在")
	}
	maxLevel := getMaxDeptLevel(ctx)
	if parentDepartment.Level+1 > maxLevel {
		return nil, fmt.Errorf("组织部门的层级最多为%d级", maxLevel)
	}
	// 判断名称
	// 子级部门只需要验证同一顶级部门下的部门名称
	topDeptId, err := DepartmentTopId(ctx, parentDepartment.Id)
	if err != nil {
		return nil, err
	}
	deptAllIds, err := repo.DepartmentUnderAllIds(ctx, topDeptId)
	if err != nil {
		return nil, errors.New("查询所有组织失败")
	}
	if repo.DepartmentExistByDeptIds(ctx, deptAllIds, department.Name) {
		return nil, fmt.Errorf("%s组织部门已经存在", department.Name)
	}
	return parentDepartment, nil
}

func getMaxDeptLevel(ctx context.Context) int {
	iamConfig, _ := conf.GetIamCfg(ctx)
	maxLevel := 5
	if iamConfig.DepartmentMaxLevel != 0 {
		maxLevel = iamConfig.DepartmentMaxLevel
	}
	return maxLevel
}

// BuildDepartmentTree 构建部门树
func BuildDepartmentTree(ctx context.Context) ([]*model.Department, error) {
	mods, err := repo.DepartmentAll(ctx)
	if err != nil {
		return nil, err
	}
	tree := make(map[string]*model.Department)
	for _, dept := range mods {
		tree[dept.Id] = dept
	}
	// 返回顶级目录
	var result []*model.Department
	// 构建树结构
	for _, dep := range mods {
		if parent, ok := tree[dep.ParentId]; ok {
			parent.Children = append(parent.Children, tree[dep.Id])
		}
		if dep.Level == 0 {
			result = append(result, dep)
		}
	}
	return result, nil
}

// DepartmentPath 获取部门path
func DepartmentPath(ctx context.Context, deptId string, tree map[string]*model.Department) (string, error) {
	if deptId == "" {
		return "", nil
	}

	// 构建dept path
	dept := tree[deptId]
	if dept == nil {
		return "", nil
	}
	deptPath := dept.Name
	for {
		if dept.ParentId == "" {
			break
		}
		dept = tree[dept.ParentId]
		deptPath = dept.Name + "/" + deptPath
	}
	return deptPath, nil

}

// DepartmentMap 查询部门map(id, department)
func DepartmentMap(ctx context.Context) (map[string]*model.Department, error) {
	depts, err := repo.DepartmentAll(ctx)
	if err != nil {
		return nil, errors.New("查询所有部门失败")
	}
	tree := make(map[string]*model.Department)
	for _, dept := range depts {
		tree[dept.Id] = dept
	}
	return tree, nil
}

// DepartmentTop 获取顶级部门
func DepartmentTop(ctx context.Context, deptId string, tree map[string]*model.Department) *model.Department {
	if deptId == "" {
		return nil
	}
	dept := tree[deptId]
	if dept == nil {
		return nil
	}
	for {
		if dept.ParentId == "" || dept.Level == 0 {
			break
		}
		dept = tree[dept.ParentId]
	}
	return dept
}

// DepartmentTop 获取顶级部门id
func DepartmentTopId(ctx context.Context, deptId string) (string, error) {
	tree, _ := DepartmentMap(ctx)
	dept := DepartmentTop(ctx, deptId, tree)
	if dept == nil {
		return "", errors.New("未找到一级组织")
	}
	return dept.Id, nil
}

// DepartmentTops 获取用户一级组织列表
func DepartmentTops(ctx context.Context, userId string) ([]*model.Department, error) {
	if userId == "" {
		return nil, errors.New("用户id不能为空")
	}
	user := &model.User{Id: userId}
	if err := repo.UserGet(ctx, user); err != nil {
		return nil, errors.New("用户不存在")
	}
	depts := make([]*model.Department, 0)
	// 审计管理员
	if user.Type == topx.TYPE_AUDIT {
		return depts, nil
	}
	// 普通用户,用户/部门管理员，查询自己的一级部门
	if user.Type == "" || user.Type == topx.TYPE_NORMAL || user.Type == topx.TYPE_MASTER || user.Type == topx.TYPE_DEPT_MASTER {
		tree, _ := DepartmentMap(ctx)
		dept := DepartmentTop(ctx, user.DeptId, tree)
		if dept != nil {
			depts = append(depts, dept)
		}
		return depts, nil
	} else {
		if allDeptTree, err := BuildDepartmentTree(ctx); err == nil {
			depts = append(depts, allDeptTree...)
		}
		return depts, nil
	}
}

// DepartmentTopIds 获取用户一级组织id
func DepartmentTopIds(ctx context.Context, userId string) []string {
	deptTopIds := make([]string, 0)
	depts, err := DepartmentTops(ctx, userId)
	if err != nil {
		return deptTopIds
	}
	for _, dept := range depts {
		deptTopIds = append(deptTopIds, dept.Id)
	}
	return deptTopIds
}

// DepartmentQuotaUpdate 修改部门配额
func DepartmentQuotaUpdate(ctx context.Context, body *model.DepartmentQuotaUpdateArgs) error {
	// 验证
	if err := quotaUpdateValidate(ctx, body); err != nil {
		return err
	}
	// 操作一级组织配额需要加锁
	mu := distlock.NewMutex(ctx, "quota-dept-key:"+body.DeptId, time.Second*30)
	// 加锁一分钟
	if err := mu.Lock(); err != nil {
		logs.Ctx(ctx).Any("deptId", body.DeptId).Err(err).Error("dept quota lock error")
		return errors.New("其他人正在操作该组织配额，请稍后")
	}
	defer mu.Unlock()
	// 获取一级组织的配额
	qdvs, _ := repo.QuotaDeparmentValue(ctx, []string{body.DeptId}, body.RegionId)
	qdvMap := make(map[string]*model.QuotaDepartmentValue, 0)
	for _, qdv := range qdvs {
		qdvMap[qdv.MetricName] = qdv
	}
	for k, v := range body.QuotaValueMap {
		if qdv, ok := qdvMap[k]; ok {
			if v != -1 && v < qdv.UsedValue {
				qm := &model.QuotaMetric{Name: k}
				if err := repo.QuotaMetricGet(ctx, qm); err != nil {
					return fmt.Errorf("%s指标不存在", qm.TotalName)
				}
				return fmt.Errorf("%s小于组织使用量，请重新设置", qm.TotalName)
			}
			qdv.TotalValue = v
		} else {
			qdvs = append(qdvs, &model.QuotaDepartmentValue{MetricName: k, DepartmentId: body.DeptId, TotalValue: v, UsedValue: 0, RegionId: body.RegionId})
		}
	}
	// 修改数据
	if err := repo.DepartmentQuotaUpdate(ctx, qdvs); err != nil {
		logs.Ctx(ctx).Err(err).Any("qdvs", qdvs).Error("repo DepartmentQuotaUpdate error")
		return errors.New("修改组织配额失败")
	}
	return nil
}

func quotaUpdateValidate(ctx context.Context, body *model.DepartmentQuotaUpdateArgs) error {
	if body.DeptId == "" {
		return errors.New("组织id不能为空")
	}
	if body.RegionId == "" {
		body.RegionId = "RegionOne"
	}
	if len(body.QuotaValueMap) == 0 {
		return nil
	}
	dept := &model.Department{Id: body.DeptId}
	if err := repo.DepartmentGet(ctx, dept); err != nil {
		return errors.New("项目不存在")
	}
	if dept.Level != 0 {
		return errors.New("只有一级组织才有配额，其他组织没有配额，不能进行配置")
	}
	// 验证组织下项目配额，是否有无限制，而组织却要修改成有限制的情况
	deptIds, err := repo.DepartmentUnderAllIds(ctx, body.DeptId)
	if err != nil {
		return errors.New("查询组织下所有组织Id失败")
	}
	projects, _ := repo.ProjectListByDeptIds(ctx, deptIds)
	if len(projects) == 0 {
		return nil
	}
	projectIds := make([]string, 0, len(projects))
	for _, project := range projects {
		projectIds = append(projectIds, project.Id)
	}
	qpvs, _ := repo.QuotaProjectValue(ctx, projectIds, body.RegionId)
	if len(qpvs) == 0 {
		return nil
	}
	qpvMap := make(map[string][]string)
	for _, qpv := range qpvs {
		if qpv.TotalValue == -1 {
			qpvMap[qpv.MetricName] = append(qpvMap[qpv.MetricName], qpv.ProjectId)
		}
	}
	errProjectIds := make([]string, 0)
	for k, v := range body.QuotaValueMap {
		if qpvTemps, ok := qpvMap[k]; ok && v != -1 {
			errProjectIds = append(errProjectIds, qpvTemps...)
		}
	}
	if len(errProjectIds) != 0 {
		errProjects, _ := repo.ProjectBatchGet(ctx, errProjectIds)
		if len(errProjects) == 0 {
			return nil
		}
		names := make([]string, 0, len(errProjects))
		for _, p := range errProjects {
			names = append(names, p.Alias)
		}
		return fmt.Errorf("组织下存在项目[%s]配额为无限制，不能将组织配额改为有限制", strings.Join(names, ","))
	}
	return nil
}

func DeptsByDeptId(depts []*model.Department, deptId string) *model.Department {
	for _, dept := range depts {
		if dept.Id == deptId {
			return dept
		}
		if dept.Children != nil {
			return DeptsByDeptId(dept.Children, deptId)
		}
	}
	return nil
}

func DepartmentProjects(ctx context.Context, filter *model.DeparmentProjectsArgs) ([]model.Project, int, error) {
	if filter.DeptId == "" {
		return nil, 0, errors.New("组织id不能为空")
	}
	projects, total, err := repo.DepartmentProjects(ctx, filter)
	if err != nil {
		logs.Ctx(ctx).Any("fitler", filter).Err(err).Error("user projects error")
		return nil, 0, errors.New("查询用户项目失败")
	}
	ProjectTrans(ctx, projects)
	return projects, total, nil
}
