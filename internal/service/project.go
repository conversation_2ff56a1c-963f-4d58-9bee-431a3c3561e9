package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"code.mysugoncloud.com/hci/assist/config/distlock"
	"code.mysugoncloud.com/hci/assist/logs"
	"code.mysugoncloud.com/hci/assist/rndm"
	"code.mysugoncloud.com/hci/assist/topx"
	"code.mysugoncloud.com/hci/iam/internal/repo"
	"code.mysugoncloud.com/hci/iam/internal/util"
	"code.mysugoncloud.com/hci/iam/model"
)

// ProjectIdsByUserId 根据userId查询所属的项目id集合
func ProjectIdsByUserId(ctx context.Context, userId string) ([]string, error) {
	if userId == "" {
		return nil, errors.New("用户id不能为空")
	}
	user := &model.User{Id: userId}
	err := repo.UserGet(ctx, user)
	if err != nil {
		return nil, errors.New("该用户不存在或已被删除")
	}

	filter := &model.ProjectPageAllArgs{
		List:       model.List{PageNum: 1, PageSize: 1000},
		CurrUserId: userId,
	}
	projects, total, err := ProjectPageAll(ctx, filter)
	if err != nil {
		return nil, errors.New("查询项目失败")
	}
	projectIds := make([]string, total)
	for _, project := range projects {
		projectIds = append(projectIds, project.Id)
	}
	return projectIds, nil
}

// ProjectDetail 项目详情
func ProjectDetail(ctx context.Context, projectId string) (*model.Project, error) {
	if projectId == "" {
		return nil, errors.New("项目id不能为空")
	}
	project := &model.Project{Id: projectId}
	if err := repo.ProjectGet(ctx, project); err != nil {
		return nil, nil
	}
	project.Name = project.Alias
	tree, err := DepartmentMap(ctx)
	if err != nil {
		return nil, err
	}
	project.DeptPath, _ = DepartmentPath(ctx, project.DeptId, tree)
	return project, nil
}

// ProjectCreate 创建项目
func ProjectCreate(ctx context.Context, args *model.ProjectCreateOrUpdateArgs) error {
	if args.DeptId == "" {
		return errors.New("部门不能为空")
	}
	if args.Name == "" {
		return errors.New("名称不能为空")
	}
	if args.QuotaValueMap == nil || len(args.QuotaValueMap) == 0 {
		return errors.New("配额不能为空")
	}
	if err := isNameDuplication(ctx, args.DeptId, args.Name, ""); err != nil {
		return err
	}

	dept := &model.Department{Id: args.DeptId}
	if err := repo.DepartmentGet(ctx, dept); err != nil {
		return errors.New("操作失败，部门不存在")
	}

	project := &model.Project{
		Id:          rndm.GUID(),
		Name:        args.Name + "_" + rndm.SUID(),
		Enabled:     true,
		Alias:       args.Name,
		Description: args.Description,
		DeptId:      args.DeptId,
		CreateTime:  time.Now(),
		ModifyTime:  time.Now(),
	}
	// 判断配额
	topDeptId, err := DepartmentTopId(ctx, args.DeptId)
	if err != nil {
		return err
	}
	mu := distlock.NewMutex(ctx, "quota-dept-key:"+topDeptId, time.Second*30)
	// 加锁
	if err := mu.Lock(); err != nil {
		logs.Ctx(ctx).Any("deptId", topDeptId).Err(err).Error("dept quota lock error")
		return errors.New("其他人正在操作部门配额，请稍后")
	}
	defer mu.Unlock()
	qdvs, qpvs, err := projectQuotaHandler(ctx, topDeptId, args.RegionId, project.Id, args.QuotaValueMap)
	if err != nil {
		return err
	}
	return repo.ProjectCreate(ctx, project, qpvs, qdvs)
}

// ProjectUpdate 项目更新
func ProjectUpdate(ctx context.Context, args *model.ProjectCreateOrUpdateArgs) error {
	if args.DeptId == "" {
		return errors.New("部门不能为空")
	}
	if args.Name == "" {
		return errors.New("名称不能为空")
	}
	if args.ProjectId == "" {
		return errors.New("项目id不能为空")
	}
	if err := isNameDuplication(ctx, args.DeptId, args.Name, args.ProjectId); err != nil {
		return err
	}
	project := &model.Project{Id: args.ProjectId}
	if err := repo.ProjectGet(ctx, project); err != nil {
		return errors.New("项目不存在")
	}
	project.Alias = args.Name
	project.Description = args.Description
	project.ModifyTime = time.Now()
	return repo.ProjectEdit(ctx, project, "alias", "description", "modify_time")
}

// isNameDuplication 验证项目名称
func isNameDuplication(ctx context.Context, deptId, name, projectId string) error {

	topDeptId, err := DepartmentTopId(ctx, deptId)
	if err != nil {
		return err
	}
	deptIds, _ := repo.DepartmentUnderAllIds(ctx, topDeptId)
	if repo.ProjectExistName(ctx, deptIds, name, projectId) {
		return errors.New("已有部门存在项目[" + name + "]")
	}
	return nil
}

// ProjectDelete 删除项目
func ProjectDelete(ctx context.Context, id, regionId string) error {
	if id == "" {
		return errors.New("项目id不能为空")
	}
	if regionId == "" {
		regionId = "RegionOne"
	}
	project := &model.Project{Id: id}
	if err := repo.ProjectGet(ctx, project); err != nil {
		return errors.New("删除失败，该id对应的项目不存在")
	}

	// todo 判断是否还有资源

	// 获取一级组织
	topDeptId, err := DepartmentTopId(ctx, project.DeptId)
	if err != nil {
		return err
	}
	// 操作一级组织配额需要加锁
	mu := distlock.NewMutex(ctx, "quota-dept-key:"+topDeptId, time.Second*30)
	// 加锁一分钟
	if err := mu.Lock(); err != nil {
		logs.Ctx(ctx).Any("deptId", topDeptId).Err(err).Error("dept quota lock error")
		return errors.New("其他人正在操作该项目配额，请稍后")
	}
	defer mu.Unlock()
	// 查询组织的配额
	qdvs, _ := repo.QuotaDeparmentValue(ctx, []string{topDeptId}, regionId)
	qdvMap := make(map[string]*model.QuotaDepartmentValue, 0)
	for _, qdv := range qdvs {
		qdvMap[qdv.MetricName] = qdv
	}
	// 查询项目的配额
	qpvs, _ := repo.QuotaProjectValue(ctx, []string{id}, regionId)
	// 修改组织配额
	for _, qpv := range qpvs {
		if qpv.TotalValue == -1 {
			// 项目配额为无限制，则组织使用量减去项目使用量
			qdvMap[qpv.MetricName].UsedValue = qdvMap[qpv.MetricName].UsedValue - qpv.UsedValue
		} else {
			// 项目有限制，则减去项目总量
			qdvMap[qpv.MetricName].UsedValue = qdvMap[qpv.MetricName].UsedValue - qpv.TotalValue
		}
	}
	// 状态删除
	project.Enabled = false
	if err := repo.ProjectDeleteRelation(ctx, project, qdvs, regionId); err != nil {
		return errors.New("删除项目失败")
	}
	return nil
}

// ProjectPageAll 查询全部项目
func ProjectPageAll(ctx context.Context, filter *model.ProjectPageAllArgs) ([]model.Project, int, error) {
	if filter.CurrUserId == "" {
		return nil, 0, errors.New("用户id不能为空")
	}
	user := &model.User{Id: filter.CurrUserId}
	if err := repo.UserGet(ctx, user); err != nil {
		return nil, 0, errors.New("用户不存在")
	}
	projects := make([]model.Project, 0)
	total := 0
	deptIds := make([]string, 0)
	var err error
	if topx.TYPE_MASTER == user.Type || topx.TYPE_DEPT_MASTER == user.Type {
		// 前端不传组织查询条件时，查询组织下所有组织id
		if filter.DeptId == "" {
			deptIds, err = repo.DepartmentUnderAllIds(ctx, user.DeptId)
			if err != nil {
				return nil, 0, errors.New("查询所有部门id失败")
			}
			filter.DataBaseDeptIds = append(filter.DataBaseDeptIds, deptIds...)
		} else {
			filter.DataBaseDeptIds = append(filter.DataBaseDeptIds, filter.DeptId)
		}

		if projects, total, err = repo.ProjectPageAll(ctx, filter); err != nil {
			logs.Ctx(ctx).Err(err).Any("filter", filter).Any("deptIds", deptIds).Error("repo ProjectPageAll error")
			return nil, 0, errors.New("查询所有项目失败")
		}
	} else if topx.TYPE_ADMIN == user.Type || topx.TYPE_SECURITY == user.Type || topx.TYPE_SYS_ADMIN == user.Type {
		if filter.DeptId != "" {
			filter.DataBaseDeptIds = append(filter.DataBaseDeptIds, filter.DeptId)
		}
		if projects, total, err = repo.ProjectPageAll(ctx, filter); err != nil {
			logs.Ctx(ctx).Err(err).Any("filter", filter).Error("repo ProjectPageAll error")
			return nil, 0, errors.New("查询所有项目失败")
		}
	} else if topx.TYPE_NORMAL == user.Type {
		if filter.DeptId != "" {
			filter.DataBaseDeptIds = append(filter.DataBaseDeptIds, filter.DeptId)
		}
		// 普通用户，查询自己的项目
		urms, _ := repo.UserRoleMappingListByUserId(ctx, filter.CurrUserId)
		for _, urm := range urms {
			if urm.ProjectId != "" {
				filter.ProjectIds = append(filter.ProjectIds, urm.ProjectId)
			}
		}

		if projects, total, err = repo.ProjectPageAll(ctx, filter); err != nil {
			logs.Ctx(ctx).Err(err).Any("filter", filter).Any("deptIds", deptIds).Error("repo ProjectPageAll error")
			return nil, 0, errors.New("查询所有项目失败")
		}
	}

	ProjectTrans(ctx, projects)
	return projects, total, nil
}

// ProjectTrans 转换项目
func ProjectTrans(ctx context.Context, projects []model.Project) {
	tree, _ := DepartmentMap(ctx)
	for i := range projects {
		// alias转为name
		projects[i].Name = projects[i].Alias
		// deptpath
		projects[i].DeptPath, _ = DepartmentPath(ctx, projects[i].DeptId, tree)
		if dept, ok := tree[projects[i].DeptId]; ok {
			projects[i].DeptName = dept.Name
		}
	}
}

// ProjectQuotaUpdate 项目配额修改
func ProjectQuotaUpdate(ctx context.Context, args *model.ProjectQuotaUpdateArgs) error {
	if args.ProjectId == "" {
		return errors.New("项目id不能为空")
	}
	if args.RegionId == "" {
		args.RegionId = "RegionOne"
	}
	if len(args.QuotaValueMap) == 0 {
		return nil
	}
	project := &model.Project{Id: args.ProjectId}
	if err := repo.ProjectGet(ctx, project); err != nil {
		return err
	}
	// 获取一级组织
	topDeptId, err := DepartmentTopId(ctx, project.DeptId)
	if err != nil {
		return err
	}
	// 操作一级组织配额需要加锁
	mu := distlock.NewMutex(ctx, "quota-dept-key:"+topDeptId, time.Second*30)
	// 加锁一分钟
	if err := mu.Lock(); err != nil {
		logs.Ctx(ctx).Any("deptId", topDeptId).Err(err).Error("dept quota lock error")
		return errors.New("其他人正在操作该项目配额，请稍后")
	}
	defer mu.Unlock()
	qdvs, qpvs, err := projectQuotaHandler(ctx, topDeptId, args.RegionId, args.ProjectId, args.QuotaValueMap)
	if err != nil {
		return err
	}
	if err := repo.ProjectQuotaUpdate(ctx, qdvs, qpvs); err != nil {
		logs.Ctx(ctx).Err(err).Any("qdvs", qdvs).Any("qpvs", qpvs).Error("repo ProjectQuotaUpdate error")
		return errors.New("修改项目失败")
	}
	return nil
}

func projectQuotaHandler(ctx context.Context, topDeptId, regionId, projectId string, quotaValueMap map[string]int64) ([]*model.QuotaDepartmentValue, []*model.QuotaProjectValue, error) {
	// 获取一级组织的配额
	qdvs, _ := repo.QuotaDeparmentValue(ctx, []string{topDeptId}, regionId)
	qdvMap := make(map[string]*model.QuotaDepartmentValue, 0)
	availableValueMap := make(map[string]int64)
	for _, qdv := range qdvs {
		qdvMap[qdv.MetricName] = qdv
		if qdv.TotalValue == -1 {
			availableValueMap[qdv.MetricName] = -1
		} else {
			availableValueMap[qdv.MetricName] = qdv.TotalValue - qdv.UsedValue
		}
	}
	// 获取项目目前的配额
	cuurqpvs, _ := repo.QuotaProjectValue(ctx, []string{projectId}, regionId)
	cuurqpvsMap := make(map[string]model.QuotaProjectValue, 0)
	for _, cuurqpv := range cuurqpvs {
		cuurqpvsMap[cuurqpv.MetricName] = cuurqpv
	}
	// 封装项目配额
	qpvs := make([]*model.QuotaProjectValue, 0)
	for k, v := range quotaValueMap {
		// 一级组织没有配置配额，则生成一级组织配额和项目配额，适配后期添加配额指标情况
		if _, ok := availableValueMap[k]; !ok {
			qpv := &model.QuotaProjectValue{
				MetricName: k,
				ProjectId:  projectId,
				TotalValue: v,
				UsedValue:  0,
				RegionId:   regionId,
			}
			qpvs = append(qpvs, qpv)
			qdv := &model.QuotaDepartmentValue{MetricName: k, DepartmentId: topDeptId, TotalValue: -1, UsedValue: 0, RegionId: regionId}
			if v != -1 {
				qdv.UsedValue = qdv.UsedValue + v
			}
			qdvs = append(qdvs, qdv)
			continue
		}

		// 判断设置的配额是否满足当前项目配额
		var cuurTotalValue int64
		cuurTotalValue = 0
		var cuurUseValue int64
		cuurUseValue = 0
		if cuurqpv, ok := cuurqpvsMap[k]; ok {
			cuurTotalValue = cuurqpv.TotalValue
			cuurUseValue = cuurqpv.UsedValue
			// 当项目配额不是无限，判断与当前配额的使用量
			if v != -1 {
				if v < cuurqpv.UsedValue {
					qm := &model.QuotaMetric{Name: k}
					if err := repo.QuotaMetricGet(ctx, qm); err != nil {
						return nil, nil, errors.New("指标不存在")
					}
					return nil, nil, fmt.Errorf("%s小于当前项目%s，请重新设置", qm.TotalName, qm.UsedName)
				}
			}
		}

		// 一级组织无限制
		if availableValueMap[k] == -1 {
			qpv := &model.QuotaProjectValue{
				MetricName: k,
				ProjectId:  projectId,
				TotalValue: v,
				UsedValue:  cuurUseValue,
				RegionId:   regionId,
			}
			qpvs = append(qpvs, qpv)
			// 当前项目为有限制->转换成有限制
			if cuurTotalValue != -1 && v != -1 {
				qdvMap[k].UsedValue = qdvMap[k].UsedValue - cuurTotalValue + v
			} else if cuurTotalValue != -1 && v == -1 {
				// 当前项目为有限制->转换成无限制
				qdvMap[k].UsedValue = qdvMap[k].UsedValue - cuurTotalValue + cuurUseValue
			} else if cuurTotalValue == -1 && v != -1 {
				// 当前项目为无限制->转换成有限制
				qdvMap[k].UsedValue = qdvMap[k].UsedValue - cuurUseValue + v
			}
			continue
		}
		// 一级组织不是无限制，项目设置成无限制
		if v == -1 {
			return nil, nil, errors.New("一级组织不是无限制，项目不能设置成无限制")
		}
		if availableValueMap[k] < v {
			qm := &model.QuotaMetric{Name: k}
			if err := repo.QuotaMetricGet(ctx, qm); err != nil {
				return nil, nil, errors.New("指标不存在")
			}
			return nil, nil, errors.New(qm.TotalName + "大于可用量，请重新设置")
		}
		qdvMap[k].UsedValue = qdvMap[k].UsedValue - cuurTotalValue + v
		qpv := &model.QuotaProjectValue{
			MetricName: k,
			ProjectId:  projectId,
			TotalValue: v,
			UsedValue:  cuurUseValue,
			RegionId:   regionId,
		}
		qpvs = append(qpvs, qpv)
	}
	return qdvs, qpvs, nil
}

// ProjectBindUser 项目绑定用户
func ProjectBindUser(ctx context.Context, body *model.ProjectBindUserArgs) error {
	if body.ProjectId == "" {
		return errors.New("项目id不能为空")
	}
	// 查询项目绑定
	urms, err := repo.URMByProjectNoRole(ctx, body.ProjectId)
	if err != nil {
		return errors.New("查询项目绑定的用户失败")
	}
	originUserIds := make([]string, 0, len(urms))
	for _, urm := range urms {
		originUserIds = append(originUserIds, urm.UserId)
	}
	// 先删除已绑定的用户
	deleteItems := util.DifferenceStr(originUserIds, body.UserIds)
	if len(deleteItems) > 0 {
		if err := repo.URMDropByUserIds(ctx, body.ProjectId, deleteItems); err != nil {
			return err
		}
	}

	// 需要新增的用户
	addItems := util.DifferenceStr(body.UserIds, originUserIds)
	if len(addItems) > 0 {
		urms := make([]*model.UserRoleMapping, 0, len(addItems))
		for _, userId := range addItems {
			urms = append(urms, &model.UserRoleMapping{Id: rndm.GUID(), UserId: userId, ProjectId: body.ProjectId})
		}
		if err := repo.UserRoleMappingBatchAdd(ctx, urms); err != nil {
			logs.Ctx(ctx).Err(err).Any("urms", urms).Error("user bind projects UserRoleMappingBatchAdd error")
			return errors.New("绑定用户项目失败")
		}
	}
	return nil
}

// ProjectUnBindUser 项目解绑用户
func ProjectUnBindUser(ctx context.Context, body *model.ProjectBindUserArgs) error {
	if body.ProjectId == "" {
		return errors.New("项目id不能为空")
	}
	if len(body.UserIds) == 0 {
		return nil
	}
	if err := repo.ProjectUnBindUsers(ctx, body); err != nil {
		logs.Ctx(ctx).Any("body", body).Err(err).Error("repo UserUnBindRoles error")
		return errors.New("批量解绑项目用户失败")
	}
	return nil
}
