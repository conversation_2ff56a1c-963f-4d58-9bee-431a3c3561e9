package service

import (
	"context"
	"errors"
	"sync"

	"code.mysugoncloud.com/hci/assist/topx"
	"code.mysugoncloud.com/hci/iam/internal/repo"
	"code.mysugoncloud.com/hci/iam/model"
)

func OverviewCount(ctx context.Context, userId string) (*model.OverviewCountReoly, error) {
	user := &model.User{Id: userId}
	if err := repo.UserGet(ctx, user); err != nil {
		return nil, errors.New("用户不存在")
	}
	overviewReoly := &model.OverviewCountReoly{}
	var wg sync.WaitGroup
	wg.Add(1)
	go deptCount(ctx, user, &wg, overviewReoly)
	wg.Add(1)
	go projectCount(ctx, user, &wg, overviewReoly)
	wg.Add(1)
	go roleCount(ctx, user, &wg, overviewReoly)
	wg.Add(1)
	go userCount(ctx, user, &wg, overviewReoly)
	// 等待所有goroutine完成
	wg.Wait()
	return overviewReoly, nil
}

// deptCount 组织数量
func deptCount(ctx context.Context, user *model.User, wg *sync.WaitGroup, result *model.OverviewCountReoly) {
	defer wg.Done()

	userType := user.Type
	if userType == topx.TYPE_MASTER || userType == topx.TYPE_DEPT_MASTER {
		deptIds, err := repo.DepartmentUnderAllIds(ctx, user.DeptId)
		if err != nil {
			result.DeptCount = 0
		} else {
			result.DeptCount = len(deptIds)
		}
	} else {
		mods, _ := repo.DepartmentAll(ctx)
		result.DeptCount = len(mods)
	}
}

// projectCount 项目数量
func projectCount(ctx context.Context, user *model.User, wg *sync.WaitGroup, result *model.OverviewCountReoly) {
	defer wg.Done()

	filter := &model.ProjectPageAllArgs{
		List:       model.List{PageNum: 1, PageSize: 10000},
		CurrUserId: user.Id,
	}
	_, count, err := ProjectPageAll(ctx, filter)
	if err != nil {
		result.ProjectCount = 0
	} else {
		result.ProjectCount = count
	}
}

// roleCount 角色数量
func roleCount(ctx context.Context, user *model.User, wg *sync.WaitGroup, result *model.OverviewCountReoly) {
	defer wg.Done()

	filter := &model.RolePageAllArgs{
		List:       model.List{PageNum: 1, PageSize: 10000},
		CurrUserId: user.Id,
	}
	_, count, err := RolePageAll(ctx, filter)
	if err != nil {
		result.RoleCount = 0
	} else {
		result.RoleCount = count
	}
}

// roleCount 角色数量
func userCount(ctx context.Context, user *model.User, wg *sync.WaitGroup, result *model.OverviewCountReoly) {
	defer wg.Done()

	filter := &model.UserListArgs{
		List:       model.List{PageNum: 1, PageSize: 10000},
		CurrUserId: user.Id,
	}
	_, count, err := UserListPage(ctx, filter)
	if err != nil {
		result.UserCount = 0
	} else {
		result.UserCount = count
	}
}
