package service

const (
	// 用户锁定缓存前缀
	UNAME_ERROR_LOCK = "uname_error_lock:"

	// 用户登录错误次数缓存前缀
	UNAME_ERROR_TIMES = "uname_error_times:"

	// 用户上次登录错误总次数,登录成功才清空
	UNAME_LAST_ERROR_COUNT = "user_last_error_count:"

	// globalsetting passwrod policytype
	CLOBAL_SETTING_PASSWORD = "password"

	// globalsetting password 错误次数
	CLOBAL_SETTING_PASSWORD_ERROR_TIMES = "error_times"

	// globalsetting password 错误上限锁定时间
	CLOBAL_SETTING_PASSWORD_LOCK_MINUTE = "lock_minute"

	// 多少分钟内连续输密码错误次数达到设定值，触发锁定
	CLOBAL_SETTING_PASSWORD_ERROR_TIME_INTERVAL = "error_time_interval"

	// token type
	TOKEN = "token"

	// token过期时间
	TOKEN_EXPIRED_KEY = "token_expired"
	// setting type user
	POLICY_TYPE = "user"
	// 设置用户是否只能在一个地方登录setting key
	ONE_PLACE_LOGIN = "one_place_login"

	// 过期的token前缀
	EXPIRE_TOKEN = "expire_token:"

	// 顶级部门标识
	MASTER_DEPARTMENT_LEVEL = 0

	// user extra description key
	USER_EXTRA_DESCRIPTION_KEY = "description"
	PASSWORD_ENCODE            = "passwordEncode"
	USER_EXTRA_SOURCE_KEY      = "source"
	// quota
	QUOTA_DEFAULT_TYPE = "quota"
	QUOTA_DEFAULT_NAME = "quota_default"
)
