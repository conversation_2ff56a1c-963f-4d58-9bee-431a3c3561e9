package service

import (
	"context"
	"time"

	"code.mysugoncloud.com/hci/iam/internal/conf"
	"code.mysugoncloud.com/hci/iam/internal/repo"
)

func CacheTicker(ctx context.Context) {
	iamcfg, err := conf.GetIamCfg(ctx)
	if err != nil {
		return
	}
	// 定义一个每隔iamcfg.CacheTicker触发一次的Ticker
	ticker := time.NewTicker(iamcfg.CacheTicker)

	// 使用一个无限循环来处理定时任务
	for range ticker.C {
		// 查询过期数据，删除
		repo.CacheDeleteExpired(ctx)
	}
}
