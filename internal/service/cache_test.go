package service

import (
	"context"
	"os"
	"testing"

	"code.mysugoncloud.com/hci/assist/config"
)

func initTest(t *testing.T) {
	os.Args = append(os.Args, "-log-path=", "-config=../../conf.toml")
	t.Log("full args", os.Args[1:])

	err := config.PreInitMgmt(context.TODO())
	if err != nil {
		t.<PERSON><PERSON>(err)
	}

	config.InitMgmt(context.TODO(), nil, nil)
	testInit()
}

func TestCacheTirker(t *testing.T) {
	initTest(t)
	CacheTicker(context.Background())
}
