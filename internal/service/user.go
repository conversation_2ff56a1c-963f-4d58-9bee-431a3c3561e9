package service

import (
	"context"
	"encoding/json"
	"errors"
	"regexp"
	"strconv"
	"time"

	"code.mysugoncloud.com/hci/assist/logs"
	"code.mysugoncloud.com/hci/assist/rndm"
	"code.mysugoncloud.com/hci/assist/topx"
	"code.mysugoncloud.com/hci/iam/internal/conf"
	"code.mysugoncloud.com/hci/iam/internal/repo"
	"code.mysugoncloud.com/hci/iam/internal/util"
	"code.mysugoncloud.com/hci/iam/model"
)

// DepartmentUsers 查询部门下用户列表
func DepartmentUsers(ctx context.Context, filter *model.DeparmentUsersArgs) ([]model.User, int, error) {
	if filter.DeptId == "" {
		return nil, 0, errors.New("部门id不能为空")
	}
	if filter.CurrUserId == "" {
		return nil, 0, errors.New("用户id不能为空")
	}
	user := &model.User{Id: filter.CurrUserId}
	if err := repo.UserGet(ctx, user); err != nil {
		return nil, 0, errors.New("用户不能存在")
	}
	// 部门管理员不查询同级部门的部门管理员
	if user.Type == topx.TYPE_DEPT_MASTER && filter.DeptId == user.DeptId {
		filter.IsDeptMaster = true
	}
	users, total, err := repo.UsersByDeptId(ctx, filter)
	if err != nil {
		return nil, 0, err
	}
	for i := range users {
		users[i].Password = ""
		if users[i].Alias == "" {
			users[i].Alias = users[i].Name
		}
		users[i].Extra = parseUserExtra(users[i].Extra, USER_EXTRA_DESCRIPTION_KEY)
	}
	return users, total, nil
}

// UserListPage 分页查询所有用户
func UserListPage(ctx context.Context, filter *model.UserListArgs) ([]model.User, int, error) {
	if filter.CurrUserId == "" {
		return nil, 0, errors.New("用户id不能为空")
	}
	users, total, err := repo.UserListPageByUserType(ctx, filter)
	if err != nil {
		return nil, 0, err
	}
	if len(users) == 0 {
		return nil, 0, nil
	}
	tree, err := DepartmentMap(ctx)
	if err != nil {
		return nil, 0, err
	}
	for i := range users {
		if users[i].Alias == "" {
			users[i].Alias = users[i].Name
		}
		// dept path
		deptPath, err := DepartmentPath(ctx, users[i].DeptId, tree)
		if err != nil {
			return nil, 0, err
		}
		users[i].DeptPath = deptPath
		users[i].Password = ""
		users[i].Extra = parseUserExtra(users[i].Extra, USER_EXTRA_DESCRIPTION_KEY)
	}
	return users, total, nil
}

// UserSubCreate 创建用户
func UserSubCreate(ctx context.Context, args *model.UserCreateArgs) (string, error) {
	if args.DeptId == "" {
		return "", errors.New("部门id不能为空")
	}
	err := validateCreateArgs(ctx, args)
	if err != nil {
		return "", err
	}
	// 判断部门是否存在
	dept := &model.Department{Id: args.DeptId}
	err = repo.DepartmentGet(ctx, dept)
	if err != nil {
		return "", errors.New("不存在部门信息id:" + args.DeptId)
	}

	passwd, err := util.DecryptByPublicKey(ctx, args.Publickey, args.Password)
	if err != nil {
		return "", err
	}
	passwd, _ = util.PasswordHash(passwd)

	user := &model.User{
		Id:               rndm.GUID(),
		Name:             args.Name,
		Alias:            args.Alias,
		DeptId:           args.DeptId,
		Email:            args.Email,
		Extra:            wrapperUserExtra(args.Extra),
		Phone:            args.Phone,
		Password:         passwd,
		CreatedAt:        time.Now(),
		LastPasswordTime: time.Now(),
		ModifyTime:       time.Now(),
		Enabled:          true,
	}

	if args.IsDepartmentManager {
		user.Type = topx.TYPE_DEPT_MASTER
	} else {
		user.Type = topx.TYPE_NORMAL
	}
	// 更新数据库
	err = repo.UserCreateSub(ctx, user)
	if err != nil {
		return "", err
	}

	return user.Id, nil
}

// validateCreateArgs 验证用户创建参数
func validateCreateArgs(ctx context.Context, args *model.UserCreateArgs) error {
	if args.Name == "" {
		return errors.New("用户名不能为空")
	}
	if args.Email == "" {
		return errors.New("邮箱不能为空")
	}
	if args.Password == "" {
		return errors.New("密码不能为空")
	}
	if args.Phone == "" {
		return errors.New("电话不能为空")
	}
	// 校验用户名、邮箱、电话
	emailPattern := "^[A-Za-z\\d]+([-_.][A-Za-z\\d]+)*@([A-Za-z\\d]+[-.])+[A-Za-z\\d]{2,4}$"
	matched, err := regexp.MatchString(emailPattern, args.Email)
	if err != nil || !matched {
		return errors.New("邮箱格式有误")
	}
	iamcfg, _ := conf.GetIamCfg(ctx)
	if iamcfg.PhonePattern != "" {
		matched, err = regexp.MatchString(iamcfg.PhonePattern, args.Phone)
		if err != nil || !matched {
			return errors.New("手机号码格式不正确")
		}
	}
	if repo.UserExist(ctx, &model.User{Name: args.Name}) {
		return errors.New("账号已存在")
	}
	if repo.UserExistByEmailOrPhone(ctx, args.Email, args.Phone) {
		return errors.New("电话或者邮箱已被占用")
	}
	return nil
}

// parseUserExtra 获取extra中key对应值
func parseUserExtra(extra, key string) string {
	if extra == "" {
		return ""
	}
	var extraMap map[string]interface{}
	err := json.Unmarshal([]byte(extra), &extraMap)
	if err != nil {
		return ""
	}
	if value, ok := extraMap[key]; ok {
		if value != nil {
			return value.(string)
		}
	}
	return ""
}

// wrapperUserExtra 将界面传过来的description字段转为JSON字符串
func wrapperUserExtra(extraOrigin string) string {
	if extraOrigin == "" {
		return ""
	}

	extraMap := make(map[string]interface{}, 0)
	extraMap[USER_EXTRA_DESCRIPTION_KEY] = extraOrigin
	extraMap[PASSWORD_ENCODE] = false
	jsonData, err := json.Marshal(extraMap)
	if err != nil {
		logs.Ctx(context.Background()).Err(err).Any("extraMap", extraMap).Error("description字段转json字符串错误")
		return ""
	}
	return string(jsonData)
}

// UserRegister 注册用户
func UserRegister(ctx context.Context, args *model.UserCreateArgs) (string, error) {
	err := validateCreateArgs(ctx, args)
	if err != nil {
		return "", err
	}
	passwd, err := util.DecryptByPublicKey(ctx, args.Publickey, args.Password)
	if err != nil {
		return "", err
	}
	passwd, _ = util.PasswordHash(passwd)

	user := &model.User{
		Id:               rndm.GUID(),
		Name:             args.Name,
		Alias:            args.Alias,
		DeptId:           args.DeptId,
		Email:            args.Email,
		Extra:            wrapperUserExtra(args.Extra),
		Phone:            args.Phone,
		Password:         passwd,
		CreatedAt:        time.Now(),
		LastPasswordTime: time.Now(),
		ModifyTime:       time.Now(),
		Type:             topx.TYPE_MASTER,
		Enabled:          true,
		LastActiveAt:     time.Now(),
	}

	if args.DepartmentName != "" {
		if repo.DepartmentExist(ctx, &model.Department{Level: 0, Name: args.DepartmentName}) {
			return "", errors.New("组织名称已存在")
		}
	} else {
		args.DepartmentName = user.Name + "_dept"
	}
	department := &model.Department{
		Id:          rndm.GUID(),
		Name:        args.DepartmentName,
		Description: parseUserExtra(user.Extra, USER_EXTRA_DESCRIPTION_KEY),
		ModifyTime:  time.Now(),
	}
	// 部门配额
	qdvs := make([]model.QuotaDepartmentValue, 0)
	if qms, err := repo.QuotaMetricAll(ctx); err == nil {
		for _, qm := range qms {
			var totalValue int64
			if v, ok := args.QuotaValueMap[qm.Name]; ok {
				totalValue = v
			} else {
				totalValue = -1
			}
			qdv := model.QuotaDepartmentValue{
				MetricName:   qm.Name,
				DepartmentId: department.Id,
				TotalValue:   totalValue,
				UsedValue:    0,
				RegionId:     args.RegionId,
			}
			qdvs = append(qdvs, qdv)
		}
	}

	user.DeptId = department.Id

	// 注册用户
	err = repo.UserRegister(ctx, user, department, qdvs)
	if err != nil {
		return "", err
	}

	return user.Id, nil
}

// UserDetail 用户详情
func UserDetail(ctx context.Context, userId, token string) (*model.User, error) {
	user := &model.User{Id: userId}
	err := repo.UserGet(ctx, user)
	if err != nil {
		return nil, errors.New("不存在该ID[" + userId + "]的用户")
	}
	user.Extra = parseUserExtra(user.Extra, USER_EXTRA_DESCRIPTION_KEY)
	if user.Alias == "" {
		user.Alias = user.Name
	}
	// 上次错误次数
	if lastLoginErrCount, err := util.CacheGet(ctx, user.Name+":"+token); err == nil {
		user.LastLoginErrorCount, _ = strconv.Atoi(lastLoginErrCount)
	}
	return user, nil
}

// UserUpdate 修改用户
func UserUpdate(ctx context.Context, args *model.UserUpdateArgs) error {
	if err := validateUpdateArgs(ctx, args); err != nil {
		return err
	}
	user := &model.User{Id: args.Id}
	if err := repo.UserGet(ctx, user); err != nil {
		return errors.New("用户不存在")
	}
	user.Alias = args.Alias
	user.Phone = args.Phone
	user.Email = args.Email
	if user.Extra == "" {
		user.Extra = wrapperUserExtra(args.Extra)
	} else {
		var extraMap map[string]interface{}
		if err := json.Unmarshal([]byte(user.Extra), &extraMap); err == nil {
			extraMap[USER_EXTRA_DESCRIPTION_KEY] = args.Extra
		}
		if jsonData, err := json.Marshal(extraMap); err == nil {
			user.Extra = string(jsonData)
		}
	}
	if err := repo.UserEdit(ctx, user, "alias", "phone", "email", "extra"); err != nil {
		return errors.New("更新用户失败")
	}
	return nil
}

func validateUpdateArgs(ctx context.Context, args *model.UserUpdateArgs) error {
	if args.Id == "" {
		return errors.New("用户id不能为空")
	}
	if args.Alias == "" {
		return errors.New("用户别名不能为空")
	}
	if args.Phone == "" {
		return errors.New("电话不能为空")
	}
	if args.Email == "" {
		return errors.New("邮箱不能为空")
	}

	// 校验用户名、邮箱、电话
	emailPattern := "^[A-Za-z\\d]+([-_.][A-Za-z\\d]+)*@([A-Za-z\\d]+[-.])+[A-Za-z\\d]{2,4}$"
	matched, err := regexp.MatchString(emailPattern, args.Email)
	if err != nil || !matched {
		return errors.New("邮箱格式有误")
	}
	iamcfg, _ := conf.GetIamCfg(ctx)
	if iamcfg.PhonePattern != "" {
		matched, err = regexp.MatchString(iamcfg.PhonePattern, args.Phone)
		if err != nil || !matched {
			return errors.New("手机号码格式不正确")
		}
	}
	if repo.UserExistByEmailOrPhoneNotId(ctx, args.Email, args.Phone, args.Id) {
		return errors.New("电话或者邮箱已被占用")
	}
	return nil
}

// UserBindRoles 用户批量绑定部门角色、项目
func UserBindRoles(ctx context.Context, body *model.UserBindRolesArgs) error {
	if body.UserId == "" {
		return errors.New("用户id不能为空")
	}
	if !repo.UserExist(ctx, &model.User{Id: body.UserId}) {
		return errors.New("用户不存在")
	}
	// 先删除用户已绑定的角色，再绑定
	if err := repo.UserRoleMappingDropUserOrProjectRole(ctx, body.UserId, body.ProjectId); err != nil {
		logs.Ctx(ctx).Err(err).Any("filter", body).Error("UserRoleMappingDropUserOrProjectRole error")
		return errors.New("删除用户角色失败")
	}
	if len(body.RoleIds) == 0 {
		return nil
	}
	urms := make([]*model.UserRoleMapping, 0)
	for _, roleId := range body.RoleIds {
		urms = append(urms, &model.UserRoleMapping{Id: rndm.GUID(), UserId: body.UserId, ProjectId: body.ProjectId, RoleId: roleId})
	}
	if err := repo.UserRoleMappingBatchAdd(ctx, urms); err != nil {
		logs.Ctx(ctx).Err(err).Any("filter", body).Error("UserRoleMappingBatchAdd error")
		return errors.New("绑定用户角色失败")
	}
	return nil
}

// UserUnBindRoles 批量解绑用户角色
func UserUnBindRoles(ctx context.Context, body *model.UserUnBindRolesArgs) error {
	if body.UserId == "" {
		return errors.New("用户id不能为空")
	}
	if !repo.UserExist(ctx, &model.User{Id: body.UserId}) {
		return errors.New("用户不存在")
	}
	if len(body.RoleIds) == 0 {
		return nil
	}
	if err := repo.UserUnBindRoles(ctx, body); err != nil {
		logs.Ctx(ctx).Any("body", body).Err(err).Error("repo UserUnBindRoles error")
		return errors.New("批量解绑用户角色失败")
	}
	return nil
}

// UserBindProjects 用户绑定项目
func UserBindProjects(ctx context.Context, filter *model.UserBindProjectsArgs) error {
	if !repo.UserExist(ctx, &model.User{Id: filter.UserId}) {
		return errors.New("用户不存在")
	}
	// 查询用户已绑定的项目
	originProjectIds, err := ProjectIdsByUserId(ctx, filter.UserId)
	if err != nil {
		logs.Ctx(ctx).Err(err).Any("filter", filter).Error("user bind projects ProjectIdsByUserId error")
		return errors.New("查询用户已绑定的项目出错")
	}

	// 先删除用户已绑定的项目，再绑定
	deleteItems := util.DifferenceStr(originProjectIds, filter.ProjectIds)
	if len(deleteItems) > 0 {
		if err := repo.UserRoleMappingDropByProjectIds(ctx, filter.UserId, deleteItems); err != nil {
			logs.Ctx(ctx).Err(err).Any("filter", filter).Error("user bind projects UserRoleMappingDropByProjectIds error")
			return errors.New("解绑用户项目失败")
		}
	}

	// 需要新增的项目
	addItems := util.DifferenceStr(filter.ProjectIds, originProjectIds)
	if len(addItems) > 0 {
		urms := make([]*model.UserRoleMapping, 0, len(addItems))
		for _, projectId := range addItems {
			urms = append(urms, &model.UserRoleMapping{Id: rndm.GUID(), ProjectId: projectId, UserId: filter.UserId})
		}
		if err := repo.UserRoleMappingBatchAdd(ctx, urms); err != nil {
			logs.Ctx(ctx).Err(err).Any("urms", urms).Error("user bind projects UserRoleMappingBatchAdd error")
			return errors.New("绑定用户项目失败")
		}

	}
	return nil
}

// UserUnBindProjects 用户解绑项目
func UserUnBindProjects(ctx context.Context, filter *model.UserBindProjectsArgs) error {
	if !repo.UserExist(ctx, &model.User{Id: filter.UserId}) {
		return errors.New("用户不存在")
	}
	if len(filter.ProjectIds) == 0 {
		return nil
	}
	if err := repo.UserUnBindProjects(ctx, filter); err != nil {
		return err
	}
	return nil
}

// UserResetPassword 重置密码
func UserResetPassword(ctx context.Context, filter *model.UserResetPasswordArgs) error {
	if filter.UserId == "" {
		return errors.New("用户id不能为空")
	}
	if filter.CurrUserId == "" {
		return errors.New("当前登录用户id不能为空")
	}
	if filter.Password == "" {
		return errors.New("密码不能为空")
	}

	user := &model.User{Id: filter.UserId}
	if err := repo.UserGet(ctx, user); err != nil {
		return errors.New("用户不存在")
	}
	currUser := &model.User{Id: filter.CurrUserId}
	if err := repo.UserGet(ctx, currUser); err != nil {
		return errors.New("当前登录用户不存在")
	}
	// 安全管理员和日志管理员 只能securityamin能重置
	if user.Type == topx.TYPE_SECURITY || user.Type == topx.TYPE_AUDIT {
		if currUser.Type != topx.TYPE_SECURITY {
			return errors.New("当前用户不能重置该用户密码")
		}
	}
	// 只能security admin重置内置用户密码
	// userSource为空表示为本平台注册的用户
	userSource := parseUserExtra(user.Extra, USER_EXTRA_SOURCE_KEY)
	if user.Type == topx.TYPE_SYS_ADMIN && userSource == "" {
		if currUser.Type != topx.TYPE_SECURITY && currUser.Type != topx.TYPE_ADMIN && currUser.Type != topx.TYPE_SYS_ADMIN {
			return errors.New("当前用户不能重置该用户密码")
		}
	}
	// 超级管理员只能超级管理员能重置密码
	if user.Type == topx.TYPE_ADMIN && currUser.Type != topx.TYPE_ADMIN {
		return errors.New("当前用户不能重置该用户密码")
	}
	pwd, err := util.DecryptByPublicKey(ctx, filter.Publickey, filter.Password)
	if err != nil {
		return err
	}
	user.Password = pwd
	if err := repo.UserEdit(ctx, user, "password"); err != nil {
		return errors.New("重置密码失败")
	}
	return nil
}

// UserExpired 设置用户过期时间
func UserExpired(ctx context.Context, userId string, expired time.Time) error {
	if userId == "" {
		return errors.New("用户id不能为空")
	}
	if expired.IsZero() {
		return errors.New("过期时间不能为空")
	}
	if expired.Before(time.Now()) {
		return errors.New("过期时间必须在当前时间之后")
	}
	user := &model.User{Id: userId}
	if err := repo.UserGet(ctx, user); err != nil {
		return errors.New("用户不存在")
	}
	user.Expired = expired
	if err := repo.UserEdit(ctx, user, "expired"); err != nil {
		return errors.New("设置过期时间失败")
	}
	return nil
}

// UserUpdateStatus 修改用户状态
func UserUpdateStatus(ctx context.Context, userId string, status bool) error {
	if userId == "" {
		return errors.New("用户id不能为空")
	}
	user := &model.User{Id: userId}
	if err := repo.UserGet(ctx, user); err != nil {
		return errors.New("用户不存在")
	}
	user.Enabled = status
	user.LastActiveAt = time.Now()
	if err := repo.UserEdit(ctx, user, "enabled", "last_active_at"); err != nil {
		return errors.New("设置过期时间失败")
	}
	return nil
}

// UserAccess 获取用户访问权限
func UserAccess(ctx context.Context, userId string) (*model.UserAccess, error) {
	if userId == "" {
		return nil, errors.New("用户id不能为空")
	}
	user := &model.User{Id: userId}
	if err := repo.UserGet(ctx, user); err != nil {
		return nil, errors.New("用户不存在")
	}
	userAccess := &model.UserAccess{UserId: userId}
	repo.UserAccessGet(ctx, userAccess)
	userAccess.Ip = user.AllowIp
	return userAccess, nil
}

// UserAccessSet 设置用户访问权限
func UserAccessSet(ctx context.Context, userAccess *model.UserAccess) error {
	// 如果设置了开始和结束时间，需要进行比对，结束时间必须大于等于开始时间
	if !userAccess.StartDate.IsZero() && !userAccess.EndDate.IsZero() {
		if userAccess.StartDate.After(userAccess.EndDate) {
			return errors.New("入参有误，开始时间小于结束时间！")
		}
	}
	// 校验用户是否存在
	if userAccess.UserId == "" {
		return errors.New("用户id不能为空")
	}
	user := &model.User{Id: userAccess.UserId}
	if err := repo.UserGet(ctx, user); err != nil {
		return errors.New("用户不存在")
	}
	if userAccess.Ip != "" {
		// 设置白名单
		user.AllowIp = userAccess.Ip
		if err := repo.UserEdit(ctx, user, "allow_ip"); err != nil {
			return errors.New("更新用户白名单失败")
		}
	}
	originUserAccess := &model.UserAccess{UserId: userAccess.UserId}
	if err := repo.UserAccessGet(ctx, originUserAccess); err != nil {
		userAccess.Id = rndm.GUID()
		userAccess.CreateTime = time.Now()
		userAccess.ModifyTime = time.Now()
		repo.UserAccessAdd(ctx, userAccess)
	} else {
		userAccess.Id = originUserAccess.Id
		userAccess.CreateTime = originUserAccess.CreateTime
		userAccess.ModifyTime = time.Now()
		repo.UserAccessEdit(ctx, userAccess, "start_date", "end_date", "limit_flag", "limit_time", "modify_time")
	}

	return nil
}

// UserDelete 用户删除
func UserDelete(ctx context.Context, userId string) error {
	if userId == "" {
		return errors.New("用户id不能为空")
	}
	user := &model.User{Id: userId}
	if err := repo.UserGet(ctx, user); err != nil {
		return errors.New("用户不存在")
	}

	if user.Name == "admin" {
		return errors.New("不能删除超级管理员")
	}
	if user.Name == "inner" {
		return errors.New("不能删除内置账号")
	}
	if user.Type == topx.TYPE_SYS_ADMIN {
		return errors.New("不能删除系统管理员")
	}
	if user.Type == topx.TYPE_SECURITY {
		return errors.New("不能删除安全管理员")
	}
	if user.Type == topx.TYPE_AUDIT {
		return errors.New("不能删除日志管理员")
	}

	if user.Type == topx.TYPE_MASTER {
		// 校验是否有子部门存在
		if repo.DepartmentExist(ctx, &model.Department{ParentId: user.DeptId}) {
			return errors.New("删除失败，该企业组织有子部门存在")
		}
		repo.UserRoleMappingDrop(ctx, &model.UserRoleMapping{UserId: userId})
		repo.UserDrop(ctx, &model.User{Id: userId})
		repo.ProjectDrop(ctx, &model.Project{DeptId: user.DeptId})
		repo.DepartmentDrop(ctx, &model.Department{Id: user.DeptId})
	} else {
		repo.UserRoleMappingDrop(ctx, &model.UserRoleMapping{UserId: userId})
		repo.UserDrop(ctx, &model.User{Id: userId})
	}
	return nil
}

// UserPageByProjectId 查询一个项目里的所有成员
func UserPageByProjectId(ctx context.Context, filter *model.UserPageByProjectIdArgs) ([]model.User, int, error) {
	if filter.ProjectId == "" {
		return nil, 0, errors.New("项目id不能为空")
	}
	if !repo.ProjectExist(ctx, &model.Project{Id: filter.ProjectId}) {
		return nil, 0, errors.New("项目不存在")
	}
	users, total, err := repo.UserPageByProjectId(ctx, filter)
	if err != nil {
		logs.Ctx(ctx).Err(err).Any("filter", filter).Error("repo UserPageByProjectId")
		return nil, 0, errors.New("查询项目成员失败")
	}
	for i := range users {
		if users[i].Alias == "" {
			users[i].Alias = users[i].Name
		}
		users[i].Extra = parseUserExtra(users[i].Extra, USER_EXTRA_DESCRIPTION_KEY)
	}

	return users, total, nil
}

// UserUnlock 解锁用户
func UserUnlock(ctx context.Context, userId string) error {
	if userId == "" {
		return errors.New("用户id不能为空")
	}
	user := &model.User{Id: userId}
	if err := repo.UserGet(ctx, user); err != nil {
		return nil
	}
	// 删除缓存中的锁
	util.CacheDrop(ctx, UNAME_ERROR_LOCK+user.Name)
	// 删除缓存中用于判断错误次数的key
	util.CacheDrop(ctx, UNAME_ERROR_TIMES+user.Name)
	return nil
}
