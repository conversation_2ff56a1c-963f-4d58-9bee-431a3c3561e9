package service

import (
	"context"
	"time"

	"code.mysugoncloud.com/hci/iam/internal/util"
)

// 获取公钥
func GetPulicKey(ctx context.Context) string {
	keyPair := util.GenKeyPair()
	// 设置有效时间12H
	util.CacheAdd(ctx, keyPair[util.PublicKey], keyPair[util.PrivateKey], 12*time.Hour)
	return keyPair[util.PublicKey]
}

// PrivateKeyGet 根据公钥获取私钥
func PrivateKeyGet(ctx context.Context, publicKey string) string {
	privateKey, _ := util.CacheGet(ctx, publicKey)
	return privateKey
}

// PrivateKeyDeletec 根据公钥删除
func PrivateKeyDelete(ctx context.Context, publicKey string) {
	util.CacheDrop(ctx, publicKey)
}
