package service

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"testing"
	"time"
	_ "time/tzdata"

	"code.mysugoncloud.com/hci/assist/config"
	"code.mysugoncloud.com/hci/assist/logs"
	"code.mysugoncloud.com/hci/iam/internal/repo"
	"code.mysugoncloud.com/hci/iam/model"
)

func testInit() error {
	logs.SetLevel(logs.LDEBUG)
	logs.SetCons(true)
	loc, _ := time.LoadLocation("Asia/Shanghai")
	fmt.Println("loc", loc)
	if loc != nil {
		time.Local = loc
	}
	os.Args = append(os.Args, "-log-path=", "-config=../../conf.toml", "-show-sql=true")
	config.PreInitMgmt(context.TODO())

	return repo.Init(context.Background(), "root:database_sugon@tcp(172.22.1.57:3306)/iam_hci?charset=utf8mb4&parseTime=true&loc=Local")
}

func TestCreateToken(t *testing.T) {
	err := testInit()
	if err != nil {
		t.Fatal(err)
	}
	loginUser := &model.LoginUserArgs{Username: "admin", Password: "keystone_sugon"}
	token, err := CreateToken(context.Background(), loginUser)
	if err != nil {
		t.Fatal(err)
	}
	logs.Ctx(context.Background()).Str("token", token).Info("token获取")
}

func TestValidateToken(t *testing.T) {
	err := testInit()
	if err != nil {
		t.Fatal(err)
	}
	token := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhbGlhcyI6IiIsInVzZXJJZCI6IjBkMmJiYjAxOGU4YjQ0Yjk4NWExNjk2NDczNzlmNDEzIiwidXNlcm5hbWUiOiJhZG1pbiIsInByb2plY3RJZCI6ImVkY2MyNDA1OWM4YjQ2OTY5YTM3MjYwZGM5YWIyMzI5IiwidXNlclR5cGUiOiJhZG1pbiIsImRlcHRfaWQiOiIyIiwiaWF0IjoxNzE3NzMwMzk4LCJpc3MiOiJzdWdvbi1jbG91ZC1pYW0ifQ.t95d8srkX8LDl2vZ85pSMzBTJwe65n4A81-D6DsyFdo"
	_, err = ValidateToken(context.Background(), token)
	if err != nil {
		t.Fatal(err)
	}
}

func TestTime(t *testing.T) {
	loc, _ := time.LoadLocation("Asia/Shanghai")
	fmt.Println("loc", loc)
	if loc != nil {
		time.Local = loc
	}
	nowTime := time.Now()
	sub, _ := time.ParseInLocation("2006-01-02 15:04:05", "2024-06-04 14:50:00", time.Local)
	diff := nowTime.Sub(sub)
	logs.Info(diff.Hours())
	days := diff / (24 * time.Hour)
	fmt.Printf("两个时间相差的天数: %d\n", days)
}

func TestBuildDepartmentTree(t *testing.T) {
	testInit()
	mods, err := BuildDepartmentTree(context.Background())
	if err != nil {
		t.Error(err)
		return
	}
	jsonData, _ := json.Marshal(mods)
	fmt.Println("------------------")
	fmt.Println(string(jsonData))

}

func TestGetUnderDeptAllIds(t *testing.T) {
	testInit()
	ids, _ := repo.DepartmentUnderAllIds(context.Background(), "1acba556fe984b11af99784b4fde0931")
	fmt.Println(ids)
}

func TestListByUserId(t *testing.T) {
	testInit()
	roles, _ := RoleListByUserId(context.Background(), "912c36ae85a44aefbd1f0b42922d90ca", "")
	jsonData, _ := json.Marshal(roles)
	fmt.Println("------------------")
	fmt.Println(string(jsonData))
}
