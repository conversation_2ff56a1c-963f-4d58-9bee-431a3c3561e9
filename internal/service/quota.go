package service

import (
	"context"
	"errors"
	"strconv"
	"strings"

	"code.mysugoncloud.com/hci/assist/logs"
	"code.mysugoncloud.com/hci/assist/topx"
	"code.mysugoncloud.com/hci/iam/internal/repo"
	"code.mysugoncloud.com/hci/iam/model"
)

// QuotaDeparmentOverview 查询部门配额
func QuotaDeparmentOverview(ctx context.Context, filter *model.QuotaByDeparmentIdArgs) ([]*model.QuotaTypeDepartmentReoly, error) {
	if filter.RegionId == "" {
		return nil, errors.New("区域id不能为空")
	}
	// 普通用户不能查询配额
	if filter.UserType == "" || filter.UserType == topx.TYPE_NORMAL {
		return nil, errors.New("你没有权限查询配额")
	}

	// 查询所有配额分类
	quotaTypes, err := repo.QuotaTypeAll(ctx)
	if err != nil {
		logs.Ctx(ctx).Err(err).Error("repo QuotaTypeAll error")
		return nil, errors.New("查询配额类型失败")
	}
	queryDeptIds := make([]string, 0)
	if filter.DepartmentId != "" {
		queryDeptIds = append(queryDeptIds, filter.DepartmentId)
	} else {
		// 查询用户的部门
		queryDeptIds = DepartmentTopIds(ctx, filter.UserId)
	}
	// 查询一级部门
	depts, err := repo.DepartmentOneLevel(ctx, queryDeptIds)
	if err != nil {
		logs.Ctx(ctx).Err(err).Any("filter", filter).Error("repo DepartmentOneLevel error")
		return nil, errors.New("查询一级部门失败")
	}
	if len(depts) == 0 {
		return nil, errors.New("未查询到一级部门")
	}
	// 查询所有配额指标
	quotaMetraics, err := repo.QuotaMetricAll(ctx)
	if err != nil {
		logs.Ctx(ctx).Err(err).Error("repo QuotaMetricAll error")
		return nil, errors.New("查询所有配额指标失败")
	}
	// 分组
	quotaMetraicMap := make(map[string][]model.QuotaMetric)
	for _, qm := range quotaMetraics {
		quotaMetraicMap[qm.TypeName] = append(quotaMetraicMap[qm.TypeName], qm)
	}
	// 查询部门配额配置
	deptIds := make([]string, 0)
	for _, dept := range depts {
		deptIds = append(deptIds, dept.Id)
	}
	quotaDeptValues, err := repo.QuotaDeparmentValue(ctx, deptIds, filter.RegionId)
	if err != nil {
		logs.Ctx(ctx).Err(err).Any("deptIds", deptIds).Error("repo QuotaByDeparmentIds error")
		return nil, errors.New("查询部门分配量失败")
	}
	quotaDeptValueMap := make(map[string][]model.QuotaDepartmentValue)
	for _, qdv := range quotaDeptValues {
		quotaDeptValueMap[qdv.DepartmentId] = append(quotaDeptValueMap[qdv.DepartmentId], *qdv)
	}
	// 封装结果
	result := make([]*model.QuotaTypeDepartmentReoly, 0)
	for _, quotaType := range quotaTypes {
		qms := quotaMetraicMap[quotaType.Name]
		if len(qms) == 0 {
			continue
		}
		quotaTypeDepts := make([]model.QuotaDepartmentReoly, 0)
		for _, dept := range depts {
			deptQuotaValue := quotaDeptValueMap[dept.Id]
			// 没有配置配额限制
			if len(deptQuotaValue) == 0 {
				quotaDept := model.QuotaDepartmentReoly{
					Id:           dept.Id,
					Name:         dept.Name,
					Description:  dept.Description,
					QuotaMetrics: qms,
				}
				quotaTypeDepts = append(quotaTypeDepts, quotaDept)
				continue
			}
			deptQuotaValueMap := make(map[string]model.QuotaDepartmentValue)
			for _, dqv := range deptQuotaValue {
				deptQuotaValueMap[dqv.MetricName] = dqv
			}
			// 填充总量和使用量
			qmsTemp := make([]model.QuotaMetric, 0)
			for _, qm := range qms {
				if dqv, ok := deptQuotaValueMap[qm.Name]; ok {
					qm.TotalValue = dqv.TotalValue
					qm.UsedValue = dqv.UsedValue
				}
				qmsTemp = append(qmsTemp, qm)
			}
			quotaDept := model.QuotaDepartmentReoly{
				Id:           dept.Id,
				Name:         dept.Name,
				Description:  dept.Description,
				QuotaMetrics: qmsTemp,
			}
			quotaTypeDepts = append(quotaTypeDepts, quotaDept)

		}
		qtd := &model.QuotaTypeDepartmentReoly{
			QuotaType: model.QuotaType{
				Name:        quotaType.Name,
				Description: quotaType.Description,
				Sequence:    quotaType.Sequence,
			},
			QuotaDepartments: quotaTypeDepts,
		}
		result = append(result, qtd)
	}

	return result, nil
}

// QuotaProjectOverview 项目配额
func QuotaProjectOverview(ctx context.Context, filter *model.QuotaProjectArgs) ([]*model.QuotaTypeProjectReoly, error) {
	if filter.RegionId == "" {
		return nil, errors.New("区域id不能为空")
	}
	// 普通用户不能查询配额
	if filter.CurrUserType == "" || filter.CurrUserType == topx.TYPE_NORMAL {
		return nil, errors.New("你没有权限查询配额")
	}

	// 查询所有配额分类
	quotaTypes, err := repo.QuotaTypeAll(ctx)
	if err != nil {
		logs.Ctx(ctx).Err(err).Error("repo QuotaTypeAll error")
		return nil, errors.New("查询配额类型失败")
	}
	// 查询项目
	projectFilter := &model.ProjectPageAllArgs{
		List: model.List{
			PageNum:  1,
			PageSize: 1000,
		},
		ProjectName: filter.ProjectName,
		CurrUserId:  filter.CurrUserId,
		DeptId:      filter.DeptId,
	}
	projects, _, err := ProjectPageAll(ctx, projectFilter)
	if err != nil || len(projects) == 0 {
		return nil, nil
	}
	// 查询所有项目配额使用情况
	projectIds := make([]string, 0)
	for _, project := range projects {
		projectIds = append(projectIds, project.Id)
	}
	quotaProjectValues, err := repo.QuotaProjectValue(ctx, projectIds, filter.RegionId)
	if err != nil {
		logs.Ctx(ctx).Err(err).Any("projectIds", projectIds).Error("repo QuotaProjectValue error")
		return nil, errors.New("查询项目分配量失败")
	}
	quotaProjectValuesMap := make(map[string][]model.QuotaProjectValue)
	for _, qpv := range quotaProjectValues {
		quotaProjectValuesMap[qpv.ProjectId] = append(quotaProjectValuesMap[qpv.ProjectId], qpv)
	}

	// 查询所有配额指标
	quotaMetraics, err := repo.QuotaMetricAll(ctx)
	if err != nil {
		logs.Ctx(ctx).Err(err).Error("repo QuotaMetricAll error")
		return nil, errors.New("查询所有配额指标失败")
	}
	// 分组
	quotaMetraicMap := make(map[string][]model.QuotaMetric)
	for _, qm := range quotaMetraics {
		quotaMetraicMap[qm.TypeName] = append(quotaMetraicMap[qm.TypeName], qm)
	}

	// 封装结果
	result := make([]*model.QuotaTypeProjectReoly, 0)
	for _, quotaType := range quotaTypes {
		qms := quotaMetraicMap[quotaType.Name]
		if len(qms) == 0 {
			continue
		}
		quotaTypeProjects := make([]model.QuotaProjectReoly, 0)
		for _, project := range projects {
			projectQuotaValue := quotaProjectValuesMap[project.Id]
			// 没有配置配额限制
			if len(projectQuotaValue) == 0 {
				quotaProject := model.QuotaProjectReoly{
					Id:           project.Id,
					Name:         project.Alias,
					DeptPath:     project.DeptPath,
					Description:  project.Description,
					QuotaMetrics: qms,
				}
				quotaTypeProjects = append(quotaTypeProjects, quotaProject)
				continue
			}
			projectQuotaValueMap := make(map[string]model.QuotaProjectValue)
			for _, pqv := range projectQuotaValue {
				projectQuotaValueMap[pqv.MetricName] = pqv
			}
			// 填充总量和使用量
			qmsTemp := make([]model.QuotaMetric, 0)
			for _, qm := range qms {
				if dqv, ok := projectQuotaValueMap[qm.Name]; ok {
					qm.TotalValue = dqv.TotalValue
					qm.UsedValue = dqv.UsedValue
				}
				qmsTemp = append(qmsTemp, qm)
			}
			quotaProject := model.QuotaProjectReoly{
				Id:           project.Id,
				Name:         project.Alias,
				DeptPath:     project.DeptPath,
				Description:  project.Description,
				QuotaMetrics: qmsTemp,
			}
			quotaTypeProjects = append(quotaTypeProjects, quotaProject)

		}
		qtd := &model.QuotaTypeProjectReoly{
			QuotaType: model.QuotaType{
				Name:        quotaType.Name,
				Description: quotaType.Description,
				Sequence:    quotaType.Sequence,
			},
			QuotaProjects: quotaTypeProjects,
		}
		result = append(result, qtd)
	}

	return result, nil
}

// QuotaDept 获取部门配额默认值
func QuotaDept(ctx context.Context, deptId, regionId string) ([]model.QuotaDefaultReoly, error) {
	// 查询所有配额分类
	quotaTypes, err := repo.QuotaTypeAll(ctx)
	if err != nil {
		logs.Ctx(ctx).Err(err).Error("repo QuotaTypeAll error")
		return nil, errors.New("查询配额类型失败")
	}
	// 查询所有配额指标
	quotaMetraics, err := repo.QuotaMetricAll(ctx)
	if err != nil {
		logs.Ctx(ctx).Err(err).Error("repo QuotaMetricAll error")
		return nil, errors.New("查询所有配额指标失败")
	}
	// 分组
	quotaMetraicMap := make(map[string][]model.QuotaMetric)
	for _, qm := range quotaMetraics {
		qm.TotalValue = -1
		//qm.TopAvailableValue = -1
		// 查询具体组织的配额
		if deptId != "" {
			// 查看部门配额详情
			qdvs, _ := repo.QuotaDeparmentValue(ctx, []string{deptId}, regionId)
			qdvMap := make(map[string]*model.QuotaDepartmentValue)
			for _, qdv := range qdvs {
				qdvMap[qdv.MetricName] = qdv
			}
			if qdv, ok := qdvMap[qm.Name]; ok {
				qm.TotalValue = qdv.TotalValue
				qm.UsedValue = qdv.UsedValue
				// if qdv.TotalValue != -1 {
				// 	qm.TopAvailableValue = qm.TotalValue - qm.UsedValue
				// }
			}
		}
		quotaMetraicMap[qm.TypeName] = append(quotaMetraicMap[qm.TypeName], qm)
	}
	result := make([]model.QuotaDefaultReoly, 0)
	for _, quotaType := range quotaTypes {
		qdR := &model.QuotaDefaultReoly{
			QuotaType:    *quotaType,
			QuotaMetrics: quotaMetraicMap[quotaType.Name],
		}
		result = append(result, *qdR)
	}
	return result, nil
}

// QuotaProject 获取项目配额默认值
func QuotaProject(ctx context.Context, deptId, projectId, regionId string) ([]model.QuotaDefaultReoly, error) {
	if deptId == "" {
		return nil, errors.New("部门id不能为空")
	}
	if regionId == "" {
		return nil, errors.New("区域id不能为空")
	}
	// 查询所有配额分类
	quotaTypes, err := repo.QuotaTypeAll(ctx)
	if err != nil {
		logs.Ctx(ctx).Err(err).Error("repo QuotaTypeAll error")
		return nil, errors.New("查询配额类型失败")
	}
	// 查询所有配额指标
	quotaMetraics, err := repo.QuotaMetricAll(ctx)
	if err != nil {
		logs.Ctx(ctx).Err(err).Error("repo QuotaMetricAll error")
		return nil, errors.New("查询所有配额指标失败")
	}

	// 获取部门可用量
	topDeptId, err := DepartmentTopId(ctx, deptId)
	if err != nil {
		return nil, err
	}
	qdvs, _ := repo.QuotaDeparmentValue(ctx, []string{topDeptId}, regionId)
	availableValueMap := make(map[string]int64)
	for _, qdv := range qdvs {
		if qdv.TotalValue == -1 {
			availableValueMap[qdv.MetricName] = -1
		} else {
			availableValueMap[qdv.MetricName] = qdv.TotalValue - qdv.UsedValue
		}
	}

	// 分组
	quotaMetraicMap := make(map[string][]model.QuotaMetric)
	// 获取默认配额配置
	qmv := getQuotaDefault(ctx)
	// 获取当前项目配额配置
	qpvMap := make(map[string]model.QuotaProjectValue)
	if projectId != "" {
		qpvs, _ := repo.QuotaProjectValue(ctx, []string{projectId}, regionId)
		for _, qpv := range qpvs {
			qpvMap[qpv.MetricName] = qpv
		}
	}
	for _, qm := range quotaMetraics {
		// 可用量
		if v, ok := availableValueMap[qm.Name]; ok {
			qm.TopAvailableValue = v
		} else {
			qm.TopAvailableValue = -1
		}
		if projectId != "" {
			if v, ok := qpvMap[qm.Name]; ok {
				qm.TotalValue = v.TotalValue
				qm.UsedValue = v.UsedValue
			}
		} else {
			// 默认总量
			if v, ok := qmv[qm.Name]; ok {
				qm.TotalValue = v
			} else {
				qm.TotalValue = -1
			}

			// 可用量不是无限制，则默认总量不能为无限制
			if qm.TopAvailableValue != -1 && qm.TotalValue == -1 {
				qm.TotalValue = qm.TopAvailableValue
			}
			// 可用不是无限制时，默认总量大于可用量，可调整默认总量
			if qm.TopAvailableValue != -1 && qm.TotalValue > qm.TopAvailableValue {
				qm.TotalValue = qm.TopAvailableValue
			}
		}

		quotaMetraicMap[qm.TypeName] = append(quotaMetraicMap[qm.TypeName], qm)
	}

	result := make([]model.QuotaDefaultReoly, 0)
	for _, quotaType := range quotaTypes {
		qdR := &model.QuotaDefaultReoly{
			QuotaType:    *quotaType,
			QuotaMetrics: quotaMetraicMap[quotaType.Name],
		}
		result = append(result, *qdR)
	}
	return result, nil
}

// getQuotaDefault 获取全局配置数量
func getQuotaDefault(ctx context.Context) map[string]int64 {
	result := make(map[string]int64)
	settings := repo.GlobalsettingGetByType(ctx, QUOTA_DEFAULT_TYPE, QUOTA_DEFAULT_NAME)
	if settings.PolicyDocument == "" {
		return result
	}
	pairs := strings.Split(settings.PolicyDocument, ",")

	for _, pair := range pairs {
		kv := strings.Split(pair, ":")
		if len(kv) == 2 {
			value, err := strconv.ParseInt(kv[1], 10, 64)
			if err == nil {
				result[kv[0]] = value
			}
		}
	}
	return result
}

// QuotaByProjectId 获取项目配额
func QuotaByProjectId(ctx context.Context, projectId, regionId string, qmNames []string) ([]model.QuotaMetricReoly, error) {
	if projectId == "" {
		return nil, errors.New("项目id不能为空")
	}
	project := &model.Project{Id: projectId}
	if err := repo.ProjectGet(ctx, project); err != nil {
		return nil, errors.New("项目不存在")
	}
	if regionId == "" {
		regionId = "RegionOne"
	}
	// 查询所有配额指标
	quotaMetraics, err := repo.QuotaMetricAllByNames(ctx, qmNames)
	if err != nil {
		logs.Ctx(ctx).Any("names", qmNames).Err(err).Error("repo QuotaMetricAll error")
		return nil, errors.New("查询所有配额指标失败")
	}

	// 获取当前项目配额配置
	qpvMap := make(map[string]model.QuotaProjectValue)
	if projectId != "" {
		qpvs, _ := repo.QuotaProjectValue(ctx, []string{projectId}, regionId)
		for _, qpv := range qpvs {
			qpvMap[qpv.MetricName] = qpv
		}
	}
	// 添加了新的配额指标，项目里面没有配置
	qmv := make(map[string]int64, 0)
	availableValueMap := make(map[string]int64, 0)
	if len(quotaMetraics) > len(qpvMap) {
		// 获取默认配额配置
		qmv = getQuotaDefault(ctx)
		// 获取部门可用量
		topDeptId, err := DepartmentTopId(ctx, project.DeptId)
		if err != nil {
			return nil, err
		}
		qdvs, _ := repo.QuotaDeparmentValue(ctx, []string{topDeptId}, regionId)
		availableValueMap = make(map[string]int64)
		for _, qdv := range qdvs {
			if qdv.TotalValue == -1 {
				availableValueMap[qdv.MetricName] = -1
			} else {
				availableValueMap[qdv.MetricName] = qdv.TotalValue - qdv.UsedValue
			}
		}
	}
	// 封装返回接口
	result := make([]model.QuotaMetricReoly, 0)
	for _, qm := range quotaMetraics {
		qpr := model.QuotaMetricReoly{Name: qm.Name, TypeName: qm.TypeName}
		if v, ok := qpvMap[qm.Name]; ok {
			qpr.TotalValue = v.TotalValue
			qpr.UsedValue = v.UsedValue
		} else {
			// 可用量
			if v, ok := availableValueMap[qm.Name]; ok {
				qm.TopAvailableValue = v
			} else {
				qm.TopAvailableValue = -1
			}
			// 默认总量
			if v, ok := qmv[qm.Name]; ok {
				qpr.TotalValue = v
			} else {
				qpr.TotalValue = -1
			}

			// 上级可用量是限制，则总量不能为无限制
			if qm.TopAvailableValue != -1 && qpr.TotalValue == -1 {
				qpr.TotalValue = qm.TopAvailableValue
			}
			// 上级可用量是限制，总量大于可用量，则调整总量为可用量
			if qm.TopAvailableValue != -1 && qpr.TotalValue > qm.TopAvailableValue {
				qpr.TotalValue = qm.TopAvailableValue
			}
		}
		result = append(result, qpr)
	}
	return result, nil
}
