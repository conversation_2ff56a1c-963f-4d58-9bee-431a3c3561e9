package service

import (
	"context"
	"errors"
	"time"

	"code.mysugoncloud.com/hci/assist/logs"
	"code.mysugoncloud.com/hci/iam/internal/repo"
	"code.mysugoncloud.com/hci/iam/model"
)

// MicroServiceCategoryList 查询微服务类别信息
func MicroServiceCategoryList(ctx context.Context, filter *model.MicroServiceCategoryListArgs) ([]*model.MicroServiceCategory, int, error) {
	mscs, total, err := repo.MicroServiceCategoryList(ctx, filter)
	if err != nil {
		logs.Ctx(ctx).Err(err).Any("filter", filter).Error("repo MicroServiceCategoryList error")
		return nil, 0, errors.New("查询分类失败")
	}
	if total == 0 {
		return mscs, total, nil
	}
	// 查询所有菜单
	microServices, err := repo.MicroServiceAll(ctx)
	if err != nil {
		logs.Ctx(ctx).Err(err).Any("filter", filter).Error("repo MicroServiceAll error")
		return nil, 0, errors.New("查询所有服务菜单失败")
	}
	tree := make(map[string]*model.MicroServiceCategory)

	for _, msc := range mscs {
		tree[msc.Id] = msc
	}
	for _, ms := range microServices {
		if msc, ok := tree[ms.CategoryId]; ok {
			msc.Children = append(msc.Children, ms)
		}
	}

	return mscs, total, nil
}

// MicroServiceCategoryUpdate 更新服务类别信息
func MicroServiceCategoryUpdate(ctx context.Context, body *model.MicroServiceCategoryUpdateArgs) error {
	if body.Id == "" {
		return errors.New("id不能为空")
	}
	msc := &model.MicroServiceCategory{Id: body.Id}
	if err := repo.MicroServiceCategoryGet(ctx, msc); err != nil {
		return errors.New("服务分类不存在")
	}
	if body.NavHidden != nil {
		msc.NavHidden = *body.NavHidden
	}
	if body.Order != nil {
		msc.Order = *body.Order
	}
	msc.ModifyTime = time.Now()
	if err := repo.MicroServiceCategoryEdit(ctx, msc, "nav_hidden", "order", "modify_time"); err != nil {
		logs.Ctx(ctx).Err(err).Any("body", body).Error("repo MicroServiceCategoryEdit error")
		return errors.New("更新服务类别失败")
	}
	return nil
}

// MicroServiceCategoryUpdate 更新服务信息
func MicroServiceUpdate(ctx context.Context, body *model.MicroServiceUpdateArgs) error {
	if body.Id == "" {
		return errors.New("id不能为空")
	}
	ms := &model.MicroService{Id: body.Id}
	if err := repo.MicroServiceGet(ctx, ms); err != nil {
		return errors.New("服务不存在")
	}
	if body.NavHidden != nil {
		ms.NavHidden = *body.NavHidden
	}
	if body.Order != nil {
		ms.Order = *body.Order
	}
	ms.ModifyTime = time.Now()
	if err := repo.MicroServiceEdit(ctx, ms, "nav_hidden", "order", "third_part_access", "modify_time"); err != nil {
		logs.Ctx(ctx).Err(err).Any("body", body).Error("repo MicroServiceEdit error")
		return errors.New("更新服务失败")
	}
	return nil
}
