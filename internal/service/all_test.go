package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"testing"
	"time"

	"code.mysugoncloud.com/hci/assist/logs"
	"code.mysugoncloud.com/hci/assist/topx"
	"code.mysugoncloud.com/hci/iam/internal/util"
	"code.mysugoncloud.com/hci/iam/model"
)

func TestGetPublicKey(t *testing.T) {
	testInit()
	publicKey := GetPulicKey(context.Background())
	logs.Ctx(context.Background()).Str("publicKey", publicKey).Info("获取publicKey")
}

func TestDecryptByPublicKey(t *testing.T) {
	testInit()
	publicKey := "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDWLL3Nt9IZGR0dACVuPT7y8/UKzgxpmWYeOEW7THyKCaUaJpHpAGp942ZlVtT5MdVS8vlkQ7JYUvfjeggK3KLX5NbW/kiwnSb6IxflFHXEiFumqErEIwmo22nCH2zKvJ6uDZ9TcABEZvwQCseOf0cUyOA3nNZv6m7R8uGTiUC+vQIDAQAB"
	password := "k3b2bl0Xq/G8rgy3bNyD4IvMCRq+VbesYdtsnqzocb0zLIJWLBzW5lSZZRji/Cxv0L0WC6lZgXD6dyQ9NMsFc5VWGkPae4e4PxtsjcBcLNnF/oiZfLFluxA7UBt6H3q6tVcFANh2SE4Xlf7jRL+jmgdfDXlrTku602T58LsblH8="
	p, err := util.DecryptByPublicKey(context.Background(), publicKey, password)
	if err != nil {
		logs.Error(err)
	}
	fmt.Println(p)
}

func TestQuotaByDeparmentId(t *testing.T) {
	testInit()
	mods, err := QuotaDeparmentOverview(context.Background(), &model.QuotaByDeparmentIdArgs{DepartmentId: "", RegionId: "RegionOne", UserId: "0d2bbb018e8b44b985a169647379f413", UserType: topx.TYPE_ADMIN})
	if err != nil {
		logs.Error(err)
	}
	jsonData, _ := json.Marshal(mods)
	fmt.Println("------------------")
	fmt.Println(string(jsonData))
}

func TestQuotaProject(t *testing.T) {
	testInit()
	mods, err := QuotaProjectOverview(context.Background(), &model.QuotaProjectArgs{DeptId: "", RegionId: "RegionOne", CurrUserId: "0d2bbb018e8b44b985a169647379f413", CurrUserType: topx.TYPE_ADMIN, ProjectName: "平台底座"})
	if err != nil {
		logs.Error(err)
	}
	jsonData, _ := json.Marshal(mods)
	fmt.Println("------------------")
	fmt.Println(string(jsonData))
}

func TestUserListPage(t *testing.T) {
	testInit()
	mods, _, err := UserListPage(context.Background(), &model.UserListArgs{List: model.List{PageSize: 100, PageNum: 1}, CurrUserId: "0d2bbb018e8b44b985a169647379f413"})
	if err != nil {
		logs.Error(err)
	}
	jsonData, _ := json.Marshal(mods)
	fmt.Println("------------------")
	fmt.Println(string(jsonData))
}

func TestOther(t *testing.T) {
	testInit()

	// 定义一个时间字符串
	timeStr := "2022-01-01 00:34:56"

	// 定义时间格式
	layout := "2006-01-02 15:04:05"

	// 使用time.Parse()函数将时间字符串转换为time.Time类型
	now, _ := time.Parse(layout, timeStr)
	// 获取当前时间点
	//now := time.Now()

	// 获取当前时间的小时、分钟和秒
	hour := now.Hour()
	minute := now.Minute()
	second := now.Second()

	// 将小时、分钟和秒转换为字符串
	hourStr := strconv.Itoa(hour)
	minuteStr := strconv.Itoa(minute)
	secondStr := strconv.Itoa(second)

	// 输出当前时间的字符串表示形式
	fmt.Println("Current time:", hourStr, minuteStr, secondStr)
}

func TestStr(t *testing.T) {
	fmt.Println(strings.Contains("12,23", "2"))
}

func TestPolicyList(t *testing.T) {
	testInit()
	policyIds := make([]string, 0)
	getAllChildId(context.Background(), "c1fd2cce952da1093f3aa062e8ca8d9e", &policyIds)
	fmt.Println(policyIds)
}

func TestPolicyDelet(t *testing.T) {
	testInit()
	PolicyDelete(context.Background(), "2e5e4a81-9a75-48c6-b208-88124b6b74ec")
}

func TestQuotaByProjectId(t *testing.T) {
	testInit()
	mods, _ := QuotaByProjectId(context.Background(), "0c137c98e6fb4d639aa9f2171fca92d6", "", []string{"ecs_instance"})
	fmt.Println("------------------")
	jsonData, _ := json.Marshal(mods)
	fmt.Println(string(jsonData))
}

func TestRolePageByUserId(t *testing.T) {
	testInit()
	filter := &model.UserRoleListArgs{
		List:   model.List{PageNum: 1, PageSize: 10},
		UserId: "a617dde9ad1441618c9c7376ee2d44fc",
	}
	mods, _, _ := RolePageByUserId(context.Background(), filter)
	fmt.Println("------------------")
	jsonData, _ := json.Marshal(mods)
	fmt.Println(string(jsonData))
}
