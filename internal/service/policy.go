package service

import (
	"context"
	"errors"
	"time"

	"code.mysugoncloud.com/hci/assist/logs"
	"code.mysugoncloud.com/hci/assist/rndm"
	"code.mysugoncloud.com/hci/assist/topx"
	"code.mysugoncloud.com/hci/iam/internal/repo"
	"code.mysugoncloud.com/hci/iam/model"
)

// GetMenu 获取菜单
func GetMenu(ctx context.Context, userId string, projectId string, serviceId string) ([]*model.RamPolicy, error) {
	if serviceId == "" {
		return nil, errors.New("serviceId不能为空")
	}
	mods := make([]*model.RamPolicy, 0)
	if userId == "" {
		return mods, nil
	}
	roles, err := RoleListByUserId(ctx, userId, projectId)
	if err != nil {
		return nil, err
	}
	roleIds := make([]string, len(roles))
	if len(roles) != 0 {
		for i, role := range roles {
			roleIds[i] = role.Id
		}
	}
	if len(roleIds) == 0 {
		return nil, nil
	}
	return getPolicyByTree(ctx, serviceId, roleIds)
}

// PolicyTree 查询权限树
func PolicyTree(ctx context.Context, userId, userType, roleId, serviceId string) ([]*model.RamPolicy, error) {
	if userId == "" {
		return nil, errors.New("用户id不能为空")
	}
	if userType == "" {
		return nil, errors.New("用户类型不能为空")
	}
	if serviceId == "" {
		return nil, errors.New("serviceId不能为空")
	}
	roleIds := make([]string, 0)
	if roleId == "" {
		if topx.TYPE_ADMIN == userType || topx.TYPE_SECURITY == userType {
			return getPolicyByTree(ctx, serviceId, nil)
		}
		roles, err := RoleListByUserId(ctx, userId, "")
		if err != nil {
			return nil, err
		}
		if len(roles) != 0 {
			for i, role := range roles {
				roleIds[i] = role.Id
			}
		}
	} else {
		roleIds = append(roleIds, roleId)
	}
	if len(roleIds) == 0 {
		return nil, nil
	}
	return getPolicyByTree(ctx, serviceId, roleIds)
}

func getPolicyByTree(ctx context.Context, serviceId string, roleIds []string) ([]*model.RamPolicy, error) {
	mods, err := repo.PolicyByUser(ctx, serviceId, roleIds)
	if err != nil {
		logs.Ctx(ctx).Err(err).Any("serviceId", serviceId).Any("roleIds", roleIds).Error("search user policy error")
		return nil, errors.New("查询菜单权限失败")
	}
	tree := make(map[string]*model.RamPolicy)
	for _, mod := range mods {
		tree[mod.Uuid] = mod
	}

	result := make([]*model.RamPolicy, 0)
	for _, policy := range mods {
		if parent, ok := tree[policy.ParentId]; ok {
			parent.Children = append(parent.Children, policy)
		} else {
			// 没有父级，则是顶级菜单
			result = append(result, policy)
		}
	}
	return result, nil
}

// PolicyCreate 策略创建
func PolicyCreate(ctx context.Context, policy *model.RamPolicy) error {
	if policy.PolicyName == "" {
		return errors.New("策略名称不能为空")
	}
	if policy.PolicyDocument == "" {
		return errors.New("策略描述不能为空")
	}
	if policy.ServiceId == "" {
		return errors.New("服务id不能为空")
	}
	if repo.PolicyExist(ctx, &model.RamPolicy{PolicyDocument: policy.PolicyDocument, ServiceId: policy.ServiceId}) {
		return errors.New("策略描述已存在，创建策略失败")
	}
	policy.CreateAt = time.Now()
	policy.Uuid = rndm.GUID()
	if err := repo.PolicyAdd(ctx, policy); err != nil {
		logs.Ctx(ctx).Err(err).Any("policy", policy).Error("repo PolicyAdd error")
		return errors.New("创建策略失败")
	}
	return nil
}

// PolicyByName 根据名称获取policy
func PolicyByName(ctx context.Context, name, serviceId string) *model.RamPolicy {
	if name == "" || serviceId == "" {
		return nil
	}
	policy := &model.RamPolicy{PolicyName: name, ServiceId: serviceId}
	if err := repo.PolicyGet(ctx, policy); err != nil {
		return nil
	}
	return policy
}

// PolicyById 根据id获取policy
func PolicyById(ctx context.Context, id string) *model.RamPolicy {
	policy := &model.RamPolicy{Uuid: id}
	if err := repo.PolicyGet(ctx, policy); err != nil {
		return nil
	}
	return policy
}

// PolicyDelete 策略删除
func PolicyDelete(ctx context.Context, id string) error {
	if id == "" {
		return errors.New("id不能为空")
	}
	policy := &model.RamPolicy{Uuid: id}
	if err := repo.PolicyGet(ctx, policy); err != nil {
		return errors.New("策略不存在")
	}
	if policy.CanDelete == "0" {
		return errors.New("该策略不允许删除")
	}
	policyIds := make([]string, 0)
	getAllChildId(ctx, id, &policyIds)
	if len(policyIds) > 0 {
		// 删除所有策略以及绑定关系
		if err := repo.PolicyDeleteRelation(ctx, policyIds); err != nil {
			logs.Ctx(ctx).Err(err).Any("policyIds", policyIds).Error("repo PolicyDeleteRelation error")
			return errors.New("删除策略失败")
		}
	}
	return nil
}

// getAllChildId 获取所有id
func getAllChildId(ctx context.Context, id string, policyIds *[]string) {
	*policyIds = append(*policyIds, id)
	policys, _ := repo.PolicyListByParentId(ctx, id)
	if len(policys) > 0 {
		for _, policy := range policys {
			getAllChildId(ctx, policy.Uuid, policyIds)
		}
	}
}

// PolicyEdit 策略修改
func PolicyEdit(ctx context.Context, id string, policy *model.RamPolicy) error {
	if !repo.PolicyExist(ctx, &model.RamPolicy{Uuid: id}) {
		return errors.New("策略不存在")
	}
	policy.Uuid = id
	return repo.PolicyEdit(ctx, policy)
}

// PolicyBind 策略绑定
func PolicyBind(ctx context.Context, args *model.PolicyBindArgs) error {
	if args.RoleId == "" {
		return errors.New("角色id不能为空")
	}
	if args.ServiceId == "" {
		return errors.New("服务不能为空")
	}
	role := &model.IamRole{Id: args.RoleId}
	if err := repo.IamRoleGet(ctx, role); err != nil {
		return errors.New("角色不存在")
	}
	if role.DeptId == "" && topx.TYPE_SECURITY != args.UserType && topx.TYPE_ADMIN != args.UserType {
		return errors.New("该角色不能修改策略")
	}
	// 先删除，在添加
	if err := repo.PolicyRoleDropByRoleId(ctx, args.RoleId, args.ServiceId); err != nil {
		logs.Ctx(ctx).Err(err).Any("args", args).Error("repo PolicyRoleDropByRoleId error")
		return errors.New("删除角色策略失败")
	}
	policyRoles := make([]*model.RamPolicyRole, 0)
	for _, policyId := range args.PolicyIds {
		policyRole := &model.RamPolicyRole{Uuid: rndm.GUID(), PolicyId: policyId, RamRoleId: args.RoleId, ServiceId: args.ServiceId}
		policyRoles = append(policyRoles, policyRole)
	}
	if len(policyRoles) > 0 {
		if err := repo.PolicyRoleAddBatch(ctx, policyRoles); err != nil {
			logs.Ctx(ctx).Err(err).Any("policyRoles", policyRoles).Error("repo PolicyRoleAddBatch error")
			return errors.New("添加角色策略失败")
		}
	}
	return nil
}
