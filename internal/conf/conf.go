package conf

import (
	"context"
	"time"

	"code.mysugoncloud.com/hci/assist/config"
	"code.mysugoncloud.com/hci/assist/globals"
	"code.mysugoncloud.com/hci/assist/logs"
)

type IamConfig struct {
	Dsn                string        `toml:"dsn"`                   // 数据库连接
	CacheTicker        time.Duration `toml:"cache_ticker"`          // iam_cache刷新间隔
	DepartmentMaxLevel int           `toml:"department_max_level"`  // 组织最大层级
	SkipTokenValidate  []string      `toml:"skip_token_validate"`   // 跳过token验证
	PhonePattern       string        `toml:"phone_pattern"`         // 电话号码验证正则表达式
	GenerateRamRoleIds []string      `toml:"generate_ram_role_ids"` // 重新导出策略角色id
}

func GetIamCfg(ctx context.Context) (*IamConfig, error) {
	ac := &IamConfig{}
	err := config.GetMgmtAny(ctx, globals.IamBasic, ac)
	if err != nil {
		logs.Ctx(ctx).Err(err).Str("key", globals.IamBasic).Error("config.GetMgmtAny")
		return ac, err
	}
	return ac, nil
}
