package websvc

import (
	"code.mysugoncloud.com/hci/assist/logs"
	"code.mysugoncloud.com/hci/assist/topx"
	"code.mysugoncloud.com/hci/iam/internal/service"
	"code.mysugoncloud.com/hci/iam/internal/util"
	"code.mysugoncloud.com/hci/iam/model"
	"github.com/labstack/echo/v4"
)

// QuotaDeparmentOverview doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	query		query	model.QuotaByDeparmentIdArgs true "请求参数"
// @Tags 配额API
// @Summary 概览查询部门配额
// @Success 200 {object} model.ResultModel{content=[]model.QuotaTypeDepartmentReoly} "返回数据"
// @Router /iam/quotas/department/overview [get]
func QuotaDeparmentOverview(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	filter := &model.QuotaByDeparmentIdArgs{}
	if err := ctx.Bind(filter); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	filter.RegionId = ctx.Request().Header.Get(topx.REGION_ID)
	filter.UserId = ctx.Request().Header.Get(topx.USER_ID)
	filter.UserType = ctx.Request().Header.Get(topx.USER_TYPE)
	quotas, err := service.QuotaDeparmentOverview(rctx, filter)
	if err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithContent(quotas))
}

// QuotaProjectOverview doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	query		query	model.QuotaProjectArgs true "请求参数"
// @Tags 配额API
// @Summary 概览查询项目配额
// @Success 200 {object} model.ResultModel{content=[]model.QuotaTypeProjectReoly} "返回数据"
// @Router /iam/quotas/project/overview [get]
func QuotaProjectOverview(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	filter := &model.QuotaProjectArgs{}
	if err := ctx.Bind(filter); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	filter.RegionId = ctx.Request().Header.Get(topx.REGION_ID)
	filter.CurrUserId = ctx.Request().Header.Get(topx.USER_ID)
	filter.CurrUserType = ctx.Request().Header.Get(topx.USER_TYPE)
	quotas, err := service.QuotaProjectOverview(rctx, filter)
	if err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithContent(quotas))
}

// QuotaDept doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	dept_id		query	string false "组织id，创建一级组织时，不传获取默认配额项"
// @Tags 配额API
// @Summary 查询部门配额
// @Success 200 {object} model.ResultModel{content=[]model.QuotaDefaultReoly} "返回数据"
// @Router /iam/quotas/department [get]
func QuotaDept(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	deptId := ctx.QueryParam("dept_id")
	regionId := ctx.Request().Header.Get(topx.REGION_ID)
	if regionId == "" {
		regionId = "RegionOne"
	}
	res, err := service.QuotaDept(rctx, deptId, regionId)
	if err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithContent(res))
}

// QuotaProject doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	dept_id		query	string true "组织id"
// @Param	project_id		query	string false "项目id"
// @Tags 配额API
// @Summary 查询项目配额
// @Success 200 {object} model.ResultModel{content=[]model.QuotaDefaultReoly} "返回数据"
// @Router /iam/quotas/project [get]
func QuotaProject(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	deptId := ctx.QueryParam("dept_id")
	projectId := ctx.QueryParam("project_id")
	regionId := ctx.Request().Header.Get(topx.REGION_ID)
	if regionId == "" {
		regionId = "RegionOne"
	}
	res, err := service.QuotaProject(rctx, deptId, projectId, regionId)
	if err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithContent(res))
}
