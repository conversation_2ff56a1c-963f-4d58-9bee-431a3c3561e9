package websvc

import (
	"code.mysugoncloud.com/hci/assist/logs"
	"code.mysugoncloud.com/hci/assist/topx"
	"code.mysugoncloud.com/hci/iam/internal/repo"
	"code.mysugoncloud.com/hci/iam/internal/service"
	"code.mysugoncloud.com/hci/iam/internal/util"
	"code.mysugoncloud.com/hci/iam/model"
	"github.com/labstack/echo/v4"
)

// DepartmentDetailById doc
// @Param	id		path	string true "部门id"
// @Tags 组织部门API
// @Summary 根据id获取部门详情
// @Success 200 {object} model.ResultModel{content=model.Department} "返回数据"
// @Router /iam/departments/{id} [get]
func DepartmentDetailById(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	id := ctx.Param("id")
	// 资源权限验证
	currUserId := ctx.Request().Header.Get(topx.USER_ID)
	if err := service.ResourceAuthDept(rctx, currUserId, id); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	dept, err := service.DepartmentDetailById(rctx, id)
	if err != nil {
		return topx.Error(ctx, topx.WithMsg("部门不存在"))
	}
	return topx.Succcess(ctx, topx.WithContent(dept))
}

// DepartmentAlls doc
// @Tags 组织部门API
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Summary 查询所有部门及各个部门子节点
// @Success 200 {object} model.ResultModel{content=model.Department} "返回数据"
// @Router /iam/departments/alls [get]
func DepartmentAlls(ctx echo.Context) error {
	userType := ctx.Request().Header.Get(topx.USER_TYPE)
	// 普通用户/审计管理员不能查看部门列表
	if userType == "" || userType == topx.TYPE_AUDIT {
		return topx.Succcess(ctx)
	}
	rctx := util.UnwarpCtx(ctx)
	depts, err := service.BuildDepartmentTree(rctx)
	if err != nil {
		logs.Ctx(rctx).Err(err).Error("BuildDepartmentTree error")
		return topx.Error(ctx, topx.WithMsg("查询部门失败"))
	}
	// 根用户/部门管理员，查询部门及以下部门
	if userType == topx.TYPE_MASTER || userType == topx.TYPE_DEPT_MASTER {
		userId := ctx.Request().Header.Get(topx.USER_ID)
		user := &model.User{Id: userId}
		err := repo.UserGet(rctx, user)
		if err != nil {
			return topx.Error(ctx, topx.WithMsg("用户不存在"))
		}
		return topx.Succcess(ctx, topx.WithContent([]*model.Department{service.DeptsByDeptId(depts, user.DeptId)}))
	} else {
		// 其他类型用户，admin/sub_admin/secrityadmin
		return topx.Succcess(ctx, topx.WithContent(depts))
	}
}

// DepartmentCreate doc
// @Param	body		body	model.CreateOrUpdateArgs true "请求参数"
// @Tags 组织部门API
// @Summary 创建子组织
// @Success 200 {object} model.ResultModel{content=model.CreateOrUpdateArgs} "返回数据"
// @Router /iam/departments [post]
func DepartmentCreate(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	param := &model.CreateOrUpdateArgs{}
	if err := ctx.Bind(param); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	currUserId := ctx.Request().Header.Get(topx.USER_ID)
	if err := service.ResourceAuthDept(rctx, currUserId, param.ParentId); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	if param.Name == "" {
		return topx.Error(ctx, topx.WithMsg("组织名称不能为空"))
	}
	if param.ParentId == "" {
		return topx.Error(ctx, topx.WithMsg("父级组织不能为空"))
	}
	department := &model.Department{Name: param.Name, Description: param.Description, ParentId: param.ParentId}
	_, err := service.DepartmentCreate(rctx, department)
	if err != nil {
		logs.Ctx(rctx).Err(err).Any("CreateOrUpdateArgs", param).Error("create dept error")
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithContent(param))
}

// DepartmentUpdate doc
// @Param	body		body	model.CreateOrUpdateArgs true "请求参数"
// @Param	id		path	string true "组织id"
// @Tags 组织部门API
// @Summary 修改组织
// @Success 200 {object} model.ResultModel{content=model.CreateOrUpdateArgs} "返回数据"
// @Router /iam/departments/{id} [put]
func DepartmentUpdate(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	param := &model.CreateOrUpdateArgs{}
	if err := ctx.Bind(param); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	id := ctx.Param("id")
	currUserId := ctx.Request().Header.Get(topx.USER_ID)
	if err := service.ResourceAuthDept(rctx, currUserId, id); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	_, err := service.DepartmentUpdate(rctx, &model.Department{Id: id, Name: param.Name, ParentId: param.ParentId, Description: param.Description})
	if err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithContent(param))
}

// DepartmentDelete doc
// @Param	id		path	string true "组织id"
// @Tags 组织部门API
// @Summary 删除组织
// @Success 200 {object} model.ResultModel "返回数据"
// @Router /iam/departments/{id} [delete]
func DepartmentDelete(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	id := ctx.Param("id")
	// 资源越权
	currUserId := ctx.Request().Header.Get(topx.USER_ID)
	if err := service.ResourceAuthDept(rctx, currUserId, id); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	err := service.DepartmentDelete(rctx, id)
	if err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx)
}

// DepartmentProjects doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	query		query	model.DeparmentProjectsArgs true "请求参数"
// @Param	id		path	string true "组织id"
// @Tags 组织部门API
// @Summary 组织项目列表
// @Success 200 {object} model.ResultModel{content=model.PageCL[model.Project]} "返回数据"
// @Router /iam/departments/{id}/projects [get]
func DepartmentProjects(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	filter := &model.DeparmentProjectsArgs{}
	if err := ctx.Bind(filter); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	filter.DeptId = ctx.Param("id")
	// 资源越权
	currUserId := ctx.Request().Header.Get(topx.USER_ID)
	if err := service.ResourceAuthDept(rctx, currUserId, filter.DeptId); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	mods, count, err := service.DepartmentProjects(rctx, filter)
	if err != nil {
		logs.Ctx(rctx).Err(err).Any("filter", filter).Debug("service.UserProjects")
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithPage(filter.GetPageNum(), filter.GetLimit(), count, mods))
}

// DepartmentQuotaUpdate doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	body		body	model.DepartmentQuotaUpdateArgs true "请求参数"
// @Param	id		path	string true "组织id"
// @Tags 组织部门API
// @Summary 修改一级组织配额
// @Success 200 {object} model.ResultModel "返回数据"
// @Router /iam/departments/quota/{id} [put]
func DepartmentQuotaUpdate(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	body := &model.DepartmentQuotaUpdateArgs{}
	if err := ctx.Bind(body); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	body.DeptId = ctx.Param("id")
	// 资源越权
	currUserId := ctx.Request().Header.Get(topx.USER_ID)
	if err := service.ResourceAuthDept(rctx, currUserId, body.DeptId); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	body.RegionId = ctx.Request().Header.Get(topx.REGION_ID)
	if err := service.DepartmentQuotaUpdate(rctx, body); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithMsg("修改组织配额成功"))
}

// DepartmentRoles doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	id		path	string true "组织id"
// @Param	query		query	model.RoleListByDeptArgs true "请求参数"
// @Tags 组织部门API
// @Summary 通过部门ID查询角色列表
// @Success 200 {object} model.ResultModel{content=[]model.IamRole} "返回数据"
// @Router /iam/departments/{id}/roles [get]
func DepartmentRoles(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	filter := &model.RoleListByDeptArgs{}
	if err := ctx.Bind(filter); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	filter.DeptId = ctx.Param("id")
	// 资源越权
	currUserId := ctx.Request().Header.Get(topx.USER_ID)
	if err := service.ResourceAuthDept(rctx, currUserId, filter.DeptId); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	mods, count, err := service.RoleListByDept(rctx, filter)
	if err != nil {
		logs.Ctx(rctx).Err(err).Any("filter", filter).Debug("service.RoleListByDept")
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithPage(filter.GetPageNum(), filter.GetLimit(), count, mods))
}

// DepartmentUsers doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	query		query	model.DeparmentUsersArgs true "请求参数"
// @Param 	id 	path 	string true "组织id"
// @Tags 组织部门API
// @Summary 通过部门ID查询用户列表
// @Success 200 {object} model.ResultModel{content=model.PageCL[model.User]} "返回数据"
// @Router /iam/departments/{id}/users [get]
func DepartmentUsers(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	filter := &model.DeparmentUsersArgs{}
	if err := ctx.Bind(filter); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	filter.DeptId = ctx.Param("id")
	filter.CurrUserId = ctx.Request().Header.Get(topx.USER_ID)
	// 资源越权
	if err := service.ResourceAuthDept(rctx, filter.CurrUserId, filter.DeptId); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	mods, count, err := service.DepartmentUsers(rctx, filter)
	if err != nil {
		logs.Ctx(rctx).Err(err).Any("filter", filter).Debug("service.GetUserPageByDeptId")
		return topx.Error(ctx, topx.WithMsg("查询用户列表失败"))
	}
	return topx.Succcess(ctx, topx.WithPage(filter.GetPageNum(), filter.GetLimit(), count, mods))
}
