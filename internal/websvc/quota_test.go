package websvc

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

func TestQuotaProject(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	req := httptest.NewRequest(http.MethodGet, "/iam/quotas/project?department_id=9ca381cafe5c49e5877bcae58ea85351", nil)
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)

	fmt.Println("------------------")
	fmt.Println(rec.Body.String())
}

func TestQuotaDept(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	req := httptest.NewRequest(http.MethodGet, "/iam/quotas/department?dept_id=0b7ea6aee45946c2a39571c467d53b49", nil)
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)

	fmt.Println("------------------")
	fmt.Println(rec.Body.String())
}

func TestQuotaProjectDefalut(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	req := httptest.NewRequest(http.MethodGet, "/iam/quotas/project?dept_id=1acba556fe984b11af99784b4fde0931", nil)
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)

	fmt.Println("------------------")
	fmt.Println(rec.Body.String())
}
