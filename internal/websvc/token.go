package websvc

import (
	"strconv"
	"strings"
	"time"

	"code.mysugoncloud.com/hci/assist/topx"
	"code.mysugoncloud.com/hci/iam/internal/service"
	"code.mysugoncloud.com/hci/iam/internal/util"
	"code.mysugoncloud.com/hci/iam/model"
	"github.com/labstack/echo/v4"
)

// CreateToken doc
// @Param	body		body	model.LoginUserArgs true "请求参数"
// @Tags 登录API
// @Summary 获取token
// @Success 200 {object} model.ResultModel{content=string} "返回数据"
// @Router /iam/oauth/token [post]
func CreateToken(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	loginUser := new(model.LoginUserArgs)
	ctx.Bind(loginUser)
	loginUser.IpAddr = ctx.RealIP()
	token, err := service.CreateToken(rctx, loginUser)
	if err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithContent(token))
}

// LoginErrCount doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	username		query	string true "用户名"
// @Tags 登录API
// @Summary 获取用户登录错误次数
// @Success 200 {object} model.ResultModel{content=int} "返回数据"
// @Router /iam/login/error [get]
func LoginErrCount(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	username := ctx.QueryParam("username")
	errCount, err := util.CacheGet(rctx, service.UNAME_ERROR_TIMES+username)
	if err != nil {
		return topx.Succcess(ctx, topx.WithContent(0))
	}
	count, _ := strconv.Atoi(errCount)
	return topx.Succcess(ctx, topx.WithContent(count))
}

// AuthCode doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Tags 登录API
// @Summary 获取验证码
// @Success 200 {object} model.ResultModel{content=string} "返回数据"
// @Router /iam/login/code [get]
func AuthCode(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	code := util.RandStringBytes(5)
	util.CacheAdd(rctx, code, "", time.Duration(60)*time.Second)
	return topx.Succcess(ctx, topx.WithContent(code))
}

// Logout doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Tags 登录API
// @Summary 登出
// @Success 200 {object} model.ResultModel{content=string} "返回数据"
// @Router /iam/login/out [delete]
func Logout(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	authorization := ctx.Request().Header.Get("Authorization")
	token := strings.TrimPrefix(authorization, "Bearer ")
	if claims, err := util.ParseToken(token); err == nil {
		util.CacheDrop(rctx, claims.UserName+":"+token)
	}
	return topx.Succcess(ctx, topx.WithMsg("退出成功"))
}
