package websvc

import (
	"code.mysugoncloud.com/hci/assist/topx"
	"code.mysugoncloud.com/hci/iam/internal/service"
	"code.mysugoncloud.com/hci/iam/internal/util"
	"github.com/labstack/echo/v4"
)

// GetPulicKey doc
// @Tags 密钥API
// @Summary 获取公钥
// @Success 200 {object} model.ResultModel{content=string} "返回数据"
// @Router /iam/publickey [get]
func GetPulicKey(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	publicKey := service.GetPulicKey(rctx)
	return topx.Succcess(ctx, topx.WithContent(publicKey))
}
