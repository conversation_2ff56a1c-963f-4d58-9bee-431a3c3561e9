package websvc

import (
	"strconv"

	"code.mysugoncloud.com/hci/assist/logs"
	"code.mysugoncloud.com/hci/assist/topx"
	"code.mysugoncloud.com/hci/iam/internal/repo"
	"code.mysugoncloud.com/hci/iam/internal/service"
	"code.mysugoncloud.com/hci/iam/internal/util"
	"code.mysugoncloud.com/hci/iam/model"
	"github.com/labstack/echo/v4"
)

// UserListPage doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	query		query	model.UserListArgs true "请求参数"
// @Tags 用户API
// @Summary 查询所有用户列表
// @Success 200 {object} model.ResultModel{content=model.PageCL[model.User]} "返回数据"
// @Router /iam/users/all [get]
func UserListPage(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	filter := &model.UserListArgs{}
	if err := ctx.Bind(filter); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	filter.CurrUserId = ctx.Request().Header.Get(topx.USER_ID)
	mods, count, err := service.UserListPage(rctx, filter)
	if err != nil {
		logs.Ctx(rctx).Err(err).Any("filter", filter).Debug("service.GetUserPageByDeptId")
		return topx.Error(ctx, topx.WithMsg("查询用户列表失败"))
	}
	return topx.Succcess(ctx, topx.WithPage(filter.GetPageNum(), filter.GetLimit(), count, mods))
}

// UserCreateSub doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	body		body	model.UserCreateArgs true "请求参数"
// @Tags 用户API
// @Summary 创建用户
// @Success 200 {object} model.ResultModel{content=string} "返回数据"
// @Router /iam/users/sub [post]
func UserCreateSub(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	body := &model.UserCreateArgs{}
	if err := ctx.Bind(body); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	id, err := service.UserSubCreate(rctx, body)
	if err != nil {
		logs.Ctx(rctx).Err(err).Any("body", body).Debug("service.UserSubCreate")
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithContent(id))
}

// UserRegister doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	body		body	model.UserCreateArgs true "请求参数"
// @Tags 用户API
// @Summary 注册用户
// @Success 200 {object} model.ResultModel{content=string} "返回数据"
// @Router /iam/users [post]
func UserRegister(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	userId := ctx.Request().Header.Get(topx.USER_ID)
	if userId != "" && repo.UserExist(rctx, &model.User{Id: userId}) {
		user := &model.User{Id: userId}
		repo.UserGet(rctx, user)
		if topx.TYPE_SECURITY != user.Type && topx.TYPE_ADMIN != user.Type && topx.TYPE_SYS_ADMIN != user.Type {
			return topx.Error(ctx, topx.WithMsg("不支持自主注册账号"))
		}
	} else {
		globalsetting := &model.Globalsettings{PolicyName: "register_enable", PolicyType: "user"}
		repo.GlobalsettingGet(rctx, globalsetting)
		if globalsetting.PolicyDocument == "false" {
			return topx.Error(ctx, topx.WithMsg("不支持自主注册账号"))
		}
	}
	body := &model.UserCreateArgs{}
	if err := ctx.Bind(body); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	body.RegionId = ctx.Request().Header.Get(topx.REGION_ID)
	if body.RegionId == "" {
		body.RegionId = "RegionOne"
	}
	id, err := service.UserRegister(rctx, body)
	if err != nil {
		logs.Ctx(rctx).Err(err).Any("body", body).Debug("service.UserRegister")
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithContent(id))
}

// UserDetail doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	user_id		path	string true "用户id"
// @Tags 用户API
// @Summary 用户详情
// @Success 200 {object} model.ResultModel{content=model.User} "返回数据"
// @Router /iam/users/{user_id} [get]
func UserDetail(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	userId := ctx.Param("user_id")
	user, err := service.UserDetail(rctx, userId, GetToken(ctx))
	if err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithContent(user))
}

// UserProjects doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	query		query	model.UserProjectsArgs true "请求参数"
// @Param	user_id		path	string true "用户id"
// @Tags 用户API
// @Summary 用户所拥有的项目列表
// @Success 200 {object} model.ResultModel{content=model.PageCL[model.Project]} "返回数据"
// @Router /iam/users/{user_id}/projects [get]
func UserProjects(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	filterTemp := &model.UserProjectsArgs{}
	if err := ctx.Bind(filterTemp); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	filter := &model.ProjectPageAllArgs{
		List:        filterTemp.List,
		ProjectName: filterTemp.ProjectName,
		CurrUserId:  ctx.Param("user_id"),
	}
	mods, count, err := service.ProjectPageAll(rctx, filter)
	if err != nil {
		logs.Ctx(rctx).Err(err).Any("filter", filterTemp).Debug("service.UserProjects")
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithPage(filterTemp.GetPageNum(), filterTemp.GetLimit(), count, mods))
}

// UserUpdate doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	body		body	model.UserUpdateArgs true "请求参数"
// @Tags 用户API
// @Summary 修改用户
// @Success 200 {object} model.ResultModel "返回数据"
// @Router /iam/users [put]
func UserUpdate(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	args := &model.UserUpdateArgs{}
	if err := ctx.Bind(args); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	err := service.UserUpdate(rctx, args)
	if err != nil {
		logs.Ctx(rctx).Err(err).Any("args", args).Debug("service.UserUpdate")
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx)
}

// UserRoles doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	query		query	model.UserRoleListArgs true "请求参数"
// @Param	user_id		path	string true "用户id"
// @Tags 用户API
// @Summary 用户已绑定的角色列表
// @Success 200 {object} model.ResultModel{content=model.PageCL[model.IamRole]} "返回数据"
// @Router /iam/users/{user_id}/roles [get]
func UserRoles(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	filter := &model.UserRoleListArgs{}
	if err := ctx.Bind(filter); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	filter.UserId = ctx.Param("user_id")
	mods, count, err := service.RolePageByUserId(rctx, filter)
	if err != nil {
		logs.Ctx(rctx).Err(err).Any("filter", filter).Debug("service.UserRoles")
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithPage(filter.GetPageNum(), filter.GetLimit(), count, mods))
}

// UserBindRoles doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	body		body	model.UserBindRolesArgs true "请求参数"
// @Param	user_id		path	string true "用户id"
// @Tags 用户API
// @Summary 用户批量绑定部门角色
// @Success 200 {object} model.ResultModel "返回数据"
// @Router /iam/users/{user_id}/bind-roles [post]
func UserBindRoles(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	body := &model.UserBindRolesArgs{}
	if err := ctx.Bind(body); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	body.UserId = ctx.Param("user_id")
	if err := service.UserBindRoles(rctx, body); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	var msg string
	if body.ProjectId == "" {
		msg = "用户绑定/解绑部门角色成功"
	} else {
		msg = "用户绑定/解绑部门角色、项目成功"
	}
	return topx.Succcess(ctx, topx.WithMsg(msg))
}

// UserUnBindRoles doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	body		body	model.UserUnBindRolesArgs true "请求参数"
// @Param	user_id		path	string true "用户id"
// @Tags 用户API
// @Summary 用户批量解绑角色
// @Success 200 {object} model.ResultModel "返回数据"
// @Router /iam/users/{user_id}/unbind-roles [post]
func UserUnBindRoles(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	body := &model.UserUnBindRolesArgs{}
	if err := ctx.Bind(body); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	body.UserId = ctx.Param("user_id")
	if err := service.UserUnBindRoles(rctx, body); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithMsg("用户批量解绑角色成功"))
}

// UserBindProjects doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	body		body	model.UserBindProjectsArgs true "请求参数"
// @Param	user_id		path	string true "用户id"
// @Tags 用户API
// @Summary 用户绑定项目
// @Success 200 {object} model.ResultModel "返回数据"
// @Router /iam/users/{user_id}/bind-projects [post]
func UserBindProjects(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	filter := &model.UserBindProjectsArgs{}
	if err := ctx.Bind(filter); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	filter.UserId = ctx.Param("user_id")
	if err := service.UserBindProjects(rctx, filter); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithMsg("用户绑定/解绑部门项目成功"))
}

// UserUnBindProjects doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	body		body	model.UserBindProjectsArgs true "请求参数"
// @Param	user_id		path	string true "用户id"
// @Tags 用户API
// @Summary 用户批量解绑项目
// @Success 200 {object} model.ResultModel "返回数据"
// @Router /iam/users/{user_id}/unbind-projects [post]
func UserUnBindProjects(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	filter := &model.UserBindProjectsArgs{}
	if err := ctx.Bind(filter); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	filter.UserId = ctx.Param("user_id")
	if err := service.UserUnBindProjects(rctx, filter); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithMsg("用户绑定/解绑部门项目成功"))
}

// UserResetPassword doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	user_id		path	string true "用户id"
// @Param	body		body	model.UserResetPasswordArgs true "请求参数"
// @Tags 用户API
// @Summary 重置密码
// @Success 200 {object} model.ResultModel "返回数据"
// @Router /iam/users/{user_id}/password [put]
func UserResetPassword(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	body := &model.UserResetPasswordArgs{}
	if err := ctx.Bind(body); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	body.UserId = ctx.Param("user_id")
	body.CurrUserId = ctx.Request().Header.Get(topx.USER_ID)
	if err := service.UserResetPassword(rctx, body); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithMsg("重置密码成功"))
}

// UserExpired doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	user_id		path	string true "用户id"
// @Param	expired		query	string true "过期时间(yyyy-MM-dd)"
// @Tags 用户API
// @Summary 设置用户过期时间
// @Success 200 {object} model.ResultModel "返回数据"
// @Router /iam/users/{user_id}/expired [post]
func UserExpired(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	userId := ctx.Param("user_id")
	expiredStr := ctx.QueryParam("expired")
	expired, err := util.TimeStrToTime(expiredStr)
	if err != nil {
		return topx.Error(ctx, topx.WithMsg("时间格式错误"))
	}
	if err := service.UserExpired(rctx, userId, expired); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithMsg("设置用户过期时间成功"))
}

// UserUpdateStatus doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	user_id		path	string true "用户id"
// @Param	status		path	bool true "用户状态"
// @Tags 用户API
// @Summary 设置用户状态
// @Success 200 {object} model.ResultModel "返回数据"
// @Router /iam/users/user_id/{user_id}/status/{status} [put]
func UserUpdateStatus(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	userId := ctx.Param("user_id")
	status, _ := strconv.ParseBool(ctx.Param("status"))
	if err := service.UserUpdateStatus(rctx, userId, status); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithMsg("修改用户状态成功"))
}

// UserAccess doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	user_id		path	string true "用户id"
// @Tags 用户API
// @Summary 获取用户访问权限
// @Success 200 {object} model.ResultModel{content=model.UserAccess} "返回数据"
// @Router /iam/users/{user_id}/access [get]
func UserAccess(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	userId := ctx.Param("user_id")
	userAccess, err := service.UserAccess(rctx, userId)
	if err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithContent(userAccess))
}

// UserAccessSet doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	user_id		path	string true "用户id"
// @Param	body		body	model.UserAccessSetArgs true "请求参数"
// @Tags 用户API
// @Summary 设置用户访问权限
// @Success 200 {object} model.ResultModel "返回数据"
// @Router /iam/users/{user_id}/set-access [post]
func UserAccessSet(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	userId := ctx.Param("user_id")
	body := &model.UserAccessSetArgs{}
	if err := ctx.Bind(body); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	userAccess := &model.UserAccess{UserId: userId, Ip: body.Ip, LimitFlag: body.LimitFlag, LimitTime: body.LimitTime}
	userAccess.StartDate, _ = util.TimeStrToTime(body.StartDate)
	userAccess.EndDate, _ = util.TimeStrToTime(body.EndDate)
	if err := service.UserAccessSet(rctx, userAccess); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithMsg("设置用户访问权限成功"))
}

// UserDelete doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	user_id		path	string true "用户id"
// @Tags 用户API
// @Summary 删除用户
// @Success 200 {object} model.ResultModel "返回数据"
// @Router /iam/users/{user_id} [delete]
func UserDelete(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	userId := ctx.Param("user_id")
	if err := service.UserDelete(rctx, userId); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithContent("删除用户成功"))
}

// UserUnlock doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	user_id		path	string true "用户id"
// @Tags 用户API
// @Summary 解锁用户
// @Success 200 {object} model.ResultModel "返回数据"
// @Router /iam/users/unlock/{user_id} [put]
func UserUnlock(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	userId := ctx.Param("user_id")
	if err := service.UserUnlock(rctx, userId); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithContent("用户解锁成功"))
}

// UserTypeList doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Tags 用户API
// @Summary 获取用户类型标签
// @Success 200 {object} model.ResultModel{content=[]model.UserTypeReoly} "返回数据"
// @Router /iam/users/type [get]
func UserTypeList(ctx echo.Context) error {
	userTypes := make([]*model.UserTypeReoly, 0, 6)
	userTypes = append(userTypes, &model.UserTypeReoly{UserType: topx.TYPE_SYS_ADMIN, UserTypeDesc: "系统管理员"})
	userTypes = append(userTypes, &model.UserTypeReoly{UserType: topx.TYPE_AUDIT, UserTypeDesc: "审计管理员"})
	userTypes = append(userTypes, &model.UserTypeReoly{UserType: topx.TYPE_SECURITY, UserTypeDesc: "安全管理员"})
	userTypes = append(userTypes, &model.UserTypeReoly{UserType: topx.TYPE_MASTER, UserTypeDesc: "一级组织管理员"})
	userTypes = append(userTypes, &model.UserTypeReoly{UserType: topx.TYPE_DEPT_MASTER, UserTypeDesc: "组织管理员"})
	userTypes = append(userTypes, &model.UserTypeReoly{UserType: topx.TYPE_NORMAL, UserTypeDesc: "普通用户"})
	return topx.Succcess(ctx, topx.WithContent(userTypes))
}
