package websvc

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"code.mysugoncloud.com/hci/iam/model"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

func TestPolicyTree(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	req := httptest.NewRequest(http.MethodGet, "/iam/ram/policy/tree?service_id=ac988dbe28ec428893304640df20087f", nil)
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	t.Log("rec=" + rec.Body.String())
}

func TestPolicyCreate(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)
	body := &model.RamPolicy{
		PolicyName:     "test",
		PolicyDocument: "test",
		ServiceId:      "ac988dbe28ec428893304640df20087f",
	}
	jsonData, _ := json.Marshal(body)
	req := httptest.NewRequest(http.MethodPost, "/iam/ram/policy", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	t.Log("rec=" + rec.Body.String())
}

func TestPolicyByName(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	req := httptest.NewRequest(http.MethodGet, "/iam/ram/policy/name?service_id=ac988dbe28ec428893304640df20087f&name=密级管理", nil)
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	t.Log("rec=" + rec.Body.String())
}

func TestPolicyById(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	req := httptest.NewRequest(http.MethodGet, "/iam/ram/policy/0adf0a5ffc0bd9413858ebe382880822", nil)
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	t.Log("rec=" + rec.Body.String())
}

func TestPolicyBind(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	body := &model.PolicyBindArgs{
		RoleId: "23546c7ab11e11ecbde1083a88902c92",
		ServiceId: "ac988dbe28ec428893304640df20087f",
		PolicyIds: []string{"880d73ee0a2390331cee75bec070e9ca", "2f29666b441d9f10b193ae5db1687345"},
	}
	jsonData, _ := json.Marshal(body)
	req := httptest.NewRequest(http.MethodPut, "/iam/ram/policy/bind-policy", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	t.Log("rec=" + rec.Body.String())
}
