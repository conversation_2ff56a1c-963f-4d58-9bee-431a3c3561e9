package websvc

import (
	"code.mysugoncloud.com/hci/assist/logs"
	"code.mysugoncloud.com/hci/assist/topx"
	"code.mysugoncloud.com/hci/iam/internal/service"
	"code.mysugoncloud.com/hci/iam/internal/util"
	"code.mysugoncloud.com/hci/iam/model"
	"github.com/labstack/echo/v4"
)

// RoleListByUserId doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	user_id		path	string true "用户id"
// @Param	project_id		query	string false "项目id"
// @Tags 角色API
// @Summary 通过用户Id查询角色列表
// @Success 200 {object} model.ResultModel{content=[]model.IamRole} "返回数据"
// @Router /iam/roles/user/{user_id} [get]
func RoleListByUserId(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	userId := ctx.Param("user_id")
	projectId := ctx.QueryParam("project_id")
	roles, err := service.RoleListByUserId(rctx, userId, projectId)
	if err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithContent(roles))
}

// RoleCreate doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	body		body	model.RoleCreateArgs true "请求参数"
// @Tags 角色API
// @Summary 添加角色
// @Success 200 {object} model.ResultModel "返回数据"
// @Router /iam/roles [post]
func RoleCreate(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	body := &model.RoleCreateArgs{}
	if err := ctx.Bind(body); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	if err := service.RoleCreate(rctx, body); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithMsg("添加角色成功"))
}

// RoleUpdate doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	body		body	model.RoleUpdateArgs true "请求参数"
// @Tags 角色API
// @Summary 更新角色
// @Success 200 {object} model.ResultModel "返回数据"
// @Router /iam/roles [put]
func RoleUpdate(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	body := &model.RoleUpdateArgs{}
	if err := ctx.Bind(body); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	if err := service.RoleUpdate(rctx, body); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithMsg("更新角色成功"))
}

// RoleDelete doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	role_id		path	string true "角色id"
// @Tags 角色API
// @Summary 删除角色
// @Success 200 {object} model.ResultModel "返回数据"
// @Router /iam/roles/{role_id} [delete]
func RoleDelete(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	roleId := ctx.Param("role_id")
	if err := service.RoleDelete(rctx, roleId); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithMsg("删除角色成功"))
}

// RoleDetail doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	role_id		path	string true "角色id"
// @Tags 角色API
// @Summary 角色详情
// @Success 200 {object} model.ResultModel{content=model.IamRole} "返回数据"
// @Router /iam/roles/{role_id} [get]
func RoleDetail(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	roleId := ctx.Param("role_id")
	role, err := service.RoleDetail(rctx, roleId)
	if err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithContent(role))
}

// RolePageAll doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	query		query	model.RolePageAllArgs true "请求参数"
// @Tags 角色API
// @Summary 查询所有角色
// @Success 200 {object} model.ResultModel{content=model.PageCL[model.IamRole]} "返回数据"
// @Router /iam/roles/all [get]
func RolePageAll(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	filter := &model.RolePageAllArgs{}
	if err := ctx.Bind(filter); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	filter.CurrUserId = ctx.Request().Header.Get(topx.USER_ID)
	mods, count, err := service.RolePageAll(rctx, filter)
	if err != nil {
		logs.Ctx(rctx).Err(err).Any("filter", filter).Debug("service.RolePageAll")
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithPage(filter.GetPageNum(), filter.GetLimit(), count, mods))
}

// RoleInnerList doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	query		query	model.RolesInnerArgs true "请求参数"
// @Tags 角色API
// @Summary 查询内置角色
// @Success 200 {object} model.ResultModel{content=model.PageCL[model.IamRole]} "返回数据"
// @Router /iam/roles/inner-role-list [get]
func RoleInnerList(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	filter := &model.RolesInnerArgs{}
	if err := ctx.Bind(filter); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	filter.CurrUserId = ctx.Request().Header.Get(topx.USER_ID)
	mods, count, err := service.RoleInnerList(rctx, filter)
	if err != nil {
		logs.Ctx(rctx).Err(err).Any("filter", filter).Debug("service RoleInnerList error")
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithPage(filter.GetPageNum(), filter.GetLimit(), count, mods))
}
