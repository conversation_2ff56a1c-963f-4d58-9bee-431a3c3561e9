package websvc

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"code.mysugoncloud.com/hci/iam/model"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

func TestRoleCreate(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	args := &model.RoleCreateArgs{
		DeptId: "9ca381cafe5c49e5877bcae58ea85351",
		Name:   "test12",
	}

	jsonData, _ := json.Marshal(args)
	req := httptest.NewRequest(http.MethodPost, "/iam/roles", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	t.Log("rec=" + rec.Body.String())
}

func TestRoleUpda(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	args := &model.RoleUpdateArgs{
		RoleCreateArgs: model.RoleCreateArgs{
			DeptId:      "9ca381cafe5c49e5877bcae58ea85351",
			Name:        "test13",
			Description: "test",
		},
		Id: "86f0c3d8-82cc-4704-b860-74042e67293e",
	}

	jsonData, _ := json.Marshal(args)
	req := httptest.NewRequest(http.MethodPut, "/iam/roles", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	t.Log("rec=" + rec.Body.String())
}

func TestRoleDelete(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	req := httptest.NewRequest(http.MethodDelete, "/iam/roles/86f0c3d8-82cc-4704-b860-74042e67293e", nil)
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	t.Log("rec=" + rec.Body.String())
}

func TestRoleDetail(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)
	req := httptest.NewRequest(http.MethodGet, "/iam/roles/b3a3dc1c-4ee2-4dd3-9d87-977078d26d5b", nil)
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	t.Log("rec=" + rec.Body.String())
}

func TestRolePageAll(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	req := httptest.NewRequest(http.MethodGet, "/iam/roles/all?name=公用&page_num=1&page_size=10", nil)
	req.Header.Set("Content-Type", "application/json")
	//initToken(req)
	req.Header.Set("authorization", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhbGlhcyI6InFxd2VxdzEyMyIsInVzZXJJZCI6IjUwZDJiN2JlLTRhM2MtNGYxMS1hNmU1LWMwNzdmNDcyOGMwZCIsInVzZXJuYW1lIjoicXF3ZXF3MTIzIiwicHJvamVjdElkIjoiZTA4Y2ZmMjctOTRlMS00N2YyLWExYjQtZDgwOTE2Njc5NjAxIiwidXNlclR5cGUiOiJvcmdhZG1pbiIsImRlcHRfaWQiOiI2MGVlMTA4NS02NzZiLTQwMWEtOTU0ZS1hMjY1MmEwMmYzMWMiLCJpc3MiOiJzdWdvbi1jbG91ZC1pYW0iLCJpYXQiOjE3MjEyNzIyOTB9.CCMaJegaArLxM24GTy3NZYcvWrWL8-8K2iTTGBTN89A")
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	fmt.Println("------------------")
	fmt.Println(rec.Body.String())
}

func TestRoleInnerList(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	req := httptest.NewRequest(http.MethodGet, "/iam/roles/inner-role-list?name=&page_num=1&page_size=10", nil)
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	//req.Header.Set("authorization", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhbGlhcyI6InFxd2VxdzEyMyIsInVzZXJJZCI6IjUwZDJiN2JlLTRhM2MtNGYxMS1hNmU1LWMwNzdmNDcyOGMwZCIsInVzZXJuYW1lIjoicXF3ZXF3MTIzIiwicHJvamVjdElkIjoiZTA4Y2ZmMjctOTRlMS00N2YyLWExYjQtZDgwOTE2Njc5NjAxIiwidXNlclR5cGUiOiJvcmdhZG1pbiIsImRlcHRfaWQiOiI2MGVlMTA4NS02NzZiLTQwMWEtOTU0ZS1hMjY1MmEwMmYzMWMiLCJpc3MiOiJzdWdvbi1jbG91ZC1pYW0iLCJpYXQiOjE3MjEyNzIyOTB9.CCMaJegaArLxM24GTy3NZYcvWrWL8-8K2iTTGBTN89A")
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	fmt.Println("------------------")
	fmt.Println(rec.Body.String())
}
