package websvc

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"
	"time"

	"code.mysugoncloud.com/hci/assist/config"
	"code.mysugoncloud.com/hci/assist/logs"
	"code.mysugoncloud.com/hci/assist/rndm"
	"code.mysugoncloud.com/hci/iam/internal/repo"
	"code.mysugoncloud.com/hci/iam/internal/service"
	"code.mysugoncloud.com/hci/iam/model"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

func testInit(t *testing.T) error {
	logs.SetLevel(logs.LDEBUG)
	logs.SetCons(true)
	loc, _ := time.LoadLocation("Asia/Shanghai")
	fmt.Println("loc", loc)
	if loc != nil {
		time.Local = loc
	}
	os.Args = append(os.Args, "-log-path=", "-config=../../conf.toml", "-show-sql=true")
	err := config.PreInitMgmt(context.TODO())
	if err != nil {
		t.Fatal(err)
	}
	config.InitMgmt(context.TODO(), nil, nil)
	return repo.Init(context.Background(), "root:database_sugon@tcp(172.22.1.57:3306)/iam_hci?charset=utf8mb4&parseTime=true&loc=Local")
}

func TestEcho(t *testing.T) {
	testInit(t)
	e := echo.New()
	Init(e)
	err := e.Start(":41000")
	t.Log(err)

}

func TestCreateToken(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)
	args := model.LoginUserArgs{Username: "hhhhhhh12", Password: "Sugon123"}
	jsonData, _ := json.Marshal(args)
	req := httptest.NewRequest(http.MethodPost, "/iam/oauth/token", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	fmt.Println("------------------")
	fmt.Println(rec.Body.String())
}

func initDeptMasterToken(req *http.Request) {
	username := "hhhhhhh12"
	tokens, _ := repo.CacheKeyPrefixMatch(context.Background(), &username)
	var token string
	if len(tokens) > 0 {
		token = strings.Replace(tokens[0].Key, username+":", "Bearer ", 1)
	} else {
		args := &model.LoginUserArgs{Username: "hhhhhhh12", Password: "Sugon123"}
		token, _ = service.CreateToken(context.TODO(), args)
	}
	req.Header.Set("authorization", token)
}

func initToken(req *http.Request) {
	username := "admin"
	tokens, _ := repo.CacheKeyPrefixMatch(context.Background(), &username)
	var token string
	if len(tokens) > 0 {
		token = strings.Replace(tokens[0].Key, username+":", "Bearer ", 1)
	} else {
		args := &model.LoginUserArgs{Username: "admin", Password: "keystone_sugon"}
		token, _ = service.CreateToken(context.TODO(), args)
	}
	req.Header.Set("authorization", token)
}

// -----user test------
// TestUserBindProjects 用户绑定项目
func TestUserBindProjects(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)
	// args := model.UserBindProjectsArgs{}
	args := model.UserBindProjectsArgs{ProjectIds: []string{"bada97b1fe8f48fe8b23e5345fe3230d", "a27464c148894fad938a99e0f5d8d4f6"}}
	jsonData, _ := json.Marshal(args)
	req := httptest.NewRequest(http.MethodPost, "/iam/users/cc293a90f6524e15b6a675ab0787a522/bind-projects", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhbGlhcyI6IiIsInVzZXJJZCI6IjBkMmJiYjAxOGU4YjQ0Yjk4NWExNjk2NDczNzlmNDEzIiwidXNlcm5hbWUiOiJhZG1pbiIsInByb2plY3RJZCI6ImVkY2MyNDA1OWM4YjQ2OTY5YTM3MjYwZGM5YWIyMzI5IiwidXNlclR5cGUiOiJhZG1pbiIsImRlcHRfaWQiOiIyIiwiaXNzIjoic3Vnb24tY2xvdWQtaWFtIiwiaWF0IjoxNzE5ODg1MzEyfQ.P4ezvyDtM8Z54PX-vHeoXG9D6EB6bodnKmvkq-Yj47c")
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	t.Log("rec=" + rec.Body.String())
}

// TestUserResetPassword 重置密码测试
// func TestUserResetPassword(t *testing.T) {
// 	testInit(t)
// 	e := echo.New()
// 	e.Use(middleware.Logger())
// 	Init(e)
// 	// 创建一个新的multipart writer
// 	body := &bytes.Buffer{}
// 	mw := multipart.NewWriter(body)

// 	// 写入纯字符串参数
// 	_ = mw.WriteField("password", "value1")
// 	_ = mw.WriteField("publickey", "value1")

// 	// 关闭multipart writer
// 	_ = mw.Close()

// 	req := httptest.NewRequest(http.MethodPut, "/iam/users/cc293a90f6524e15b6a675ab0787a522/password", body)
// 	// 设置Content-Type和boundary
// 	contentType := mw.FormDataContentType()
// 	req.Header.Set("Content-Type", contentType)
// 	req.Header.Set("authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhbGlhcyI6IiIsInVzZXJJZCI6IjBkMmJiYjAxOGU4YjQ0Yjk4NWExNjk2NDczNzlmNDEzIiwidXNlcm5hbWUiOiJhZG1pbiIsInByb2plY3RJZCI6ImVkY2MyNDA1OWM4YjQ2OTY5YTM3MjYwZGM5YWIyMzI5IiwidXNlclR5cGUiOiJhZG1pbiIsImRlcHRfaWQiOiIyIiwiaXNzIjoic3Vnb24tY2xvdWQtaWFtIiwiaWF0IjoxNzE5ODg1MzEyfQ.P4ezvyDtM8Z54PX-vHeoXG9D6EB6bodnKmvkq-Yj47c")
// 	rec := httptest.NewRecorder()
// 	e.ServeHTTP(rec, req)
// 	t.Log("rec=" + rec.Body.String())
// }

func TestUserResetPassword(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)
	args := &model.UserResetPasswordArgs{
		Password:  "K0H9/PUtcEexsFU2FDqy4EmcwDeCbcUxu5qeiQVr7iOfgb38cbg/eLIWRFtcQEtsh9Pbd7hxbBKWiKVvkp2uVwsfXRVQIJpCA5PjVncoepZm7T2dioSYw/mGovegyziB8q/Oi06t+K4LmT7A9V9KT5U4ISGQSJZmr3tt1aMUS5w=",
		Publickey: "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDYAZvobQCIW5D+er0YzGrzNaXVXVb5dVbPgjCoAmW0eThBgF5DS3ST69W21qBd8VqYoXfmCv2tWtwFBc0/jZjDWvfXtNcltTOlO5ebclWou/PkR4hMrsgycbUYsekg6F3oC1qncmF1sWtV7s61OhO5o9PqWeWjWjlpyA/6v5ANwwIDAQAB",
	}
	jsonData, _ := json.Marshal(args)
	req := httptest.NewRequest(http.MethodPut, "/iam/users/2476485042ef40808f4b2a478f197538/password", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	fmt.Println("------------------")
	fmt.Println(rec.Body.String())
}

// TestUserExpired 测试设置用户过期时间
func TestUserExpired(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	req := httptest.NewRequest(http.MethodPost, "/iam/users/cc293a90f6524e15b6a675ab0787a522/expired?expired=2024-07-06", nil)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhbGlhcyI6IiIsInVzZXJJZCI6IjBkMmJiYjAxOGU4YjQ0Yjk4NWExNjk2NDczNzlmNDEzIiwidXNlcm5hbWUiOiJhZG1pbiIsInByb2plY3RJZCI6ImVkY2MyNDA1OWM4YjQ2OTY5YTM3MjYwZGM5YWIyMzI5IiwidXNlclR5cGUiOiJhZG1pbiIsImRlcHRfaWQiOiIyIiwiaXNzIjoic3Vnb24tY2xvdWQtaWFtIiwiaWF0IjoxNzE5ODg1MzEyfQ.P4ezvyDtM8Z54PX-vHeoXG9D6EB6bodnKmvkq-Yj47c")
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	t.Log("rec=" + rec.Body.String())
}

// TestUserUpdateStatus 测试修改用户状态
func TestUserUpdateStatus(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	req := httptest.NewRequest(http.MethodPut, "/iam/users/user_id/cc293a90f6524e15b6a675ab0787a522/status/true", nil)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhbGlhcyI6IiIsInVzZXJJZCI6IjBkMmJiYjAxOGU4YjQ0Yjk4NWExNjk2NDczNzlmNDEzIiwidXNlcm5hbWUiOiJhZG1pbiIsInByb2plY3RJZCI6ImVkY2MyNDA1OWM4YjQ2OTY5YTM3MjYwZGM5YWIyMzI5IiwidXNlclR5cGUiOiJhZG1pbiIsImRlcHRfaWQiOiIyIiwiaXNzIjoic3Vnb24tY2xvdWQtaWFtIiwiaWF0IjoxNzE5ODg1MzEyfQ.P4ezvyDtM8Z54PX-vHeoXG9D6EB6bodnKmvkq-Yj47c")
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	t.Log("rec=" + rec.Body.String())
}

func TestUserAccess(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)
	req := httptest.NewRequest(http.MethodGet, "/iam/users/cc293a90f6524e15b6a675ab0787a522/access", nil)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhbGlhcyI6IiIsInVzZXJJZCI6IjBkMmJiYjAxOGU4YjQ0Yjk4NWExNjk2NDczNzlmNDEzIiwidXNlcm5hbWUiOiJhZG1pbiIsInByb2plY3RJZCI6ImVkY2MyNDA1OWM4YjQ2OTY5YTM3MjYwZGM5YWIyMzI5IiwidXNlclR5cGUiOiJhZG1pbiIsImRlcHRfaWQiOiIyIiwiaXNzIjoic3Vnb24tY2xvdWQtaWFtIiwiaWF0IjoxNzE5ODg1MzEyfQ.P4ezvyDtM8Z54PX-vHeoXG9D6EB6bodnKmvkq-Yj47c")
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	t.Log("rec=" + rec.Body.String())
}

func TestUserAccessSet(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)
	args := model.UserAccessSetArgs{
		Ip: "***********",
		//EndDate: "2024-07-09",
		//StartDate: "2024-07-04",
		LimitFlag: true,
		LimitTime: "{\"MONDAY\":\"3,4,5,6\",\"TUESDAY\":\"\",\"WEDNESDAY\":\"\",\"THURSDAY\":\"\",\"FRIDAY\":\"\",\"SATURDAY\":\"\",\"SUNDAY\":\"\"}",
	}
	jsonData, _ := json.Marshal(args)
	req := httptest.NewRequest(http.MethodPost, "/iam/users/cc293a90f6524e15b6a675ab0787a522/set-access", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhbGlhcyI6IiIsInVzZXJJZCI6IjBkMmJiYjAxOGU4YjQ0Yjk4NWExNjk2NDczNzlmNDEzIiwidXNlcm5hbWUiOiJhZG1pbiIsInByb2plY3RJZCI6ImVkY2MyNDA1OWM4YjQ2OTY5YTM3MjYwZGM5YWIyMzI5IiwidXNlclR5cGUiOiJhZG1pbiIsImRlcHRfaWQiOiIyIiwiaXNzIjoic3Vnb24tY2xvdWQtaWFtIiwiaWF0IjoxNzE5ODg1MzEyfQ.P4ezvyDtM8Z54PX-vHeoXG9D6EB6bodnKmvkq-Yj47c")
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	t.Log("rec=" + rec.Body.String())
}

func TestUserDelete(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	req := httptest.NewRequest(http.MethodDelete, "/iam/users/b3d2f361-6e96-4a6b-9702-0784e7168134", nil)
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	t.Log("rec=" + rec.Body.String())

}

func TestUserPageByProjectId(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	req := httptest.NewRequest(http.MethodGet, "/iam/users/by-project?project_id=a27464c148894fad938a99e0f5d8d4f6&page_num=1&page_size=10", nil)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhbGlhcyI6IiIsInVzZXJJZCI6IjBkMmJiYjAxOGU4YjQ0Yjk4NWExNjk2NDczNzlmNDEzIiwidXNlcm5hbWUiOiJhZG1pbiIsInByb2plY3RJZCI6ImVkY2MyNDA1OWM4YjQ2OTY5YTM3MjYwZGM5YWIyMzI5IiwidXNlclR5cGUiOiJhZG1pbiIsImRlcHRfaWQiOiIyIiwiaXNzIjoic3Vnb24tY2xvdWQtaWFtIiwiaWF0IjoxNzE5ODg1MzEyfQ.P4ezvyDtM8Z54PX-vHeoXG9D6EB6bodnKmvkq-Yj47c")
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	t.Log("rec=" + rec.Body.String())
}

func TestUserRegister(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	args := &model.UserCreateArgs{
		Name:     "hhhhhhh12",
		Alias:    "hhhhhhh12",
		Email:    "<EMAIL>",
		Phone:    "17889898989",
		Password: "Sugon123",
	}

	jsonData, _ := json.Marshal(args)
	req := httptest.NewRequest(http.MethodPost, "/iam/users", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	fmt.Println("------------------")
	fmt.Println(rec.Body.String())
}

func TestUserListPage(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	req := httptest.NewRequest(http.MethodGet, "/iam/users/all?page_num=1&page_size=10", nil)
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	fmt.Println("------------------")
	fmt.Println(rec.Body.String())
}

func TestUserType(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	req := httptest.NewRequest(http.MethodGet, "/iam/users/type", nil)
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	fmt.Println("------------------")
	fmt.Println(rec.Body.String())
}

func TestOverviewCount(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	req := httptest.NewRequest(http.MethodGet, "/iam/overview/count", nil)
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	fmt.Println("------------------")
	fmt.Println(rec.Body.String())
}

func TestUUID(t *testing.T) {
	fmt.Println("------------------")
	fmt.Println(rndm.GUID())
}

func BenchmarkUUID(b *testing.B) {
	for range b.N {
		rndm.UUID()
	}
}

func BenchmarkGUID(b *testing.B) {
	for range b.N {
		rndm.GUID()
	}
}

func TestLoginErrCount(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	req := httptest.NewRequest(http.MethodGet, "/iam/login/error?username=hhhhhhh12", nil)
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	fmt.Println("------------------")
	fmt.Println(rec.Body.String())
}

func TestAuthCode(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	req := httptest.NewRequest(http.MethodGet, "/iam/login/code", nil)
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	fmt.Println("------------------")
	fmt.Println(rec.Body.String())
}

func TestStrLower(t *testing.T) {
	input := "Hello123World"
	lowercased := strings.ToLower(input)
	fmt.Println(lowercased)
}

func TestUserProjects(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	req := httptest.NewRequest(http.MethodGet, "/iam/users/0d2bbb018e8b44b985a169647379f413/projects?page_num=1&page_size=10", nil)
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	fmt.Println("------------------")
	fmt.Println(rec.Body.String())
}

func TestUserUnBindRoles(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)
	body := &model.UserUnBindRolesArgs{RoleIds: []string{"23546c7ab11e11ecbde1083a88902c92"}}

	jsonData, _ := json.Marshal(body)
	req := httptest.NewRequest(http.MethodPost, "/iam/users/8925d92be2224987aa3d4eb2346d9c0c/unbind-roles", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	fmt.Println("------------------")
	fmt.Println(rec.Body.String())
}

func TestLogout(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	req := httptest.NewRequest(http.MethodDelete, "/iam/login/out", nil)
	req.Header.Set("Content-Type", "application/json")
	//initToken(req)
	req.Header.Set("authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhbGlhcyI6IiIsInVzZXJJZCI6IjBkMmJiYjAxOGU4YjQ0Yjk4NWExNjk2NDczNzlmNDEzIiwidXNlcm5hbWUiOiJhZG1pbiIsInByb2plY3RJZCI6ImVkY2MyNDA1OWM4YjQ2OTY5YTM3MjYwZGM5YWIyMzI5IiwidXNlclR5cGUiOiJhZG1pbiIsImRlcHRfaWQiOiIiLCJpc3MiOiJzdWdvbi1jbG91ZC1pYW0iLCJpYXQiOjE3MjAxNzAyMzF9.2_BQk9NziXMm4uOEcG3HDN3FeeZ15MfVxTQd2Sfg22w")
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	fmt.Println("------------------")
	fmt.Println(rec.Body.String())

}
