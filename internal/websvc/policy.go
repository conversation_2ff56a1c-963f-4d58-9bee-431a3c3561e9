package websvc

import (
	"fmt"
	"net/http"
	"strings"

	"code.mysugoncloud.com/hci/assist/logs"
	"code.mysugoncloud.com/hci/assist/topx"
	"code.mysugoncloud.com/hci/iam/internal/conf"
	"code.mysugoncloud.com/hci/iam/internal/repo"
	"code.mysugoncloud.com/hci/iam/internal/service"
	"code.mysugoncloud.com/hci/iam/internal/util"
	"code.mysugoncloud.com/hci/iam/model"
	"github.com/labstack/echo/v4"
)

// GetMenu doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	service_id		query	string true "服务id"
// @Tags ram策略管理
// @Summary 获取菜单列表
// @Success 200 {object} model.ResultModel{content=model.RamPolicy} "返回数据"
// @Router /iam/ram/policy/menu [get]
func GetMenu(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	userId := ctx.Request().Header.Get(topx.USER_ID)
	projectId := ctx.Request().Header.Get(topx.CURRENT_PROJECT_ID)
	serviceId := ctx.QueryParam("service_id")
	mods, err := service.GetMenu(rctx, userId, projectId, serviceId)
	if err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithContent(mods))
}

// PolicyTree doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	service_id		query	string true "服务id"
// @Param	role_id		query	string false "角色id"
// @Tags ram策略管理
// @Summary 获取策略树形列表
// @Success 200 {object} model.ResultModel{content=[]model.RamPolicy} "返回数据"
// @Router /iam/ram/policy/tree [get]
func PolicyTree(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	userId := ctx.Request().Header.Get(topx.USER_ID)
	userType := ctx.Request().Header.Get(topx.USER_TYPE)
	serviceId := ctx.QueryParam("service_id")
	roleId := ctx.QueryParam("role_id")
	mods, err := service.PolicyTree(rctx, userId, userType, roleId, serviceId)
	if err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithContent(mods))
}

// PolicyCreate doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	body		body	model.RamPolicy true "请求参数"
// @Tags ram策略管理
// @Summary 创建策略
// @Success 200 {object} model.ResultModel "返回数据"
// @Router /iam/ram/policy [post]
func PolicyCreate(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	body := &model.RamPolicy{}
	if err := ctx.Bind(body); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	if err := service.PolicyCreate(rctx, body); err != nil {
		logs.Ctx(rctx).Err(err).Any("body", body).Debug("service.PolicyCreate")
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithMsg("创建策略成功"))
}

// PolicyByName doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	service_id		query	string true "服务id"
// @Param	name		query	string true "策略名称"
// @Tags ram策略管理
// @Summary 根据名称查询策略
// @Success 200 {object} model.ResultModel{content=model.RamPolicy} "返回数据"
// @Router /iam/ram/policy/name [get]
func PolicyByName(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	serviceId := ctx.QueryParam("service_id")
	name := ctx.QueryParam("name")
	return topx.Succcess(ctx, topx.WithContent(service.PolicyByName(rctx, name, serviceId)))
}

// PolicyById doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	id		path	string true "id"
// @Tags ram策略管理
// @Summary 根据id查询策略
// @Success 200 {object} model.ResultModel{content=model.RamPolicy} "返回数据"
// @Router /iam/ram/policy/{id} [get]
func PolicyById(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	id := ctx.Param("id")
	return topx.Succcess(ctx, topx.WithContent(service.PolicyById(rctx, id)))
}

// PolicyDelete doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	id		path	string true "id"
// @Tags ram策略管理
// @Summary 根据id删除策略
// @Success 200 {object} model.ResultModel "返回数据"
// @Router /iam/ram/policy/{id} [delete]
func PolicyDelete(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	id := ctx.Param("id")
	if err := service.PolicyDelete(rctx, id); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithContent("删除策略成功"))
}

// PolicyEdit doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	id		path	string true "id"
// @Param	body		body	model.RamPolicy true "请求参数"
// @Tags ram策略管理
// @Summary 修改策略
// @Success 200 {object} model.ResultModel "返回数据"
// @Router /iam/ram/policy/{id} [put]
func PolicyEdit(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	id := ctx.Param("id")
	body := &model.RamPolicy{}
	if err := ctx.Bind(body); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	if err := service.PolicyEdit(rctx, id, body); err != nil {
		logs.Ctx(rctx).Err(err).Any("body", body).Debug("service.PolicyEdit")
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithMsg("修改策略成功"))
}

// PolicyBind doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	body		body	model.PolicyBindArgs true "请求参数"
// @Tags ram策略管理
// @Summary 角色绑定策略
// @Success 200 {object} model.ResultModel "返回数据"
// @Router /iam/ram/policy/bind-policy [put]
func PolicyBind(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	args := &model.PolicyBindArgs{}
	if err := ctx.Bind(args); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	args.UserType = ctx.Request().Header.Get(topx.USER_TYPE)
	if err := service.PolicyBind(rctx, args); err != nil {
		logs.Ctx(rctx).Err(err).Any("args", args).Debug("service PolicyBind error")
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithMsg("绑定策略成功"))
}

// PolicyExport doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Tags ram策略管理
// @Summary 生成策略的sql
// @Router /iam/ram/policy/generate-sql [get]
func PolicyExport(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	// 设置响应头，以便浏览器将其视为文件下载
	ctx.Response().Header().Set("Content-Disposition", "attachment; filename=policy.sql")
	ctx.Response().Header().Set("Content-Type", "application/force-download;charset=utf-8")
	// 写入文件内容到响应体

	fileContent := []byte("#########################################  policy sql  ##########################################\n")

	str := "DELETE from  ram_policy where owner_id is null or owner_id = '';\n"
	fileContent = append(fileContent, str...)

	ramRoleIds := []string{
		"23546c7ab11e11ecbde1083a88902c92",
		"22dc7fd7a67e11eb9db98257b6a5d0d7",
		"5a5508808dc641bd83f2ca62ac641ee9",
		"6d4372436aa34fd5a015550c22ba605c",
		"94d25a5fa44b4e02b46f87fba31e1af1",
		"c16c9944ca324aa693d3da6f7e7484f7",
		"c16c9944ca324aa693d3da6f7e789121",
	}
	iamCfg, _ := conf.GetIamCfg(rctx)
	if len(iamCfg.GenerateRamRoleIds) > 0 {
		ramRoleIds = append(ramRoleIds, iamCfg.GenerateRamRoleIds...)
	}
	ramRoleIdsTemp := make([]string, 0)
	for _, str := range ramRoleIds {
		ramRoleIdsTemp = append(ramRoleIdsTemp, "'"+str+"'")
	}
	sqlDelPolicyRole := "DELETE from ram_policy_role WHERE ram_role_id in (" + strings.Join(ramRoleIdsTemp, ",") + ");\n\n"
	fileContent = append(fileContent, sqlDelPolicyRole...)
	// 导出policy
	policyEntities, _ := repo.PolicyListNoOwner(rctx)
	for _, policy := range policyEntities {

		tempStr := fmt.Sprintf("REPLACE INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`, `service_id`)"+
			"VALUES (null, '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', %d, '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s');\n",
			policy.Uuid, policy.PolicyName, policy.PolicyDocument, policy.PolicyType, policy.Comments, policy.OwnerId, policy.Action, policy.Effect, policy.ParentId, policy.MethodUrl, policy.Sort, policy.CreateBy, util.TimeToStr(policy.CreateAt, ""), policy.Index, policy.Icon, policy.LinkId, policy.ResourcePolicyFlag, policy.CanDelete, policy.ServiceId)
		fileContent = append(fileContent, tempStr...)
	}
	fileContent = append(fileContent, "\n"...)
	policyRoleEntities, _ := repo.PolicyRoleListByRoleIds(rctx, ramRoleIds)
	for _, policyRole := range policyRoleEntities {
		tempStr := fmt.Sprintf("REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`, `service_id`) VALUES ('%s', '%s', '%s', '%s');\n", policyRole.Uuid, policyRole.PolicyId, policyRole.RamRoleId, policyRole.ServiceId)
		fileContent = append(fileContent, tempStr...)
	}
	return ctx.Blob(http.StatusOK, "application/force-download", fileContent)
}
