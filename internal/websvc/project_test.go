package websvc

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"code.mysugoncloud.com/hci/iam/model"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

func TestProjectDetail(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	req := httptest.NewRequest(http.MethodGet, "/iam/projects/a27464c148894fad938a99e0f5d8d4f6", nil)
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	t.Log("rec=" + rec.Body.String())
}

func TestProjectCreate(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	args := &model.ProjectCreateOrUpdateArgs{
		DeptId:        "4451e7ca606ba41f5562ec109bad363e",
		Name:          "默认项目",
		QuotaValueMap: map[string]int64{"ecs_instance": 100, "ram": 120},
	}

	jsonData, _ := json.Marshal(args)
	req := httptest.NewRequest(http.MethodPost, "/iam/projects", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	t.Log("rec=" + rec.Body.String())
}

func TestProjectUpdate(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	args := &model.ProjectCreateOrUpdateArgs{
		DeptId: "9ca381cafe5c49e5877bcae58ea85351",
		Name:   "qqweqw12",
	}

	jsonData, _ := json.Marshal(args)
	req := httptest.NewRequest(http.MethodPut, "/iam/projects/5a7bf2c4-3a80-4a8d-9b0d-2dbadaf6d980", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	t.Log("rec=" + rec.Body.String())
}

func TestProjectDelete(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)
	req := httptest.NewRequest(http.MethodDelete, "/iam/projects/7560280b-f23b-4330-b4e2-37dbb9ed574c", nil)
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	t.Log("rec=" + rec.Body.String())
}

func TestProjectPageAll(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	req := httptest.NewRequest(http.MethodGet, "/iam/projects/by-user-type?name=&page_num=1&page_size=10", nil)
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	t.Log("rec=" + rec.Body.String())
}

func TestProjectQuotaUpdate(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	args := &model.ProjectQuotaUpdateArgs{
		QuotaValueMap: map[string]int64{"ecs_instance": 300, "ram": 100, "root_gb": 1024},
	}

	jsonData, _ := json.Marshal(args)
	req := httptest.NewRequest(http.MethodPut, "/iam/projects/quota/7560280b-f23b-4330-b4e2-37dbb9ed574c", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	fmt.Println("------------------")
	fmt.Println(rec.Body.String())
}

func TestProjectUsers(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	req := httptest.NewRequest(http.MethodGet, "/iam/projects/0c137c98e6fb4d639aa9f2171fca92d6/users?page_num=1&page_size=10&name=测试", nil)
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	fmt.Println("------------------")
	fmt.Println(rec.Body.String())
}

func TestProjectBindUser(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)
	body := &model.ProjectBindUserArgs{UserIds: []string{"210124a93db5491c99f314f750224e92", "8925d92be2224987aa3d4eb2346d9c0c"}}

	jsonData, _ := json.Marshal(body)
	req := httptest.NewRequest(http.MethodPost, "/iam/projects/0c137c98e6fb4d639aa9f2171fca92d6/bind-users", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	fmt.Println("------------------")
	fmt.Println(rec.Body.String())
}

func TestProjectUnBindUser(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)
	body := &model.ProjectBindUserArgs{UserIds: []string{"8925d92be2224987aa3d4eb2346d9c0c"}}

	jsonData, _ := json.Marshal(body)
	req := httptest.NewRequest(http.MethodPost, "/iam/projects/0c137c98e6fb4d639aa9f2171fca92d6/unbind-users", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	fmt.Println("------------------")
	fmt.Println(rec.Body.String())
}
