package websvc

import (
	"net/http"
	"path"
	"strings"

	"code.mysugoncloud.com/hci/assist/docx"
	"code.mysugoncloud.com/hci/assist/logs"
	"code.mysugoncloud.com/hci/assist/topx"
	_ "code.mysugoncloud.com/hci/iam/docs"
	"code.mysugoncloud.com/hci/iam/internal/conf"
	"code.mysugoncloud.com/hci/iam/internal/service"
	"code.mysugoncloud.com/hci/iam/internal/util"
	"github.com/labstack/echo/v4"
)

// @Title iam’s Api文档
//
// @securityDefinitions.apikey	Authorization
// @in							header
// @name						Authorization
// @description					Description Authorization
// @securityDefinitions.apikey	Current-Project-Id
// @in							header
// @name						Current-Project-Id
// @description					Description Current-Project-Id
// @securityDefinitions.apikey	Projectid
// @in							header
// @name						Projectid
// @description					Description Projectid
func Init(eng *echo.Echo) error {
	//eng.Use(ValidateTokenMiddleware)
	iam := eng.Group("/iam")
	swagger := docx.DocsHandler(docx.InstanceName("iam"), docx.DocExpansion("none"), docx.PersistAuthorization(true))
	iam.Use(ValidateTokenMiddleware)
	iam.GET("/swagger/*", swagger)
	iam.POST("/oauth/token", CreateToken)
	iam.GET("/publickey", GetPulicKey)
	iam.GET("/overview/count", OverviewCount)
	iam.GET("/login/error", LoginErrCount)
	iam.GET("/login/code", AuthCode)
	iam.DELETE("/login/out", Logout)
	// 部门
	{
		iam.GET("/departments/:id", DepartmentDetailById)
		iam.GET("/departments/alls", DepartmentAlls)
		iam.POST("/departments", DepartmentCreate)
		iam.PUT("/departments/:id", DepartmentUpdate)
		iam.DELETE("/departments/:id", DepartmentDelete)
		iam.GET("/departments/:id/projects", DepartmentProjects)
		iam.GET("/departments/:id/roles", DepartmentRoles)
		iam.GET("/departments/:id/users", DepartmentUsers)
		iam.PUT("/departments/quota/:id", DepartmentQuotaUpdate)

	}
	// 用户
	{
		iam.GET("/users/all", UserListPage)
		iam.POST("/users/sub", UserCreateSub)
		iam.POST("/users", UserRegister)
		iam.GET("/users/:user_id", UserDetail)
		iam.GET("/users/:user_id/projects", UserProjects)
		iam.GET("/users/:user_id/roles", UserRoles)
		iam.PUT("/users", UserUpdate)
		iam.POST("/users/:user_id/bind-roles", UserBindRoles)
		iam.POST("/users/:user_id/unbind-roles", UserUnBindRoles)
		iam.POST("/users/:user_id/bind-projects", UserBindProjects)
		iam.POST("/users/:user_id/unbind-projects", UserUnBindProjects)
		iam.PUT("/users/:user_id/password", UserResetPassword)
		iam.POST("/users/:user_id/expired", UserExpired)
		iam.PUT("/users/user_id/:user_id/status/:status", UserUpdateStatus)
		iam.GET("/users/:user_id/access", UserAccess)
		iam.POST("/users/:user_id/set-access", UserAccessSet)
		iam.DELETE("/users/:user_id", UserDelete)
		iam.PUT("/users/unlock/:user_id", UserUnlock)
		iam.GET("/users/type", UserTypeList)
	}
	// policy
	{
		iam.GET("/ram/policy/menu", GetMenu)
		iam.GET("/ram/policy/tree", PolicyTree)
		iam.POST("/ram/policy", PolicyCreate)
		iam.GET("/ram/policy/name", PolicyByName)
		iam.GET("/ram/policy/:id", PolicyById)
		iam.DELETE("/ram/policy/:id", PolicyDelete)
		iam.PUT("/ram/policy/:id", PolicyEdit)
		iam.PUT("/ram/policy/bind-policy", PolicyBind)
		iam.GET("/ram/policy/generate-sql", PolicyExport)
	}
	// quota
	{
		iam.GET("/quotas/department/overview", QuotaDeparmentOverview)
		iam.GET("/quotas/project/overview", QuotaProjectOverview)
		iam.GET("/quotas/department", QuotaDept)
		iam.GET("/quotas/project", QuotaProject)
	}
	// 角色
	{
		iam.POST("/roles", RoleCreate)
		iam.PUT("/roles", RoleUpdate)
		iam.DELETE("/roles/:role_id", RoleDelete)
		iam.GET("/roles/:role_id", RoleDetail)
		iam.GET("/roles/all", RolePageAll)
		iam.GET("/roles/inner-role-list", RoleInnerList)
	}
	// 项目
	{
		iam.GET("/projects/:id", ProjectDetail)
		iam.POST("/projects", ProjectCreate)
		iam.PUT("/projects/:id", ProjectUpdate)
		iam.DELETE("/projects/:id", ProjectDelete)
		iam.GET("/projects/all", ProjectPageAll)
		iam.PUT("/projects/quota/:id", ProjectQuotaUpdate)
		iam.GET("/projects/:id/users", ProjectUsers)
		iam.POST("/projects/:id/bind-users", ProjectBindUser)
		iam.POST("/projects/:id/unbind-users", ProjectUnBindUser)
	}
	// 服务类别
	{
		iam.GET("/micro-service-categories", MicroServiceCategoryList)
		iam.PUT("/micro-service-categories", MicroServiceCategoryUpdate)
		iam.PUT("/micro-services", MicroServiceUpdate)
	}
	return nil
}

func ValidateTokenMiddleware(next echo.HandlerFunc) echo.HandlerFunc {
	return func(ctx echo.Context) error {
		rctx := util.UnwarpCtx(ctx)
		// 跳过验证
		pathUrl := ctx.Path()
		v, err := conf.GetIamCfg(rctx)
		if err != nil {
			logs.Ctx(rctx).Err(err).Error("conf.GetIamCfg")
			return err
		}
		skipTokenValidate := v.SkipTokenValidate
		if strings.Contains(strings.Join(skipTokenValidate, " "), pathUrl) {
			return next(ctx)
		} else {
			for _, skip := range skipTokenValidate {
				if strings.Contains(skip, "*") {
					// 正则匹配
					if matched, _ := path.Match(skip, pathUrl); matched {
						return next(ctx)
					}
				}
			}
		}

		authorization := ctx.Request().Header.Get("Authorization")
		logs.Debugf("authorization:[{%s}]", authorization)
		if authorization == "" {
			logs.Error("*****头部验证不通过，请在头部输入  authorization")
			return ctx.JSON(http.StatusUnauthorized, map[string]string{"msg": "Token为空", "debug": "Token is required"})
		}
		token := strings.TrimPrefix(authorization, "Bearer ")
		// is login elsewhere
		if service.CheckOnePlaceLogin(rctx, token) {
			return ctx.JSON(http.StatusGone, map[string]string{"msg": "账号已在其他地方登录", "debug": "Logged in elsewhere"})
		}
		// 验证token
		user, err := service.ValidateToken(rctx, token)
		if err != nil {
			logs.Ctx(rctx).Err(err).Any("token", token).Error("validata token failed")
			return ctx.JSON(http.StatusUnauthorized, map[string]string{"msg": "Token已失效", "debug": err.Error()})
		}

		// 重新设置header头
		ctx.Request().Header.Set(topx.USER_ID, user.Id)
		ctx.Request().Header.Set(topx.USER_NAME, user.Name)
		ctx.Request().Header.Set(topx.USER_TYPE, user.Type)
		regionId := ctx.Request().Header.Get(topx.REGION_ID)
		// 默认设置区域
		if regionId == "" {
			ctx.Request().Header.Set(topx.REGION_ID, "RegionOne")
		}
		return next(ctx)
	}
}

// GetToken 获取token
func GetToken(ctx echo.Context) string {
	authorization := ctx.Request().Header.Get("Authorization")
	return strings.Replace(authorization, "Bearer ", "", -1)
}
