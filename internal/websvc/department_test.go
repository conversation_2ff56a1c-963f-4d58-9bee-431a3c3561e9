package websvc

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"code.mysugoncloud.com/hci/iam/model"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

func TestDepartmentQuotaUpdate(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	args := &model.DepartmentQuotaUpdateArgs{
		QuotaValueMap: map[string]int64{"ecs_instance": 1000, "ram": 100, "root_gb": 1024},
	}

	jsonData, _ := json.Marshal(args)
	req := httptest.NewRequest(http.MethodPut, "/iam/departments/quota/0b7ea6aee45946c2a39571c467d53b49", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	fmt.Println("------------------")
	fmt.Println(rec.Body.String())
}

func TestDepartmentAlls(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	req := httptest.NewRequest(http.MethodGet, "/iam/departments/alls", nil)
	req.Header.Set("Content-Type", "application/json")
	// initToken(req)
	req.Header.Set("authorization", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhbGlhcyI6ImhoaGhoaGgxMiIsInVzZXJJZCI6IjdiNWYwYTIwYWZkMTJlNzAxYWQyNjMwMDc0YWZiNjQzIiwidXNlcm5hbWUiOiJoaGhoaGhoMTIiLCJ1c2VyVHlwZSI6Im9yZ2FkbWluIiwiZGVwdF9pZCI6IjgzMDk5MDhjYjY4NDFiYjM1MzQ3Yjk1NWJjMWIwZTcwIiwiaXNzIjoic3Vnb24tY2xvdWQtaWFtIiwiaWF0IjoxNzIxMzc2MTg4fQ.jyesYRTaHgd56LZSAB2KLVG4muUA3h2DvOjctER8uQQ")
	rec := httptest.NewRecorder()

	e.ServeHTTP(rec, req)
	fmt.Println("------------------")
	fmt.Println(rec.Body.String())
}

func TestDepartmentCreate(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	args := &model.CreateOrUpdateArgs{
		Name:        "默认子组织",
		Description: "默认子组织",
		ParentId:    "a2e32bd28524c652d4e87d6e06231cf9",
	}

	jsonData, _ := json.Marshal(args)
	req := httptest.NewRequest(http.MethodPost, "/iam/departments", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	fmt.Println("------------------")
	fmt.Println(rec.Body.String())
}

func TestDepartmentRoles(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	req := httptest.NewRequest(http.MethodGet, "/iam/departments/9ca381cafe5c49e5877bcae58ea85351/roles?page_size=10&page_num=1", nil)
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	fmt.Println("------------------")
	fmt.Println(rec.Body.String())
}

func TestDepartmentUsers(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	req := httptest.NewRequest(http.MethodGet, "/iam/departments/9ca381cafe5c49e5877bcae58ea85351/users?page_size=10&page_num=1", nil)
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	fmt.Println("------------------")
	fmt.Println(rec.Body.String())
}

func TestDepartmentProjects(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	req := httptest.NewRequest(http.MethodGet, "/iam/departments/9ca381cafe5c49e5877bcae58ea85351/projects?page_size=10&page_num=1", nil)
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	fmt.Println("------------------")
	fmt.Println(rec.Body.String())
}

func TestDepartmentDetailById(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	req := httptest.NewRequest(http.MethodGet, "/iam/departments/8309908cb6841bb35347b955bc1b0e70", nil)
	req.Header.Set("Content-Type", "application/json")
	// initToken(req)
	initDeptMasterToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	fmt.Println("------------------")
	fmt.Println(rec.Body.String())
}
