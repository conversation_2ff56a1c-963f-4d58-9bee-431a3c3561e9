package websvc

import (
	"code.mysugoncloud.com/hci/assist/topx"
	"code.mysugoncloud.com/hci/iam/internal/service"
	"code.mysugoncloud.com/hci/iam/internal/util"
	"github.com/labstack/echo/v4"
)

// OverviewCount doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Tags 概览API
// @Summary 组织/项目/角色/用户数量
// @Success 200 {object} model.ResultModel{content=model.OverviewCountReoly} "返回数据"
// @Router /iam/overview/count [get]
func OverviewCount(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	userId := ctx.Request().Header.Get(topx.USER_ID)
	mod, err := service.OverviewCount(rctx, userId)
	if err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithContent(mod))
}
