package websvc

import (
	"code.mysugoncloud.com/hci/assist/logs"
	"code.mysugoncloud.com/hci/assist/topx"
	"code.mysugoncloud.com/hci/iam/internal/service"
	"code.mysugoncloud.com/hci/iam/internal/util"
	"code.mysugoncloud.com/hci/iam/model"
	"github.com/labstack/echo/v4"
)

// MicroServiceCategoryList doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	query		query	model.MicroServiceCategoryListArgs true "请求参数"
// @Tags 菜单API
// @Summary 查询菜单类别信息
// @Success 200 {object} model.ResultModel{content=model.PageCL[model.MicroServiceCategory]} "返回数据"
// @Router /iam/micro-service-categories [get]
func MicroServiceCategoryList(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	filter := &model.MicroServiceCategoryListArgs{}
	if err := ctx.Bind(filter); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	mods, count, err := service.MicroServiceCategoryList(rctx, filter)
	if err != nil {
		return topx.Error(ctx, topx.WithMsg("查询微服务类别信息失败"))
	}
	return topx.Succcess(ctx, topx.WithPage(filter.GetPageNum(), filter.GetLimit(), count, mods))
}

// MicroServiceCategoryUpdate doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	body		body	model.MicroServiceCategoryUpdateArgs true "请求参数"
// @Tags 菜单API
// @Summary 更新菜单类别信息
// @Success 200 {object} model.ResultModel "返回数据"
// @Router /iam/micro-service-categories [put]
func MicroServiceCategoryUpdate(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	body := &model.MicroServiceCategoryUpdateArgs{}
	if err := ctx.Bind(body); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	if err := service.MicroServiceCategoryUpdate(rctx, body); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithMsg("更新服务类别信息成功"))
}

// MicroServiceUpdate doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	body		body	model.MicroServiceUpdateArgs true "请求参数"
// @Tags 菜单API
// @Summary 修改菜单信息
// @Success 200 {object} model.ResultModel "返回数据"
// @Router /iam/micro-services [put]
func MicroServiceUpdate(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	body := &model.MicroServiceUpdateArgs{}
	if err := ctx.Bind(body); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	if err := service.MicroServiceUpdate(rctx, body); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithMsg("更新服务信息成功"))
}
