package websvc

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"code.mysugoncloud.com/hci/iam/model"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

func TestMicroServiceCategoryList(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)

	req := httptest.NewRequest(http.MethodGet, "/iam/micro-service-categories?page_num=1&page_size=100", nil)
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	t.Log("rec=" + rec.Body.String())
}

func TestMicroServiceCategoryUpdate(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)
	navHidden := true
	var order float64 = 11
	body := &model.MicroServiceCategoryUpdateArgs{
		Id:        "13a38073a36111eb8230f20ed01ddf38",
		NavHidden: &navHidden,
		Order:     &order,
	}
	jsonData, _ := json.Marshal(body)
	req := httptest.NewRequest(http.MethodPut, "/iam/micro-service-categories", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	t.Log("rec=" + rec.Body.String())
}

func TestMicroServiceUpdate(t *testing.T) {
	testInit(t)
	e := echo.New()
	e.Use(middleware.Logger())
	Init(e)
	navHidden := false
	var order float64 = 5
	body := &model.MicroServiceUpdateArgs{
		Id:        "********************************",
		NavHidden: &navHidden,
		Order:     &order,
	}
	jsonData, _ := json.Marshal(body)
	req := httptest.NewRequest(http.MethodPut, "/iam/micro-services", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	initToken(req)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	fmt.Println("------------------")
	fmt.Println(rec.Body.String())
}
