package websvc

import (
	"code.mysugoncloud.com/hci/assist/logs"
	"code.mysugoncloud.com/hci/assist/topx"
	"code.mysugoncloud.com/hci/iam/internal/service"
	"code.mysugoncloud.com/hci/iam/internal/util"
	"code.mysugoncloud.com/hci/iam/model"
	"github.com/labstack/echo/v4"
)

// ProjectDetail doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	id		path	string true "项目id"
// @Tags 项目API
// @Summary 项目详情
// @Success 200 {object} model.ResultModel{content=model.Project} "返回数据"
// @Router /iam/projects/{id} [get]
func ProjectDetail(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	projectId := ctx.Param("id")
	project, err := service.ProjectDetail(rctx, projectId)
	if err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithContent(project))
}

// ProjectCreate doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	body		body	model.ProjectCreateOrUpdateArgs true "请求参数"
// @Tags 项目API
// @Summary 创建项目
// @Success 200 {object} model.ResultModel "返回数据"
// @Router /iam/projects [post]
func ProjectCreate(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	body := &model.ProjectCreateOrUpdateArgs{}
	if err := ctx.Bind(body); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	regionId := ctx.Request().Header.Get(topx.REGION_ID)
	body.RegionId = regionId
	if err := service.ProjectCreate(rctx, body); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithMsg("创建项目成功"))
}

// ProjectUpdate doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	body		body	model.ProjectCreateOrUpdateArgs true "请求参数"
// @Param	id		path	string true "项目id"
// @Tags 项目API
// @Summary 修改项目
// @Success 200 {object} model.ResultModel "返回数据"
// @Router /iam/projects/{id} [put]
func ProjectUpdate(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	body := &model.ProjectCreateOrUpdateArgs{}
	if err := ctx.Bind(body); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	body.ProjectId = ctx.Param("id")
	if err := service.ProjectUpdate(rctx, body); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithMsg("修改项目成功"))
}

// ProjectDelete doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	id		path	string true "项目id"
// @Tags 项目API
// @Summary 删除项目
// @Success 200 {object} model.ResultModel "返回数据"
// @Router /iam/projects/{id} [delete]
func ProjectDelete(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	id := ctx.Param("id")
	regionId := ctx.Request().Header.Get(topx.REGION_ID)
	if err := service.ProjectDelete(rctx, id, regionId); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithContent("删除项目成功"))
}

// ProjectPageAll doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	query		query	model.ProjectPageAllArgs true "请求参数"
// @Tags 项目API
// @Summary 根据用户类型查询所有项目列表
// @Success 200 {object} model.ResultModel{content=model.PageCL[model.Project]} "返回数据"
// @Router /iam/projects/all [get]
func ProjectPageAll(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	filter := &model.ProjectPageAllArgs{}
	if err := ctx.Bind(filter); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	filter.CurrUserId = ctx.Request().Header.Get(topx.USER_ID)
	mods, count, err := service.ProjectPageAll(rctx, filter)
	if err != nil {
		logs.Ctx(rctx).Err(err).Any("filter", filter).Debug("service.ProjectPageAll")
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithPage(filter.GetPageNum(), filter.GetLimit(), count, mods))
}

// ProjectQuotaUpdate doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	body		body	model.ProjectQuotaUpdateArgs true "请求参数"
// @Param	id		path	string true "项目id"
// @Tags 项目API
// @Summary 修改项目配额
// @Success 200 {object} model.ResultModel "返回数据"
// @Router /iam/projects/quota/{id} [put]
func ProjectQuotaUpdate(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	body := &model.ProjectQuotaUpdateArgs{}
	if err := ctx.Bind(body); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	body.ProjectId = ctx.Param("id")
	body.RegionId = ctx.Request().Header.Get(topx.REGION_ID)
	if err := service.ProjectQuotaUpdate(rctx, body); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithMsg("修改项目配额成功"))
}

// ProjectUsers doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	query		query	model.UserPageByProjectIdArgs true "请求参数"
// @Tags 项目API
// @Summary 查询项目所有用户
// @Success 200 {object} model.ResultModel{content=model.PageCL[model.User]} "返回数据"
// @Router /iam/projects/{id}/users [get]
func ProjectUsers(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	filter := &model.UserPageByProjectIdArgs{}
	if err := ctx.Bind(filter); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	filter.ProjectId = ctx.Param("id")
	mods, count, err := service.UserPageByProjectId(rctx, filter)
	if err != nil {
		logs.Ctx(rctx).Err(err).Any("filter", filter).Debug("service.UserPageByProjectId")
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithPage(filter.GetPageNum(), filter.GetLimit(), count, mods))
}

// ProjectBindUser doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	body		body	model.ProjectBindUserArgs true "请求参数"
// @Param	id		path	string true "项目id"
// @Tags 项目API
// @Summary 项目关联用户
// @Success 200 {object} model.ResultModel "返回数据"
// @Router /iam/projects/{id}/bind-users [post]
func ProjectBindUser(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	body := &model.ProjectBindUserArgs{}
	if err := ctx.Bind(body); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	body.ProjectId = ctx.Param("id")
	if err := service.ProjectBindUser(rctx, body); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithContent("项目关联用户成功"))
}

// ProjectUnBindUser doc
// @Security Authorization
// @Security Current-Project-Id
// @Security Projectid
// @Param	body		body	model.ProjectBindUserArgs true "请求参数"
// @Param	id		path	string true "项目id"
// @Tags 项目API
// @Summary 项目批量解绑用户
// @Success 200 {object} model.ResultModel "返回数据"
// @Router /iam/projects/{id}/unbind-users [post]
func ProjectUnBindUser(ctx echo.Context) error {
	rctx := util.UnwarpCtx(ctx)
	body := &model.ProjectBindUserArgs{}
	if err := ctx.Bind(body); err != nil {
		logs.Ctx(rctx).Err(err).Error("ctx.Bind")
		return topx.Error(ctx, topx.WithMsg("参数错误"))
	}
	body.ProjectId = ctx.Param("id")
	if err := service.ProjectUnBindUser(rctx, body); err != nil {
		return topx.Error(ctx, topx.WithErr(err))
	}
	return topx.Succcess(ctx, topx.WithContent("项目关联用户成功"))
}
