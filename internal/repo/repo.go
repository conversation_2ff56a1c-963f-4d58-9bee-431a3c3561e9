package repo

import (
	"context"
	"database/sql"
	"fmt"
	"regexp"
	"strings"

	"code.mysugoncloud.com/hci/assist/config"
	"code.mysugoncloud.com/hci/assist/logs"
	"code.mysugoncloud.com/hci/iam/internal/conf"
	"code.mysugoncloud.com/hci/iam/model"
	"github.com/go-sql-driver/mysql"
	_ "github.com/go-sql-driver/mysql"
	"xorm.io/xorm"
	xlog "xorm.io/xorm/log"
)

var db *xorm.Engine
var dsnReg = regexp.MustCompile(":.*@")

func Init(ctx context.Context, dsn string) error {

	if dsn == "" {
		v, err := conf.GetIamCfg(ctx)
		if err != nil {
			logs.Ctx(ctx).Err(err).Error("conf.GetIamCfg")
			return err
		}
		dsn = v.Dsn
	}
	tryInitDb(dsn)
	eng, err := xorm.NewEngine("mysql", dsn)
	if err != nil {
		logs.Ctx(ctx).Err(err).Error("xorm.NewEngine")
		return err
	}
	db = eng
	logs.Ctx(ctx).Info(dsnReg.ReplaceAllString(dsn, ":******@"))
	sl := logs.Xorm(xlog.LOG_INFO)
	switch config.GetLogLevel() {
	case "debug":
		sl.SetLevel(xlog.LOG_DEBUG)
	case "info":
		sl.SetLevel(xlog.LOG_INFO)
	case "warn":
		sl.SetLevel(xlog.LOG_WARNING)
	case "error":
		sl.SetLevel(xlog.LOG_ERR)
	}
	db.SetLogger(sl)
	db.SetMaxIdleConns(8)
	db.SetMaxOpenConns(24)
	db.ShowSQL(config.GetShowSql())
	if err = db.Ping(); err != nil {
		return err
	}
	if config.GetSyncDb() {
		err := db.Sync(
			new(model.User),
			new(model.IamCache),
			new(model.Globalsettings),
			new(model.Department),
			new(model.IamRole),
			new(model.Project),
			new(model.RamPolicy),
			new(model.RamPolicyRole),
			new(model.UserRoleMapping),
			new(model.UserAccess),
			new(model.MicroService),
			new(model.MicroServiceCategory),
			new(model.QuotaType),
			new(model.QuotaMetric),
			new(model.QuotaDepartmentValue),
			new(model.QuotaProjectValue),
		)
		logs.Ctx(ctx).Err(err).Info("sync database iam")
	}
	// logs.Info("iam model init")
	// migration := migrate.New(db, migrate.DefaultOptions, []*migrate.Migration{
	// 	{
	// 		ID: "202406281400",
	// 		Migrate: func(tx *xorm.Engine) error {
	// 			tx.Insert(&model.User{Name: "hahah"})
	// 			return nil
	// 		},
	// 		Rollback: func(tx *xorm.Engine) error { return nil },
	// 	},
	// })
	// err = migration.Migrate()
	// logs.Ctx(ctx).Err(err).Info("iam migration")
	return nil
}

func Close() error {
	return db.Close()
}

func fixlike(str string) string {
	str = strings.ReplaceAll(str, "_", "\\_")
	return strings.ReplaceAll(str, "%", "\\%")
}

func tryInitDb(dsn string) {
	cfg, err := mysql.ParseDSN(dsn)
	if err != nil {
		logs.With().Err(err).Warn("mysql.ParseDSN")
		return
	}
	initSQL := fmt.Sprintf("CREATE DATABASE IF NOT EXISTS %s default character set utf8mb4 collate utf8mb4_general_ci", cfg.DBName)
	cfg.DBName = ""
	db, err := sql.Open("mysql", cfg.FormatDSN())
	if err != nil {
		logs.With().Err(err).Warn("sql.Open")
		return
	}
	defer db.Close()
	_, err = db.Exec(initSQL)
	if err != nil {
		logs.With().Err(err).Warn("db.Exec")
		return
	}
}
