package repo

import (
	"context"

	"code.mysugoncloud.com/hci/iam/model"
)

func UserRoleMappingAdd(ctx context.Context, mod *model.UserRoleMapping) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.Insert(mod); err != nil {
		sess.Rollback()
		return err
	}
	return sess.Commit()
}

func UserRoleMappingBatchAdd(ctx context.Context, mod []*model.UserRoleMapping) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.Insert(mod); err != nil {
		sess.Rollback()
		return err
	}
	return sess.Commit()
}

func UserUnBindRoles(ctx context.Context, body *model.UserUnBindRolesArgs) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	sess.And("user_id = ?", body.UserId)
	sess.In("role_id", body.RoleIds)
	if _, err := sess.Delete(&model.UserRoleMapping{}); err != nil {
		sess.Rollback()
		return err
	}
	return sess.Commit()
}

func ProjectUnBindUsers(ctx context.Context, body *model.ProjectBindUserArgs) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	sess.And("project_id = ?", body.ProjectId)
	sess.In("user_id", body.UserIds)
	if _, err := sess.Delete(&model.UserRoleMapping{}); err != nil {
		sess.Rollback()
		return err
	}
	return sess.Commit()
}

func UserUnBindProjects(ctx context.Context, body *model.UserBindProjectsArgs) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	sess.And("user_id = ?", body.UserId)
	sess.And("role_id is null or role_id = ?", "")
	sess.In("project_id", body.ProjectIds)
	if _, err := sess.Delete(&model.UserRoleMapping{}); err != nil {
		sess.Rollback()
		return err
	}
	return sess.Commit()
}

// UserRoleMappingListByUserId 根据用户id查询
func UserRoleMappingListByUserId(ctx context.Context, userId string) ([]model.UserRoleMapping, error) {
	mods := make([]model.UserRoleMapping, 0)
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Where("user_id = ?", userId)
	err := sess.Find(&mods)
	if err != nil {
		return nil, err
	}
	return mods, nil
}

// URMByProjectNoRole 根据项目id查询，不返回有角色的绑定关系
func URMByProjectNoRole(ctx context.Context, projectId string) ([]model.UserRoleMapping, error) {
	mods := make([]model.UserRoleMapping, 0)
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Where("project_id = ?", projectId)
	sess.And("role_id is null or role_id = ?", "")
	err := sess.Find(&mods)
	if err != nil {
		return nil, err
	}
	return mods, nil
}

func UserRoleMappingDrop(ctx context.Context, mod *model.UserRoleMapping) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.Delete(mod); err != nil {
		sess.Rollback()
		return err
	}
	sess.Commit()
	return nil
}

func UserRoleMappingDropUserOrProjectRole(ctx context.Context, userId, projectId string) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	sess.Where("`user_id` = ?", userId)
	sess.And("`project_id` = ?", projectId)
	sess.And("`role_id` <> ?", "")
	if _, err := sess.Delete(&model.UserRoleMapping{}); err != nil {
		sess.Rollback()
		return err
	}
	sess.Commit()
	return nil
}

func UserRoleMappingDropByProjectIds(ctx context.Context, userId string, projectIds []string) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	sess.Where("`user_id` = ?", userId)
	sess.In("project_id", projectIds)
	if _, err := sess.Delete(&model.UserRoleMapping{}); err != nil {
		sess.Rollback()
		return err
	}
	sess.Commit()
	return nil
}

func URMDropByUserIds(ctx context.Context, projectId string, userIds []string) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	sess.Where("`project_id` = ?", projectId)
	sess.And("role_id is null or role_id = ?", "")
	sess.In("user_id", userIds)
	if _, err := sess.Delete(&model.UserRoleMapping{}); err != nil {
		sess.Rollback()
		return err
	}
	sess.Commit()
	return nil
}
