package repo

import (
	"context"

	"code.mysugoncloud.com/hci/assist/globals"
	"code.mysugoncloud.com/hci/iam/model"
)

func IamRoleExist(ctx context.Context, mod *model.IamRole) bool {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	has, _ := sess.Exist(mod)
	return has
}

func IamRoleExistNotId(ctx context.Context, id string, mod *model.IamRole) bool {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	if id != "" {
		sess.Where("id<>?", id)
	}
	has, _ := sess.Exist(mod)
	return has
}

func IamRoleGet(ctx context.Context, mod *model.IamRole) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	if has, _ := sess.Get(mod); !has {
		return globals.ErrNotfound
	}
	return nil
}

func IamRoleAdd(ctx context.Context, mod *model.IamRole) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.Insert(mod); err != nil {
		sess.Rollback()
		return err
	}

	return sess.Commit()
}

func IamRoleEdit(ctx context.Context, mod *model.IamRole, cols ...string) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.ID(mod.Id).Cols(cols...).Update(mod); err != nil {
		sess.Rollback()
		return err
	}
	sess.Commit()
	return nil
}

func IamRoleDrop(ctx context.Context, mod *model.IamRole) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.Delete(mod); err != nil {
		sess.Rollback()
		return err
	}
	sess.Commit()
	return nil
}

func IamRoleFindByUserId(ctx context.Context, userId string, projectId string) ([]model.IamRole, error) {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	mods := make([]model.IamRole, 0)
	sess.Table("iam_role").Alias("role").Distinct().Join("INNER", []string{"user_role_project_mapping", "mapping"}, "role.id = mapping.role_id").Where("mapping.user_id = ?", userId)
	if projectId != "" {
		sess.And("mapping.project_id = ? or mapping.project_id = ?", projectId, "")
	}
	sess.Select("role.*")
	err := sess.Find(&mods)
	if err != nil {
		return nil, err
	}
	return mods, nil
}

func IamRoleListByDept(ctx context.Context, filter *model.RoleListByDeptArgs, cols ...string) ([]model.IamRole, int, error) {
	mods := make([]model.IamRole, 0, filter.GetLimit())
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	if len(cols) > 0 {
		sess.Cols(cols...)
	}
	sess.And("`dept_id` = ? or `type` = ? or `type` = ?", filter.DeptId, "default", "public")
	if filter.Name != "" {
		sess.And("`name` like concat('%',?,'%')", fixlike(filter.Name))
	}
	sess.Desc("create_time")
	sess.Limit(filter.GetLimit(), filter.GetOffset())
	count, err := sess.FindAndCount(&mods)
	return mods, int(count), err
}

func IamRoleListByType(ctx context.Context, filter *model.UserRoleListArgs, userType string, cols ...string) ([]model.IamRole, int, error) {
	mods := make([]model.IamRole, 0, filter.GetLimit())
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	if len(cols) > 0 {
		sess.Cols(cols...)
	}
	sess.And("`type` = ?", userType)
	sess.Desc("create_time")
	sess.Limit(filter.GetLimit(), filter.GetOffset())
	count, err := sess.FindAndCount(&mods)
	return mods, int(count), err
}

// IamRoleListByMaster 分页查询用户角色，包含自己的内置角色
func IamRoleListByMaster(ctx context.Context, filter *model.UserRoleListArgs, userType string, deptIds []string, cols ...string) ([]model.IamRole, int, error) {
	mods := make([]model.IamRole, 0, filter.GetLimit())
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	if len(cols) > 0 {
		sess.Cols(cols...)
	}
	sess.In("dept_id", deptIds)
	sess.And("`type` is null or `type` = ?", "")
	sess.Or("`type` = ?", userType)
	sess.Desc("create_time")
	sess.Limit(filter.GetLimit(), filter.GetOffset())
	count, err := sess.FindAndCount(&mods)
	return mods, int(count), err
}

func IamRoleByUserIdPage(ctx context.Context, filter *model.UserRoleListArgs, cols ...string) ([]model.IamRole, int, error) {
	mods := make([]model.IamRole, 0, filter.GetLimit())
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	if len(cols) > 0 {
		sess.Cols(cols...)
	}
	sess.Table("iam_role").Alias("role").Join("INNER", []string{"user_role_project_mapping", "mapping"}, "role.id = mapping.role_id").Where("mapping.user_id = ?", filter.UserId)
	if filter.ProjectId != "" {
		sess.And("mapping.project_id = ? or mapping.project_id is null or mapping.project_id = ?", filter.ProjectId, "")
	} else {
		sess.And("mapping.project_id is null or mapping.project_id = ?", "")
	}
	sess.Select("role.*").GroupBy("`role`.id")
	sess.Desc("role.create_time")
	sess.Limit(filter.GetLimit(), filter.GetOffset())
	count, err := sess.FindAndCount(&mods)
	return mods, int(count), err
}

// IamRoleAllPage 分页模糊查询所有用户角色
func IamRoleAllPage(ctx context.Context, filter *model.RolePageAllArgs, cols ...string) ([]model.IamRole, int, error) {
	mods := make([]model.IamRole, 0, filter.GetLimit())
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	if len(cols) > 0 {
		sess.Cols(cols...)
	}
	if len(filter.DataBaseDeptIds) > 0 {
		sess.In("dept_id", filter.DataBaseDeptIds).Or("`type` = ?", "public")
	}

	if filter.RoleTypeIsNull {
		sess.And("`type` = ? or `type` = ?", "", "public")
	}

	if filter.Name != "" {
		sess.And("`name` like concat('%',?,'%')", fixlike(filter.Name))
	}
	sess.Desc("create_time")
	sess.Limit(filter.GetLimit(), filter.GetOffset())
	count, err := sess.FindAndCount(&mods)
	return mods, int(count), err
}

func IamRoleInnerPage(ctx context.Context, filter *model.RolesInnerArgs, cols ...string) ([]model.IamRole, int, error) {
	mods := make([]model.IamRole, 0, filter.GetLimit())
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	if len(cols) > 0 {
		sess.Cols(cols...)
	}
	sess.NotIn("`type`", "public")
	sess.And("dept_id is null or dept_id = ?", "")
	if filter.Name != "" {
		sess.And("`name` like concat('%',?,'%')", fixlike(filter.Name))
	}
	sess.Desc("create_time")
	sess.Limit(filter.GetLimit(), filter.GetOffset())
	count, err := sess.FindAndCount(&mods)
	return mods, int(count), err
}
