package repo

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"code.mysugoncloud.com/hci/assist/config"
	"code.mysugoncloud.com/hci/assist/logs"
	"code.mysugoncloud.com/hci/iam/model"
)

func testInit() error {
	logs.SetLevel(logs.LDEBUG)
	logs.SetCons(true)
	loc, _ := time.LoadLocation("Asia/Shanghai")
	fmt.Println("loc", loc)
	if loc != nil {
		time.Local = loc
	}
	os.Args = append(os.Args, "-log-path=", "-config=../../conf.toml", "-show-sql=true")
	config.PreInitMgmt(context.TODO())
	return Init(context.Background(), "root:database_sugon@tcp(172.22.1.57:3306)/iam_hci?charset=utf8mb4&parseTime=true&loc=Local")
}

func TestDrop(t *testing.T) {
	testInit()
	CacheDrop(context.Background(), &model.IamCache{Key: "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDDTuqopjnkXFpN06F/QTA1CA2rSPNaWpqbah8SUTSaOfvrzprBwYANh+lxW/bC242rBWA3LYtkhSxC00X32/KgxPqMCLmMBVD4zA15OMWRolFdGVYihgN5mtBFrWX4YIk3R7M9vyjTo7ve8jBddMACBNeT/qawAZWXuTHiKIHw5QIDAQAB"})

}
