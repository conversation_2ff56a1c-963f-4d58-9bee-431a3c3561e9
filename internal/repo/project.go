package repo

import (
	"context"

	"code.mysugoncloud.com/hci/assist/globals"
	"code.mysugoncloud.com/hci/iam/model"
)

func ProjectExist(ctx context.Context, mod *model.Project) bool {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	has, _ := sess.Exist(mod)
	return has
}

func ProjectGet(ctx context.Context, mod *model.Project) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.And("enabled = ?", true)
	if has, _ := sess.Get(mod); !has {
		return globals.ErrNotfound
	}
	return nil
}

func ProjectBatchGet(ctx context.Context, projectIds []string) ([]*model.Project, error) {
	mods := make([]*model.Project, 0)
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.In("id", projectIds)
	err := sess.Find(&mods)
	return mods, err
}

func ProjectAdd(ctx context.Context, mod *model.Project) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.Insert(mod); err != nil {
		sess.Rollback()
		return err
	}

	return sess.Commit()
}

func ProjectCreate(ctx context.Context, mod *model.Project, qpvs []*model.QuotaProjectValue, qdvs []*model.QuotaDepartmentValue) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.Insert(mod); err != nil {
		sess.Rollback()
		return err
	}

	if _, err := sess.Insert(qpvs); err != nil {
		sess.Rollback()
		return err
	}

	if _, err := sess.Delete(&model.QuotaDepartmentValue{DepartmentId: qdvs[0].DepartmentId, RegionId: qdvs[0].RegionId}); err != nil {
		sess.Rollback()
		return err
	}
	if _, err := sess.Insert(qdvs); err != nil {
		sess.Rollback()
		return err
	}

	return sess.Commit()
}

func ProjectEdit(ctx context.Context, mod *model.Project, cols ...string) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.ID(mod.Id).Cols(cols...).Update(mod); err != nil {
		sess.Rollback()
		return err
	}
	sess.Commit()
	return nil
}

func ProjectDrop(ctx context.Context, mod *model.Project) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.Delete(mod); err != nil {
		sess.Rollback()
		return err
	}
	sess.Commit()
	return nil
}

func ProjectListByDeptIds(ctx context.Context, deptIds []string) ([]model.Project, error) {
	mods := make([]model.Project, 0)
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.In("dept_id", deptIds)
	err := sess.Find(&mods)
	return mods, err
}

func ProjectExistName(ctx context.Context, deptIds []string, name string, id string) bool {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.And("`enabled` = true and `alias` = ?", name)
	if id != "" {
		sess.And("`id` <> ?", id)
	}
	if len(deptIds) > 0 {
		sess.In("dept_id", deptIds)
	}
	has, _ := sess.Exist(&model.Project{})
	return has
}

func ProjectPageAll(ctx context.Context, filter *model.ProjectPageAllArgs, cols ...string) ([]model.Project, int, error) {
	mods := make([]model.Project, 0, filter.GetLimit())
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	if len(cols) > 0 {
		sess.Cols(cols...)
	}
	if len(filter.DataBaseDeptIds) > 0 {
		sess.In("dept_id", filter.DataBaseDeptIds)
	}
	if len(filter.ProjectIds) > 0 {
		sess.In("id", filter.ProjectIds)
	}

	if filter.ProjectName != "" {
		sess.And("`alias` like concat('%',?,'%')", fixlike(filter.ProjectName))
	}
	sess.And("enabled = ?", true)
	sess.Desc("create_time")
	sess.Limit(filter.GetLimit(), filter.GetOffset())
	count, err := sess.FindAndCount(&mods)
	return mods, int(count), err
}

// ProjectQuotaUpdate 更新部门配额
func ProjectQuotaUpdate(ctx context.Context, qdvs []*model.QuotaDepartmentValue, qpvs []*model.QuotaProjectValue) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	// 防止循环更新，并且每次更新还要判断是否需要插入，直接先删除再插入
	if _, err := sess.Delete(&model.QuotaDepartmentValue{DepartmentId: qdvs[0].DepartmentId, RegionId: qdvs[0].RegionId}); err != nil {
		sess.Rollback()
		return err
	}
	if _, err := sess.Insert(qdvs); err != nil {
		sess.Rollback()
		return err
	}
	if _, err := sess.Delete(&model.QuotaProjectValue{ProjectId: qpvs[0].ProjectId, RegionId: qpvs[0].RegionId}); err != nil {
		sess.Rollback()
		return err
	}
	if _, err := sess.Insert(qpvs); err != nil {
		sess.Rollback()
		return err
	}

	return sess.Commit()
}

func ProjectDeleteRelation(ctx context.Context, project *model.Project, qdvs []*model.QuotaDepartmentValue, regionId string) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	// 软删除项目
	if _, err := sess.ID(project.Id).Cols("enabled").Update(project); err != nil {
		sess.Rollback()
		return err
	}
	// 删除项目配置的角色
	if _, err := sess.Delete(&model.UserRoleMapping{ProjectId: project.Id}); err != nil {
		sess.Rollback()
		return err
	}
	// 删除项目配额
	if _, err := sess.Delete(&model.QuotaProjectValue{ProjectId: project.Id, RegionId: regionId}); err != nil {
		sess.Rollback()
		return err
	}
	// 修改部门配额
	if _, err := sess.Delete(&model.QuotaDepartmentValue{DepartmentId: qdvs[0].DepartmentId, RegionId: qdvs[0].RegionId}); err != nil {
		sess.Rollback()
		return err
	}
	if _, err := sess.Insert(qdvs); err != nil {
		sess.Rollback()
		return err
	}
	return sess.Commit()
}
