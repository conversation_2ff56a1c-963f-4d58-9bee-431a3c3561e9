package repo

import (
	"context"

	"code.mysugoncloud.com/hci/assist/globals"
	"code.mysugoncloud.com/hci/iam/model"
	"xorm.io/xorm"
)

func DepartmentExist(ctx context.Context, mod *model.Department) bool {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	has, _ := sess.Exist(mod)
	return has
}

func DepartmentExistNotId(ctx context.Context, id string, mod *model.Department) bool {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	if id != "" {
		sess.Where("id<>?", id)
	}
	has, _ := sess.Exist(mod)
	return has
}

// DepartmentExistByDeptIds 查看在DeptIds是否存在组织
func DepartmentExistByDeptIds(ctx context.Context, deptIds []string, name string) bool {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.In("id", deptIds)
	sess.And("name = ?", name)
	has, _ := sess.Exist(&model.Department{})
	return has
}

func DepartmentGet(ctx context.Context, mod *model.Department) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	if has, _ := sess.Get(mod); !has {
		return globals.ErrNotfound
	}
	return nil
}

func DepartmentAdd(ctx context.Context, mod *model.Department) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.Insert(mod); err != nil {
		sess.Rollback()
		return err
	}

	return sess.Commit()
}

func DepartmentEdit(ctx context.Context, mod *model.Department, cols ...string) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.ID(mod.Id).Cols(cols...).Update(mod); err != nil {
		sess.Rollback()
		return err
	}
	sess.Commit()
	return nil
}

// DepartmentAll 查询所有部门
func DepartmentAll(ctx context.Context) ([]*model.Department, error) {
	mods := make([]*model.Department, 0)
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	err := sess.Find(&mods)
	return mods, err
}

func DepartmentDrop(ctx context.Context, mod *model.Department) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.Delete(mod); err != nil {
		sess.Rollback()
		return err
	}
	// 删除配额
	if _, err := sess.Delete(&model.QuotaDepartmentValue{DepartmentId: mod.Id}); err != nil {
		sess.Rollback()
		return err
	}
	sess.Commit()
	return nil
}

func DepartmentTreeByDeptId(ctx context.Context, deptId string) (*model.Department, error) {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)

	department := &model.Department{Id: deptId}
	if has, _ := sess.Get(department); !has {
		return nil, globals.ErrNotfound
	}
	var err error
	department.Children, err = getSubDepartments(sess, department.Id)
	if err != nil {
		return nil, err
	}

	return department, nil

}

// DepartmentUnderAllIds 查询部门以及子部分所有部门id
func DepartmentUnderAllIds(ctx context.Context, deptId string) ([]string, error) {
	dept, err := DepartmentTreeByDeptId(ctx, deptId)
	if err != nil {
		return nil, err
	}
	ids := make([]string, 0)
	recursionDept(dept, &ids)
	return ids, nil
}

func recursionDept(detp *model.Department, ids *[]string) {
	*ids = append(*ids, detp.Id)
	if detp.Children != nil {
		for _, child := range detp.Children {
			recursionDept(child, ids)
		}
	}
}

func getSubDepartments(sess *xorm.Session, parentId string) ([]*model.Department, error) {
	var departments []*model.Department
	err := sess.Where("parent_id=?", parentId).Find(&departments)
	if err != nil {
		return nil, err
	}

	for _, dept := range departments {
		children, err := getSubDepartments(sess, dept.Id)
		if err != nil {
			return nil, err
		}
		dept.Children = children
	}

	return departments, nil
}

func DepartmentOneLevel(ctx context.Context, deptIds []string) ([]*model.Department, error) {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	mods := make([]*model.Department, 0)

	sess.And("level = ?", 0)
	if len(deptIds) > 0 {
		sess.In("id", deptIds)
	}
	err := sess.Find(&mods)
	return mods, err
}

func DepartmentQuotaUpdate(ctx context.Context, qdvs []*model.QuotaDepartmentValue) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	// 防止循环更新，并且每次更新还要判断是否需要插入，直接先删除再插入
	if _, err := sess.Delete(&model.QuotaDepartmentValue{DepartmentId: qdvs[0].DepartmentId, RegionId: qdvs[0].RegionId}); err != nil {
		sess.Rollback()
		return err
	}
	if _, err := sess.Insert(qdvs); err != nil {
		sess.Rollback()
		return err
	}

	return sess.Commit()
}

func DepartmentProjects(ctx context.Context, filter *model.DeparmentProjectsArgs) ([]model.Project, int, error) {
	mods := make([]model.Project, 0, filter.GetLimit())
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)

	sess.And("dept_id = ?", filter.DeptId)
	if filter.ProjectName != "" {
		sess.And("`alias` like concat('%',?,'%')", fixlike(filter.ProjectName))
	}
	sess.Desc("create_time")
	sess.Limit(filter.GetLimit(), filter.GetOffset())
	count, err := sess.FindAndCount(&mods)
	return mods, int(count), err
}
