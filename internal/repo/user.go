package repo

import (
	"context"
	"errors"

	"code.mysugoncloud.com/hci/assist/rndm"
	"code.mysugoncloud.com/hci/assist/topx"
	"code.mysugoncloud.com/hci/iam/model"
	"xorm.io/xorm"
)

func UserExist(ctx context.Context, mod *model.User) bool {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	has, _ := sess.Exist(mod)
	return has
}

func UserExistByEmailOrPhone(ctx context.Context, email, phone string) bool {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	has, _ := sess.Where("`email` = ? or `phone` = ?", email, phone).Exist(&model.User{})
	return has
}

func UserExistByEmailOrPhoneNotId(ctx context.Context, email, phone, id string) bool {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Where("`email` = ? or `phone` = ?", email, phone)
	sess.And("`id` <> ?", id)
	has, _ := sess.Exist(&model.User{})
	return has
}

func UserGet(ctx context.Context, mod *model.User) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	if has, _ := sess.Get(mod); !has {
		return errors.New("用户不存在，登录失败！")
	}
	return nil
}

func UserAdd(ctx context.Context, mod *model.User) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.Insert(mod); err != nil {
		sess.Rollback()
		return err
	}

	return sess.Commit()
}

func UserDrop(ctx context.Context, mod *model.User) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.Delete(mod); err != nil {
		sess.Rollback()
		return err
	}
	sess.Commit()
	return nil
}

// 创建部门用户
func UserCreateSub(ctx context.Context, mod *model.User) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if mod.Type == topx.TYPE_DEPT_MASTER {
		if err := innerRoleBind(sess, mod.Id, mod.Type); err != nil {
			sess.Rollback()
			return err
		}
	}

	if _, err := sess.Insert(mod); err != nil {
		sess.Rollback()
		return err
	}
	return sess.Commit()
}

// innerRoleBind 绑定内置角色
func innerRoleBind(sess *xorm.Session, userId, roleType string) error {
	// 绑定内置角色
	role := &model.IamRole{Type: roleType}
	if has, _ := sess.Get(role); !has {
		return errors.New("还未内置" + roleType + "角色")
	}
	urm := &model.UserRoleMapping{UserId: userId, RoleId: role.Id}
	// 先删除在插入，防止数据重复
	if _, err := sess.Delete(urm); err != nil {
		return err
	}
	urm.Id = rndm.GUID()
	if _, err := sess.Insert(urm); err != nil {
		return err
	}
	return nil
}

// UserRegister 注册用户
func UserRegister(ctx context.Context, user *model.User, department *model.Department, qdvs []model.QuotaDepartmentValue) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	// 保存部门
	if _, err := sess.Insert(department); err != nil {
		sess.Rollback()
		return err
	}
	// 保存部门配额
	if _, err := sess.Insert(qdvs); err != nil {
		sess.Rollback()
		return err
	}
	// 绑定内置角色
	if err := innerRoleBind(sess, user.Id, topx.TYPE_MASTER); err != nil {
		sess.Rollback()
		return err
	}
	// 保存用户
	if _, err := sess.Insert(user); err != nil {
		sess.Rollback()
		return err
	}
	return sess.Commit()
}

func UserEdit(ctx context.Context, mod *model.User, cols ...string) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.ID(mod.Id).Cols(cols...).Update(mod); err != nil {
		sess.Rollback()
		return err
	}
	sess.Commit()
	return nil
}

func UsersByDeptId(ctx context.Context, filter *model.DeparmentUsersArgs, cols ...string) ([]model.User, int, error) {
	mods := make([]model.User, 0, filter.GetLimit())
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	if len(cols) > 0 {
		sess.Cols(cols...)
	}
	if filter.DeptId != "" {
		sess.And("`dept_id` = ?", filter.DeptId)
	}
	if filter.Name != "" {
		sess.And("`name` like concat('%',?,'%') or `alias` like concat('%',?,'%')", fixlike(filter.Name), fixlike(filter.Name))
	}
	if filter.IsDeptMaster {
		sess.And("`type` = ?", topx.TYPE_NORMAL)
	}
	if filter.QueryUserType != "" {
		sess.And("`type` = ?", filter.QueryUserType)
	}
	sess.Desc("created_at")
	sess.Limit(filter.GetLimit(), filter.GetOffset())
	count, err := sess.FindAndCount(&mods)
	return mods, int(count), err
}

// UserListPageByUserType 根据用户类型分页查询用户列表
func UserListPageByUserType(ctx context.Context, filter *model.UserListArgs, cols ...string) ([]model.User, int, error) {
	mods := make([]model.User, 0, filter.GetLimit())
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	if len(cols) > 0 {
		sess.Cols(cols...)
	}
	user := &model.User{Id: filter.CurrUserId}
	if has, _ := sess.Get(user); !has {
		return nil, 0, errors.New("用户不存在")
	}
	if filter.Name != "" {
		sess.And("`name` like concat('%',?,'%') or `alias` like concat('%',?,'%')", fixlike(filter.Name), fixlike(filter.Name))
	}
	if filter.QueryUserType != "" {
		sess.And("`type` = ?", filter.QueryUserType)
	}
	switch user.Type {
	case topx.TYPE_MASTER:
		// 根账号
		if filter.DeptId != "" {
			sess.And("dept_id = ?", filter.DeptId)
		} else {
			deptIds, err := DepartmentUnderAllIds(ctx, user.DeptId)
			if err != nil {
				return nil, 0, errors.New("查询部门下所有部门失败")
			}
			if len(deptIds) > 0 {
				sess.In("dept_id", deptIds)
			}
		}
	case topx.TYPE_DEPT_MASTER:
		// 部门管理员
		if filter.DeptId != "" {
			sess.And("dept_id = ?", filter.DeptId)
		} else {
			deptIds, err := DepartmentUnderAllIds(ctx, user.DeptId)
			if err != nil {
				return nil, 0, errors.New("查询部门下所有部门失败")
			}
			if len(deptIds) > 0 {
				sess.In("dept_id", deptIds)
			}
		}
		// 部门管理员不查询同级部门的部门管理员
		sess.And("`type` = ?", topx.TYPE_NORMAL)
	case topx.TYPE_ADMIN:
		// 超级管理员
		if filter.DeptId != "" {
			sess.And("dept_id = ?", filter.DeptId)
		}
	case topx.TYPE_SYS_ADMIN:
		// 系统管理员
		if filter.DeptId != "" {
			sess.And("dept_id = ?", filter.DeptId)
		} else {
			sess.And("`dept_id` is not null")
		}
		// 查询除sysadmin用户外其他非普通用户并且有组织的用户
		sess.And("`type` = ? or `type` <> ?", topx.TYPE_NORMAL, topx.TYPE_SYS_ADMIN)
	case topx.TYPE_SECURITY:
		// 安全管理员
		if filter.DeptId != "" {
			sess.And("dept_id = ?", filter.DeptId)
		}
		sess.And("`dept_id` is not null or `type` = ? or `type` = ?", topx.TYPE_SYS_ADMIN, topx.TYPE_AUDIT)
	default:
		// 其他类型用户
		return nil, 0, nil
	}

	sess.Desc("created_at")
	sess.Limit(filter.GetLimit(), filter.GetOffset())
	count, err := sess.FindAndCount(&mods)
	return mods, int(count), err
}

func UserPageByProjectId(ctx context.Context, filter *model.UserPageByProjectIdArgs) ([]model.User, int, error) {
	mods := make([]model.User, 0, filter.GetLimit())
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)

	sess.Table("`user`").Join("LEFT", "`user_role_project_mapping`", "`user`.id = `user_role_project_mapping`.user_id").Where("project_id=?", filter.ProjectId)
	if filter.Name != "" {
		sess.And("`user`.`name` like concat('%',?,'%') or `alias` like concat('%',?,'%')", fixlike(filter.Name), fixlike(filter.Name))
	}
	sess.Select("`user`.*").GroupBy("`user`.id")
	sess.Limit(filter.GetLimit(), filter.GetOffset())
	count, err := sess.FindAndCount(&mods)
	return mods, int(count), err
}
