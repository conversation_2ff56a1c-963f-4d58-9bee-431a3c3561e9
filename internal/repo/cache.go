package repo

import (
	"context"
	"time"

	"code.mysugoncloud.com/hci/assist/globals"
	"code.mysugoncloud.com/hci/iam/model"
)

func IamCacheExist(ctx context.Context, mod *model.IamCache) bool {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	has, _ := sess.Exist(mod)
	return has
}

func CacheGet(ctx context.Context, mod *model.IamCache) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	if has, _ := sess.Get(mod); !has {
		return globals.ErrNotfound
	}
	return nil
}

func CacheAdd(ctx context.Context, mod *model.IamCache) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.Insert(mod); err != nil {
		sess.Rollback()
		return err
	}

	return sess.Commit()
}

func CacheEdit(ctx context.Context, mod *model.IamCache, cols ...string) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.ID(mod.Id).Cols(cols...).Update(mod); err != nil {
		sess.Rollback()
		return err
	}
	sess.Commit()
	return nil
}

func CacheDrop(ctx context.Context, mod *model.IamCache) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.Delete(mod); err != nil {
		sess.Rollback()
		return err
	}
	sess.Commit()
	return nil
}

func CacheDeleteExpired(ctx context.Context) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	sess.Table("iam_cache")
	sess.Where("expired_time < ?", time.Now())
	if _, err := sess.Delete(&model.IamCache{}); err != nil {
		sess.Rollback()
		return err
	}
	sess.Commit()
	return nil
}

// CacheKeyPrefixMatch 前缀匹配，例如 admin:*
func CacheKeyPrefixMatch(ctx context.Context, keyPrefix *string) ([]model.IamCache, error) {
	mods := make([]model.IamCache, 0)
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)

	if keyPrefix != nil && *keyPrefix != "" {
		sess.And("`key` like concat(?,'%')", fixlike(*keyPrefix))
	}
	err := sess.Find(&mods)
	return mods, err

}
