package repo

import (
	"context"

	"code.mysugoncloud.com/hci/assist/globals"
	"code.mysugoncloud.com/hci/iam/model"
)

func PolicyExist(ctx context.Context, mod *model.RamPolicy) bool {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	has, _ := sess.Exist(mod)
	return has
}

func PolicyGet(ctx context.Context, mod *model.RamPolicy) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	if has, _ := sess.Get(mod); !has {
		return globals.ErrNotfound
	}
	return nil
}

func PolicyAdd(ctx context.Context, mod *model.RamPolicy) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.Insert(mod); err != nil {
		sess.Rollback()
		return err
	}

	return sess.Commit()
}

func PolicyRoleAddBatch(ctx context.Context, mods []*model.RamPolicyRole) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.Insert(mods); err != nil {
		sess.Rollback()
		return err
	}

	return sess.Commit()
}

func PolicyEdit(ctx context.Context, mod *model.RamPolicy, cols ...string) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.ID(mod.Uuid).Cols(cols...).Update(mod); err != nil {
		sess.Rollback()
		return err
	}
	sess.Commit()
	return nil
}

func PolicyByUser(ctx context.Context, serviceId string, roleIds []string) ([]*model.RamPolicy, error) {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	mods := make([]*model.RamPolicy, 0)
	sess.Table("ram_policy_role").Distinct().Join("LEFT", "ram_policy", "ram_policy.uuid = ram_policy_role.policy_id").Where("ram_policy.owner_id IS NULL or ram_policy.owner_id = ''")
	if serviceId != "" {
		sess.And("ram_policy_role.service_id = ?", serviceId)
	}
	if len(roleIds) > 0 {
		sess.In("ram_policy_role.ram_role_id", roleIds)
	}
	sess.Select("ram_policy.*")
	sess.OrderBy("ram_policy.sort, ram_policy.policy_name")
	err := sess.Find(&mods)
	if err != nil {
		return nil, err
	}
	return mods, nil
}

func PolicyListByParentId(ctx context.Context, parentId string) ([]*model.RamPolicy, error) {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	mods := make([]*model.RamPolicy, 0)

	sess.And("parent_id = ?", parentId)
	err := sess.Find(&mods)
	if err != nil {
		return nil, err
	}
	return mods, nil
}

func PolicyDeleteRelation(ctx context.Context, ids []string) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()

	if _, err := sess.In("uuid", ids).Delete(&model.RamPolicy{}); err != nil {
		sess.Rollback()
		return err
	}
	if _, err := sess.In("policy_id", ids).Delete(&model.RamPolicyRole{}); err != nil {
		sess.Rollback()
		return err
	}

	return sess.Commit()
}

func PolicyRoleDropByRoleId(ctx context.Context, roleId, serviceId string) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if roleId != "" {
		sess.And("ram_role_id = ?", roleId)
	}
	if serviceId != "" {
		sess.And("service_id = ?", serviceId)
	}
	if _, err := sess.Delete(&model.RamPolicyRole{}); err != nil {
		sess.Rollback()
		return err
	}
	sess.Commit()
	return nil
}

func PolicyListNoOwner(ctx context.Context) ([]*model.RamPolicy, error) {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	mods := make([]*model.RamPolicy, 0)

	sess.And("owner_id is null or owner_id = ''")
	err := sess.Find(&mods)
	if err != nil {
		return nil, err
	}
	return mods, nil
}

func PolicyRoleListByRoleIds(ctx context.Context, roleIds []string) ([]*model.RamPolicyRole, error) {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	mods := make([]*model.RamPolicyRole, 0)

	sess.In("ram_role_id", roleIds)
	err := sess.Find(&mods)
	if err != nil {
		return nil, err
	}
	return mods, nil
}
