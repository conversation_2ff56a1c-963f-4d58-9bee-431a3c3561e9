package repo

import (
	"context"

	"code.mysugoncloud.com/hci/assist/globals"
	"code.mysugoncloud.com/hci/iam/model"
)

func QuotaMetricGet(ctx context.Context, mod *model.QuotaMetric) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	if has, _ := sess.Get(mod); !has {
		return globals.ErrNotfound
	}
	return nil
}

func QuotaTypeAll(ctx context.Context) ([]*model.QuotaType, error) {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	mods := make([]*model.QuotaType, 0)
	sess.Asc("sequence")
	err := sess.Find(&mods)
	if err != nil {
		return nil, err
	}
	return mods, nil
}

func QuotaMetricAll(ctx context.Context) ([]model.QuotaMetric, error) {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	mods := make([]model.QuotaMetric, 0)
	sess.Asc("sequence")
	err := sess.Find(&mods)
	if err != nil {
		return nil, err
	}
	return mods, nil
}

func QuotaMetricAllByNames(ctx context.Context, names []string) ([]model.QuotaMetric, error) {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	mods := make([]model.QuotaMetric, 0)
	if len(names) > 0 {
		sess.In("name", names)
	}
	sess.Asc("sequence")
	err := sess.Find(&mods)
	if err != nil {
		return nil, err
	}
	return mods, nil
}

func QuotaDeparmentValue(ctx context.Context, deptIds []string, regionId string) ([]*model.QuotaDepartmentValue, error) {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	mods := make([]*model.QuotaDepartmentValue, 0)
	if len(deptIds) > 0 {
		sess.In("department_id", deptIds)
	}
	if regionId != "" {
		sess.And("region_id = ?", regionId)
	}
	err := sess.Find(&mods)
	return mods, err
}

func QuotaProjectValue(ctx context.Context, projectIds []string, regionId string) ([]model.QuotaProjectValue, error) {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	mods := make([]model.QuotaProjectValue, 0)
	if len(projectIds) > 0 {
		sess.In("project_id", projectIds)
	}
	if regionId != "" {
		sess.And("region_id = ?", regionId)
	}
	err := sess.Find(&mods)
	return mods, err
}
