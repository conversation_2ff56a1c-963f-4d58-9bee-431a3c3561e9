package repo

import (
	"context"

	"code.mysugoncloud.com/hci/assist/globals"
	"code.mysugoncloud.com/hci/iam/model"
)

func UserAccessExist(ctx context.Context, mod *model.UserAccess) bool {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	has, _ := sess.Exist(mod)
	return has
}

func UserAccessGet(ctx context.Context, mod *model.UserAccess) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	if has, _ := sess.Get(mod); !has {
		return globals.ErrNotfound
	}
	return nil
}

func UserAccessAdd(ctx context.Context, mod *model.UserAccess) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.Insert(mod); err != nil {
		sess.Rollback()
		return err
	}

	return sess.Commit()
}

func UserAccessEdit(ctx context.Context, mod *model.UserAccess, cols ...string) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.ID(mod.Id).Cols(cols...).Update(mod); err != nil {
		sess.Rollback()
		return err
	}
	sess.Commit()
	return nil
}

func UserAccessDrop(ctx context.Context, mod *model.UserAccess) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.Delete(mod); err != nil {
		sess.Rollback()
		return err
	}
	sess.Commit()
	return nil
}
