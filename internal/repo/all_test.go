package repo

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"

	"code.mysugoncloud.com/hci/assist/rndm"
	"code.mysugoncloud.com/hci/assist/topx"
	"code.mysugoncloud.com/hci/iam/model"
)

func TestGetDepartmentTree(t *testing.T) {
	testInit()
	dept, err := DepartmentTreeByDeptId(context.Background(), "1acba556fe984b11af99784b4fde0931")
	if err != nil {
		t.Error(err)
	}
	jsonData, _ := json.Marshal(dept)
	fmt.Println("------------------")
	fmt.Println(string(jsonData))
}

func TestPolicyByUser(t *testing.T) {
	testInit()

	mods, _ := PolicyByUser(context.Background(), "ac988dbe28ec428893304640df20087f", []string{"c16c9944ca324aa693d3da6f7e7484f7"})
	jsonData, _ := json.Marshal(mods)
	fmt.Println("------------------")
	fmt.Println(string(jsonData))
}

func TestUserCreateSub(t *testing.T) {
	testInit()
	err := UserCreateSub(context.Background(), &model.User{Id: rndm.GUID(), Name: "test-test001", Type: topx.TYPE_DEPT_MASTER})
	if err != nil {
		t.Error(err.Error())
	}
}

func TestUserPageByProjectId(t *testing.T) {
	testInit()
	filter := &model.UserPageByProjectIdArgs{
		List: model.List{
			PageNum:  2,
			PageSize: 10,
		},
		ProjectId: "1 or 1=1",
	}
	mods, _, _ := UserPageByProjectId(context.Background(), filter)
	jsonData, _ := json.Marshal(mods)
	fmt.Println("------------------")
	fmt.Println(string(jsonData))
}
