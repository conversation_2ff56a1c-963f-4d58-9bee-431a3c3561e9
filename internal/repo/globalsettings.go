package repo

import (
	"context"

	"code.mysugoncloud.com/hci/assist/globals"
	"code.mysugoncloud.com/hci/iam/model"
)

func GlobalsettingGet(ctx context.Context, mod *model.Globalsettings) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	if has, _ := sess.Get(mod); !has {
		return globals.ErrNotfound
	}
	return nil
}

// GlobalsettingGetByType 获取全局设置
func GlobalsettingGetByType(ctx context.Context, policyType, policyName string) *model.Globalsettings {
	globalsetting := &model.Globalsettings{PolicyType: policyType, PolicyName: policyName}
	GlobalsettingGet(ctx, globalsetting)
	return globalsetting
}

func GlobalsettingAdd(ctx context.Context, mod *model.Globalsettings) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.Insert(mod); err != nil {
		sess.Rollback()
		return err
	}

	return sess.Commit()
}

func GlobalsettingEdit(ctx context.Context, mod *model.Globalsettings, cols ...string) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.ID(mod.Uuid).Cols(cols...).Update(mod); err != nil {
		sess.Rollback()
		return err
	}
	sess.Commit()
	return nil
}

func GlobalsettingDrop(ctx context.Context, mod *model.Globalsettings) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.Delete(mod); err != nil {
		sess.Rollback()
		return err
	}
	sess.Commit()
	return nil
}
