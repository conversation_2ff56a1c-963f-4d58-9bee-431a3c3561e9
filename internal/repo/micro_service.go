package repo

import (
	"context"

	"code.mysugoncloud.com/hci/assist/globals"
	"code.mysugoncloud.com/hci/iam/model"
)

func MicroServiceExist(ctx context.Context, mod *model.MicroService) bool {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	has, _ := sess.Exist(mod)
	return has
}

func MicroServiceCategoryExist(ctx context.Context, mod *model.MicroServiceCategory) bool {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	has, _ := sess.Exist(mod)
	return has
}

func MicroServiceGet(ctx context.Context, mod *model.MicroService) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	if has, _ := sess.Get(mod); !has {
		return globals.ErrNotfound
	}
	return nil
}

func MicroServiceCategoryGet(ctx context.Context, mod *model.MicroServiceCategory) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	if has, _ := sess.Get(mod); !has {
		return globals.ErrNotfound
	}
	return nil
}

func MicroServiceAdd(ctx context.Context, mod *model.MicroService) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.Insert(mod); err != nil {
		sess.Rollback()
		return err
	}

	return sess.Commit()
}

func MicroServiceEdit(ctx context.Context, mod *model.MicroService, cols ...string) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.ID(mod.Id).Cols(cols...).Update(mod); err != nil {
		sess.Rollback()
		return err
	}
	sess.Commit()
	return nil
}

func MicroServiceCategoryEdit(ctx context.Context, mod *model.MicroServiceCategory, cols ...string) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.ID(mod.Id).Cols(cols...).Update(mod); err != nil {
		sess.Rollback()
		return err
	}
	sess.Commit()
	return nil
}

func MicroServiceDrop(ctx context.Context, mod *model.MicroService) error {
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	sess.Begin()
	if _, err := sess.Delete(mod); err != nil {
		sess.Rollback()
		return err
	}
	sess.Commit()
	return nil
}

func MicroServiceCategoryList(ctx context.Context, filter *model.MicroServiceCategoryListArgs, cols ...string) ([]*model.MicroServiceCategory, int, error) {
	mods := make([]*model.MicroServiceCategory, 0, filter.GetLimit())
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)
	if len(cols) > 0 {
		sess.Cols(cols...)
	}
	if filter.Name != "" {
		sess.And("`name` like concat('%',?,'%')", fixlike(filter.Name))
	}

	sess.Asc("order")
	sess.Limit(filter.GetLimit(), filter.GetOffset())
	count, err := sess.FindAndCount(&mods)
	return mods, int(count), err
}

func MicroServiceAll(ctx context.Context) ([]*model.MicroService, error) {
	mods := make([]*model.MicroService, 0)
	sess := db.NewSession()
	defer sess.Close()
	sess.Context(ctx)

	sess.Asc("order")
	if err := sess.Find(&mods); err != nil {
		return nil, err
	}
	return mods, nil
}
