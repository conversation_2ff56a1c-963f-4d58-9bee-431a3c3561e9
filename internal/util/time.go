package util

import (
	"strconv"
	"time"
)

// 当前时间与传入时间的间隔天数
// 不足24h不算一天
func CalculateTotalDay(t time.Time) int {
	nowTime := time.Now()
	var diff time.Duration
	if nowTime.Before(t) {
		diff = t.Sub(nowTime)
	} else {
		diff = nowTime.Sub(t)
	}
	days := diff / (24 * time.Hour)
	return int(days)
}

// TimeStrToTime 字符串转时间 yyyy-MM-dd格式
func TimeStrToTime(timeStr string) (time.Time, error) {
	if timeStr == "" {
		return time.Time{}, nil
	}
	// 定义时间格式
	layout := "2006-01-02"
	// 将字符串转换为time.Time类型
	t, err := time.ParseInLocation(layout, timeStr, time.Local)
	if err != nil {
		return time.Time{}, err
	}
	return t, nil
}

// GetWeekByDate 获取今天为周几
func GetWeekByDate(nowTime time.Time) string {
	// 获取今天是星期几
	weekday := nowTime.Weekday()

	// 输出星期几的英文标识
	switch weekday {
	case time.Sunday:
		return "SUNDAY"
	case time.Monday:
		return "MONDAY"
	case time.Tuesday:
		return "TUESDAY"
	case time.Wednesday:
		return "WEDNESDAY"
	case time.Thursday:
		return "THURSDAY"
	case time.Friday:
		return "FRIDAY"
	case time.Saturday:
		return "SATURDAY"
	}
	return ""
}

// GetTimeHourStr 获取时间小时数
func GetTimeHourStr(dateTime time.Time) string {
	hour := dateTime.Hour()
	return strconv.Itoa(hour)
}

// TimeToStr time转字符串
func TimeToStr(t time.Time, layout string) string {
	if t.IsZero() {
		return ""
	}
	if layout == "" {
		layout = "2006-01-02 15:04:05"
	}
	return t.Format(layout)
}
