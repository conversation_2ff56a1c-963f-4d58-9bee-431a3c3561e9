package util

import (
	"errors"
	"strings"
	"time"

	"code.mysugoncloud.com/hci/iam/model"
	"github.com/golang-jwt/jwt/v5"
)

var mySecret = []byte("sugon-cloud-iam")

// MyClaims 业务数据
type MyClaims struct {
	Alias    string `json:"alias"`
	UserID   string `json:"userId"`
	UserName string `json:"username"`
	UserType string `json:"userType"`
	DeptId   string `json:"dept_id"`
	jwt.RegisteredClaims
}

func GenToken(user *model.User) (string, error) {
	// 创建我们自己声明的数据
	c := MyClaims{
		user.Alias,
		user.Id,
		user.Name,
		user.Type,
		user.DeptId,
		jwt.RegisteredClaims{
			IssuedAt: jwt.NewNumericDate(time.Now()), // 发布时间
			Issuer:   "sugon-cloud-iam",              // 签发人
		},
	}
	// 使用指定的签名方法创建签名对象
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, c)
	// 返回token
	return token.SignedString(mySecret)
}

// ParseToken 解析 JWT
func ParseToken(tokenString string) (*MyClaims, error) {
	mc := new(MyClaims)
	// 解析token
	token, err := jwt.ParseWithClaims(tokenString, mc, func(token *jwt.Token) (i interface{}, err error) {
		return mySecret, nil
	})
	if err != nil {
		if strings.Contains(err.Error(), "token is expired by") {
			return nil, errors.New("token已过期")
		}
		return nil, err
	}

	if token.Valid { // 校验token
		return mc, nil
	}
	return nil, errors.New("invalid token")
}
