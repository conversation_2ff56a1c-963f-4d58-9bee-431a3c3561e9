package util

import (
	"context"
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"errors"
	"strings"
	"time"

	"code.mysugoncloud.com/hci/assist/logs"
	"code.mysugoncloud.com/hci/iam/internal/repo"
	"code.mysugoncloud.com/hci/iam/model"
)

const PrivateKey string = "RSAPrivateKey"
const PublicKey string = "RSAPublicKey"

var KeyPairMap map[string]string

func GenKeyPair() map[string]string {
	privateKey, err := rsa.GenerateKey(rand.Reader, 1024)
	if err != nil {
		panic("生成 RSA 密钥对失败: " + err.Error())
	}
	// 将私钥转换为 ASN.1 PKCS#1 DER 编码
	privateDER := x509.MarshalPKCS1PrivateKey(privateKey)
	// 将 DER 编码的私钥转换为 PEM 格式
	privPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: privateDER,
	})

	basePrivate := string(privPEM)
	publicKey := &privateKey.PublicKey
	// 将公钥转换为 ASN.1 PKIX DER 编码
	pubDER, _ := x509.MarshalPKIXPublicKey(publicKey)
	// 将 DER 编码的公钥转换为 PEM 格式
	pubPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "RSA PUBLIC KEY",
		Bytes: pubDER,
	})
	basePub := pemform(pubPEM)
	return map[string]string{PrivateKey: basePrivate, PublicKey: basePub}
}

func pemform(pem []byte) string {
	basePrivate := string(pem)
	basePrivate = strings.Replace(basePrivate, "-----BEGIN RSA PUBLIC KEY-----", "", 1)
	basePrivate = strings.Replace(basePrivate, "-----END RSA PUBLIC KEY-----", "", 1)
	basePrivate = strings.Replace(basePrivate, "\n", "", -1)
	return basePrivate
}

func DecryptByPublicKey(ctx context.Context, publicKey, password string) (string, error) {
	if publicKey == "" {
		return password, nil
	}
	cache := &model.IamCache{Key: publicKey}
	err := repo.CacheGet(ctx, cache)
	if err != nil {
		return "", errors.New("公钥已被使用")
	}
	// 判断是否过期
	if time.Now().After(cache.ExpiredTime) {
		repo.CacheDrop(ctx, cache)
		return "", errors.New("公钥已过期")
	}
	repo.CacheDrop(ctx, cache)
	return DecryptByPrivateKey(password, cache.Value)
}

// EncryptByPublicKey 公钥加密
func EncryptByPublicKey(data, base64PublicKey string) string {
	pubDER, _ := base64.StdEncoding.DecodeString(base64PublicKey)
	pubKeyInterface, _ := x509.ParsePKIXPublicKey(pubDER)
	publicKey := pubKeyInterface.(*rsa.PublicKey)

	// 对原始数据进行加密
	message := []byte(data)
	encrypted, err := rsa.EncryptPKCS1v15(rand.Reader, publicKey, message)
	if err != nil {
		panic(err)
	}
	return string(encrypted)
}

// DecryptByPrivateKey 私钥解密
func DecryptByPrivateKey(data, basePrivateKey string) (string, error) {
	// 解析 PEM 编码的私钥
	block, _ := pem.Decode([]byte(basePrivateKey))
	if block == nil || block.Type != "RSA PRIVATE KEY" {
		logs.Ctx(context.Background()).Str("basePrivateKey", basePrivateKey).Info("解析私钥")
		return "", errors.New("解密失败")
	}

	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		logs.Ctx(context.Background()).Str("basePrivateKey", basePrivateKey).Info("解析私钥")
		return "", errors.New("解密失败")
	}
	password, _ := base64.StdEncoding.DecodeString(data)
	decrypted, err := rsa.DecryptPKCS1v15(rand.Reader, privateKey, password)
	if err != nil {
		return "", err
	}
	return string(decrypted), nil
}
