package util

// Difference 求两个字符串切片差集
func DifferenceStr(slice1, slice2 []string) []string {
	m := make(map[string]bool)
	for _, item := range slice2 {
		m[item] = true
	}

	var result []string
	for _, item := range slice1 {
		if !m[item] {
			result = append(result, item)
		}
	}

	return result
}

// RemoveDuplicatesStr 切片去重
func RemoveDuplicatesStr(slice []string) []string {
	if len(slice) == 0 {
		return slice
	}
	m := make(map[string]bool)
	var result []string
	for _, item := range slice {
		if !m[item] {
			m[item] = true
			result = append(result, item)
		}
	}
	return result
}

// RemoveElementInPlaceOptimized 切片去除某个元素
func RemoveElementInPlaceOptimized(slice []string, s string) []string {
	for i, v := range slice {
		if v == s {
			copy(slice[i:], slice[i+1:])
			slice[len(slice)-1] = "" // Optional: clear the last element
			return slice[:len(slice)-1]
		}
	}
	return slice
}
