package util

import (
	"context"
	"time"

	"code.mysugoncloud.com/hci/assist/globals"
	"code.mysugoncloud.com/hci/iam/internal/repo"
	"code.mysugoncloud.com/hci/iam/model"
)

// 获取缓存
func CacheGet(ctx context.Context, key string) (string, error) {
	mod := &model.IamCache{Key: key}
	err := repo.CacheGet(ctx, mod)
	if err != nil {
		return "", err
	}
	// 缓存已过期
	if time.Now().After(mod.ExpiredTime) {
		repo.CacheDrop(ctx, mod)
		return "", globals.ErrNotfound
	}
	return mod.Value, nil
}

// 添加缓存，如果key相同，则会覆盖
func CacheAdd(ctx context.Context, key string, value string, timeout time.Duration) error {
	mod := &model.IamCache{Key: key}
	err := repo.CacheGet(ctx, mod)
	if err != nil {
		mod.Value = value
		mod.ExpiredTime = time.Now().Add(timeout)
		return repo.CacheAdd(ctx, mod)
	}
	mod.Value = value
	mod.ExpiredTime = time.Now().Add(timeout)
	return repo.CacheEdit(ctx, mod, "value", "expired_time")
}

// 删除缓存
func CacheDrop(ctx context.Context, key string) error {
	mod := &model.IamCache{Key: key}
	err := repo.CacheGet(ctx, mod)
	if err != nil {
		return err
	}
	return repo.CacheDrop(ctx, mod)
}

// 重置缓存时间
func CacheResetExpiredTime(ctx context.Context, key string, timeout time.Duration) error {
	mod := &model.IamCache{Key: key}
	err := repo.CacheGet(ctx, mod)
	if err != nil {
		return err
	}
	mod.ExpiredTime = time.Now().Add(timeout)
	return repo.CacheEdit(ctx, mod, "expired_time")
}

// 延长缓存时间
func CacheExtendExpiredTime(ctx context.Context, key string, timeout time.Duration) error {
	mod := &model.IamCache{Key: key}
	err := repo.CacheGet(ctx, mod)
	if err != nil {
		return err
	}
	mod.ExpiredTime = mod.ExpiredTime.Add(timeout)
	return repo.CacheEdit(ctx, mod, "expired_time")
}

// 判断key是否存在
func CacheHasKey(ctx context.Context, key string) bool {
	mod := &model.IamCache{Key: key}
	return repo.IamCacheExist(ctx, mod)
}

// CacheEditValue 更新缓存
func CacheEditValue(ctx context.Context, key string, value string) error {
	mod := &model.IamCache{Key: key}
	err := repo.CacheGet(ctx, mod)
	if err != nil {
		return err
	}
	mod.Value = value
	return repo.CacheEdit(ctx, mod, "value")
}

// CacheEditValueResetExpired 更新缓存并重置缓存时间
func CacheEditValueResetExpired(ctx context.Context, key string, value string, timeout time.Duration) error {
	mod := &model.IamCache{Key: key}
	err := repo.CacheGet(ctx, mod)
	if err != nil {
		return err
	}
	mod.Value = value
	mod.ExpiredTime = time.Now().Add(timeout)
	return repo.CacheEdit(ctx, mod, "value")
}
