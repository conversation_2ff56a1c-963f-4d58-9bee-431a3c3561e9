package util

import (
	"testing"

	"code.mysugoncloud.com/hci/iam/model"
)

func TestToken(t *testing.T) {
	user := &model.User{Alias: "admin", Name: "admin", Type: "admin", DeptId: "2", Id: "0d2bbb018e8b44b985a169647379f413"}
	token, err := GenToken(user)
	if err != nil {
		t.Log(err.Error())
		return
	}
	t.Log("token=" + token)
	claim, err := ParseToken(token)
	if err != nil {
		t.Log(err)
		return
	}
	t.Log("userId=" + claim.UserID)
}
