<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.sugon.cloud</groupId>
        <artifactId>sugoncloud-iam</artifactId>
        <version>20250930-STABLE-SNAPSHOT</version>
    </parent>
    <artifactId>sugoncloud-iam-client</artifactId>
    <dependencies>
        <dependency>
            <groupId>com.sugon.cloud</groupId>
            <artifactId>sugoncloud-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sugon.cloud</groupId>
            <artifactId>sugoncloud-iam-common</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>knife4j-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>knife4j-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-validation</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--feign依赖-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
