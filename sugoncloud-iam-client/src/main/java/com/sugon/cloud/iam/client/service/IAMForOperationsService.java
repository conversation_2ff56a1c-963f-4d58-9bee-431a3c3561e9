package com.sugon.cloud.iam.client.service;

import com.sugon.cloud.common.model.IdNameInfo;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.common.model.dto.RegionInfoDTO;
import com.sugon.cloud.iam.common.model.dto.UserDeptAndUserIdsDTO;
import com.sugon.cloud.iam.common.model.dto.UserOwnDeptAndProjectDTO;
import com.sugon.cloud.iam.common.model.vo.ProjectUpdateByOperationVO;
import com.sugon.cloud.iam.common.model.vo.UserViewVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/16 11:12
 **/
@FeignClient(value = "sugoncloud-iam-api", url = "${sugoncloudIamApiUrl:}")
public interface IAMForOperationsService {

    @GetMapping("/api/users/{user_id}/departments-and-projects")
    ResultModel<UserOwnDeptAndProjectDTO> getUserOwnDepartmentsAndProjects(@PathVariable("user_id") String userId,
                                                                           @RequestParam(value = "project_type", required = false)String projectType);

    @GetMapping("/api/region/{id}/full-infos")
    ResultModel<RegionInfoDTO> fullDetail(@PathVariable("id") String id);

    @PostMapping("/api/users/by-ids")
    ResultModel<List<IdNameInfo>> userInfos(@RequestBody List<String> ids);

    @PostMapping("/api/departments/by-ids")
    ResultModel<List<IdNameInfo>> deptInfos(@RequestBody List<String> ids);

    @PutMapping("/api/projects/{id}/update-project-by-operations")
    ResultModel updateProjectByOperation(@PathVariable("id") String id,
                                         @RequestBody ProjectUpdateByOperationVO projectUpdateByOperationVO);

    @ApiOperation("根据组织id和用户id集合获取用户列表")
    @PostMapping("/api/users/getUsersByDeptIdAndUserIds")
    ResultModel<PageCL<UserViewVO>> getUsersByDeptIdAndUserIds(@RequestBody UserDeptAndUserIdsDTO userDeptAndUserIdsDTO);

    /**
     * 根据当前用户和组织id并且不包含已有的用户列表
     */
    @ApiOperation("根据当前用户和组织id并且不包含已有的用户列表")
    @PostMapping("/api/users/getUsersByCurrentUserIdAndDeptIdAndNotUserIds")
    ResultModel<PageCL<UserViewVO>> getUsersByCurrentUserIdAndDeptIdAndNotUserIds(@RequestBody UserDeptAndUserIdsDTO userDeptAndUserIdsDTO);
}
