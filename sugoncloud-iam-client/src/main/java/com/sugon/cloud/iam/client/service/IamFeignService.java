package com.sugon.cloud.iam.client.service;

import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.common.enums.BlacklistTypeEnum;
import com.sugon.cloud.iam.common.model.vo.*;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@FeignClient(value = "sugoncloud-iam-api", url = "${sugoncloudIamApiUrl:}")
//@RequestMapping
@Component
public interface IamFeignService {
    @GetMapping("/api/oauth/validate-token")
    ResultModel<ValidateTokenUserResponseVO> validateToken(@RequestParam("token") String token);

    /**
     * token 通过header传递
     *
     * @return
     */
    //req.setHeader("Authorization", "token")
    @GetMapping("/api/sca4/inner-validate")
    ResultModel<ValidateTokenUserResponseVO> validateToken();

    @PostMapping("/api/sca4/validate-sca4")
    ResultModel<ValidateTokenUserResponseVO> validateSCA4();

    @GetMapping("/api/roles/user/{user_id}")
    ResultModel<List<RoleResponseVO>> getRoleByUserId(@PathVariable("user_id") String userId,
                                                      @RequestParam(value = "project_id", required = false) String projectId);

    @GetMapping("/api/roles/{role_id}")
    ResultModel<RoleResponseVO> getRoleById(@PathVariable("role_id") String roleId);

    @GetMapping("/api/roles/inner")
    ResultModel<List<RoleResponseVO>> innerRole();

    @GetMapping("/api/projects/{id}")
    ResultModel<ProjectDetailVO> projectDetail(@PathVariable("id") String id);

    @GetMapping("/api/endpoint/{service_name}/{region_id}")
    ResultModel<CreateEndpointResponseParam> findByServiceNameAndRegionId(@PathVariable("service_name") String serviceName,
                                                                          @PathVariable("region_id") String regionId);

    /**
     * 通过project的id集合查询项目集合
     *
     * @param ids
     * @return
     */
    @GetMapping("/api/projects/by-ids")
    ResultModel<List<ProjectDetailVO>> listProjectsByIds(@RequestParam("ids") List<String> ids);

    @GetMapping("/api/globalsettings/info")
    ResultModel<GlobalsettingsEntityVo> getGlobalsettingsByname(@RequestParam(value = "policy_type") String type,
                                                                @RequestParam(value = "policy_key") String policyKey);

    @GetMapping("/api/projects/all")
    ResultModel<List<ProjectDetailVO>> getAllProjects();

    @PostMapping("/api/projects/without-depart")
    ResultModel<CreateWithDepartProjectVO> createWithoutDepart(@Validated @RequestBody CreateWithDepartProjectVO createWithDepartProjectVO);

    @GetMapping("/api/users/by-ids")
    ResultModel<List<UserVo>> users(@RequestParam("ids") List<String> ids);

    @PostMapping("/api/oauth/token")
    ResultModel login(@RequestBody LoginUserVO loginUserVO);

    @GetMapping("/api/departments/all")
    ResultModel<DepartmentTreeDetailVO> findAll();


    @GetMapping("/api/departments/{id}")
    ResultModel<DepartmentDetailVO> detail(@PathVariable("id") String id);

    @GetMapping("/api/departments/alls")
    ResultModel<List<DepartmentTreeDetailVO>> findAlls(@RequestParam(value = "user_id", required = false) String userId);

    @GetMapping("/api/departments/alls")
    ResultModel<List<DepartmentTreeDetailVO>> findAlls();

    @GetMapping("/api/users/{user_id}/projects")
    ResultModel<PageCL<ProjectDetailVO>> userProjects(@PathVariable("user_id") String userId,
                                                      @RequestParam(value = "name", required = false) String name,
                                                      @RequestParam(value = "page_num", defaultValue = "1", required = false) int pageNum,
                                                      @RequestParam(value = "page_size", defaultValue = "15", required = false) int pageSize);

    @GetMapping("/api/departments/{id}/projects")
    ResultModel<PageCL<ProjectDetailVO>> pageList(@PathVariable("id") String deptId,
                                                  @RequestParam(value = "name", required = false) String name,
                                                  @RequestParam(value = "query_sub", required = false, defaultValue = "false") Boolean querySub,
                                                  @RequestParam(value = "page_num", defaultValue = "1", required = false) int pageNum,
                                                  @RequestParam(value = "page_size", defaultValue = "15", required = false) int pageSize);

    @ApiOperation("通过用户ID查询用户列表")
    @GetMapping("/api/users/{user_id}/list")
    @ApiImplicitParams({@ApiImplicitParam(name = "user_id", value = "用户ID", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "page_num", value = "页码", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "page_size", value = "每页显示条数", paramType = "query", dataType = "int")})
    ResultModel<PageCL<UserViewVO>> userList(@PathVariable("user_id") String userId,
                                             @RequestParam(value = "page_num", defaultValue = "0", required = false) int pageNum,
                                             @RequestParam(value = "page_size", defaultValue = "15", required = false) int pageSize,
                                             HttpServletRequest request);

    /**
     * 用户详情
     *
     * @param userId 用户ID
     * @return 用户详情
     */
    @GetMapping("/api/users/{user_id}")
    ResultModel<UserViewVO> userDetail(@PathVariable("user_id") String userId);

    @GetMapping("/api/users/{user_id}/not-envelope")
    ResultModel<UserViewVO> userDetailNotEnvelope(@PathVariable("user_id") String userId);

    @GetMapping("/api/roles/all-forhash")
    ResultModel<List<String>> listUseForHash(@RequestParam(value = "user_id") String userId);

    @PostMapping("/api/temporary-secret-key")
    ResultModel creatTemporarySecretKsy(@RequestParam("time") Integer minute, @RequestParam(value = "policy", required = false) String policy);

    @GetMapping(value = "/api/micro-services")
    ResultModel list(@RequestParam(value = "name", required = false) String name,
                     @RequestParam(value = "collected", required = false) String collected,
                     @RequestParam(value = "need_policy", required = false, defaultValue = "true") boolean needPolicy,
                     @RequestParam(value = "page_num", defaultValue = "1", required = false) int pageNum,
                     @RequestParam(value = "page_size", defaultValue = "15", required = false) int pageSize);

    @GetMapping("/api/micro-services/all")
    ResultModel<List<MicroServiceVO>> listAll();

    @GetMapping("/api/region/for-feign")
    ResultModel<List<RegionVo>> regionListForFeign();

    @GetMapping("/api/roles/dept/{dept_id}")
    ResultModel<PageCL<RoleResponseVO>> listByDept(@PathVariable("dept_id") String deptId,
                                                   @RequestParam(value = "name", required = false) String name,
                                                   @RequestParam(value = "page_num", defaultValue = "1", required = false) int pageNum,
                                                   @RequestParam(value = "page_size", defaultValue = "15", required = false) int pageSize);

    @GetMapping("/api/users/all")
    ResultModel<PageCL<UserViewVO>> listAllUser(@RequestParam(value = "page_num", defaultValue = "0", required = false) int pageNum,
                                                @RequestParam(value = "page_size", defaultValue = "15", required = false) int pageSize,
                                                @RequestParam(value = "name", required = false) String name);

    @GetMapping("/api/users/by-project")
    ResultModel<PageCL<UserViewVO>> userRoles(@RequestParam(value = "page_num", defaultValue = "1", required = false) int pageNum,
                                              @RequestParam(value = "page_size", defaultValue = "15", required = false) int pageSize,
                                              @RequestParam(value = "project_id") String projectId,
                                              HttpServletRequest request);


    @GetMapping("/api/resource/user")
    ResultModel<String> getAllUser();


    @GetMapping("/api/resource/dept")
    ResultModel<String> getAllDept();


    @GetMapping("/api/resource/project")
    ResultModel<String> getAllProject();

    @GetMapping("/api/roles/all")
    ResultModel<PageCL<RoleResponseVO>> listByDept(@RequestParam(value = "name", required = false) String name
            , @RequestParam(value = "page_num", defaultValue = "1", required = false) int pageNum
            , @RequestParam(value = "page_size", defaultValue = "15", required = false) int pageSize);

    @GetMapping(value = "/api/blacklist-micro-service/list")
    List<BlacklistMicroServiceVo> getBlacklistMicroServiceVo(@RequestParam(name = "type") BlacklistTypeEnum type);

    @GetMapping(value = "/api/blacklist-micro-service/all")
    ResultModel<List<BlacklistMicroServiceVo>> getBlacklistMicroServiceAll(@RequestParam(name = "type") BlacklistTypeEnum type);

    @GetMapping("/api/roles/user-operations/{user_id}")
    ResultModel<List<RoleResponseVO>> listByUserIdForOperations(@PathVariable("user_id") String userId,
                                                                @RequestParam(value = "project_id", required = false) String projectId);

    @GetMapping("/api/projects/by-user-type")
    ResultModel<PageCL<ProjectDetailVO>> listAllUser(@RequestParam(value = "page_num", defaultValue = "0", required = false) int pageNum,
                                                     @RequestParam(value = "page_size", defaultValue = "15", required = false) int pageSize,
                                                     @RequestParam(value = "name", required = false) String name,
                                                     @RequestParam(value = "project_type", required = false) String projectType,
                                                     @RequestParam(value = "meter_type", required = false) String meterType);

    @GetMapping("/api/departments/top-dept/{user_id}")
    ResultModel<DepartmentDetailVO> topDept(@PathVariable("user_id") String userId);

    @GetMapping("/api/departments/top-dept/by-project-id/{project_id}")
    ResultModel<DepartmentDetailVO> topDeptByProjectId(@PathVariable("project_id") String projectId);

    @PostMapping("/api/messages/validate-code")
    ResultModel validateCode(@RequestParam Integer code,
                             @RequestParam(required = false, defaultValue = "true") Boolean expired);

    @GetMapping("/enc-dec/mfa-switch")
    boolean getMfaSwitch();

    @GetMapping("/api/oauth/need-mfa-code")
    ResultModel<Boolean> checkUserNeedmfaCode(@RequestParam("username") String username,
                                              @RequestParam(value = "business_type", required = false) String businessType);

    @GetMapping("/api/messages/danger-operation-url")
    ResultModel<List<DangerOperationUrlVO>> getDangerOperationUrl(@RequestParam("user_id") String userId);

    @PostMapping("/api/messages/notice")
    ResultModel<String> sendPlatformNotice(@RequestBody PlatformSendNoticeVO vo);

    @GetMapping("/api/users/dept/{dept_id}/list")
    ResultModel<PageCL<UserViewVO>> userListByDeptId(@PathVariable("dept_id") String deptId,
                                                     @RequestParam(value = "page_num", defaultValue = "0", required = false) int pageNum,
                                                     @RequestParam(value = "page_size", defaultValue = "15", required = false) int pageSize,
                                                     @RequestParam(value = "name", required = false) String name);

    @GetMapping("/api/departments/by/{dept_id}")
    ResultModel<List<DepartmentTreeDetailVO>> getDeptsByDeptId(@PathVariable("dept_id") String deptId);

    @GetMapping("/api/users/org/user")
    ResultModel<PageCL<UserViewVO>> listOrgUser(@RequestParam(value = "page_num", defaultValue = "0", required = false) int pageNum,
                                                @RequestParam(value = "page_size", defaultValue = "15", required = false) int pageSize,
                                                @RequestParam(value = "name", required = false) String name);
    @GetMapping("/api/departments/org/dept-ids")
    ResultModel<List<String>> orgDeptIds();

    @GetMapping("/api/sca4/validate/ak-sk")
    ResultModel<ValidateTokenUserResponseVO> validateAkSk(@RequestParam("token") String token);

    @ApiOperation("cce csi使用")
    @ApiImplicitParams({@ApiImplicitParam(name = "projectId", value = "凭证Id", required = true, dataType = "String", paramType = "path")})
    @GetMapping("/api/secret-key/{project_id}/secret")
    @DocumentIgnore
    ResultModel<SecretKeyVO> getAkSkByProjectId(@PathVariable("project_id") String projectId);
}
