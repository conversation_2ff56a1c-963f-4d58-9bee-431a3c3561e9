package com.sugon.cloud.iam.client.service;

import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.iam.common.model.vo.QuotaMetric;
import com.sugon.cloud.iam.common.model.vo.QuotaMetricAndProjectsUsedVO;
import com.sugon.cloud.iam.common.model.vo.QuotaMetricTopology;
import com.sugon.cloud.iam.common.model.vo.QuotaProjectValue;
import com.sugon.cloud.iam.common.model.vo.QuotaRootUserEntity;
import com.sugon.cloud.iam.common.model.vo.QuotaType;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "sugoncloud-iam-api", url = "${sugoncloudIamApiUrl:}")
//@RequestMapping
public interface QuotaFeignService {

    @GetMapping("/api/quotas/project")
    ResultModel<List<QuotaType>> getQuotaByProjectId(@RequestParam(value = "project_id", required = false) String projectId,
                                                     @RequestParam(value = "type_name", required = false) String typeName);

    @PutMapping("/api/quotas/project/used")
    ResultModel<QuotaProjectValue> updateProjectQuotaUsed(@Validated @RequestBody QuotaProjectValue quotaProjectValue);

    @GetMapping("/api/quotas/rootuser/overview")
    ResultModel<List<QuotaType>> getQuotaOverview(@RequestParam(value = "category_id", required = false) String categoryId);

    @GetMapping("/api/quotas/rootuser/list")
    ResultModel<List<QuotaRootUserEntity>> getRootUserQuotaList(@RequestParam(value = "type_name", required = false) String typeName,
                                                                @RequestParam(value = "department_name", required = false) String departmentName);

    @PutMapping("/api/quotas/rootuser/set")
    ResultModel<List<QuotaMetric>> updateRootUserQuota(@RequestBody List<QuotaMetric> quotaMetrics);

    @GetMapping("/api/quotas/topology")
    ResultModel<List<QuotaMetricTopology>> getTopology(@RequestParam(value = "type_name", required = false) String typeName,
                                                       @RequestParam(value = "department_id", required = false) String departmentId);

    /**
     * 查询组织配额
     *
     * @param departmentId
     * @param typeName
     * @param categoryId
     * @return
     */
    @GetMapping("/api/quotas/department")
    ResultModel<List<QuotaType>> getQuotaByDeparmentId(@RequestParam(value = "department_id") String departmentId,
                                                       @RequestParam(value = "type_name", required = false) String typeName,
                                                       @RequestParam(value = "category_id", required = false) String categoryId);

    /**
     * 更新组织配额
     *
     * @param departmentId
     * @param quotaType
     * @return
     */
    @PutMapping("/api/quotas/department/{department_id}")
    ResultModel<QuotaType> updateDepartmentQuota(@PathVariable("department_id") String departmentId,
                                                 @Validated @RequestBody QuotaType quotaType);

    @PutMapping("/api/quotas/department/total-used/{department_id}")
    @ApiOperation("更新根组织配额总量和使用量")
    ResultModel<QuotaType> updateDepartmentQuotaAndUsed(@PathVariable("department_id") String departmentId,
                                                        @Validated @RequestBody QuotaType quotaType);

    @GetMapping("/api/quotas/reset/use_total_quota")
    @ApiOperation("重置配额使用量和总量(用于初始化新增的配额指标同步)")
    ResultModel resetTotalQuotaByProjects(@RequestBody List<QuotaMetricAndProjectsUsedVO> quotaMetricAndProjectsUsedVOs);

    @PutMapping("/api/messages/synchronize-quota/{project_id}")
    ResultModel synchronizeSmsProjectQuota(@PathVariable("project_id") String projectId);
}
