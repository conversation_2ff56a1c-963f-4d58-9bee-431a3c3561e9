//go:generate swag init --instanceName iam -g internal/websvc/router.go
//go:generate protoc --proto_path=./iampb/ --go_out=./iampb/ --go_opt=paths=source_relative --go-grpc_out=./iampb/ --go-grpc_opt=paths=source_relative iampb.proto
package iam

import (
	"math/rand"
	"time"

	"code.mysugoncloud.com/hci/iam/iamrpc"
	"code.mysugoncloud.com/hci/iam/internal/repo"
	"code.mysugoncloud.com/hci/iam/internal/service"
	"code.mysugoncloud.com/hci/iam/internal/websvc"
	"github.com/labstack/echo/v4"
	"google.golang.org/grpc"

	"context"

	_ "github.com/go-sql-driver/mysql"
)

// InitMgmt 管控节点注册子模块
// 传入nil则不初始化对应服务
// 空字符串则进行默认数据库连接
func InitMgmt(ctx context.Context, dsn *string, rpc *grpc.Server, eng *echo.Echo) error {
	if dsn != nil {
		err := repo.Init(ctx, *dsn)
		if err != nil {
			return err
		}
	}
	if rpc != nil {
		err := iamrpc.InitRpc(ctx, rpc)
		if err != nil {
			return err
		}
	}
	if eng != nil {
		err := websvc.Init(eng)
		if err != nil {
			return err
		}
	}
	// 启动定时任务，刷新缓存
	go service.CacheTicker(ctx)
	// 初始化一次随机数生成器
	rand.Seed(time.Now().UnixNano())
	return nil
}

// InitMgmt 计算节点注册子模块
func InitWork(ctx context.Context, rpc *grpc.Server) error {
	return nil
}

// MgmtClose 管控节点子模块释放资源
func MgmtClose() error {
	return repo.Close()
}

// WorkClose 计算节点子模块释放资源
func WorkClose() error {
	return nil
}
