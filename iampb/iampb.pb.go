// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: iampb.proto

package iampb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateTokenArgs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
}

func (x *CreateTokenArgs) Reset() {
	*x = CreateTokenArgs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iampb_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTokenArgs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTokenArgs) ProtoMessage() {}

func (x *CreateTokenArgs) ProtoReflect() protoreflect.Message {
	mi := &file_iampb_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTokenArgs.ProtoReflect.Descriptor instead.
func (*CreateTokenArgs) Descriptor() ([]byte, []int) {
	return file_iampb_proto_rawDescGZIP(), []int{0}
}

func (x *CreateTokenArgs) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *CreateTokenArgs) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type CreaateTokenReoly struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *CreaateTokenReoly) Reset() {
	*x = CreaateTokenReoly{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iampb_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreaateTokenReoly) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreaateTokenReoly) ProtoMessage() {}

func (x *CreaateTokenReoly) ProtoReflect() protoreflect.Message {
	mi := &file_iampb_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreaateTokenReoly.ProtoReflect.Descriptor instead.
func (*CreaateTokenReoly) Descriptor() ([]byte, []int) {
	return file_iampb_proto_rawDescGZIP(), []int{1}
}

func (x *CreaateTokenReoly) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type ValidateTokenArgs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *ValidateTokenArgs) Reset() {
	*x = ValidateTokenArgs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iampb_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateTokenArgs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateTokenArgs) ProtoMessage() {}

func (x *ValidateTokenArgs) ProtoReflect() protoreflect.Message {
	mi := &file_iampb_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateTokenArgs.ProtoReflect.Descriptor instead.
func (*ValidateTokenArgs) Descriptor() ([]byte, []int) {
	return file_iampb_proto_rawDescGZIP(), []int{2}
}

func (x *ValidateTokenArgs) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type ValidateTokenReoly struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId   string `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId,omitempty"`
	UserName string `protobuf:"bytes,2,opt,name=userName,proto3" json:"userName,omitempty"`
	Alias    string `protobuf:"bytes,3,opt,name=alias,proto3" json:"alias,omitempty"`
	UserType string `protobuf:"bytes,4,opt,name=userType,proto3" json:"userType,omitempty"`
	DeptId   string `protobuf:"bytes,5,opt,name=deptId,proto3" json:"deptId,omitempty"`
}

func (x *ValidateTokenReoly) Reset() {
	*x = ValidateTokenReoly{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iampb_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateTokenReoly) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateTokenReoly) ProtoMessage() {}

func (x *ValidateTokenReoly) ProtoReflect() protoreflect.Message {
	mi := &file_iampb_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateTokenReoly.ProtoReflect.Descriptor instead.
func (*ValidateTokenReoly) Descriptor() ([]byte, []int) {
	return file_iampb_proto_rawDescGZIP(), []int{3}
}

func (x *ValidateTokenReoly) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ValidateTokenReoly) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *ValidateTokenReoly) GetAlias() string {
	if x != nil {
		return x.Alias
	}
	return ""
}

func (x *ValidateTokenReoly) GetUserType() string {
	if x != nil {
		return x.UserType
	}
	return ""
}

func (x *ValidateTokenReoly) GetDeptId() string {
	if x != nil {
		return x.DeptId
	}
	return ""
}

var File_iampb_proto protoreflect.FileDescriptor

var file_iampb_proto_rawDesc = []byte{
	0x0a, 0x0b, 0x69, 0x61, 0x6d, 0x70, 0x62, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x69,
	0x61, 0x6d, 0x70, 0x62, 0x22, 0x49, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x41, 0x72, 0x67, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x22,
	0x29, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52,
	0x65, 0x6f, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x29, 0x0a, 0x11, 0x56, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x72, 0x67, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x92, 0x01, 0x0a, 0x12, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x6f, 0x6c, 0x79, 0x12, 0x16, 0x0a, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x70, 0x74, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x70, 0x74, 0x49, 0x64, 0x32, 0x93, 0x01, 0x0a, 0x06, 0x49,
	0x61, 0x6d, 0x41, 0x70, 0x69, 0x12, 0x41, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x16, 0x2e, 0x69, 0x61, 0x6d, 0x70, 0x62, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x72, 0x67, 0x73, 0x1a, 0x18, 0x2e, 0x69,
	0x61, 0x6d, 0x70, 0x62, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x52, 0x65, 0x6f, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x46, 0x0a, 0x0d, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x18, 0x2e, 0x69, 0x61, 0x6d, 0x70,
	0x62, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x41,
	0x72, 0x67, 0x73, 0x1a, 0x19, 0x2e, 0x69, 0x61, 0x6d, 0x70, 0x62, 0x2e, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x6f, 0x6c, 0x79, 0x22, 0x00,
	0x42, 0x25, 0x5a, 0x23, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x6d, 0x79, 0x73, 0x75, 0x67, 0x6f, 0x6e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x68, 0x63, 0x69, 0x2f, 0x69, 0x61,
	0x6d, 0x2f, 0x69, 0x61, 0x6d, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_iampb_proto_rawDescOnce sync.Once
	file_iampb_proto_rawDescData = file_iampb_proto_rawDesc
)

func file_iampb_proto_rawDescGZIP() []byte {
	file_iampb_proto_rawDescOnce.Do(func() {
		file_iampb_proto_rawDescData = protoimpl.X.CompressGZIP(file_iampb_proto_rawDescData)
	})
	return file_iampb_proto_rawDescData
}

var file_iampb_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_iampb_proto_goTypes = []interface{}{
	(*CreateTokenArgs)(nil),    // 0: iampb.CreateTokenArgs
	(*CreaateTokenReoly)(nil),  // 1: iampb.CreaateTokenReoly
	(*ValidateTokenArgs)(nil),  // 2: iampb.ValidateTokenArgs
	(*ValidateTokenReoly)(nil), // 3: iampb.ValidateTokenReoly
}
var file_iampb_proto_depIdxs = []int32{
	0, // 0: iampb.IamApi.CreateToken:input_type -> iampb.CreateTokenArgs
	2, // 1: iampb.IamApi.ValidateToken:input_type -> iampb.ValidateTokenArgs
	1, // 2: iampb.IamApi.CreateToken:output_type -> iampb.CreaateTokenReoly
	3, // 3: iampb.IamApi.ValidateToken:output_type -> iampb.ValidateTokenReoly
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_iampb_proto_init() }
func file_iampb_proto_init() {
	if File_iampb_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_iampb_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateTokenArgs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iampb_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreaateTokenReoly); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iampb_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateTokenArgs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iampb_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateTokenReoly); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_iampb_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_iampb_proto_goTypes,
		DependencyIndexes: file_iampb_proto_depIdxs,
		MessageInfos:      file_iampb_proto_msgTypes,
	}.Build()
	File_iampb_proto = out.File
	file_iampb_proto_rawDesc = nil
	file_iampb_proto_goTypes = nil
	file_iampb_proto_depIdxs = nil
}
