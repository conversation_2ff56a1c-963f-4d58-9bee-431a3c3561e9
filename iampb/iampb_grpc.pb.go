// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.6
// - protoc             v5.27.0
// source: iampb.proto

package iampb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.59.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	IamApi_CreateToken_FullMethodName   = "/iampb.IamApi/CreateToken"
	IamApi_ValidateToken_FullMethodName = "/iampb.IamApi/ValidateToken"
)

// IamApiClient is the client API for IamApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type IamApiClient interface {
	// 获取镜像
	CreateToken(ctx context.Context, in *CreateTokenArgs, opts ...grpc.CallOption) (*CreaateTokenReoly, error)
	// 验证token
	ValidateToken(ctx context.Context, in *ValidateTokenArgs, opts ...grpc.CallOption) (*ValidateTokenReoly, error)
}

type iamApiClient struct {
	cc grpc.ClientConnInterface
}

func NewIamApiClient(cc grpc.ClientConnInterface) IamApiClient {
	return &iamApiClient{cc}
}

func (c *iamApiClient) CreateToken(ctx context.Context, in *CreateTokenArgs, opts ...grpc.CallOption) (*CreaateTokenReoly, error) {
	out := new(CreaateTokenReoly)
	err := c.cc.Invoke(ctx, IamApi_CreateToken_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamApiClient) ValidateToken(ctx context.Context, in *ValidateTokenArgs, opts ...grpc.CallOption) (*ValidateTokenReoly, error) {
	out := new(ValidateTokenReoly)
	err := c.cc.Invoke(ctx, IamApi_ValidateToken_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// IamApiServer is the server API for IamApi service.
// All implementations must embed UnimplementedIamApiServer
// for forward compatibility
type IamApiServer interface {
	// 获取镜像
	CreateToken(context.Context, *CreateTokenArgs) (*CreaateTokenReoly, error)
	// 验证token
	ValidateToken(context.Context, *ValidateTokenArgs) (*ValidateTokenReoly, error)
	mustEmbedUnimplementedIamApiServer()
}

// UnimplementedIamApiServer must be embedded to have forward compatible implementations.
type UnimplementedIamApiServer struct {
}

func (UnimplementedIamApiServer) CreateToken(context.Context, *CreateTokenArgs) (*CreaateTokenReoly, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateToken not implemented")
}
func (UnimplementedIamApiServer) ValidateToken(context.Context, *ValidateTokenArgs) (*ValidateTokenReoly, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateToken not implemented")
}
func (UnimplementedIamApiServer) mustEmbedUnimplementedIamApiServer() {}

// UnsafeIamApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to IamApiServer will
// result in compilation errors.
type UnsafeIamApiServer interface {
	mustEmbedUnimplementedIamApiServer()
}

func RegisterIamApiServer(s grpc.ServiceRegistrar, srv IamApiServer) {
	s.RegisterService(&IamApi_ServiceDesc, srv)
}

func _IamApi_CreateToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTokenArgs)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IamApiServer).CreateToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: IamApi_CreateToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IamApiServer).CreateToken(ctx, req.(*CreateTokenArgs))
	}
	return interceptor(ctx, in, info, handler)
}

func _IamApi_ValidateToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateTokenArgs)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IamApiServer).ValidateToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: IamApi_ValidateToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IamApiServer).ValidateToken(ctx, req.(*ValidateTokenArgs))
	}
	return interceptor(ctx, in, info, handler)
}

// IamApi_ServiceDesc is the grpc.ServiceDesc for IamApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var IamApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "iampb.IamApi",
	HandlerType: (*IamApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateToken",
			Handler:    _IamApi_CreateToken_Handler,
		},
		{
			MethodName: "ValidateToken",
			Handler:    _IamApi_ValidateToken_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "iampb.proto",
}
