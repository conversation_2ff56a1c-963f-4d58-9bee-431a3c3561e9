syntax = "proto3";
package iampb;
option go_package = "code.mysugoncloud.com/hci/iam/iampb";

service IamApi {
    // 获取镜像
    rpc CreateToken (CreateTokenArgs) returns (CreaateTokenReoly) {}
    // 验证token
    rpc ValidateToken (ValidateTokenArgs) returns (ValidateTokenReoly) {}
}

message CreateTokenArgs {
    string username = 1;
    string password = 2;
}

message CreaateTokenReoly {
    string token = 1;
}

message ValidateTokenArgs {
    string token = 1;
}

message ValidateTokenReoly {
    string userId = 1;
    string userName = 2;
    string alias = 3;
    string userType = 4;
    string deptId = 5;
}