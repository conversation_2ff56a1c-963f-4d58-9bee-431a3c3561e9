package iamrpc

import (
	"context"
	"errors"
	"time"

	"code.mysugoncloud.com/hci/assist/config"
	"code.mysugoncloud.com/hci/assist/logs"
	pb "code.mysugoncloud.com/hci/iam/iampb"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/keepalive"
	"google.golang.org/grpc/status"
)

type IamApiClient struct {
	conn *grpc.ClientConn
	pb.IamApiClient
}

func New() (*IamApiClient, error) {
	addr := config.GetMgmtAddr()
	kacp := keepalive.ClientParameters{Time: 60 * time.Second, Timeout: 40 * time.Second, PermitWithoutStream: true}
	conn, err := grpc.NewClient(addr,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithUnaryInterceptor(unaryInterceptor),
		grpc.WithStreamInterceptor(streamInterceptor),
		grpc.WithKeepaliveParams(kacp) /*grpc.WithDisableRetry()*/)
	if err != nil {
		logs.With().Err(err).Error("grpc.NewClient")
		return nil, err
	}
	//ctx, cancel := context.WithTimeout(context.Background(), 60 * time.Minute)
	//defer cancel()
	s := &IamApiClient{}
	s.conn = conn
	s.IamApiClient = pb.NewIamApiClient(conn)
	return s, nil
}

func (s *IamApiClient) Close() {
	s.conn.Close()
}

func unaryInterceptor(ctx context.Context, method string, req, reply any, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
	err := invoker(ctx, method, req, reply, cc, opts...)
	if err != nil {
		status, ok := status.FromError(err)
		if ok {
			return errors.New(status.Message())
		}
	}
	return err
}

func streamInterceptor(ctx context.Context, desc *grpc.StreamDesc, cc *grpc.ClientConn, method string, streamer grpc.Streamer, opts ...grpc.CallOption) (grpc.ClientStream, error) {
	s, err := streamer(ctx, desc, cc, method, opts...)
	if err != nil {
		status, ok := status.FromError(err)
		if ok {
			return s, errors.New(status.Message())
		}
	}
	return s, nil
}
