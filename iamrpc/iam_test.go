package iamrpc

import (
	"context"
	"os"
	"testing"
	"time"

	"code.mysugoncloud.com/hci/assist/config"
	"code.mysugoncloud.com/hci/assist/rpcx"
	pb "code.mysugoncloud.com/hci/iam/iampb"
	"code.mysugoncloud.com/hci/iam/internal/repo"
)

func initTest(t *testing.T) {
	os.Args = append(os.Args, "-log-path=", "-config=../conf.toml")
	t.Log("full args", os.Args[1:])
	err := config.PreInitMgmt(context.TODO())
	if err != nil {
		t.Fatal(err)
	}
	repo.Init(context.Background(), "root:database_sugon@tcp(172.22.1.57:3306)/iam_hci?charset=utf8mb4&parseTime=true&loc=Local")
	ctx := context.TODO()
	//t.Log(config.GetBasic())
	grpc := rpcx.NewRpc(ctx)
	InitRpc(ctx, grpc)
	//config.InitMgmt(ctx, grpc, nil)
	//t.Log(InitMgmt(ctx, config.Ptr(""), grpc, nil))
	go rpcx.RunRpc(ctx, grpc, "0.0.0.0:1009")
	time.Sleep(time.Second)
}

func TestQueryImage(t *testing.T) {
	initTest(t)
	client, err := New()
	if err != nil {
		t.Error(err)
	}
	token, err := client.CreateToken(context.TODO(), &pb.CreateTokenArgs{Username: "admin", Password: "keystone_sugon"})
	if err != nil {
		t.Error(err)
	}
	t.Log(token.Token)
}
