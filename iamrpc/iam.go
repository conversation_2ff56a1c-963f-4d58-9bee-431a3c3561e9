package iamrpc

import (
	"context"

	pb "code.mysugoncloud.com/hci/iam/iampb"
	"code.mysugoncloud.com/hci/iam/internal/service"
	"code.mysugoncloud.com/hci/iam/model"
)

func (s *IamApiServer) CreateToken(ctx context.Context, in *pb.CreateTokenArgs) (out *pb.CreaateTokenReoly, err error) {
	out = &pb.CreaateTokenReoly{}
	mod, err := service.CreateToken(ctx, &model.LoginUserArgs{Username: in.Username, Password: in.Password})
	if err != nil {
		return out, err
	}
	out.Token = mod
	return out, nil
}

func (s *IamApiServer) ValidateToken(ctx context.Context, in *pb.ValidateTokenArgs) (out *pb.ValidateTokenReoly, err error) {
	out = &pb.ValidateTokenReoly{}
	user, err := service.ValidateToken(ctx, in.Token)
	if err != nil {
		return out, err
	}
	out.UserId = user.Id
	out.UserName = user.Name
	out.UserType = user.Type
	out.Alias = user.Alias
	out.DeptId = user.DeptId
	return out, nil
}
