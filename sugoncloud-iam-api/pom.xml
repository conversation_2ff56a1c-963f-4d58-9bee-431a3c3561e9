<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.sugon.cloud</groupId>
        <artifactId>sugoncloud-iam</artifactId>
        <version>20250930-STABLE-SNAPSHOT</version>
    </parent>

    <artifactId>sugoncloud-iam-api</artifactId>

    <dependencies>
        <!--spring cloud-->
        <dependency>
            <groupId>com.sugon.cloud</groupId>
            <artifactId>sugoncloud-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-context</artifactId>
        </dependency>

        <!--spring boot-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-actuator</artifactId>
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-logging</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jersey</artifactId>
        </dependency>
        <!-- security -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <!--自省和监控的集成功能-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <!--注册中心-->
<!--       <dependency>-->
<!--            <groupId>com.alibaba.cloud</groupId>-->
<!--            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>-->
<!--        </dependency>-->
        <!--配置中心-->
<!--        <dependency>-->
<!--            <groupId>com.alibaba.cloud</groupId>-->
<!--            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>-->
<!--        </dependency>-->
        <!-- mybatis-plus -->

        <!--持久层-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--swagger-->
        <dependency>
            <groupId>com.spring4all</groupId>
            <artifactId>swagger-spring-boot-starter</artifactId>
        </dependency>
        <!-- 添加spring-boot-starter-log4j2的依赖包 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>

        <!-- 加上这个才能辨认到log4j2.yml文件 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>0.8.0</version>
        </dependency>
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
            <version>3.4.0</version>
        </dependency>
        <!--<dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>runtime</scope>
            <optional>true</optional>
        </dependency>-->
        <dependency>
            <groupId>com.sugon.cloud</groupId>
            <artifactId>sugoncloud-iam-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>
        <!--sugoncloud-log-client-->
        <dependency>
            <groupId>com.sugon.cloud</groupId>
            <artifactId>sugoncloud-log-client</artifactId>
            <!--和坚石诚信冲突-->
            <exclusions>
                <exclusion>
                    <artifactId>jna</artifactId>
                    <groupId>net.java.dev.jna</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--sugoncloud-policy-api-->
        <dependency>
            <groupId>com.sugon.cloud</groupId>
            <artifactId>sugoncloud-policy-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>druid</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>druid-spring-boot-starter</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.lettuce</groupId>
                    <artifactId>lettuce-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-community-db-support</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.integration</groupId>
            <artifactId>spring-integration-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.sugon.cloud</groupId>
            <artifactId>sugoncloud-xxl-job-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sugon.cloud</groupId>
            <artifactId>sugoncloud-redis-distributed-lock</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.sugon.cloud</groupId>-->
<!--            <artifactId>sugoncloud-license-client</artifactId>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>com.alibaba.cloud</groupId>-->
<!--                    <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->

        <!-- qr二维码生成依赖-->
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>3.5.1</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-ribbon</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sugon.cloud</groupId>
            <artifactId>sugoncloud-resource-auth-common</artifactId>
        </dependency>

        <dependency>
            <groupId>Fisherman</groupId>
            <artifactId>FishermanDsvs</artifactId>
            <version>1.0.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/dsvs_api.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>FishermanJCE</groupId>
            <artifactId>FishermanJCE</artifactId>
            <version>2.2.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/FishermanJCE-2.2.0.jar
            </systemPath>
        </dependency>
        <dependency>
            <groupId>Dean</groupId>
            <artifactId>bcprov</artifactId>
            <version>1.0.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/bcprov-jdk15on-147.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>Dean</groupId>
            <artifactId>dean_srj_api</artifactId>
            <version>5.7.6</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/dean_srj_api_v5.7.6.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>InfoSec</groupId>
            <artifactId>NetPassApi8</artifactId>
            <version>5.4.0.2</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/NetPassApi8-5.4.0.2.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>InfoSec</groupId>
            <artifactId>NetPassCommon8</artifactId>
            <version>5.4.0.2</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/NetPassCommon8-5.4.0.2.jar</systemPath>
        </dependency>
<!--        <dependency>
            <groupId>com.sugon.cloud</groupId>
            <artifactId>sugoncloud-operations-client</artifactId>
        </dependency>-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.1.6</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel-core</artifactId>
            <version>3.1.1</version>
        </dependency>
        <!--        坚石诚信双因子  start -->

        <!--        适配低版本jna-->
       <!-- <dependency>
            <groupId>com.ft.opt</groupId>
            <artifactId>jftauthng</artifactId>
            <version>1.0.1</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/jftauthng.jar</systemPath>
        </dependency>-->
        <!--        适配高版本jna 不支持序列化-->
        <dependency>
            <groupId>com.ft.opt</groupId>
            <artifactId>jftauthng</artifactId>
            <version>1.0.1</version>
            <scope>system</scope>
<!--            <systemPath>${project.basedir}/src/main/resources/lib/jftauthng1.2.3.jar</systemPath>-->
            <systemPath>${project.basedir}/src/main/resources/lib/jftauthng1.0.jar</systemPath>
        </dependency>
<!--        低版本的jna不支持aarch64-->
       <!-- <dependency>
            <groupId>com.ft.opt</groupId>
            <artifactId>jna</artifactId>
            <version>1.0.1</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/jna.jar</systemPath>
        </dependency>-->
        <!--        高版本的jna支持aarch64 不支持序列化-->
        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna</artifactId>
            <version>5.5.0</version>
        </dependency>

        <dependency>
            <groupId>com.ft.opt</groupId>
            <artifactId>idtrusttokenmanagerintf</artifactId>
            <version>2.5.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/IDtrustTokenManagerIntf_2.5.0.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.ft.opt</groupId>
            <artifactId>platform</artifactId>
            <version>1.0.1</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/platform.jar</systemPath>
        </dependency>
        <!--        坚石诚信双因子  end -->


        <dependency>
            <groupId>com.sugoncloudapi</groupId>
            <artifactId>sugoncloud-sdk-java</artifactId>
            <version>8.0.2</version>
        </dependency>
        <!-- Spring Boot 邮件发送 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>

        <!-- Thymeleaf 模板引擎 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>

        <dependency>
            <groupId>ognl</groupId>
            <artifactId>ognl</artifactId>
            <version>3.2.6</version>
        </dependency>
    </dependencies>
    <build>
        <finalName>sugoncloud-iam-api</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <executable>true</executable>
                    <mainClass>com.sugon.cloud.iam.api.SugonCloudIamApiApplication</mainClass>
                    <layout>JAR</layout>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                        <configuration>
                            <attach>false</attach>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
