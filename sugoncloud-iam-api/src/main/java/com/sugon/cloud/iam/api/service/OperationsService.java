package com.sugon.cloud.iam.api.service;

import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.vo.ProjectContractOptionQueryParamVO;
import com.sugon.cloud.iam.api.vo.ProjectContractVO;

/**
 * @Author: yangdingshan
 * @Date: 2024/6/19 17:19
 * @Description:
 */
public interface OperationsService {

    /**
     * 查询合同列表
     *
     * @param projectContractOptionQueryParamVO
     * @return
     */
    ResultModel<PageCL<ProjectContractVO>> projectContractOptions(ProjectContractOptionQueryParamVO projectContractOptionQueryParamVO);


    /**
     * 查询合同列表
     *
     * @param pageNum
     * @param pageSize
     * @param orgId
     * @param contractType
     * @param meterType
     * @param paymentType
     * @param saleUser
     * @param startTime
     * @param endTime
     * @param projectId
     * @param resourceId
     * @param name
     * @param contractCode
     * @return
     */
    ResultModel<PageCL<ProjectContractVO>> list(int pageNum,
                                                int pageSize,
                                                String orgId,
                                                String contractType,
                                                String meterType,
                                                String paymentType,
                                                String saleUser,
                                                String startTime,
                                                String endTime,
                                                String projectId,
                                                String resourceId,
                                                String name,
                                                String contractCode);

    /**
     * 根据项目id查询所有审批流程
     *
     * @param projectId
     * @param approvalStatus
     * @return
     */
    ResultModel getAllApplyListByProjectId(String projectId, String approvalStatus);
}
