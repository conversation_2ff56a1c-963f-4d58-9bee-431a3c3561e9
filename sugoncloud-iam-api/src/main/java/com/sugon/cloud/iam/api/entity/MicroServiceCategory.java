package com.sugon.cloud.iam.api.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sugon.cloud.common.model.entity.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * (MicroServiceCategory)实体类
 *
 * <AUTHOR>
 * @since 2021-04-23 09:45:33
 */
@Data
@TableName("micro_service_category")
public class MicroServiceCategory extends BaseEntity {
    private String name;
    private String description;
    @TableField("`order`")
    private Double order;
    private Boolean navHidden;
    private String type;
}
