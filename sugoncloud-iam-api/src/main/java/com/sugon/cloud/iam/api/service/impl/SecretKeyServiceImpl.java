package com.sugon.cloud.iam.api.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.entity.SecretKeyEntity;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.mapper.SecretKeyMapper;
import com.sugon.cloud.iam.api.service.SecretKeyService;

import com.sugon.cloud.iam.api.service.UserService;
import com.sugon.cloud.iam.common.model.vo.ValidateTokenUserResponseVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Base64;
import java.util.List;
import java.util.Objects;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class SecretKeyServiceImpl extends ServiceImpl<SecretKeyMapper, SecretKeyEntity> implements SecretKeyService {

    private final SecretKeyMapper secretKeyMapper;

    private final UserService userService;

    @Override
    public PageCL<SecretKeyEntity> getSecretKeyPage(List<String> userIds, int pageNumber, int pageSize) {
        IPage<SecretKeyEntity> page = new Page(pageNumber, pageSize);
        IPage<SecretKeyEntity> secretPage = secretKeyMapper.selectPage(page, new QueryWrapper<SecretKeyEntity>().lambda()
                .in(SecretKeyEntity::getUserId, userIds)
                .eq(SecretKeyEntity::isInnerKey, false));
        secretPage.getRecords().forEach(e->e.setSecretKey(null));
        PageCL<SecretKeyEntity> pageCL = new PageCL<>();
        pageCL.setPageSize(pageSize);
        pageCL.setPageNum(pageNumber);
        pageCL.setList(secretPage.getRecords());
        pageCL.setTotal((int)secretPage.getTotal());
        pageCL.setPageCount((int) ((secretPage.getTotal()+pageSize-1) / pageSize));
        return pageCL;
    }

    @Override
    public ResultModel<ValidateTokenUserResponseVO> validateBasicToken(String token) {
        try {
            log.debug("csi token: {}", token);
            if (StringUtils.isEmpty(token)) return ResultModel.error("token不能为空");
            if (token.startsWith("Basic ")) {
                token = token.substring(6);
            }
            token = new String(Base64.getDecoder().decode(token));
            if (!token.contains(":")) {
                log.error("token格式错误");
                return ResultModel.error("token格式错误");
            }
            String[] tokens = token.split(":");
            String accessKey = tokens[0];
            SecretKeyEntity secretKeyEntity = this.getOne(new LambdaQueryWrapper<SecretKeyEntity>()
                    .eq(SecretKeyEntity::getAccessKey, accessKey));
            if (Objects.isNull(secretKeyEntity)) {
                log.error("未查询到认证数据");
                return ResultModel.error("未查询到认证数据");
            }
            if (StringUtils.isBlank(tokens[1]) || !tokens[1].trim().equals(secretKeyEntity.getSecretKey())) {
                log.error("token验证失败");
                return ResultModel.error("token验证失败");
            }
            String userId = secretKeyEntity.getUserId();
            User user = userService.getById(userId);
            if (user == null) {
                log.error("从token中解析出的user id {} 无效", userId);
                return ResultModel.error("无效的认证信息");
            }
            ValidateTokenUserResponseVO.ValidateTokenUserResponseVOBuilder builder = ValidateTokenUserResponseVO.builder();
            // 允许多人登录
            builder.loginElsewhere(false);
            return ResultModel.success("", builder.userId(userId)
                    .username(user.getName())
                    .userType(user.getType())
                    .build());
        } catch (Exception e) {
            log.error("validate ak-sk error ",e);
        }
        return ResultModel.error("validate ak-sk error");
    }
}
