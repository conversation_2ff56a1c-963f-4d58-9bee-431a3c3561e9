package com.sugon.cloud.iam.api.controller.keystone;

import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.iam.api.entity.keystone.CreateEndpointResponse;
import com.sugon.cloud.iam.api.entity.keystone.ListEndpointResponse;
import com.sugon.cloud.iam.api.entity.keystone.ListResourceLinkResponse;
import com.sugon.cloud.iam.api.entity.keystone.VO.CreateEndpointVO;
import com.sugon.cloud.iam.api.entity.exception.KeyStoneMethodArgumentNotValidException;
import com.sugon.cloud.iam.api.service.EndpointService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;


@RestController
@RequestMapping("/n/endpoints")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Api(tags = {"keystone-endpoint操作"})
@DocumentIgnore
public class EndpointNativeController {

    private final EndpointService endpointService;

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @ApiOperation(value = "创建endpoint")
    public CreateEndpointResponse createEndpoint(@RequestBody CreateEndpointVO createEndpointVO) {
        if (Objects.isNull(createEndpointVO.getEndpoint())) {
            throw new KeyStoneMethodArgumentNotValidException("create_endpoint() param endpoint can not be null");
        }
        if (Objects.isNull(createEndpointVO.getEndpoint().getInterfaceParam())) {
            throw new KeyStoneMethodArgumentNotValidException("create_endpoint() param endpoint.interface can not be null");
        }
        if (Objects.isNull(createEndpointVO.getEndpoint().getUrl())) {
            throw new KeyStoneMethodArgumentNotValidException("create_endpoint() param endpoint.url can not be null");
        }
        if (Objects.isNull(createEndpointVO.getEndpoint().getServiceId())) {
            throw new KeyStoneMethodArgumentNotValidException("create_endpoint() param endpoint.service_id can not be null");
        }
        return endpointService.createEndpoint(createEndpointVO);
    }

    @GetMapping
    @ApiOperation(value = "查询endpoint")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "interface", value = "接口", required = false, dataType = "String")
            , @ApiImplicitParam(name = "service_id", value = "服务id", required = false, dataType = "String")
            , @ApiImplicitParam(name = "region_id", value = "区域id", required = false, dataType = "String")})
    public ListEndpointResponse listEndpoint(@RequestParam(value = "interface", required = false) String interfaceParam,
                                             @RequestParam(value = "service_id", required = false) String serviceId,
                                             @RequestParam(value = "region_id", required = false) String regionId,
                                             HttpServletRequest request) {
        ListEndpointResponse listEndpointResponse = new ListEndpointResponse();
        ListResourceLinkResponse listResourceLinkResponse = new ListResourceLinkResponse();
        String queryString = request.getQueryString();
        StringBuffer requestURL = request.getRequestURL();
        if (!StringUtils.isEmpty(queryString)) {
            requestURL.append("?").append(queryString);
        }
        listResourceLinkResponse.setSelf(requestURL.toString());
        listEndpointResponse.setEndpoints(endpointService.listEndpoint(interfaceParam, serviceId,regionId));
        listEndpointResponse.setLinks(listResourceLinkResponse);
        return listEndpointResponse;
    }

    @GetMapping("/{endpoint_id}")
    @ApiOperation(value = "查询endpoint详情")
    public CreateEndpointResponse endpointDetail(@PathVariable("endpoint_id") String endpointId) {
        return endpointService.endpointDetail(endpointId);
    }
}
