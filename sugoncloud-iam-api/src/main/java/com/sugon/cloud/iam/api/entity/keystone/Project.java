package com.sugon.cloud.iam.api.entity.keystone;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sugon.cloud.common.model.entity.BaseEntity;
import com.sugon.cloud.iam.api.handler.StringListTypeHandler;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@TableName(value = "project", autoResultMap = true)
public class Project extends BaseEntity {
    @TableId(type = IdType.UUID)
    private String id;
    private String name;
    @JsonProperty("domain_id")
    private String domainId;
    private String description;
    private boolean enabled;
    @JsonProperty("is_domain")
    @TableField("is_domain")
    private boolean domain;
    @JsonProperty("parent_id")
    private String parentId;
    @JsonProperty("dept_id")
    private String deptId;
    private String alias;

    /**
     *
     * 项目类型：正式项目(formal)、试用项目(test)
     */
    private String type;
    @TableField(value = "meter_types", typeHandler = StringListTypeHandler.class)
    private List<String> meterTypes;
    private Date startTime;
    private Date endTime;
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String taskStatus;
    // 自服务送审类型
    @JsonProperty("operation_approve_type")
    private String operationApproveType;
    public boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public boolean getDomain() {
        return domain;
    }

    public void setDomain(boolean domain) {
        this.domain = domain;
    }
}
