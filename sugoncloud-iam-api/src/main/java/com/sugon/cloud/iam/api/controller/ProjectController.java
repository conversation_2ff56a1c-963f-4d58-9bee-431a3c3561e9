package com.sugon.cloud.iam.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.constants.HeaderParamConstant;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.common.mapper.ModelMapper;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.entity.UserRoleMapping;
import com.sugon.cloud.iam.api.entity.keystone.Project;
import com.sugon.cloud.iam.api.service.EsSearchService;
import com.sugon.cloud.iam.api.service.ProjectService;
import com.sugon.cloud.iam.api.service.UserRoleMappingService;
import com.sugon.cloud.iam.api.service.resourceauth.DeptResourceAuthHandler;
import com.sugon.cloud.iam.api.service.resourceauth.ProjectResourceAuthHandler;
import com.sugon.cloud.iam.api.utils.CommonUtils;
import com.sugon.cloud.iam.api.vo.CreateOrUpdateDepartmentVO;
import com.sugon.cloud.iam.api.vo.CreateOrUpdateProjectVO;
import com.sugon.cloud.iam.api.vo.ProjectContractVO;
import com.sugon.cloud.iam.api.vo.ProjectResourceVO;
import com.sugon.cloud.iam.api.vo.UpdateProjectOperationTypeVO;
import com.sugon.cloud.iam.common.model.vo.CreateWithDepartProjectVO;
import com.sugon.cloud.iam.common.model.vo.ProjectDetailVO;
import com.sugon.cloud.iam.common.model.vo.ProjectUpdateByOperationVO;
import com.sugon.cloud.log.client.starter.annotation.LogRecordAnnotation;
import com.sugon.cloud.resource.auth.common.aop.ResourceAuth;
import io.jsonwebtoken.lang.Collections;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 项目(Project)表控制层
 *
 * <AUTHOR>
 * @since 2021-04-12 10:51:58
 */
@RestController
@RequestMapping("/api/projects")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Api(tags = {"项目API"})
public class ProjectController {
    /**
     * 服务对象
     */
    private final ProjectService projectService;
    private final EsSearchService esSearchService;
    private final ModelMapper mapper;
    @Value("${operations.enabled:false}")
    private boolean operationsEnabled;

    private final UserRoleMappingService userRoleMappingService;

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/{id}")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "项目ID", required = true, dataType = "String", paramType = "path")})
    @ApiOperation("查询项目详情")
    @LogRecordAnnotation(value = "查询项目详情", detail = "查询项目详情：{PROJECT{#id}}", resourceId = "{{#id}}", resource = "{PROJECT{#id}}")
    @ResourceAuth(handler = ProjectResourceAuthHandler.class, resources = "#id")
    public ResultModel<ProjectDetailVO> detail(@PathVariable("id") String id) {
        return ResultModel.success(this.projectService.queryById(id));
    }

    @GetMapping("admin-default")
    @ApiOperation("admin默认租户")
    @DocumentIgnore
    @ResourceAuth(handler = ProjectResourceAuthHandler.class, resources = "#id")
    public ResultModel<ProjectDetailVO> getAdminDefaultProject() {
        return ResultModel.success(this.projectService.queryById("admin-inner-project"));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param ids 查询体
     * @return 单条数据
     */
    @GetMapping("/by-ids")
    @ApiImplicitParams({@ApiImplicitParam(name = "ids", value = "项目ID集合", required = true, dataType = "String", paramType = "query")})
    @ApiOperation("查询项目详情")
    @ResourceAuth(handler = ProjectResourceAuthHandler.class, resources = "#ids")
    public ResultModel<List<ProjectDetailVO>> listByIds(@RequestParam("ids") List<String> ids) {
        if (Collections.isEmpty(ids)) {
            return ResultModel.success(Lists.newArrayList());
        }
        Collection<Project> projects = this.projectService.listByIds(ids);
        projects.stream().forEach(p -> p.setName(p.getAlias()));
        return ResultModel.success(projects);
    }

    @PostMapping
    @ApiOperation("创建项目")
    @LogRecordAnnotation(value = "创建项目", detail = "创建项目:{{#createOrUpdateProjectVO.name}}", resourceId = "{{#id}}", resource = "{{#createOrUpdateProjectVO.name}}")
    @ResourceAuth(handler = DeptResourceAuthHandler.class, resources = "#createOrUpdateProjectVO.deptId")
    public ResultModel<CreateOrUpdateProjectVO> create(@Validated @RequestBody CreateOrUpdateProjectVO createOrUpdateProjectVO) {
        projectService.insert(createOrUpdateProjectVO);
        return ResultModel.success("创建项目成功", createOrUpdateProjectVO);
    }

    @PostMapping("/without-depart")
    @ApiOperation("创建无组织项目")
    @LogRecordAnnotation(value = "创建无组织项目", detail = "创建项目:{{#createWithDepartProjectVO.name}}")
    @DocumentIgnore
    public ResultModel<CreateWithDepartProjectVO> createWithoutDepart(@Validated @RequestBody CreateWithDepartProjectVO createWithDepartProjectVO) {
        projectService.insertProjectWithoutDepart(createWithDepartProjectVO);
        return ResultModel.success("创建项目成功", createWithDepartProjectVO);
    }

    @PutMapping("/{id}")
    @ApiOperation("修改项目")
    @LogRecordAnnotation(value = "修改项目", detail = "修改项目:{{#createOrUpdateProjectVO.name}}", resourceId = "{{#id}}", resource = "{{#createOrUpdateProjectVO.name}}")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "项目ID", required = true, dataType = "String", paramType = "path")})
    @ResourceAuth(handler = ProjectResourceAuthHandler.class, resources = "#id")
    public ResultModel<CreateOrUpdateDepartmentVO> update(@PathVariable("id") String id,
                                                          @Validated @RequestBody CreateOrUpdateProjectVO createOrUpdateProjectVO) {
        try {
            createOrUpdateProjectVO.setId(id);
            projectService.update(createOrUpdateProjectVO);
            return ResultModel.success("修改项目成功", createOrUpdateProjectVO);
        } catch (Exception e) {
            if (CommonUtils.isContainChinese(e.getMessage())) {
                return ResultModel.error(e.getMessage());
            }
            return ResultModel.error("修改项目成功失败");
        }

    }

    /**
     * 通过主键删除单条数据
     *
     * @param id 主键
     */
    @DeleteMapping("/{id}")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "项目ID", required = true, dataType = "String", paramType = "path")})
    @ApiOperation("删除项目")
    @LogRecordAnnotation(value = "删除项目", detail = "删除项目:{{#_ret.resource}}", resourceId = "{{#id}}", resource = "{PROJECT{#id}}")
    @ResourceAuth(handler = ProjectResourceAuthHandler.class, resources = "#id")
    public ResultModel delete(HttpServletRequest req, @PathVariable("id") String id) {
        String regionId = req.getHeader(HeaderParamConstant.REGION_ID);
        if (StringUtils.isEmpty(regionId)) {
            return ResultModel.error("请选择区域");
        }
        return this.projectService.deleteById(id, regionId);
    }

    @GetMapping("/all")
    @ResourceAuth(handler = ProjectResourceAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    @ApiOperation("查询所有项目")
    public ResultModel<List<ProjectDetailVO>> getAllProjects() {
        return ResultModel.success(projectService.getAllProjects());
    }

    @GetMapping("/{id}/resources")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "项目ID", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "page_num", value = "页码", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "page_size", value = "每页显示条数", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "type", value = "服务类型", paramType = "query", dataType = "String")})
    @ApiOperation("查询项目的所有资源")
    @ResourceAuth(handler = ProjectResourceAuthHandler.class, resources = "#id")
    @Deprecated
    @DocumentIgnore
    public ResultModel<List<Map<String, Object>>> getAllResourceById(HttpServletRequest req,
                                                                     @PathVariable("id") String id,
                                                                     @RequestParam(value = "page_num", defaultValue = "1", required = false) int pageNum,
                                                                     @RequestParam(value = "page_size", defaultValue = "10", required = false) int pageSize,
                                                                     @RequestParam(value = "type", required = false) String type) {
        String regionId = req.getHeader(HeaderParamConstant.REGION_ID);
        if (StringUtils.isEmpty(regionId)) {
            return ResultModel.error("请选择区域");
        }
        Long total = esSearchService.searchResourceCount(regionId, id, type);
        PageCL<Map<String, Object>> pageCL = new PageCL();
        int pageCount = (Integer.parseInt(String.valueOf(total)) + pageSize - 1) / pageSize;
        pageCL.setPageCount(pageCount);
        pageCL.setTotal(Integer.parseInt(String.valueOf(total)));
        pageCL.setPageNum(pageNum);
        pageCL.setPageSize(pageSize);
        pageCL.setSize(pageSize);
        pageCL.setList(esSearchService.searchResource(regionId, id, type, pageNum, pageSize));
        return ResultModel.success("查询项目的所有资源成功", pageCL);
    }

    @ApiOperation("根据用户类型查询项目列表")
    @GetMapping("/by-user-type")
    @ApiImplicitParams({@ApiImplicitParam(name = "page_num", value = "页码", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "page_size", value = "每页显示条数", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "name", value = "项目名称", paramType = "query", dataType = "String")})
    public ResultModel<PageCL<ProjectDetailVO>> listAllUser(HttpServletRequest req,
                                                            @RequestParam(value = "page_num", defaultValue = "0", required = false) int pageNum,
                                                            @RequestParam(value = "page_size", defaultValue = "15", required = false) int pageSize,
                                                            @RequestParam(value = "name", required = false) String name,
                                                            @RequestParam(value = "project_type", required = false) String projectType,
                                                            @RequestParam(value = "meter_type", required = false) String meterType) {
        try {
            PageCL<ProjectDetailVO> userPage = projectService.getAllProjectsByUserType(req.getHeader(HeaderParamConstant.USER_ID), pageNum, pageSize, name, projectType, meterType);
            return ResultModel.success(userPage);
        } catch (Exception e) {
            if (CommonUtils.isContainChinese(e.getMessage())) {
                return ResultModel.error(e.getMessage());
            }
            return ResultModel.error("根据用户类型查询项目列表失败");
        }
    }

    @ApiOperation("根据组织查询项目列表")
    @GetMapping("/by-department-all")
    @ApiImplicitParams({@ApiImplicitParam(name = "page_num", value = "页码", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "page_size", value = "每页显示条数", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "name", value = "项目名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "department_id", value = "组织ID", paramType = "query", dataType = "String")})
    @ResourceAuth(handler = DeptResourceAuthHandler.class, resources = "#departmentId")
    public ResultModel<PageCL<ProjectDetailVO>> listAllDepartment(HttpServletRequest req,
                                                                  @RequestParam(value = "page_num", defaultValue = "0", required = false) int pageNum,
                                                                  @RequestParam(value = "page_size", defaultValue = "10", required = false) int pageSize,
                                                                  @RequestParam(value = "name", required = false) String name,
                                                                  @RequestParam(value = "department_id") String departmentId
            , @RequestParam(value = "project_type", required = false) String projectType
            , @RequestParam(value = "meter_type", required = false) String meterType) {
        try {
            PageCL<ProjectDetailVO> projectPage = projectService.listAllDepartment(req.getHeader(HeaderParamConstant.USER_ID), departmentId, pageNum, pageSize, name, projectType, meterType);
            return ResultModel.success(projectPage);
        } catch (Exception e) {
            return ResultModel.error("根据组织查询项目列表");
        }
    }


    @ApiOperation("根据合同查询项目列表下拉项")
    @GetMapping("/contract-options")
    @ApiImplicitParams({@ApiImplicitParam(name = "name", value = "项目名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "meter_type", value = "计费类型（统一计费：contract_meter_unification | 合同计费：contract_meter_contract）", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "department_id", value = "组织ID", paramType = "query", dataType = "String")})
    @DocumentIgnore
    public ResultModel<List<ProjectDetailVO>> listContractOptions(@RequestHeader(value = HeaderParamConstant.USER_ID) String userId, @RequestParam(value = "name", required = false) String name,
                                                                  @RequestParam(value = "department_id", required = false) String departmentId,
                                                                  @RequestParam(value = "meter_type") String meterType) {
        return ResultModel.success(projectService.listContractOptions(userId, name, meterType, departmentId));
    }

    @ApiOperation("查询项目绑定的合同列表下拉项")
    @GetMapping("/project-contract-options")
    @ApiImplicitParams({@ApiImplicitParam(name = "name", value = "合同名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "project_id", value = "项目ID", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "dept_id", value = "组织ID", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "meter_types", value = "计费类型", paramType = "query", dataType = "List"),
            @ApiImplicitParam(name = "code", value = "合同编号", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "page_num", value = "页码", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "page_size", value = "每页显示条数", paramType = "query", dataType = "int")})
    @DocumentIgnore
    public ResultModel<PageCL<ProjectContractVO>> listProjectContractOptions(@RequestParam(value = "project_id", required = false) String projectId,
                                                                             @RequestParam(value = "dept_id", required = false) String deptId,
                                                                             @RequestParam(value = "meter_types", required = false) List<String> meterTypes,
                                                                             @RequestParam(value = "name", required = false) String name,
                                                                             @RequestParam(value = "code", required = false) String code,
                                                                             @RequestParam(value = "page_num", defaultValue = "1", required = false) int pageNum,
                                                                             @RequestParam(value = "page_size", defaultValue = "10", required = false) int pageSize) {
        return ResultModel.success(projectService.listProjectContractOptions(projectId, deptId, meterTypes, name, code, pageNum, pageSize));
    }


    @PutMapping("/{id}/extension")
    @ApiOperation("测试项目延期")
    @DocumentIgnore
    public ResultModel extension(@PathVariable("id") String id, @RequestParam("extension_date") String extensionDate) {
        projectService.extension(id, extensionDate);
        return ResultModel.success("项目延期成功");
    }

    @PutMapping("/{id}/update-project-by-operations")
    @ApiOperation("用于项目回收、延期、转正等操作时进行的修改项目")
    @DocumentIgnore
    public ResultModel updateProjectByOperation(@PathVariable("id") String id,
                                                @Valid @RequestBody ProjectUpdateByOperationVO projectUpdateByOperationVO) {
        projectService.updateProjectByOperation(id, projectUpdateByOperationVO);
        return ResultModel.success("修改项目成功", id);
    }

    @PutMapping("/operation-approve-type/{id}")
    @ApiOperation("修改项目送审类型")
    @LogRecordAnnotation(value = "修改项目计费审批类型", detail = "【{{#project.alias}}】项目{{#approveType}}计费审批")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "项目ID", required = true, dataType = "String", paramType = "path")})
    @ResourceAuth(handler = ProjectResourceAuthHandler.class, resources = "#id")
    @DocumentIgnore
    public ResultModel updateOperationApproveType(@PathVariable("id") String id,
                                                  @Validated @RequestBody UpdateProjectOperationTypeVO vo) {
        try {
            vo.setId(id);
            projectService.updateOperationApproveType(vo);
            return ResultModel.success("修改成功", "");
        } catch (Exception e) {
            if (CommonUtils.isContainChinese(e.getMessage())) {
                return ResultModel.error(e.getMessage());
            }
            return ResultModel.error("修改项目计费审批类型失败");
        }

    }

    @GetMapping(value = "getOperationEnable")
    @ApiOperation(value = "是否启用自服务")
    @DocumentIgnore
    public ResultModel<Boolean> getOperationEnable() {
        return ResultModel.success(operationsEnabled);
    }

    @GetMapping("/resource-link")
    @ApiOperation("项目资源管理服务列表接口")
    public ResultModel<List<ProjectResourceVO>> getProjectResourceLink() {
        return ResultModel.success(projectService.getProjectResourceLink());
    }

    @PutMapping("change/{id}")
    @ApiOperation("项目变更组织")
    @LogRecordAnnotation(value = "项目变更组织", detail = "项目变更组织:{{#createOrUpdateProjectVO.name}}")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "项目ID", required = true, dataType = "String", paramType = "path")})
    @ResourceAuth(handler = ProjectResourceAuthHandler.class, resources = "#id")
    public ResultModel<CreateOrUpdateDepartmentVO> change(@PathVariable("id") String id,
                                                          @Validated @RequestBody CreateOrUpdateProjectVO createOrUpdateProjectVO) {
        try {
            projectService.update(null, new LambdaUpdateWrapper<Project>()
                    .eq(Project::getId, id)
                    .set(Project::getDeptId, createOrUpdateProjectVO.getDeptId()));
            //移除当前项目下的所有用户
            userRoleMappingService.remove(new LambdaQueryWrapper<UserRoleMapping>()
                    .eq(UserRoleMapping::getProjectId, id));
            return ResultModel.success("迁移项目成功", createOrUpdateProjectVO);
        } catch (Exception e) {
            if (CommonUtils.isContainChinese(e.getMessage())) {
                return ResultModel.error(e.getMessage());
            }
            return ResultModel.error("项目变更组织失败");
        }

    }
}
