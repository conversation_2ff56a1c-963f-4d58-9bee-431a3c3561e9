package com.sugon.cloud.iam.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-02-02 10:38
 */
@Data
@ApiModel("MFA认证结果")
public class MfaResultVO implements Serializable {

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("mfa结果，0-成功，1-激活令牌")
    private Integer mfaResult;

    @ApiModelProperty("激活码")
    private String activeCode;
}
