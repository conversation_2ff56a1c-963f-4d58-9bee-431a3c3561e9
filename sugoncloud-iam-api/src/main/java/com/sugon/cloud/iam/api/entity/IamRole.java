package com.sugon.cloud.iam.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sugon.cloud.common.model.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("iam_role")
@ApiModel(value = "角色")
public class IamRole extends BaseEntity {
    @TableId(type = IdType.UUID)
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("角色名称")
    private String name;
    @ApiModelProperty("描述")
    private String description;
    @ApiModelProperty("所属组织Id")
    private String deptId;
    @ApiModelProperty("角色类型")
    private String type;

}
