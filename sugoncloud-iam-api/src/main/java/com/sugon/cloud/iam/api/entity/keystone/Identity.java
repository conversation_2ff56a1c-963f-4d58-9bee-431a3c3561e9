package com.sugon.cloud.iam.api.entity.keystone;

import com.sugon.cloud.iam.api.entity.keystone.VO.TokenRequestVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "身份认证")
public class Identity {

    @ApiModelProperty(value = "方法列表")
    private List<String> methods;
    @ApiModelProperty(value = "用户密码实体")
    private Password password;
    @ApiModelProperty(value = "token请求体")
    private TokenRequestVO token;
}
