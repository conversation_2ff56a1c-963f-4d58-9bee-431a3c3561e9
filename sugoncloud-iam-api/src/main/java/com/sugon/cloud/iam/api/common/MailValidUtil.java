package com.sugon.cloud.iam.api.common;

import com.sun.mail.util.MailConnectException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.mail.*;
import java.util.Properties;
import java.util.regex.Pattern;

public class MailValidUtil {

    private static final Logger logger = LoggerFactory.getLogger (MailValidUtil.class);

    public static String validSendMail(String mailHost, String sendMail, String sendMailPassword, String port) {
        Properties props = new Properties ();
        props.setProperty ("mail.enable", "true");
        props.setProperty ("mail.transport.protocol", "smtp"); // 邮件发送协议
        props.setProperty ("mail.smtp.port", port);// SMTP邮件服务器默认端口
        props.setProperty ("mail.smtp.auth", "true"); // 是否要求身份认证
        props.setProperty ("mail.smtp.host", mailHost); // smtp邮件服务器
        // 是否启用调试模式（启用调试模式可打印客户端与服务器交互过程时一问一答的响应消息）
        props.setProperty ("mail.debug", "true");
        props.setProperty ("mail.smtp.connectiontimeout", "4000"); // 设置邮件服务器连接超时时限(毫秒)
        props.setProperty ("mail.smtp.timeout", "10000");
        props.setProperty ("mail.smtp.writetimeout", "120000");

        Transport transport = null;
        try {
            // 创建Session实例对象
            Session session = Session.getInstance (props);
            // 通过session得到transport对象
            transport = session.getTransport ();
            // 使用邮箱的用户名和密码连上邮件服务器，发送邮件时，发件人需要提交邮箱的用户名和密码给smtp服务器，
            // 用户名和密码都通过验证之后才能够正常发送邮件给收件人。
            transport.connect (mailHost, sendMail, sendMailPassword);
            transport.close ();
        } catch (NoSuchProviderException e) { // 邮箱服务器供应商异常
            // e.printStackTrace();
            logger.error ("邮箱服务器供应商异常", e);
            return "邮箱服务器供应商异常";
        } catch (MailConnectException e) { // 邮箱服务器连接超时异常
            // e.printStackTrace();
            logger.error ("邮箱服务器连接超时异常", e);
            return "邮箱服务器连接超时异常";
        } catch (AuthenticationFailedException e) { // 邮箱帐号或密码错误异常
            // e.printStackTrace();
            logger.error ("发件人地址或密码错误", e);
            return "发件人地址或密码错误";
        } catch (MessagingException e) { // 消息传送异常
            // e.printStackTrace();
            logger.error ("消息传送异常", e);
            return "消息传送异常";
        } catch (Exception e){
            e.printStackTrace();
            return "消息传送异常";
        }
        return "";
    }

    public static boolean checkEmail(String email) {
        final String regex = "^[A-Za-z\\d]+([-_.][A-Za-z\\d]+)*@([A-Za-z\\d]+[-.])+[A-Za-z\\d]{2,4}$";
        boolean matches = Pattern.matches(regex, email);
        return matches;
    }

}
