package com.sugon.cloud.iam.api.entity.keystone.VO;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 兼容OpenStack原生project创建
 */
@Data
@ApiModel(value = "创建project")
public class ProjectNativeCreateVO {
    @ApiModelProperty("项目id")
    private String id;
    @ApiModelProperty("项目名称")
    private String name;
    @JsonProperty("domain_id")
    @ApiModelProperty("域id")
    private String domainId;
    @ApiModelProperty("描述")
    private String description;
    @ApiModelProperty("是否启用")
    private boolean enabled;
    @JsonProperty("is_domain")
    @ApiModelProperty("是否为域")
    private boolean domain;

    public boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public boolean getDomain() {
        return domain;
    }

    public void setDomain(boolean domain) {
        this.domain = domain;
    }
}
