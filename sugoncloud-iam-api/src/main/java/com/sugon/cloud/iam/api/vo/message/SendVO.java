package com.sugon.cloud.iam.api.vo.message;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Set;

/**
 * @Author: yangdingshan
 * @Date: 2025/1/25 11:28
 * @Description:
 */
@Data
@ApiModel("通用发送")
public class SendVO {

    @ApiModelProperty(value = "联系方式", required = true)
    @NotEmpty(message = "联系方式不能为空")
    private Set<String> contacts;

    @ApiModelProperty(value = "模板参数, 与模板顺序保持一致", required = true)
    @NotEmpty(message = "模板参数为空")
    @JsonProperty("template_params")
    private List<String> templateParams;

    @ApiModelProperty(value = "模板ID", required = true)
    @NotBlank(message = "模板ID不能为空")
    @JsonProperty("template_id")
    private String templateId;

    @ApiModelProperty(value = "发送方式，sms:短信 email:邮箱", required = true)
    @NotBlank(message = "发送方式不能为空")
    @JsonProperty("send_mode")
    private String sendMode;
}
