package com.sugon.cloud.iam.api.controller;

import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.common.mapper.ModelMapper;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.common.utils.I18nUtil;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.entity.LicenseInfo;
import com.sugon.cloud.iam.api.entity.MicroService;
import com.sugon.cloud.iam.api.exception.BusinessException;
import com.sugon.cloud.iam.api.service.MicroServiceService;
import com.sugon.cloud.iam.api.service.impl.MicroServiceServiceImpl;
import com.sugon.cloud.iam.api.service.resourceauth.SecurityUserTypeAuthHandler;
import com.sugon.cloud.iam.api.utils.CommonUtils;
import com.sugon.cloud.iam.api.vo.CreateMicroServiceVO;
import com.sugon.cloud.iam.api.vo.UpdateMicroServiceVO;
import com.sugon.cloud.iam.common.model.vo.MicroServiceDetailVO;
import com.sugon.cloud.iam.common.model.vo.MicroServiceVO;
import com.sugon.cloud.log.client.context.LogRecordContext;
import com.sugon.cloud.log.client.starter.annotation.LogRecordAnnotation;
import com.sugon.cloud.resource.auth.common.aop.ResourceAuth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Objects;

/**
 * (MicroService)表控制层
 *
 * <AUTHOR>
 * @since 2021-04-15 09:25:37
 */
@RestController
@RequestMapping("/api/micro-services")
@Api(tags = "微服务API")
public class MicroServiceController {
    /**
     * 服务对象
     */
    @Resource
    private MicroServiceService microServiceService;

    /**
     * 查询列表数据
     *
     * @return 数据列表
     */
    @GetMapping
    @ApiOperation("查询微服务信息")
    @ApiImplicitParams({@ApiImplicitParam(name = "name", value = "微服务名称",dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "collected", value = "已收藏(true为只查询已收藏)",dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "page_num", value = "当前页",defaultValue = "1",dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "page_size", value = "每页数量", defaultValue = "15", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "is_hidden", value = "是否隐藏", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "need_policy", value = "是否需要代理", dataType = "Boolean", defaultValue = "true", paramType = "query"),
            @ApiImplicitParam(name = "is_cloud_product", value = "是否是云产品", dataType = "String", paramType = "query"),
    })
    public ResultModel<PageCL<MicroServiceDetailVO>> list(@RequestParam(value = "name", required = false) String name,
                                                          @RequestParam(value = "collected", required = false) String collected,
                                                          @RequestParam(value = "need_policy", required = false, defaultValue = "true") boolean needPolicy,
                                                          @RequestParam(value = "page_num", defaultValue = "1", required = false) int pageNum,
                                                          @RequestParam(value = "page_size", defaultValue = "15" ,required = false) int pageSize,
                                                          @RequestParam(value = "is_hidden", required = false) String isHidden,
                                                          @RequestParam(value = "is_cloud_product", required = false) String isCloudProduct) {
        PageCL<MicroServiceDetailVO> pageResult = this.microServiceService.pageList(name,pageNum,pageSize,collected, needPolicy,isHidden, isCloudProduct);
        return ResultModel.success(pageResult);
    }

    @PostMapping
    @ApiOperation("创建微服务信息")
    @ResourceAuth(handler = SecurityUserTypeAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    @DocumentIgnore
    @LogRecordAnnotation(value = "新增微服务", detail = "新增微服务{{#createMicroServiceVO.name}}", resourceId = "{{#id}}", resource = "{{#createMicroServiceVO.name}}", auditFlag = "true")
    public ResultModel<CreateMicroServiceVO> save(@Validated @RequestBody CreateMicroServiceVO createMicroServiceVO) {
        MicroService insert = this.microServiceService.insert(createMicroServiceVO);
        LogRecordContext.putVariable("id", insert.getId());
        return ResultModel.success("创建微服务信息成功", createMicroServiceVO);
    }

    @PutMapping
    @ApiOperation("修改微服务信息")
    @DocumentIgnore
    @ResourceAuth(handler = SecurityUserTypeAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    @LogRecordAnnotation(value = "修改微服务", detail = "修改微服务{{#name}}", resourceId = "{{#updateMicroServiceVO.id}}", resource = "{{#name}}", auditFlag = "true")
    public ResultModel<MicroServiceDetailVO> update(@Validated @RequestBody UpdateMicroServiceVO updateMicroServiceVO) {
        MicroService update = this.microServiceService.update(updateMicroServiceVO);
        LogRecordContext.putVariable("name", update.getName());
        return ResultModel.success("修改微服务信息成功", updateMicroServiceVO);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除微服务信息")
    @DocumentIgnore
    @ResourceAuth(handler = SecurityUserTypeAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    @LogRecordAnnotation(value = "删除微服务类别信息", detail = "删除微服务信息{{#name}}", resourceId = "{{#id}}", resource = "{{#name}}", auditFlag = "true")
    public ResultModel delete(@PathVariable String id) {
        MicroService microService = microServiceService.queryById(id);
        if (Objects.isNull(microService)) {
            throw new BusinessException("微服务id[" + id + "]不存在");
        }
        if (!CommonInstance.MICRO_CUSTOM.equals(microService.getType())) {
            throw new BusinessException("非自定义微服务不能删除");
        }
        LogRecordContext.putVariable("name", microService.getName());
        microServiceService.deleteById(id);
        return ResultModel.success("删除微服务信息成功", "");
    }

    @PutMapping("/{id}/collect")
    @ApiOperation("微服务加入收藏")
    @DocumentIgnore
    public ResultModel<CreateMicroServiceVO> collect(@PathVariable("id") String id) {
        this.microServiceService.collectOrUnCollect(id, true);
        return ResultModel.success("微服务加入收藏成功", null);
    }

    @PutMapping("/{id}/unCollect")
    @ApiOperation("微服务移除收藏")
    @DocumentIgnore
    public ResultModel<CreateMicroServiceVO> unCollect(@PathVariable("id") String id) {
        this.microServiceService.collectOrUnCollect(id, false);
        return ResultModel.success("微服务移除收藏成功", null);
    }

    /**
     * 导入版本
     *
     * @param imageFile
     * @param
     * @return
     */
    @PostMapping(value = "/{id}/upload")
    @ApiOperation("导入版本")
    @DocumentIgnore
    public ResultModel<LicenseInfo> uploadLicense(@PathVariable String id, @RequestParam("licenseFile") MultipartFile imageFile) {
        String licenseStr = "";
        try {
            //license密文文本
            InputStream inputStream = imageFile.getInputStream();
            licenseStr = MicroServiceServiceImpl.readFileGetStr(inputStream);
            LicenseInfo licenseInfo = microServiceService.upload(id,licenseStr);
            return ResultModel.success(licenseInfo);
        }catch (IOException e) {
            if (CommonUtils.isContainChinese(e.getMessage())) {
                return ResultModel.error(e.getMessage());
            }
            return ResultModel.error("授权文件读取失败");
        }catch (Exception e) {
            if (CommonUtils.isContainChinese(e.getMessage())) {
                return ResultModel.error(e.getMessage());
            }
            return ResultModel.error("导入失败", "导入失败");
        }
    }

    @GetMapping("/all")
    @ApiOperation("查询所有微服务")
    @DocumentIgnore
    public ResultModel<List<MicroServiceVO>> listAll(HttpServletRequest request) {
        List<MicroService> result= this.microServiceService.getAllMicroService();
        result.forEach(s -> {
            // 国际化处理
            s.setName(I18nUtil.getMessageByKey(s.getName(), s.getName()));
            s.setDescription(I18nUtil.getMessageByKey(s.getDescription(), s.getDescription()));
        });
        ModelMapper modelMapper = new ModelMapper();
        List<MicroServiceVO> list = modelMapper.mapList(result, MicroServiceVO.class);
        return ResultModel.success(list);
    }

}
