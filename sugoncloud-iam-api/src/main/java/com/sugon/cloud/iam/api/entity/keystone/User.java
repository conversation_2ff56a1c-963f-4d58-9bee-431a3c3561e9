package com.sugon.cloud.iam.api.entity.keystone;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.serializer.SimplePropertyPreFilter;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
@TableName("user")
@ApiModel(value = "用户实体")
public class User {
    @ApiModelProperty(value = "账号")
    private String name;
    @ApiModelProperty(value = "名称")
    private String alias;
    @ApiModelProperty(value = "密码")
    private String password;
    @JsonProperty(value = "default_project_id")
    @ApiModelProperty(value = "默认项目")
    private String defaultProjectId;
    @TableField(exist = false)
    @ApiModelProperty(value = "项目实体")
    private Project project;
    @TableField(exist = false)
    @ApiModelProperty(value = "角色列表")
    private List<Role> roles;
    @TableId(type = IdType.UUID)
    private String id;
    @ApiModelProperty(value = "扩展信息")
    private String extra;
    private boolean enabled = true;
    @JsonProperty("created_at")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createdAt;
    @JsonProperty("last_active_at")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后操作时间")
    private Date lastActiveAt;
    @JsonProperty("domain_id")
    @ApiModelProperty(value = "所属域")
    private String domainId;
    @JsonProperty(value = "password_expires_at")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "密码过期时间")
    private Date passwordExpiresAt;
    @TableField(exist = false)
    private Object federated;
    @TableField(exist = false)
    @ApiModelProperty(value = "描述")
    private Object description;
    @TableField(exist = false)
    @ApiModelProperty(value = "操作")
    private Object options;
    @ApiModelProperty(value = "邮箱")
    private String email;
    @ApiModelProperty(value = "电话")
    private String phone;
    @JsonProperty(value = "dept_id")
    @ApiModelProperty(value = "组织")
    private String deptId;
    @ApiModelProperty(value = "用户类型")
    private String type;
    @JsonFormat(pattern="yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "过期时间")
    private Date expired;
    @JsonProperty("allow_ip")
    @ApiModelProperty(value = "允许登录ip")
    private String allowIp;
    @JsonProperty("last_login_ip")
    @ApiModelProperty(value = "最后登录id")
    private String lastLoginIp;
    @JsonProperty("hash_role")
    @ApiModelProperty(value = "角色hash")
    private String hashRole;
    @ApiModelProperty(value = "随机向量")
    private String iv;
    @JsonProperty("hash")
    @ApiModelProperty(value = "用户hash")
    private String hash;
    @TableField(
            fill = FieldFill.INSERT_UPDATE
    )
    @JsonProperty("modify_time")
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @JsonProperty(value = "last_password_time")
    @ApiModelProperty(value = "密码最后修改时间")
    private Date lastPasswordTime;

    public boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String toEncryptAndDecryptJSONStr() {
        SimplePropertyPreFilter filter = new SimplePropertyPreFilter();
        filter.getExcludes().add("lastActiveAt");
        filter.getExcludes().add("lastLoginIp");
        filter.getExcludes().add("hashRole");
        filter.getExcludes().add("hash");
        filter.getExcludes().add("iv");
        return JSONObject.toJSONString(this, filter, SerializerFeature.PrettyFormat);
    }
}
