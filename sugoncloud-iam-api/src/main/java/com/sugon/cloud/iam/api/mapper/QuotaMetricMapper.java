package com.sugon.cloud.iam.api.mapper;

import com.sugon.cloud.iam.common.model.vo.QuotaMetric;
import com.sugon.cloud.iam.common.model.vo.QuotaMetricTopology;
import com.sugon.cloud.iam.common.model.vo.QuotaProjectValue;
import com.sugon.cloud.iam.common.model.vo.QuotaRootUserEntity;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface QuotaMetricMapper {

    List<QuotaMetric> getQuotaMetricList(Map map);

    List<QuotaMetric> getDepartmentQuotaByParam(Map map);

    List<QuotaMetric> getDepartmentQuotaByParamDisk(Map map);

    List<QuotaMetric> getProjectQuotaByParamDisk(Map map);

    List<QuotaMetric> getProjectQuotaByParam(Map map);

    QuotaMetric getDepartmentQuotaValue(Map map);

    QuotaMetric getProjectQuotaValue(Map map);

    List<QuotaMetric> getQuotaOverview(String regionId);

    List<QuotaRootUserEntity> getRootUserQuota(Map map);

    List<QuotaMetric> getDepartmentQuotaByType(Map map);

    List<Map> getProjectRootGbQuotaByParam(Map map);

    void insertDepartmentQuota(QuotaMetric quotaMetric);

    void updateDepartmentQuota(QuotaMetric quotaMetric);

    void updateDepartmentQuotaAndUsed(QuotaMetric quotaMetric);

    void insertProjectQuota(QuotaMetric quotaMetric);

    void insertBatchProjectQuota(@Param("quotaMetrics") List<QuotaMetric> quotaMetrics);

    void updateProjectQuota(QuotaMetric quotaMetric);

    void updateDepartmentUsed(QuotaMetric quotaMetric);

    void updateProjectUsed(QuotaProjectValue quotaProjectValue);

    void deleteProjectQuota(@Param("projectId") String projectId, @Param("regionId") String regionId);

    void deleteProjectQuotaByProjectsAndMetric(@Param("projectIds") List<String> projectIds, @Param("regionId") String regionId, @Param("metric") String metric);

    void deleteDepartmentQuota(@Param("departmentId") String departmentId, @Param("regionId") String regionId);

    List<QuotaMetricTopology> getTopology(Map map);

    List<QuotaMetric> getQuotaProjectsByProjectIds(Map<String, Object> map);
}
