package com.sugon.cloud.iam.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sugon.cloud.iam.api.enums.ActionTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
@Accessors(chain = true)
@TableName("service_action")
public class ActionForm implements Serializable {
    private static final long serialVersionUID = -8515852102263215990L;

    @TableId(type = IdType.UUID)
    private String id;

    @ApiModelProperty("服务操作(服务名:类名:方法名)")
    @NotBlank(message = "操作不能为空")
    @Size(min = 2,max = 128)
    @Pattern(regexp = "(\\w{1,}):(\\w{1,})$")
    private String action;

    @ApiModelProperty("操作类别(READ、WRITE、LIST、AUTH)")
    @TableField("action_type")
    private ActionTypeEnum actionType;

    @ApiModelProperty("服务ID")
    @NotBlank(message = "服务ID不能为空")
    @Size(min = 2,max = 64)
    private String serviceId;

    @ApiModelProperty("描述")
    @NotBlank(message = "描述不能为空")
    @Size(min = 2,max = 32)
    private String description;

}
