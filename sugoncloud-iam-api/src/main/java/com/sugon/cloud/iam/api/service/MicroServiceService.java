package com.sugon.cloud.iam.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.entity.LicenseInfo;
import com.sugon.cloud.iam.api.entity.MicroService;
import com.sugon.cloud.iam.api.vo.CreateMicroServiceVO;
import com.sugon.cloud.iam.api.vo.PolicyFilterVO;
import com.sugon.cloud.iam.api.vo.UpdateMicroServiceVO;
import com.sugon.cloud.iam.common.model.vo.MicroServiceDetailVO;

import java.util.List;

/**
 * (MicroService)表服务接口
 *
 * <AUTHOR>
 * @since 2021-04-15 09:35:39
 */
public interface MicroServiceService extends IService<MicroService> {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    MicroService queryById(String id);

    List<MicroService> getAllMicroService();

    /**
     * 新增数据
     *
     * @param microService 实例对象
     * @return 实例对象
     */
    MicroService insert(CreateMicroServiceVO microService);

    /**
     * 修改数据
     *
     * @param updateMicroServiceVO 实例对象
     * @return 实例对象
     */
    MicroService update(UpdateMicroServiceVO updateMicroServiceVO);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    void deleteById(String id);

    /**
     * 列表
     *
     * @param name
     * @param pageNum
     * @param pageSize
     * @param collected
     * @return
     */
    PageCL<MicroServiceDetailVO> pageList(String name, int pageNum, int pageSize, String collected, boolean needPolicy,String isHidden, String isCloudProduct);

    /**
     * 加入/移除收藏
     *
     * @param id                微服务id
     * @param isCollected(true: 收藏操作 | false: 移除收藏操作)
     */
    void collectOrUnCollect(String id, boolean isCollected);

    LicenseInfo upload(String id, String licenseStr) throws Exception;

    void saveLicense(String microserviceId, LicenseInfo licenseInfo);

    PolicyFilterVO getPolicyFilter();
}
