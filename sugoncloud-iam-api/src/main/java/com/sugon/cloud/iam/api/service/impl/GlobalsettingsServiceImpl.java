package com.sugon.cloud.iam.api.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sugon.cloud.iam.api.entity.GlobalsettingsEntity;
import com.sugon.cloud.iam.api.mapper.GlobalsettingsMapper;
import com.sugon.cloud.iam.api.service.GlobalsettingsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service
public class GlobalsettingsServiceImpl extends ServiceImpl<GlobalsettingsMapper, GlobalsettingsEntity> implements GlobalsettingsService {
    @Autowired
    private GlobalsettingsMapper globalsettingsMapper;
    @Override
    public GlobalsettingsEntity getGlobalsettingsEntity(String type, String key) {
        GlobalsettingsEntity ramGlobalsettingsEntity=null;
        ramGlobalsettingsEntity =  globalsettingsMapper.selectOne(new LambdaQueryWrapper<GlobalsettingsEntity>().eq(GlobalsettingsEntity::getPolicyName,key).eq(GlobalsettingsEntity::getPolicyType,type));
        return ramGlobalsettingsEntity;
    }

    @Override
    public boolean getBmStatus() {
        try {
            GlobalsettingsEntity setting = globalsettingsMapper.selectOne(new LambdaQueryWrapper<GlobalsettingsEntity>().eq(GlobalsettingsEntity::getPolicyName, "status").eq(GlobalsettingsEntity::getPolicyType, "bm"));
            if (Objects.isNull(setting) || StrUtil.isBlank(setting.getPolicyDocument())) {
                return false;
            }
            return Boolean.parseBoolean(setting.getPolicyDocument());
        } catch (Exception e) {
            log.error("get bm status error", e);
        }
        return false;
    }

    @Override
    public boolean getSecurityAdminLoginStatus() {
        try {
            GlobalsettingsEntity setting = globalsettingsMapper.selectOne(new LambdaQueryWrapper<GlobalsettingsEntity>().eq(GlobalsettingsEntity::getPolicyName, "security_login_status").eq(GlobalsettingsEntity::getPolicyType, "bm"));
            if (Objects.isNull(setting) || StrUtil.isBlank(setting.getPolicyDocument())) {
                return false;
            }
            return Boolean.parseBoolean(setting.getPolicyDocument());
        } catch (Exception e) {
            log.error("get bm security login status error", e);
        }
        return false;
    }
}
