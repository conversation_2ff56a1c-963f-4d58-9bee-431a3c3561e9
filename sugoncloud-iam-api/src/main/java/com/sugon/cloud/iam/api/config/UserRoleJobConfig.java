package com.sugon.cloud.iam.api.config;

import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.rabbitmq.common.configuration.XxlJobUtilConfig;
import com.sugon.cloud.rabbitmq.common.model.XxlJobInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class UserRoleJobConfig implements CommandLineRunner{

    @Value("${iam.task.userRole}")
    private String cron;
    private final XxlJobUtilConfig xxlJobUtil;

    @Override
    public void run(String... args) {
        try {
            log.info("::::::::::::::user_role_sync start");
            XxlJobInfo jobInfo = new XxlJobInfo();
            jobInfo.setJobDesc("user_role_sync_handler");
            jobInfo.setAuthor("<EMAIL>");
            jobInfo.setScheduleConf(cron);
            jobInfo.setExecutorHandler(CommonInstance.USER_ROLE_SYNC_HANDLER);
            xxlJobUtil.addAndStart(jobInfo);
            log.info("::::::::::::::user_role_sync end");
        } catch (Exception e) {
            log.error("::::::::::::::user_role_sync fail", e);
        }
    }
}
