package com.sugon.cloud.iam.api.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sugon.cloud.common.mapper.ModelMapper;
import com.sugon.cloud.common.utils.UUIDUtil;
import com.sugon.cloud.iam.api.entity.keystone.Catalog;
import com.sugon.cloud.iam.common.model.vo.CreateResourceResponseLinksParam;
import com.sugon.cloud.iam.api.entity.keystone.CreateServiceResponse;
import com.sugon.cloud.iam.api.entity.keystone.CreateServiceResponseParam;
import com.sugon.cloud.iam.api.entity.keystone.VO.CreateServiceParam;
import com.sugon.cloud.iam.api.entity.keystone.VO.CreateServiceVO;
import com.sugon.cloud.iam.api.entity.exception.ResourceNotFoundException;
import com.sugon.cloud.iam.api.mapper.CatalogMapper;
import com.sugon.cloud.iam.api.service.ServiceService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RefreshScope
public class ServiceServiceImpl implements ServiceService {

    private final ModelMapper mapper;
    
    private final CatalogMapper catalogMapper;

    @Value("#{'${vip.ip}'.concat(':').concat('${vip.port}')}")
    private String linksPrefix;

    @Override
    public CreateServiceResponse createService(CreateServiceVO createServiceVO) {
        String id = UUIDUtil.get32UUID();
        CreateServiceParam service = createServiceVO.getService();
        Catalog catalog = mapper.map(service, Catalog.class);
        catalog.setId(id);
        catalog.setEnabled(true);
        JSONObject extra = new JSONObject();
        extra.put("name",service.getName());
        extra.put("description",service.getDescription());
        catalog.setExtra(extra.toString());
        catalogMapper.insert(catalog);
        
        CreateServiceResponse result = new CreateServiceResponse();
        result.setService(catalog2CreateServiceResponseParam(catalog));
        return result;
    }

    @Override
    public CreateServiceResponse serviceDetail(String serviceId) {
        Catalog catalog = catalogMapper.selectById(serviceId);
        if(Objects.isNull(catalog)){
            throw new ResourceNotFoundException("Could not find service: " + serviceId + ".");
        }
        CreateServiceResponse result = new CreateServiceResponse();
        result.setService(catalog2CreateServiceResponseParam(catalog));
        return result;
    }

    @Override
    public List<CreateServiceResponseParam> listService(String name, String type) {
        List<Catalog> catalogs = catalogMapper.selectList(new QueryWrapper<Catalog>().lambda()
                .eq(!StringUtils.isEmpty(name), Catalog::getName, name)
                .eq(!StringUtils.isEmpty(type), Catalog::getType, type));
        return catalogs.stream().map(catalog -> catalog2CreateServiceResponseParam(catalog)).collect(Collectors.toList());
    }

    /**
     * 将数据库查询到的catalog数据结构转为需要展示的数据结构
     * @param catalog
     * @return
     */
    private CreateServiceResponseParam catalog2CreateServiceResponseParam(Catalog catalog){
        CreateServiceResponseParam serviceResponseParam = mapper.map(catalog,CreateServiceResponseParam.class);
        serviceResponseParam.setLinks(new CreateResourceResponseLinksParam("http://" + linksPrefix + "/v3/services/" + catalog.getId()));
        return serviceResponseParam;
    }
}
