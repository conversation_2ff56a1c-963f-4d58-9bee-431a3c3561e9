package com.sugon.cloud.iam.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sugon.cloud.common.model.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * (MicroService)实体类
 *
 * <AUTHOR>
 * @since 2021-04-15 09:35:33
 */
@Data
@TableName("micro_service")
@ApiModel(value = "微服务")
public class MicroService extends BaseEntity {
    @ApiModelProperty("微服务名称")
    private String name;
    @ApiModelProperty("描述")
    private String description;
    @ApiModelProperty("服务ID")
    private String serviceId;
    @ApiModelProperty("链接")
    private String link;
    @ApiModelProperty("分类ID")
    private String categoryId;
    @ApiModelProperty("是否隐藏")
    private Boolean navHidden;
    @ApiModelProperty("最新请求时间")
    private Date latestRequestTime;
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty("采集时间")
    private Date collectTime;
    @ApiModelProperty("版本")
    private String version;
    @TableField("`order`")
    @ApiModelProperty("排序")
    private Double order;
    /**
     * 是否为第三方接入
     */
    @ApiModelProperty("是否为第三方接入")
    private Boolean thirdPartAccess;
    @ApiModelProperty("公开链接")
    private String publicLink;
    @ApiModelProperty("是否需要策略")
    private Boolean needPolicy;
    @ApiModelProperty("许可key")
    private String licenseKey;
    /**
     * 类型inner:内部,costom:自定义
     */
    private String type;
    /**
     * 打开方式Internal:内部加载,external:外部跳转
     */
    private String openMode;
}
