package com.sugon.cloud.iam.api.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.entity.UserRoleMapping;
import com.sugon.cloud.iam.api.mapper.UserRoleMappingMapper;
import com.sugon.cloud.iam.api.service.UserRoleMappingService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class UserRoleMappingServiceImpl extends ServiceImpl<UserRoleMappingMapper, UserRoleMapping> implements UserRoleMappingService {
    @Resource(name = "defaultRedisTemplate")
    private final RedisTemplate<String, Object> redisTemplate;
    @Override
    public void syncUserRole2Redis() {
         List<UserRoleMapping> userRoleMappings = this.baseMapper.leftJoinUser();
        Map<String, List<UserRoleMapping>> map = userRoleMappings.stream()
                .collect(Collectors.groupingBy(UserRoleMapping::getUserId));
        for (Map.Entry<String, List<UserRoleMapping>> userMapping : map.entrySet()) {
            String key = userMapping.getKey();
            List<UserRoleMapping> userRoleMappingList = userMapping.getValue();
            StringBuilder roleIds = new StringBuilder();
            for (int i = 0; i < userRoleMappingList.size(); i++) {
                if (Objects.nonNull(userRoleMappingList.get(i).getRoleId())) {
                    roleIds.append(userRoleMappingList.get(i).getRoleId());
                    if (i < userRoleMappingList.size() -1) {
                        roleIds.append(",");
                    }
                }
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("policy", roleIds.toString());
            redisTemplate.opsForValue().set(CommonInstance.IAM_ROLEDB_USERS +key, jsonObject.toString());
        }
    }
}
