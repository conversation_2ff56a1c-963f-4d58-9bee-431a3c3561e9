package com.sugon.cloud.iam.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

@Data
@Accessors(chain = true)
@TableName("secret_key")
@ApiModel(value = "秘钥key")
public class SecretKeyEntity implements Serializable {

    private static final long serialVersionUID = 5982848894994422949L;
    @TableId(type = IdType.UUID)
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("访问key")
    private String accessKey;
    @ApiModelProperty(value = "秘钥key")
    private String secretKey;
    @ApiModelProperty("是否启用")
    private boolean enabled;
    @Size(max = 64)
    @ApiModelProperty("描述")
    private String description;
    @ApiModelProperty("创建时间")
    private Date createAt;
    @ApiModelProperty("用户id")
    private String userId;
    @JsonIgnore
    private boolean innerKey;

    public boolean getEnabled() {
        return enabled;
    }

}
