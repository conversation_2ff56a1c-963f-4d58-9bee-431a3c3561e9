package com.sugon.cloud.iam.api.service.message.messagetype;

import com.sugon.cloud.iam.api.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Author: yangdingshan
 * @Date: 2025/2/9 15:50
 * @Description:
 */
@Component
@Slf4j
@RefreshScope
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MessageTypeFactory {

    private final Map<String, MessageTypeService> messageTypeServiceMap;

    @Value("${mfa.type}")
    private String mfaType;

    public MessageTypeService getInstance() {
        String serviceKey = "message_" + mfaType;
        MessageTypeService messageTypeService = messageTypeServiceMap.get(serviceKey);
        if (messageTypeService == null) {
            throw new BusinessException("系统目前MFA配置为：" + mfaType + ",不支持短信或邮箱发送");
        }
        return messageTypeService;
    }

    public MessageTypeService getInstance(String type) {
        String serviceKey = "message_" + type;
        MessageTypeService messageTypeService = messageTypeServiceMap.get(serviceKey);
        if (messageTypeService == null) {
            throw new BusinessException("type值为：" + type + ",不支持短信或邮箱发送");
        }
        return messageTypeService;
    }
}
