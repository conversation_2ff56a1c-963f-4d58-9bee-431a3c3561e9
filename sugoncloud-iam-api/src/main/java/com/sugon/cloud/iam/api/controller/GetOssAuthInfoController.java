package com.sugon.cloud.iam.api.controller;

import cn.hutool.core.collection.CollectionUtil;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.iam.api.common.CommonInstance;

import com.sugon.cloud.iam.api.entity.SecretKeyEntity;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.service.SecretKeyService;
import com.sugon.cloud.iam.api.service.StrategyService;
import com.sugon.cloud.iam.api.service.UserService;
import com.sugon.cloud.iam.api.service.UserStrategyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

@Api(tags = "获取oss需要的信息")
@RequestMapping(value = "/api/oss-auth-info")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RestController
@Log4j2
@DocumentIgnore
public class GetOssAuthInfoController {

    private final StrategyService strategyService;
    private static  final String token = "98e3fd2ef54c4a97aa609bf67795696e";
    private final UserStrategyService userStrategyService;

    private final SecretKeyService secretKeyService;

    @Resource(name = "defaultRedisTemplate")
    private final RedisTemplate<String, String> redisTemplate;
    private final UserService userService;
    @GetMapping("policy-list")
    @ApiOperation("获取策略")
    public List<JSONObject> getPolicyList(HttpServletRequest request) {
        String header = request.getHeader(CommonInstance.AUTHORIZATION);
        if (!token.equals(header)) return null;
        List<JSONObject> policyList = new ArrayList<>();
        strategyService.list().forEach(strategyPO -> {
            JSONObject jsonObject = JSONObject.parseObject(strategyPO.getPolicy());
            jsonObject.put("ID", strategyPO.getId());
            policyList.add(jsonObject);
        });
        return policyList;
    }

    @ApiOperation("获取用户策略")
    @GetMapping("user-policy")
    public List<Map<String, String>>  getUserStrategy(HttpServletRequest request) {
        String header = request.getHeader(CommonInstance.AUTHORIZATION);
        if (!token.equals(header)) return null;
        List<Map<String, String>> userStrategy = new ArrayList<>();
        userStrategyService.list().forEach(userStrategyEntity -> {
            Map<String, String> map = new HashMap<>();
            map.put("policyId", userStrategyEntity.getStrategyId());
            map.put("userId", userStrategyEntity.getUserId());
            userStrategy.add(map);
        });
        return userStrategy;
    }
    @ApiOperation("获取ak-sk")
    @GetMapping("ak-sk")
    public List<JSONObject> getSecretKeyService(HttpServletRequest request) {
        String header = request.getHeader(CommonInstance.AUTHORIZATION);
        if (!token.equals(header)) return null;
        List<JSONObject> secretKey = new ArrayList<>();
        secretKeyService.list().forEach(secretKeyEntity -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("secretKey", secretKeyEntity.getSecretKey());
            jsonObject.put("accessKey", secretKeyEntity.getAccessKey());
            jsonObject.put("userId", secretKeyEntity.getUserId());
            jsonObject.put("status", secretKeyEntity.getEnabled()?1:0);
            jsonObject.put("expiresAt",null);
            //fix bug 868ba4c8
            User user = userService.getById(secretKeyEntity.getUserId());
            if (user != null) {
                //有的用户不存在type设置为-
                jsonObject.put("userType",user.getType() == null?"-":user.getType());
                secretKey.add(jsonObject);
            }
        });
        Set<String> keys = redisTemplate.keys(CommonInstance.IAM_STS_USERS_REDIS_KEY + "*");
        if (CollectionUtil.isNotEmpty(keys)) {
            assert keys != null;
            keys.forEach(key -> {
                String s = redisTemplate.opsForValue().get(key);
                if (StringUtils.isNotBlank(s)) {
                    //fix bug 868ba4c8 start
                    JSONObject jsonObject = JSONObject.parseObject(s);
                    if (jsonObject != null) {
                        Object userType = jsonObject.get("userType");
                        if (userType == null) {
                            User user = userService.getById(jsonObject.getString("userId"));
                            if (user != null) {
                                //有的用户不存在type设置为-
                                jsonObject.put("userType",user.getType() == null?"-":user.getType());
                                secretKey.add(jsonObject);
                            }
                        } else {
                            secretKey.add(jsonObject);
                        }
                    }
                    //fix bug 868ba4c8 end
//                    secretKey.add(JSONObject.parseObject(s));
                }
            });
        }
        return secretKey;
    }

    @ApiOperation("通过AK获取")
    @GetMapping("{ak}")
    public JSONObject getSkByAk(HttpServletRequest request,
                          @PathVariable("ak") String ak) {
        String header = request.getHeader(CommonInstance.AUTHORIZATION);
        if (!token.equals(header)) return null;
        SecretKeyEntity secretKeyEntity = secretKeyService.getOne(new LambdaQueryWrapper<SecretKeyEntity>()
                .eq(SecretKeyEntity::getAccessKey, ak));
        JSONObject jsonObject = new JSONObject();
        if (secretKeyEntity!= null) {
            jsonObject.put("secretKey", secretKeyEntity.getSecretKey());
            jsonObject.put("accessKey", secretKeyEntity.getAccessKey());
            jsonObject.put("userId", secretKeyEntity.getUserId());
            jsonObject.put("status", secretKeyEntity.getEnabled()?1:0);
            jsonObject.put("expiresAt",null);
            return jsonObject;
        }
        String s = redisTemplate.opsForValue().get((CommonInstance.IAM_STS_USERS_REDIS_KEY + ak));
        log.info("result jsonObject {}", s);
        if (StringUtils.isNotBlank(s)) {
            return JSONObject.parseObject(s);
        }
        return jsonObject;
    }
}
