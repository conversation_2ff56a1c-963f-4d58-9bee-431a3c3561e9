package com.sugon.cloud.iam.api.service.strategy;

import com.google.common.collect.Maps;
import com.sugon.cloud.iam.api.service.MFAVerifyService;
import com.sugon.cloud.log.client.model.CommonBusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-02-02 14:18
 */
@Component
public class StrategyFactory {

    @Autowired
    private Map<String, MFAVerifyService> verifyMap = Maps.newConcurrentMap();

    public MFAVerifyService verifyService(String type){
        // 组装bean名称
        type = type + "MFAVerify";
        MFAVerifyService mfaVerifyService = Optional.ofNullable(verifyMap.get(type)).orElseThrow(() ->
                new CommonBusinessException("双因子厂商不存在"));

        return mfaVerifyService;
    }
}
