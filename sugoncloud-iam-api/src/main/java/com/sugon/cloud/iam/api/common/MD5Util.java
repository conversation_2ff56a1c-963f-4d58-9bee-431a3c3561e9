package com.sugon.cloud.iam.api.common;


import java.security.MessageDigest;
import java.util.Random;


public class MD5Util {
    public static final String sign = "sugon_cloud";
    /***
     * MD5加码 生成32位md5码
     */
    public static String string2MD5(String inStr){
        MessageDigest md5 = null;
        try{
            md5 = MessageDigest.getInstance("MD5");
        }catch (Exception e){
            System.out.println(e.toString());
            e.printStackTrace();
            return "";
        }
        char[] charArray = inStr.toCharArray();
        byte[] byteArray = new byte[charArray.length];

        for (int i = 0; i < charArray.length; i++)
            byteArray[i] = (byte) charArray[i];
        byte[] md5Bytes = md5.digest(byteArray);
        StringBuffer hexValue = new StringBuffer();
        for (int i = 0; i < md5Bytes.length; i++){
            int val = ((int) md5Bytes[i]) & 0xff;
            if (val < 16)
                hexValue.append("0");
            hexValue.append(Integer.toHexString(val));
        }
        return hexValue.toString();

    }

    /**
     * 随机数
     * @param place 定义随机数的位数
     */
    public static String randomGen(int place) {
        String base = "qwertyuioplkjhgfdsazxcvbnmQAZWSXEDCRFVTGBYHNUJMIKLOP0123456789";
        StringBuffer sb = new StringBuffer();
        Random rd = new Random();
        for(int i=0;i<place;i++) {
            sb.append(base.charAt(rd.nextInt(base.length())));
        }
        return sb.toString();
    }

    /**
     * 加密解密算法 执行一次加密，两次解密
     */
    public static String convertMD5(String inStr){

        char[] a = inStr.toCharArray();
        for (int i = 0; i < a.length; i++){
            a[i] = (char) (a[i] ^ 't');
        }
        String s = new String(a);
        return s;

    }

    // 测试主函数
/*    public static void main(String args[]) {
        String salt = randomGen(8);
        System.out.println("盐：" + salt);
        System.out.println("加密的：" + AESUtil.encrypt("/api",MD5Util.string2MD5(salt)));
        System.out.println("加密的：" + AESUtil.encrypt("2019-06-07",MD5Util.string2MD5(MD5Util.sign)));
        System.out.println("加密的：" + AESUtil.encrypt("/api/keystone",MD5Util.string2MD5(MD5Util.sign)));
        System.out.println("加密的：" + AESUtil.encrypt("2019-06-07",MD5Util.string2MD5(MD5Util.sign)));//api/roles
        System.out.println("加密的：" + AESUtil.encrypt("/api/roles",MD5Util.string2MD5(MD5Util.sign)));
        System.out.println("加密的：" + AESUtil.encrypt("2019-06-01",MD5Util.string2MD5(MD5Util.sign)));
        long start = new Date().getTime();
        String target = AESUtil.decrypt("5F760D24B4AB85DA387D323040BD762F", MD5Util.string2MD5(MD5Util.sign));
        long end = new Date().getTime();
        System.out.printf("-------"+(end-start)+"--------");
        System.out.println(AESUtil.decrypt("5BD22237E69A6F0A5179C4F0EDB21CC6",MD5Util.string2MD5("0nCPbhvq")));
    }*/
}
