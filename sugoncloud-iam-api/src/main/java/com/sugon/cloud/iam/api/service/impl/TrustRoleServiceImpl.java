package com.sugon.cloud.iam.api.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sugon.cloud.iam.api.entity.keystone.Trust;
import com.sugon.cloud.iam.api.entity.keystone.TrustRole;
import com.sugon.cloud.iam.api.mapper.TrustMapper;
import com.sugon.cloud.iam.api.mapper.TrustRoleMapper;
import com.sugon.cloud.iam.api.service.TrustRoleService;
import com.sugon.cloud.iam.api.service.TrustService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class TrustRoleServiceImpl extends ServiceImpl<TrustRoleMapper, TrustRole> implements TrustRoleService {

}
