package com.sugon.cloud.iam.api.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.service.UserService;
import com.sugon.cloud.iam.api.utils.EncryptAndDecryptUtil;
import com.sugon.cloud.log.client.service.MessageFeignService;
import com.sugon.cloud.log.common.model.vo.MessageVo;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
@RefreshScope
public class UserIntegrityCheckHandler {

    @Value("${encryption.enabled}")
    private boolean encryptionEnabled;
    @Value("${iam.securityAdminUserId}")
    private String securityAdminUserId;
    private final UserService userService;
    private final EncryptAndDecryptUtil encryptAndDecryptUtil;
    private final MessageFeignService messageFeignService;
    @Value("${default-regionId:RegionOne}")
    private String defaultRegionId;
    @XxlJob(CommonInstance.USER_INTEGRITY_CHECK_HANDLER)
    public void run() {
        if (encryptionEnabled) {
            log.debug("check user integrity begin...");
            List<User> users = userService.list(new LambdaQueryWrapper<User>()
                    .isNotNull(User::getType)
                    .or()
                    .isNotNull(User::getDeptId));
            users.forEach(user -> {
                if (!encryptAndDecryptUtil.verifyHash(user.getHash(), user.toEncryptAndDecryptJSONStr())) {
                    String userId = user.getId();
                    log.info("userId=[{}] is updated.", userId);
                    try {
                        // 完整性有修改，发送邮件给用户本人
                        MessageVo messageVo = new MessageVo();
                        messageVo.setUser(userId);
                        messageVo.setSvc("sugoncloud-iam-api");
                        messageVo.setContent("您的用户完整性有被篡改");
                        messageVo.setEmailEnable(true);
                        messageVo.setRegionid(defaultRegionId);
                        messageFeignService.createMessage(messageVo);
                        if (!userId.equals(securityAdminUserId)) {
                            // 完整性有修改，发送邮件给安全管理员
                            messageVo.setUser(securityAdminUserId);
                            messageVo.setContent("用户[" + user.getName() + "]完整性有被篡改");
                            messageFeignService.createMessage(messageVo);
                        }
                    } catch (Exception e) {
                        log.info("userId=[{}] is send email fail.", userId);
                    }
                }
            });
            log.debug("check user integrity end...");
        }
    }
}
