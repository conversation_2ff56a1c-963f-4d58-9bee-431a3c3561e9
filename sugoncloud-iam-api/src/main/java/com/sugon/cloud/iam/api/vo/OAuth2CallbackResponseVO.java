package com.sugon.cloud.iam.api.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/05/23 09:42
 **/
@Data
@Builder
@ApiModel("OAuth2回调结果")
public class OAuth2CallbackResponseVO {
    @JsonProperty("password_update")
    @ApiModelProperty("密码是否需要修改")
    private boolean passwordUpdate;
    @JsonProperty("phone_or_email_update")
    @ApiModelProperty("电话或邮箱是否需要修改")
    private boolean phoneOrEmailUpdate;
    @ApiModelProperty("token")
    private String token;
}
