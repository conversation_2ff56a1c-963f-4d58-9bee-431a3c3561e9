package com.sugon.cloud.iam.api.handler;

import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

@Component
public class RestAuthenticationAccessDeniedHandler implements AccessDeniedHandler {
    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response, AccessDeniedException e) throws IOException, ServletException, IOException {
        response.setContentType("application/json;charset=utf-8");
        String message = "This is not a recognized Fernet token" + request.getHeader("X-Subject-Token");
        PrintWriter out = response.getWriter();
        out.write("{\"code\":\"404,message:\""+message+"}");
    }
}