package com.sugon.cloud.iam.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ft.otp.core.ReturnResult;
import com.sugon.cloud.iam.api.entity.SeedEntity;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.web.multipart.MultipartFile;

/**
 * (Seeds)表服务接口
 *
 * <AUTHOR>
 * @since 2024-08-07 10:45:33
 */

public interface SeedsService extends IService<SeedEntity> {

    ReturnResult upload(MultipartFile file1, MultipartFile file2);
}
