package com.sugon.cloud.iam.api.controller;

import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.constants.HeaderParamConstant;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.iam.api.entity.Department;
import com.sugon.cloud.iam.api.mapper.DepartmentMapper;
import com.sugon.cloud.iam.api.service.DepartmentService;
import com.sugon.cloud.iam.api.service.QuotaService;
import com.sugon.cloud.iam.api.utils.CommonUtils;
import com.sugon.cloud.iam.common.model.vo.*;
import com.sugon.cloud.log.client.context.LogRecordContext;
import com.sugon.cloud.iam.api.service.resourceauth.AdminUserTypeAuthHandler;
import com.sugon.cloud.iam.api.service.resourceauth.DeptResourceAuthHandler;
import com.sugon.cloud.iam.api.service.resourceauth.ProjectResourceAuthHandler;
import com.sugon.cloud.log.client.starter.annotation.LogRecordAnnotation;
import com.sugon.cloud.resource.auth.common.aop.ResourceAuth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/api/quotas")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Api(tags = {"配额API"})
public class QuotaController {
    Logger logger = LoggerFactory.getLogger(QuotaController.class);

    private final QuotaService quotaService;

    private final HttpServletRequest request;

    private final DepartmentMapper departmentMapper;

    @GetMapping("/department")
    @ApiImplicitParams({@ApiImplicitParam(name = "department_id", value = "组织ID", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "type_name", value = "配额类型名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "category_id", value = "配额所属分类", required = false, dataType = "String", paramType = "query")})
    @ApiOperation("查询组织配额")
    @ResourceAuth(handler = DeptResourceAuthHandler.class, resources = "#departmentId")
    public ResultModel<List<QuotaType>> getQuotaByDeparmentId(@RequestParam(value = "department_id") String departmentId,
                                                              @RequestParam(value = "type_name",required = false) String typeName,
                                                              @RequestParam(value = "category_id",required = false) String categoryId){
        return ResultModel.success("查询组织配额成功",this.quotaService.getQuotaByDeparmentId(departmentId, typeName, categoryId));
    }

    @GetMapping("/project")
    @ApiImplicitParams({@ApiImplicitParam(name = "project_id", value = "项目ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "type_name", value = "配额类型名称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "category_id", value = "配额所属分类", dataType = "String", paramType = "query")})
    @ApiOperation("查询项目配额")
    @ResourceAuth(handler = ProjectResourceAuthHandler.class, resources = "#projectId")
    public ResultModel<List<QuotaType>> getQuotaByProjectId(@RequestParam(value = "project_id", required = false) String projectId,
                                                            @RequestParam(value = "type_name",required = false) String typeName,
                                                            @RequestParam(value = "category_id",required = false) String categoryId){
        return ResultModel.success("查询项目配额成功",this.quotaService.getQuotaByProjectId(projectId, typeName, categoryId));
    }

    @PutMapping("/department/{department_id}")
    @ApiOperation("更新组织配额")
    @LogRecordAnnotation(value = "更新组织配额", detail = "更新[{{#regionId}}]区域组织[{DEPARTMENT{#departmentId}}]的配额", resourceId = "{{#departmentId}}", resource = "{DEPARTMENT{#departmentId}}")
    @ResourceAuth(handler = DeptResourceAuthHandler.class, resources = "#departmentId")
    public ResultModel<QuotaType> updateDepartmentQuota(@PathVariable("department_id") String departmentId,
                                                        @Validated @RequestBody QuotaType quotaType){
        LogRecordContext.putVariable("regionId", request.getHeader(HeaderParamConstant.REGION_ID));
        return ResultModel.success("更新组织配额成功",this.quotaService.updateDepartmentQuota(departmentId,quotaType));
    }

    @PutMapping("/department/total-used/{department_id}")
    @ApiOperation("更新根组织配额总量和使用量")
    @LogRecordAnnotation(value = "更新组织配额总量和使用量", detail = "更新[{{#regionId}}]区域组织[{DEPARTMENT{#departmentId}}]的配额总量和使用量", resourceId = "{{#departmentId}}", resource = "{DEPARTMENT{#departmentId}}")
    @ResourceAuth(handler = DeptResourceAuthHandler.class, resources = "#departmentId")
    @DocumentIgnore
    public ResultModel<QuotaType> updateDepartmentQuotaAndUsed(@PathVariable("department_id") String departmentId,
                                                               @Validated @RequestBody QuotaType quotaType) {
        LogRecordContext.putVariable("regionId", request.getHeader(HeaderParamConstant.REGION_ID));
        return ResultModel.success("更新组织配额成功", quotaService.updateDepartmentQuotaAndUsed(departmentId, quotaType));
    }

    @PutMapping("/project/{project_id}")
    @ApiOperation("更新项目配额")
    @LogRecordAnnotation(value = "更新项目配额", detail = "更新[{{#regionId}}]区域项目[{PROJECT{#projectId}}]的配额", resourceId = "{{#projectId}}", resource = "{PROJECT{#projectId}}")
    @ResourceAuth(handler = ProjectResourceAuthHandler.class, resources = "#projectId")
    public ResultModel<QuotaType> updateProjectQuota(@PathVariable("project_id") String projectId,
                                                        @Validated @RequestBody QuotaType quotaType){
        LogRecordContext.putVariable("regionId", request.getHeader(HeaderParamConstant.REGION_ID));
        return ResultModel.success("更新项目配额成功",this.quotaService.updateProjectQuota(projectId,quotaType));
    }

    @PutMapping("/project/used")
    @ApiOperation("更新项目配额使用量")
    @LogRecordAnnotation(value = "更新项目配额使用量", detail = "更新[{{#regionId}}]区域项目[{PROJECT{#quotaProjectValue.projectId}}]的配额使用量", resourceId = "{{#quotaProjectValue.projectId}}", resource = "{PROJECT{#quotaProjectValue.projectId}}")
    @ResourceAuth(handler = ProjectResourceAuthHandler.class, resources = "#quotaProjectValue.projectId")
    public ResultModel<QuotaProjectValue> updateProjectQuotaUsed(@Validated @RequestBody QuotaProjectValue quotaProjectValue){
        return ResultModel.success("更新项目配额使用量成功",this.quotaService.updateProjectQuotaUsed(quotaProjectValue));
    }

    @GetMapping("/rootuser/overview")
    @ApiImplicitParams({@ApiImplicitParam(name = "category_id", value = "配额所属分类", dataType = "String", paramType = "query")})
    @ApiOperation("查询配额总量使用情况")
    @ResourceAuth(handler = AdminUserTypeAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    @DocumentIgnore
    public ResultModel<List<QuotaType>> getQuotaOverview(@RequestParam(value = "category_id",required = false) String categoryId){
        return ResultModel.success("查询配额总量使用情况",this.quotaService.getQuotaOverview(categoryId));
    }

    @GetMapping("/rootuser/list")
    @ApiOperation("查询所有根账号配额列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "type_name", value = "配额类型", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "department_name", value = "根账号组织", dataType = "String", paramType = "query")})
    @ResourceAuth(handler = AdminUserTypeAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    @DocumentIgnore
    public ResultModel<List<QuotaRootUserEntity>> getRootUserQuotaList(@RequestParam(value = "type_name",required = false) String typeName,
                                                                       @RequestParam(value = "department_name",required = false) String departmentName){
        return ResultModel.success("查询所有根账号配额列表",this.quotaService.getRootUserQuotaList(typeName,departmentName));
    }

    @PutMapping("/rootuser/set")
    @ApiOperation("更新根账号配额")
    @ResourceAuth(handler = AdminUserTypeAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    @LogRecordAnnotation(value = "更新组织配额", detail = "更新组织{{#name}}的配额", resourceId = "{{#id}}", resource = "{{#name}}")
    @DocumentIgnore
    public ResultModel<List<QuotaMetric>> updateRootUserQuota(@RequestBody List<QuotaMetric> quotaMetrics){
        try {
            if (CollectionUtils.isNotEmpty(quotaMetrics)) {
                String departmentId = quotaMetrics.get(0).getDepartmentId();
                Department department = departmentMapper.selectById(departmentId);
                if (department != null) {
                    LogRecordContext.putVariable("name", department.getName());
                    LogRecordContext.putVariable("id", departmentId);
                }
            }
            return ResultModel.success("更新根账号配额成功",this.quotaService.updateRootUserQuota(quotaMetrics));
        } catch (Exception e){
            logger.error("updateRootUserQuota error:", e);
            if (CommonUtils.isContainChinese(e.getMessage())) {
                return ResultModel.error(e.getMessage());
            }
            return ResultModel.error("更新根账号配额失败");
        }
    }

    @GetMapping("/reset")
    @ApiOperation("重置配额")
    @ResourceAuth(handler = AdminUserTypeAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    @DocumentIgnore
    public ResultModel reset(@RequestHeader("regionid") String regionid){
        quotaService.resetQuota(regionid);
        return ResultModel.success("重置配额成功",null);
    }

    @GetMapping("/reset/{region_id}/rootGb")
    @ApiOperation("重置系统盘配额")
    @DocumentIgnore
    public ResultModel resetRootGb(@PathVariable("region_id") String regionId){
        quotaService.resetQuotaRootGb(regionId,"ecs_system_disk");
        return ResultModel.success("重置配额成功",null);
    }

    @GetMapping("/reset/use_total_quota")
    @ApiOperation("重置配额使用量和总量(用于初始化新增的配额指标同步,只允许单区域调用)")
    @ResourceAuth(handler = AdminUserTypeAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    @DocumentIgnore
    public ResultModel resetTotalQuotaByProjects(@RequestBody List<QuotaMetricAndProjectsUsedVO> quotaMetricAndProjectsUsedVOs){
        quotaService.resetTotalQuotaByProjects(quotaMetricAndProjectsUsedVOs);
        return ResultModel.success("重置配额成功",null);
    }

    @GetMapping("/topology")
    @ApiOperation("VDC项目配额拓扑")
    @LogRecordAnnotation(value = "VDC项目配额拓扑", detail = "查看[{DEPARTMENT{#departmentId}}]的配额拓扑", resourceId = "{{#departmentId}}", resource = "{DEPARTMENT{#departmentId}}")
    @ApiImplicitParams({@ApiImplicitParam(name = "type_name", value = "配额类型", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "department_id", value = "根账号组织", dataType = "String", paramType = "query")})
    @ResourceAuth(handler = AdminUserTypeAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    @DocumentIgnore
    public ResultModel<List<QuotaMetricTopology>> getTopology(@RequestParam(value = "type_name",required = false) String typeName,
                                                              @RequestParam(value = "department_id",required = false) String departmentId){
        return ResultModel.success("VDC项目配额拓扑",this.quotaService.getTopology(typeName,departmentId));
    }

    @DocumentIgnore
    @ResourceAuth(handler = AdminUserTypeAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    @ApiOperation("同步根组织的使用量")
    @PutMapping("/reset/root-qutota")
    public ResultModel resetRootDeptQuota (@RequestHeader("regionid") String regionid) {
        quotaService.resetRootDeptQuota(regionid);
        return ResultModel.success("重置根组织的使用量成功",null);
    }
}
