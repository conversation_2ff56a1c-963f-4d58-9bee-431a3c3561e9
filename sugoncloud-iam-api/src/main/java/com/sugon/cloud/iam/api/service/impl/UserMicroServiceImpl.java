package com.sugon.cloud.iam.api.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sugon.cloud.iam.api.entity.BlacklistMicroService;
import com.sugon.cloud.iam.api.entity.MicroService;
import com.sugon.cloud.iam.api.entity.UserMicroEntity;
import com.sugon.cloud.iam.api.entity.UserMicroViewer;
import com.sugon.cloud.iam.api.mapper.MicroServiceMapper;
import com.sugon.cloud.iam.api.mapper.UserMicroMapper;
import com.sugon.cloud.iam.api.service.BlacklistMicroServiceService;
import com.sugon.cloud.iam.api.service.MicroServiceCategoryService;
import com.sugon.cloud.iam.api.service.UserMicroService;
import com.sugon.cloud.iam.common.enums.BlacklistTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class UserMicroServiceImpl extends ServiceImpl<UserMicroMapper, UserMicroEntity> implements UserMicroService {
    private final MicroServiceCategoryService microServiceCategoryService;
    private final UserMicroMapper userMicroMapper;
    private final MicroServiceMapper microServiceMapper;
    @Value("${black.enable:false}")
    private boolean blackEnable;
    private final BlacklistMicroServiceService blacklistMicroServiceService;
    @Override
    public List<UserMicroViewer> queryList(String userId) {
//        List<String> authorizedLicenseKeys = microServiceCategoryService.getAuthorizedLicenseKeys();
        List<String> blackMicroIds = Collections.emptyList();
        List<String> cateGoryIds = Collections.emptyList();
        if (blackEnable) {
            List<BlacklistMicroService> blacklistMicroServices = blacklistMicroServiceService.listByTypes(Arrays.asList(BlacklistTypeEnum.MICRO, BlacklistTypeEnum.CATEGORY));
            blackMicroIds = blacklistMicroServices.stream().filter(p -> BlacklistTypeEnum.MICRO.equals(p.getType())).map(BlacklistMicroService::getId).collect(Collectors.toList());
            cateGoryIds = blacklistMicroServices.stream().filter(p -> BlacklistTypeEnum.CATEGORY.equals(p.getType())).map(BlacklistMicroService::getId).collect(Collectors.toList());
        }
        List<String> authorizedLicenseMicroServiceIds = microServiceMapper.selectList(new LambdaQueryWrapper<MicroService>()
                .select(MicroService::getId)
                .notIn(blackEnable && CollectionUtil.isNotEmpty(blackMicroIds), MicroService::getId, blackMicroIds)
                .notIn(blackEnable && CollectionUtil.isNotEmpty(cateGoryIds), MicroService::getCategoryId, cateGoryIds)                )
//                .in(MicroService::getLicenseKey, authorizedLicenseKeys))
                .stream()
                .map(MicroService::getId)
                .collect(Collectors.toList());
        return userMicroMapper.queryList(userId)
                .stream()
                .filter(item -> authorizedLicenseMicroServiceIds.contains(item.getMicroId()))
                .collect(Collectors.toList());
    }
}
