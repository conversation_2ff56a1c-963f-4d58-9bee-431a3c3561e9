package com.sugon.cloud.iam.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sugon.cloud.common.model.IdNameInfo;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.entity.CloudViewGatewayUser;
import com.sugon.cloud.iam.api.entity.Department;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.vo.*;
import com.sugon.cloud.iam.common.model.dto.UserDeptAndUserIdsDTO;
import com.sugon.cloud.iam.common.model.dto.UserOwnDeptAndProjectDTO;
import com.sugon.cloud.iam.common.model.vo.UserViewVO;
import com.sugon.cloud.iam.common.model.vo.UserVo;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface UserService extends IService<User> {

    String getProjectIdByUserName(String name);
    User getByName(String name);
    String createUser(UserCreateOrUpdateVO userVO);
    String createAdmin(UserCreateOrUpdateVO userVO, boolean checkUser);
    String createSubUser(UserCreateOrUpdateVO userVO, boolean checkUser);

    PageCL<UserViewVO> getUserPage(String userId, Integer pageNum,Integer pageSize, String currentUserId);

    String updatePassword(String userId, String password, String oldPassword);

    String delete(String userId);

    @Transactional(propagation = Propagation.REQUIRED)
    String deleteAdmin(String userId);

    int assignmentRole(String userId, String roleId, String projectId, boolean userExist);

    int removeRole(String userId, String roleId);

    PageCL<UserViewVO> getUserPageByDeptId(String deptId, Integer pageNum, Integer pageSize, String name, String userId, String userType);

    int assignmentProject(String userId, String projectId);

    int update(UserCreateOrUpdateVO user );

    /**
     * 用户批量绑定角色
     * @param userId
     * @param userBindRolesVO
     */
    void batchAssignmentRoles(String userId, UserBindRolesVO userBindRolesVO);

    /**
     * 用户批量绑定项目
     * @param userId
     * @param userBindProjectsVO
     */
    void batchAssignmentProjects(String userId, UserBindProjectsVO userBindProjectsVO);

    List<UserVo> getUsers(List<String> ids);


    /**
     * cloud view从gateway迁移主用户
     * @param cloudViewGatewayUser 主用户信息
     */
    Department createMasterUserFromGateway(CloudViewGatewayUser cloudViewGatewayUser);
    /**
     * cloud view从gateway迁移子用户
     * @param cloudViewGatewayUser 主用户信息
     * @param department 用户所属组织
     */
    void createSubUserFromGateway(CloudViewGatewayUser cloudViewGatewayUser, Department department);

    PageCL<UserViewVO>  getUsersByProjectId(int pageNum, int pageSize, String projectId, String userId) throws Exception;

    PageCL<UserViewVO> listAllUser(String currentUserId, Integer pageNum, Integer pageSize, String name);

    List<User> listInnerUser();

    void updateUserHash(String userId);

    void setUserDefaultResourceProject(String userId, String projectId, boolean cancel);

    UserOwnDeptAndProjectDTO getUserOwnDepartmentsAndProjects(String userId, String projectType);

    PageCL<UserViewVO> getUsersByDeptIdAndUserIds(UserDeptAndUserIdsDTO userDeptAndUserIdsDTO);

    PageCL<UserViewVO> getUsersByCurrentUserIdAndDeptIdAndNotUserIds(UserDeptAndUserIdsDTO userDeptAndUserIdsDTO);

    /**
     * 解锁用户
     * @param userId
     */
    String unlockUser(String userId);

    /**
     * 设置用户访问控制信息
     * @param accessControlVO
     * @param userId
     * @return
     */
    String setAccessControl(AccessControlVO accessControlVO, String userId);

    /**
     * 更新用户验证
     *
     * @param user
     */
    void updateUserCheck(UserCreateOrUpdateVO user);

    /**
     * inner用户登录验证
     *
     * @param username
     * @return
     */
    void checkInnerUserLogin(String username);

    /**
     * 获取用户的联系人信息
     * @param username
     * @return
     */
    UserViewVO getContactInfo(String username);

    /**
     * 检查平台用户
     * @param username
     */
    void checkPlatformUser(String username);


    PageCL<UserViewVO> listOrgUser(String currentUserId, Integer pageNum, Integer pageSize, String name);

    void bindUserRole(List<UserRoleVO> userRoleVOS);

    void unbindUserRole(List<UserRoleVO> userRoleVOS);

    void bindUserProject(List<UserProjectVO> userProjectVOS);

    void unbindUserProject(List<UserProjectVO> userProjectVOS);

}
