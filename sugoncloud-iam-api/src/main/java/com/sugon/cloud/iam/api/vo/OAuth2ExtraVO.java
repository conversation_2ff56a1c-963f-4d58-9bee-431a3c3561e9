package com.sugon.cloud.iam.api.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/05/19 09:10
 **/
@Data
@ApiModel("OAuth2相关参数")
public class OAuth2ExtraVO {
    @JsonProperty("service_provider_name")
    @ApiModelProperty("服务提供商")
    @NotBlank(message = "服务提供商必填")
    private String serviceProviderName;

    @JsonProperty("client_id")
    @ApiModelProperty("客户端Id")
    @NotBlank(message = "客户端Id必填")
    private String clientId;

    @JsonProperty("client_secret")
    @ApiModelProperty("客户端秘钥")
    private String clientSecret;

    @JsonProperty("token_get_method")
    @NotBlank(message = "token获取方法必填")
    @ApiModelProperty("token获取方法:GET|POST")
    private String tokenGetMethod;

    @ApiModelProperty("授权范围")
    @NotBlank(message = "授权范围必填")
    private String scope;

    @JsonProperty("authorize_url")
    @NotBlank(message = "授权端点地址必填")
    @ApiModelProperty("授权端点地址")
    private String authorizeUrl;

    @JsonProperty("token_url")
    @ApiModelProperty("token端点地址")
    @NotBlank(message = "token端点地址必填")
    private String tokenUrl;

    @JsonProperty("user_info_url")
    @ApiModelProperty("用户信息端点地址")
    @NotBlank(message = "用户信息端点地址必填")
    private String userInfoUrl;

//    @JsonProperty("logout_url")
//    @ApiModelProperty("注销会话端点地址")
//    private String logoutUrl;
//
//    @JsonProperty("logout_sync")
//    @ApiModelProperty("同步注销")
//    private boolean logoutSync;

    @JsonProperty("always_update_user_info")
    @ApiModelProperty("总是更新用户信息")
    private boolean alwaysUpdateUserInfo;

    @JsonProperty("user_info_attr_mapping")
    @ApiModelProperty("用户属性映射")
    @NotNull(message = "用户属性映射必填")
    private Map<String, String> userInfoAttrMapping;
}
