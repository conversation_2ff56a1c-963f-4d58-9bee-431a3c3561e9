package com.sugon.cloud.iam.api.provider;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.mapper.UserMapper;
import com.sugon.cloud.iam.api.service.impl.UserDetailsServiceImpl;
import com.sugon.cloud.iam.api.utils.EncryptAndDecryptUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;

import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SugonDaoAuthenticationProvider extends DaoAuthenticationProvider {

    private final PasswordEncoder passwordEncoder;
    @Value("${encryption.enabled:false}")
    private boolean encryptionEnabled;
    @Autowired
    private EncryptAndDecryptUtil encryptAndDecryptUtil;
    @Autowired
    private UserMapper userMapper;
    public SugonDaoAuthenticationProvider(UserDetailsServiceImpl userDetailsService, PasswordEncoder passwordEncoder){
        setUserDetailsService(userDetailsService);
        this.passwordEncoder = passwordEncoder;
    }
    @Override
    protected void additionalAuthenticationChecks(UserDetails userDetails, UsernamePasswordAuthenticationToken authentication) throws AuthenticationException {
        if (authentication.getCredentials() == null) {
            log.debug("Authentication failed: no credentials provided");
            throw new BadCredentialsException(this.messages.getMessage("AbstractUserDetailsAuthenticationProvider.badCredentials", "Bad credentials"));
        } else if (encryptionEnabled){//加密模式需要先通过密码机解密再匹配
            log.info("Authentication by encryptionEnabled");
            LambdaQueryWrapper<User> userQueryWrapper = new LambdaQueryWrapper<>();
            userQueryWrapper.eq(User::getName, userDetails.getUsername());
            User user = userMapper.selectOne(userQueryWrapper);
            String encodePassword = encryptAndDecryptUtil.decryptByIv(userDetails.getPassword(), user.getIv());
            String presentedPassword = authentication.getCredentials().toString();
            if (!this.passwordEncoder.matches(presentedPassword, encodePassword)) {
                log.debug("Authentication failed: password does not match stored value");
                throw new BadCredentialsException(this.messages.getMessage("AbstractUserDetailsAuthenticationProvider.badCredentials", "Bad credentials"));
            }
        } else {
            String presentedPassword = authentication.getCredentials().toString();
            if (!this.passwordEncoder.matches(presentedPassword, userDetails.getPassword())) {
                log.debug("Authentication failed: password does not match stored value");
                throw new BadCredentialsException(this.messages.getMessage("AbstractUserDetailsAuthenticationProvider.badCredentials", "Bad credentials"));
            }
        }
    }
}