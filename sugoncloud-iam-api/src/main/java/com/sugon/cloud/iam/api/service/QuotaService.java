package com.sugon.cloud.iam.api.service;

import com.sugon.cloud.iam.api.vo.DepartmentDetailVO;
import com.sugon.cloud.iam.common.model.vo.*;

import java.util.List;

public interface QuotaService {
    List<QuotaType> getQuotaByDeparmentId(String departmentId, String typeName, String categoryId);

    List<QuotaType> getQuotaByProjectId(String projectId, String typeName, String categoryId);

    QuotaType updateDepartmentQuota(String departmentId, QuotaType quotaType);

    QuotaType updateProjectQuota(String projectId, QuotaType quotaType);

    QuotaProjectValue updateProjectQuotaUsed(QuotaProjectValue quotaProjectValue);

    void deleteProjectQuota(ProjectDetailVO projectDetailVO);

    void deleteDepartmentQuota(DepartmentDetailVO departmentDetailVO);

    List<QuotaType> getQuotaOverview(String categoryId);

    List<QuotaRootUserEntity> getRootUserQuotaList(String typeName, String departmentName);

    List<QuotaMetric> updateRootUserQuota(List<QuotaMetric> quotaMetrics) throws Exception;

    void resetQuota(String regionid);

    void resetQuota(String regionid, List<String> metricName);

    void resetQuotaRootGb(String regionId, String metricName);

    void resetTotalQuotaByProjects(List<QuotaMetricAndProjectsUsedVO> quotaMetricAndProjectsUsedVOs);

    List<QuotaMetricTopology> getTopology(String typeName, String departmentId);

    void resetRootDeptQuota(String regionId);

    QuotaType updateDepartmentQuotaAndUsed(String departmentId, QuotaType quotaType);
}
