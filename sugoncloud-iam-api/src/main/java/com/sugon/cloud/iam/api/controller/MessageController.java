package com.sugon.cloud.iam.api.controller;

import cn.hutool.core.collection.CollUtil;
import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.mapper.ModelMapper;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.common.utils.UUIDUtil;
import com.sugon.cloud.iam.api.entity.DangerOperationUrl;
import com.sugon.cloud.iam.api.service.QuotaService;
import com.sugon.cloud.iam.api.service.message.DangerOperationUrlService;
import com.sugon.cloud.iam.api.service.message.MessageService;
import com.sugon.cloud.iam.api.service.message.messagetype.SmsMessageService;
import com.sugon.cloud.iam.api.vo.message.AuthorizeSendCodeVO;
import com.sugon.cloud.iam.api.vo.message.SendVO;
import com.sugon.cloud.iam.common.model.vo.DangerOperationUrlVO;
import com.sugon.cloud.iam.common.model.vo.PlatformSendNoticeVO;
import com.sugon.cloud.iam.common.model.vo.QuotaProjectValue;
import com.sugon.cloud.log.common.utils.CommonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @Author: yangdingshan
 * @Date: 2025/1/25 10:41
 * @Description:
 */
@Api(tags = "通知相关API")
@RequestMapping(value = "/api/messages")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RestController
@Slf4j
@RefreshScope
public class MessageController {

    private final MessageService messageService;

    private final RedisTemplate<String, Object> redisTemplate;

    private final DangerOperationUrlService dangerOperationUrlService;

    private final ModelMapper mapper;

    private final SmsMessageService smsMessageService;

    private final QuotaService quotaService;

    @Value("${default-regionId:RegionOne}")
    private String defaultRegionId;
    @PostMapping("/send")
    @ApiOperation("通用发送")
    public ResultModel<String> send(@RequestBody @Valid SendVO vo) {
        messageService.send(vo);
        return ResultModel.success("", "发送成功");
    }

    @PostMapping("/code")
    @ApiOperation("认证发送验证码")
    public ResultModel<String> authorizeSendCode(@RequestBody @Valid AuthorizeSendCodeVO vo) {
        messageService.authorizeSendCode(vo);
        return ResultModel.success("", "发送成功");
    }

    @PostMapping("/notice")
    @ApiOperation("平台通知类消息")
    public ResultModel<String> sendPlatformNotice(@RequestBody @Valid PlatformSendNoticeVO vo) {
        messageService.sendPlatformNotice(vo);
        return ResultModel.success("", "发送成功");
    }

    @PostMapping("/validate-code")
    @ApiOperation("验证验证码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "验证码", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = "expired", value = "是否使验证码失效", paramType = "query", defaultValue = "true", dataType = "boolean")
    })
    public ResultModel validateCode(@RequestParam Integer code,
                                    @RequestParam(required = false, defaultValue = "true") Boolean expired) {
        try {
            if (Boolean.FALSE.equals(redisTemplate.hasKey(String.valueOf(code)))) {
                return ResultModel.error("验证码错误,请重新输入");
            }
            if (Boolean.TRUE.equals(expired)) {
                redisTemplate.delete(String.valueOf(code));
            }
            return ResultModel.success("验证成功", "");
        } catch (Exception e) {
            log.error("验证验证码失败", e);
            if (CommonUtils.isContainChinese(e.getMessage())) {
                return ResultModel.error(e.getMessage());
            }
            return ResultModel.error("验证码错误,请重新输入");
        }
    }

    @GetMapping("/danger-operation-url")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "user_id", value = "用户id", dataType = "String")
    })
    @ApiOperation("危险操作URL列表")
    public ResultModel<List<DangerOperationUrlVO>> getDangerOperationUrl(@RequestParam(value = "user_id", required = false) String userId) {
        List<DangerOperationUrl> operationUrls = dangerOperationUrlService.getDangerOperationUrl(userId);
        if (CollUtil.isEmpty(operationUrls)) {
            return ResultModel.success("");
        }
        return ResultModel.success(mapper.mapList(operationUrls, DangerOperationUrlVO.class));
    }

    @PostMapping("/danger-operation-url")
    @ApiOperation("增加危险操作URL")
    public ResultModel<String> addDangerOperationUrl(@RequestBody @Valid DangerOperationUrlVO vo) {
        DangerOperationUrl operationUrl = mapper.map(vo, DangerOperationUrl.class);
        operationUrl.setId(UUIDUtil.get32UUID());
        dangerOperationUrlService.save(operationUrl);
        return ResultModel.success("");
    }

    @DeleteMapping("/danger-operation-url/{id}")
    @ApiOperation("删除危险操作URL")
    public ResultModel<String> deleteDangerOperationUrl(@PathVariable String id) {
        dangerOperationUrlService.removeById(id);
        return ResultModel.success("");
    }

    @PutMapping("/synchronize-quota/{project_id}")
    @ApiOperation("同步项目短信配额")
    @ApiImplicitParams({@ApiImplicitParam(name = "project_id", value = "项目ID", required = true, paramType = "path")})
    @DocumentIgnore
    public ResultModel synchronizeSmsProjectQuota(@PathVariable("project_id") String projectId) {
        ResultModel resultModel = new ResultModel();
        try {
            int feeCount = smsMessageService.getFeeCount(projectId);
            QuotaProjectValue quotaProjectValue = new QuotaProjectValue();
            quotaProjectValue.setName("sms_volume");
            quotaProjectValue.setUsedValue((long) feeCount);
            quotaProjectValue.setProjectId(projectId);
            quotaProjectValue.setRegionId(defaultRegionId);
            quotaService.updateProjectQuotaUsed(quotaProjectValue);
            resultModel = ResultModel.success("同步项目配额成功", true);
        } catch (Exception e) {
            log.error("同步项目配额失败", e);
            resultModel.setStatusCode(0);
            String errorMsg = "同步项目配额失败";
            if (CommonUtils.isContainChinese(e.getMessage())) {
                resultModel.setStatusMes(e.getMessage());
            } else {
                resultModel.setStatusMes(errorMsg);
            }
        }
        return resultModel;
    }
}
