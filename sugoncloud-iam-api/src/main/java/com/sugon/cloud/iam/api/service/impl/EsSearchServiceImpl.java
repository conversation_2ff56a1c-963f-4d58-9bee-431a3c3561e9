package com.sugon.cloud.iam.api.service.impl;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.google.common.collect.Lists;
import com.sugon.cloud.iam.api.config.MultiRegionEsConfig;
import com.sugon.cloud.iam.api.entity.MultiEsEntity;
import com.sugon.cloud.iam.api.exception.BusinessException;
import com.sugon.cloud.iam.api.service.EsSearchService;
import com.sugon.cloud.log.common.utils.ElasticsearchUtil;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.nio.reactor.IOReactorConfig;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.common.text.Text;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Description:
 * <p>
 * ClassName: EsSearch
 * date: 2021/12/9 16:19
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class EsSearchServiceImpl implements EsSearchService {

    @Value("${es.resources-index}")
    private String resourceIndex;
    /**
     * 连接池
     */
    @Value("${elasticsearch.pool}")
    private String poolSize;

    @Value("${elasticsearch.timeout}")
    private String timeout;

    @Value("${elasticsearch.keepAliveTime:1}")
    private Long keepAliveTime;

    @Autowired
    private MultiRegionEsConfig multiRegionEsConfig;


    private RestHighLevelClient createRestHighLevelClient(String regionId) {
        RestHighLevelClient restHighLevelClient = null;
        if(Objects.nonNull(multiRegionEsConfig) && !CollectionUtils.isEmpty(multiRegionEsConfig.getMultiEsEntityList())){
            MultiEsEntity multiEsEntity = null;
            for(MultiEsEntity met : multiRegionEsConfig.getMultiEsEntityList()){
                if(regionId.equals(met.getName())){
                    multiEsEntity = met;
                }
            }
            if(Objects.nonNull(multiEsEntity)){
                /* 用户认证对象 */
                CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                /* 设置账号密码 */
                credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(multiEsEntity.getUsername(), multiEsEntity.getPassword()));
                /* 创建rest client对象 */
                RestClientBuilder builder = RestClient.builder(new HttpHost(multiEsEntity.getIp(), Integer.parseInt(multiEsEntity.getPort())))
                        .setHttpClientConfigCallback(
                                httpClientBuilder -> httpClientBuilder.setDefaultIOReactorConfig(IOReactorConfig.custom()
                                        .setIoThreadCount(Integer.parseInt(poolSize))
                                        .build()).setDefaultCredentialsProvider(credentialsProvider)
                                        .setKeepAliveStrategy((response, context) -> TimeUnit.MINUTES.toMillis(keepAliveTime))
                        )
                        .setRequestConfigCallback(
                                requestConfigBuilder -> requestConfigBuilder
                                        .setConnectTimeout(Integer.parseInt(timeout))
                                        .setSocketTimeout(60000)
                        );
                restHighLevelClient = new RestHighLevelClient(builder);
            }

        }

        return restHighLevelClient;
    }

    @Override
    public Long searchResourceCount(String regionId, String project_id, String type) {
        RestHighLevelClient restHighLevelClient = this.createRestHighLevelClient(regionId);
        if(Objects.isNull(restHighLevelClient)){
            throw new BusinessException("查询项目上资源数量时出错");
        }
        try {
            CountRequest countRequest = new CountRequest(resourceIndex);
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termQuery("project_id.keyword",project_id));
            //过滤租户，租户类型资源的idcard就是自己的project_id
            boolQuery.mustNot(QueryBuilders.termQuery("idcard.keyword",project_id));
            boolQuery.mustNot(QueryBuilders.matchPhrasePrefixQuery("name", "SecGroup_sugoncloud").analyzer("standard"));
            //过滤默认安全组sg，名称为default，类型为sg的资源
            boolQuery.mustNot(QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("type.keyword","sg"))
                    .must(QueryBuilders.termQuery("name.keyword","default")));
            //过滤默认网络ACL，名称为default，类型为acl的资源
            boolQuery.mustNot(QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("type.keyword","acl"))
                    .must(QueryBuilders.termQuery("name.keyword","default")));
            if(!StringUtils.isEmpty(type)){
                boolQuery.must(QueryBuilders.termQuery("type.keyword",type));
            }
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQuery);
            countRequest.source(searchSourceBuilder);
            CountResponse countResponse = restHighLevelClient.count(countRequest, RequestOptions.DEFAULT);
            if (ObjectUtils.isNotEmpty(countResponse)){
                return countResponse.getCount();
            }
            return 0l;
        }catch (Exception e){
            throw new BusinessException("查询项目上资源数量时出错");
        }
    }

    @Override
    public List<Map<String, Object>> searchResource(String regionId, String project_id, String type, int pageNum, int pageSize) {
        RestHighLevelClient restHighLevelClient = this.createRestHighLevelClient(regionId);
        if(Objects.isNull(restHighLevelClient)){
            throw new BusinessException("查询项目上资源数量时出错");
        }
        List<Map<String, Object>> list = Lists.newArrayList();
        try {
            SearchRequest searchRequest = new SearchRequest(resourceIndex);
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termQuery("project_id.keyword",project_id));
            //过滤租户，租户类型资源的idcard就是自己的project_id
            boolQuery.mustNot(QueryBuilders.termQuery("idcard.keyword",project_id));
            boolQuery.mustNot(QueryBuilders.matchPhrasePrefixQuery("name", "SecGroup_sugoncloud").analyzer("standard"));
            //过滤默认安全组sg，名称为default，类型为sg的资源
            boolQuery.mustNot(QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("type.keyword","sg"))
                    .must(QueryBuilders.termQuery("name.keyword","default")));
            //过滤默认网络ACL，名称为default，类型为acl的资源
            boolQuery.mustNot(QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("type.keyword","acl"))
                    .must(QueryBuilders.termQuery("name.keyword","default")));
            if(!StringUtils.isEmpty(type)){
                boolQuery.must(QueryBuilders.termQuery("type.keyword",type));
            }
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQuery);
            searchSourceBuilder.from((pageNum - 1) * pageSize);
            //时间倒序
            searchSourceBuilder.size(pageSize);
            FieldSortBuilder fieldSortBuilder = new FieldSortBuilder("createdate").order(SortOrder.DESC);
            searchSourceBuilder.sort(fieldSortBuilder);
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            List<Map<String, Object>> datas = this.setSearchResponse(searchResponse);
            if(!CollectionUtils.isEmpty(datas)){
                list.addAll(datas);
            }
        }catch (Exception e){
            throw new BusinessException("查询项目上的所有资源出错");
        }
        //5119 evs、evss时间显示不对
        if ("evs".equalsIgnoreCase(type) || "evss".equalsIgnoreCase(type)) {
            list.forEach(o -> {
                Object createdate = o.get("createdate");
                if (ObjectUtils.isNotEmpty(createdate)) {
                    DateTime parse = DateUtil.parse(createdate.toString(), "yyyy-MM-dd HH:mm:ss");
                    Date date = new Date(parse.getTime() + 8 * 60 * 60 * 1000);
                    o.put("createdate", DateUtil.format(date, "yyyy-MM-dd HH:mm:ss"));
                }
            });
        }
        return list;
    }

    private List<Map<String, Object>> setSearchResponse(SearchResponse searchResponse) {
        List<Map<String, Object>> sourceList = new ArrayList<Map<String, Object>>();
        for (SearchHit searchHit : searchResponse.getHits().getHits()) {
            searchHit.getSourceAsMap().put("id", searchHit.getId());
            sourceList.add(searchHit.getSourceAsMap());
        }
        return sourceList;
    }

}
