package com.sugon.cloud.iam.api.entity.keystone.VO;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sugon.cloud.iam.api.entity.keystone.CreateRoleResponseParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@TableName(value = "trust")
@ApiModel(value = "租户授权响应参数")
public class TrustResultVO {
    @TableId(type = IdType.UUID)
    @ApiModelProperty("主键")
    private String id;
    @JsonProperty(value = "trustor_user_id")
    @ApiModelProperty("委托用户id")
    private String trustorUserId;
    @JsonProperty(value = "trustee_user_id")
    @ApiModelProperty("受托人用户ID")
    private String trusteeUserId;
    @JsonProperty(value = "project_id")
    @ApiModelProperty("项目ID")
    private String projectId;
    @ApiModelProperty("是否允许重委托")
    private boolean impersonation;
    @JsonProperty(value = "deletedAt")
    @ApiModelProperty("删除时间")
    private Date deletedAt;
    @JsonProperty(value = "expiresAt")
    @ApiModelProperty("过期时间")
    private Date expires_at;
    @JsonProperty(value = "remaining_uses")
    @ApiModelProperty("剩余使用次数")
    private int remainingUses;
    @JsonProperty(value = "expires_at_int")
    @ApiModelProperty("过期时间")
    private long expiresAtInt;
    @ApiModelProperty("角色列表")
    private List<CreateRoleResponseParam> roles;
    @JsonProperty(value = "roles_links")
    private Map rolesLinks;
    @ApiModelProperty("资源链接")
    private Map links;
}
