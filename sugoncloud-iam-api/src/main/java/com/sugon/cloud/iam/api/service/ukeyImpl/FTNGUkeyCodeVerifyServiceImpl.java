package com.sugon.cloud.iam.api.service.ukeyImpl;

import com.alibaba.fastjson.JSONObject;
import com.ft.otp.core.ReturnResult;
import com.sugon.cloud.iam.api.service.MFAVerifyService;
import com.sugon.cloud.iam.api.utils.FTNGApi;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

@RefreshScope
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Service("ftngMFAVerify")
@Slf4j
/**
 * 坚石诚信双因子校验
 */
@ConditionalOnExpression("${mfa.enabled} &&'${mfa.type}'.contains('ftng')")

public class FTNGUkeyCodeVerifyServiceImpl implements MFAVerifyService {

    private final FTNGApi ftngApi;
    @Override
    public Boolean verifyMfaToken(String tokenSN, String mfaCode, String challenge) {
        try {
            log.debug("ftng verify token: {}, code: {}, challenge: {}", tokenSN, mfaCode, challenge);
            if (StringUtils.isBlank(mfaCode)) {
                log.error("mfa code can not blank");
                return false;
            }
            ReturnResult returnResult = ftngApi.authOTP(tokenSN, mfaCode);
            log.debug("ftng verify result: {}", JSONObject.toJSONString(returnResult));
            return returnResult.getReturnCode() == 0;
        } catch (Exception e) {
            log.error("ftng verify error", e);
            return false;
        }
    }
}
