package com.sugon.cloud.iam.api.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.constants.HeaderParamConstant;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.common.mapper.ModelMapper;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.ann.CreateGroup;
import com.sugon.cloud.iam.api.ann.UpdateGroup;
import com.sugon.cloud.iam.api.entity.IamRole;
import com.sugon.cloud.iam.api.entity.UserRoleMapping;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.exception.BusinessException;
import com.sugon.cloud.iam.api.service.GlobalsettingsService;
import com.sugon.cloud.iam.api.service.IamRoleService;
import com.sugon.cloud.iam.api.service.UserRoleMappingService;
import com.sugon.cloud.iam.api.service.UserService;
import com.sugon.cloud.iam.api.service.resourceauth.DeptResourceAuthHandler;
import com.sugon.cloud.iam.api.service.resourceauth.RoleAuthHandler;
import com.sugon.cloud.iam.api.service.resourceauth.SecurityUserTypeAuthHandler;
import com.sugon.cloud.iam.api.service.resourceauth.UserCreateAuthHandler;
import com.sugon.cloud.iam.api.service.resourceauth.UserResourceAuthHandler;
import com.sugon.cloud.iam.api.utils.EncryptAndDecryptUtil;
import com.sugon.cloud.iam.api.vo.RoleCreateOrUpdate;
import com.sugon.cloud.iam.common.model.vo.RoleResponseVO;
import com.sugon.cloud.log.client.starter.annotation.LogRecordAnnotation;
import com.sugon.cloud.resource.auth.common.aop.ResourceAuth;
import com.sugon.cloud.resource.auth.common.aop.ResourceAuths;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Api(tags = "角色API")
@RequestMapping(value = "/api/roles")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RestController
public class IamRoleController {

    private final IamRoleService roleService;
    private final ModelMapper mapper;
    @Value("${encryption.enabled}")
    private boolean encryption;
    private final EncryptAndDecryptUtil encryptAndDecryptUtil;
    private final UserService userService;
    private final UserRoleMappingService userRoleMappingService;
    private final GlobalsettingsService globalsettingsService;

    @PostMapping
    @ApiOperation("添加角色")
    @LogRecordAnnotation(value = "添加角色", detail = "添加角色:{{#roleCreateOrUpdate.name}}", resourceId = "{{#id}}", resource = "{{#roleCreateOrUpdate.name}}", auditFlag = "true")
    @ResourceAuths(value = {
            @ResourceAuth(handler = UserCreateAuthHandler.class, resources = TypeUtil.TYPE_ADMIN),
            @ResourceAuth(handler = DeptResourceAuthHandler.class, resources = "#roleCreateOrUpdate.deptId"),
    })
    public ResultModel<String> createRole(@RequestBody @Validated({CreateGroup.class}) RoleCreateOrUpdate roleCreateOrUpdate) {
        roleService.createIamRole(roleCreateOrUpdate);
        return ResultModel.success("添加角色成功", roleCreateOrUpdate.getId());
    }

    @DeleteMapping("{role_id}")
    @ApiOperation("删除角色")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "role_id", value = "角色ID", required = true, dataType = "String", paramType = "path"),
    })
    @LogRecordAnnotation(value = "删除角色", detail = "删除角色:{ROLE{#roleId}}", resourceId = "{{#roleId}}", resource = "{ROLE{#roleId}}", auditFlag = "true")
    @ResourceAuth(handler = RoleAuthHandler.class, resources = "#roleId")
    public ResultModel<String> deleteRole(@PathVariable("role_id") String roleId) {
        List<UserRoleMapping> userRoleMappings = new ArrayList<>();
        if (encryption) {
            userRoleMappings = roleService.listUserRoleMappingByRoleId(roleId);
        }
        String name = roleService.deleteRole(roleId);

        if (encryption) {
            for (UserRoleMapping userRoleMapping : userRoleMappings) {
                this.updateUserRoleHash(userRoleMapping.getUserId());
            }
        }
        //future 删除角色 重新同步 用户角色关系到Redis中 start
        userRoleMappingService.syncUserRole2Redis();
        //future 删除角色 重新同步 用户角色关系到Redis中 end
        return ResultModel.success("删除角色成功", name,null);
    }

    @PutMapping()
    @ApiOperation("更新")
    @LogRecordAnnotation(value = "更新角色", detail = "更新角色:{{#roleCreateOrUpdate.name}}", resourceId = "{{#roleCreateOrUpdate.id}}", resource = "{{#roleCreateOrUpdate.name}}", auditFlag = "true")
    @ResourceAuth(handler = RoleAuthHandler.class, resources = "#roleCreateOrUpdate.id")
    public ResultModel<String> update (@RequestBody @Validated(UpdateGroup.class) RoleCreateOrUpdate roleCreateOrUpdate) {
        IamRole originRole = roleService.getById(roleCreateOrUpdate.getId());
        if (Objects.isNull(originRole)) {
            throw new BusinessException("角色不存在: " + roleCreateOrUpdate.getId());
        }
        if (TypeUtil.isInnerRole(originRole.getType())) {
            throw new BusinessException("内置角色不能被修改");
        }
        // 管理员修改，整个角色表中名称不能重复，其他角色是组织下不能重复
        boolean existed = roleService.count(new LambdaQueryWrapper<IamRole>()
                    .eq(IamRole::getName, roleCreateOrUpdate.getName())
                    .eq(TypeUtil.TYPE_SUB_ADMIN.equals(originRole.getType()), IamRole::getType, TypeUtil.TYPE_SUB_ADMIN)
                    .eq(!TypeUtil.TYPE_SUB_ADMIN.equals(originRole.getType()), IamRole::getDeptId, roleCreateOrUpdate.getDeptId())
                    .ne(IamRole::getId, roleCreateOrUpdate.getId())) > 0;

        if (existed) throw new BusinessException("角色名已存在: " + roleCreateOrUpdate.getName());
        IamRole role = new IamRole();
        BeanUtils.copyProperties(roleCreateOrUpdate, role);
        roleService.saveOrUpdate(role);
        return ResultModel.success("修改角色成功", null);
    }

    @GetMapping("user/{user_id}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "user_id", value = "用户ID", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "project_id", value = "项目ID", dataType = "String", paramType = "query"),
    })
    @ApiOperation("通过用户Id查询角色列表")
    public ResultModel<List<RoleResponseVO>> listByUserId (@PathVariable("user_id") String userId,
                                                           @RequestParam(value = "project_id", required = false) String projectId) {
        return ResultModel.success(roleService.list(userId, projectId, false));
    }

    @GetMapping("user/{user_id}/project/{project_id}")
    @ApiOperation("通过用户Id、项目Id查询角色列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "user_id", value = "用户ID", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "project_id", value = "项目ID", required = true, dataType = "String", paramType = "path"),
    })
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    public ResultModel<List<RoleResponseVO>> listByUserIdAndProjectId (@PathVariable("user_id") String userId,
                                                          @PathVariable("project_id") String projectId) {
        return ResultModel.success(mapper.mapList(roleService.findByUserIdAndProjectId(userId, projectId), RoleResponseVO.class));
    }

    @GetMapping("dept/{dept_id}")
    @ApiOperation("通过组织ID查询角色列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dept_id", value = "组织ID", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "name", value = "模糊查询名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "page_num", value = "页码", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "page_size", value = "每页显示条数", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "user_type", value = "用户类型", paramType = "query", dataType = "String"),
    })
    @ResourceAuth(handler = DeptResourceAuthHandler.class, resources = "#deptId")
    public ResultModel<PageCL<RoleResponseVO>> listByDept(@PathVariable("dept_id") String deptId,
                                                          @RequestParam(value = "name", required = false) String name,
                                                          @RequestParam(value = "page_num", defaultValue = "1", required = false) int pageNum,
                                                          @RequestParam(value = "page_size", defaultValue = "15", required = false) int pageSize,
                                                          @RequestParam(value = "user_type", required = false) String userType) {
        PageCL<RoleResponseVO> pageResult = roleService.listByDept(deptId, name, pageNum, pageSize, userType);
        return ResultModel.success(pageResult);
    }

    @GetMapping("/{role_id}")
    @ApiOperation("通过角色ID查询角色详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "role_id", value = "角色ID", required = true, dataType = "String", paramType = "path"),
    })
    @ResourceAuth(handler = RoleAuthHandler.class, resources = "#roleId")
    public ResultModel<RoleResponseVO> roleDetail (@PathVariable("role_id") String roleId) {
        RoleResponseVO roleResponseVO = new RoleResponseVO();
        IamRole iamRole = roleService.getById(roleId);
        BeanUtils.copyProperties(iamRole, roleResponseVO);
        return ResultModel.success(roleResponseVO);
    }

    @GetMapping("/inner")
    @ApiOperation("查出内置角色")
    @ResourceAuth(handler = SecurityUserTypeAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    @DocumentIgnore
    public ResultModel<List<RoleResponseVO>> innerRole() {
        List<String> innerRoleTypes;
        if (globalsettingsService.getBmStatus()) {
            innerRoleTypes = TypeUtil.getBmInnerRoleTypes();
        } else {
            innerRoleTypes = TypeUtil.getInnerRoleTypes();
        }
        List<IamRole> list = roleService.list(new QueryWrapper<IamRole>().lambda().in(IamRole::getType, innerRoleTypes));
        List<RoleResponseVO> vs = Lists.newArrayList();
        list.forEach(r->{
            RoleResponseVO v = mapper.map(r, RoleResponseVO.class);
            vs.add(v);
        });
        return ResultModel.success(vs);
    }

    @GetMapping("/all")
    @ApiOperation("查询所有角色")
    @ApiImplicitParams({@ApiImplicitParam(name = "name", value = "模糊查询名称",paramType = "query" ,dataType = "String"),
            @ApiImplicitParam(name = "page_num", value = "页码",paramType = "query" ,dataType = "int"),
            @ApiImplicitParam(name = "page_size", value = "每页显示条数", paramType = "query", dataType = "int")})
    public ResultModel<PageCL<RoleResponseVO>> listByDept(HttpServletRequest req,
                                                          @RequestParam(value = "name", required = false) String name,
                                                          @RequestParam(value = "page_num", defaultValue = "1", required = false) int pageNum,
                                                          @RequestParam(value = "page_size", defaultValue = "15" ,required = false) int pageSize) {
        PageCL<RoleResponseVO> pageResult = roleService.listAll(req.getHeader(HeaderParamConstant.USER_ID),name,pageNum,pageSize);
        return ResultModel.success(pageResult);
    }

    @GetMapping("/all-forhash")
    @ApiOperation("查询所有角色-for-hash")
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    @DocumentIgnore
    public ResultModel<List<String>> listUseForHash(@RequestParam(value = "user_id") String userId) {
        return ResultModel.success(roleService.listUseForHash(userId));
    }

    private void updateUserRoleHash(String userId) {
        if (encryption) {
            List<String> ids = roleService.listUseForHash(userId).stream().sorted().collect(Collectors.toList());
            String hash = encryptAndDecryptUtil.sign(JSONObject.toJSONString(ids));
            userService.lambdaUpdate().eq(User::getId, userId).set(User::getHashRole, hash).update();
        }
    }
}
