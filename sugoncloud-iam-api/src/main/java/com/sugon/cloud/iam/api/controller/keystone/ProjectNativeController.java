package com.sugon.cloud.iam.api.controller.keystone;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.iam.api.entity.keystone.Assignment;
import com.sugon.cloud.iam.api.entity.keystone.Project;
import com.sugon.cloud.iam.api.entity.keystone.Role;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.entity.keystone.VO.ProjectNativeCreateVO;
import com.sugon.cloud.iam.api.entity.keystone.VO.ProjectNativeDetailVO;
import com.sugon.cloud.iam.api.entity.keystone.VO.ProjectSubmitVO;
import com.sugon.cloud.iam.api.entity.exception.*;
import com.sugon.cloud.iam.api.service.AssignmentService;
import com.sugon.cloud.iam.api.service.ProjectService;
import com.sugon.cloud.iam.api.service.RoleService;
import com.sugon.cloud.iam.api.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/v3/projects")
@Api(tags = {"项目操作"})
@DocumentIgnore
public class ProjectNativeController {
    @Value("${vip.ip}")
    private String ramIP;
    @Value("${vip.port}")
    private String ramPort;
    @Autowired
    private RoleService roleService;
    @Autowired
    private UserService userService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private AssignmentService assignmentService;

    @PutMapping("{project_id}/users/{user_id}/roles/{role_id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @ApiOperation(value = "为用户分配角色")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "project_id", value = "项目ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "user_id", value = "用户ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "role_id", value = "角色ID", required = true, dataType = "String")})
    public void assignRole2UserOnProject(@PathVariable("project_id") String projectId,
                                         @PathVariable("user_id") String userId,
                                         @PathVariable("role_id") String roleId,
                                         HttpServletResponse response) {
        LambdaQueryWrapper<Project> projectWrapper = new LambdaQueryWrapper<>();
        projectWrapper.eq(Project::getId, projectId);
        Project project = projectService.getOne(projectWrapper);
        if (project == null) throw new ProjectCannotFindException("{\n" +
                "    \"error\": {\n" +
                "        \"message\": \"Could not find project: "+projectId+".\",\n" +
                "        \"code\": 404,\n" +
                "        \"title\": \"Not Found\"\n" +
                "    }\n" +
                "}");

        LambdaQueryWrapper<User> userWrapper = new LambdaQueryWrapper<>();
        userWrapper.eq(User::getId, userId);
        User user = userService.getOne(userWrapper);
        if (user == null) throw new UserCannotFindException("{\n" +
                "    \"error\": {\n" +
                "        \"message\": \"Could not find user: "+userId+".\",\n" +
                "        \"code\": 404,\n" +
                "        \"title\": \"Not Found\"\n" +
                "    }\n" +
                "}");
        LambdaQueryWrapper<Role> roleWrapper = new LambdaQueryWrapper<>();
        roleWrapper.eq(Role::getId, roleId);
        Role role = roleService.getOne(roleWrapper);
        if (role == null) throw new RoleCannotFindException("{\n" +
                "    \"error\": {\n" +
                "        \"message\": \"Could not find role: "+roleId+".\",\n" +
                "        \"code\": 404,\n" +
                "        \"title\": \"Not Found\"\n" +
                "    }\n" +
                "}");
        response.setStatus(HttpServletResponse.SC_NO_CONTENT);
        LambdaQueryWrapper<Assignment> assignmentWrapper = new LambdaQueryWrapper<>();
        assignmentWrapper.eq(Assignment::getActorId, userId);
        assignmentWrapper.eq(Assignment::getRoleId, roleId);
        assignmentWrapper.eq(Assignment::getTargetId, projectId);
        Assignment assignment = assignmentService.getOne(assignmentWrapper);
        if (assignment != null) {
            return;
        }
        if (assignment == null) assignment = new Assignment();
        assignment.setActorId(userId);
        assignment.setRoleId(roleId);
        assignment.setTargetId(projectId);
        assignment.setType("UserProject");
        assignment.setInherited(false);
        assignmentService.save(assignment);
    }

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @ApiOperation(value = "创建项目")
    public Map<String, Object> createProject(@RequestBody ProjectSubmitVO projectSubmitVO) {
        ProjectNativeCreateVO projectVO = projectSubmitVO.getProject();
        String parentId = null;
        if (StringUtils.isNotBlank(projectVO.getDomainId())) {
            LambdaQueryWrapper<Project> projectWrapper = new LambdaQueryWrapper<>();
            projectWrapper.eq(Project::getId, projectVO.getDomainId());
            Project targetProject = projectService.getOne(projectWrapper);
            if (targetProject == null) throw new ProjectCannotFindException("{\n" +
                    "    \"error\": {\n" +
                    "        \"message\": \"Could not find domain: "+projectVO.getDomainId()+".\",\n" +
                    "        \"code\": 404,\n" +
                    "        \"title\": \"Not Found\"\n" +
                    "    }\n" +
                    "}");
            parentId = targetProject.getId();
        }
        if (StringUtils.isNotBlank(projectVO.getName())) {
            LambdaQueryWrapper<Project> projectWrapper = new LambdaQueryWrapper<>();
            projectWrapper.eq(Project::getName, projectVO.getName());
            projectWrapper.eq(StringUtils.isNotBlank(projectVO.getDomainId()), Project::getDomainId, projectVO.getDomainId());
            Project sameProject = projectService.getOne(projectWrapper);
            if (sameProject != null) {
                throw new ResourceConflictException("Conflict occurred attempting to store project - it is not permitted to have two projects with the same name in the same domain : "+projectVO.getName());
            }
        }else {
            throw new KeyStoneMethodArgumentNotValidException("name is a required property");
        }
        if (projectVO.getDomain() && StringUtils.isNotBlank(projectVO.getDomainId()))
            throw new NativeBadRequestException("{\n" +
                    "    \"error\": {\n" +
                    "        \"message\": \"only root projects are allowed to act as domains.\",\n" +
                    "        \"code\": 400,\n" +
                    "        \"title\": \"Bad Request\"\n" +
                    "    }\n" +
                    "}");
        Project project = new Project();
        BeanUtils.copyProperties(projectVO, project);
        if (projectVO.getDomain()) {
            project.setDomainId("<<keystone.domain.root>>");
            if ("Default".equals(projectVO.getName())) {
                project.setId("default");
            }
        }
        project.setParentId(parentId);
        projectService.save(project);
        ProjectNativeDetailVO detailVO = new ProjectNativeDetailVO();
        BeanUtils.copyProperties(project, detailVO);
        detailVO.setDomain(projectVO.getDomain());
        String href = "http://"+ramIP+":"+ramPort+"/v3/projects/"+ project.getId();
        Map links = Maps.newHashMap();
        links.put("self", href);
        detailVO.setLinks(links);
        Map returnMap = new HashMap();
        returnMap.put("project", detailVO);
        return  returnMap;
    }

    @GetMapping
    @ApiOperation(value = "获取项目列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "domain_id", value = "域id", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "enabled", value = "是否启用", dataType = "boolean", paramType = "query"),
            @ApiImplicitParam(name = "is_domain", value = "是否是域", dataType = "boolean", paramType = "query"),
            @ApiImplicitParam(name = "name", value = "项目名称", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "parent_id", value = "父id", dataType = "string", paramType = "query"),
        })
    public Map<String, Object> projectList(@RequestParam(value = "domain_id", required = false) String domainId,
                            @RequestParam(value = "enabled", required = false) Boolean enabled,
                            @RequestParam(value = "is_domain", required = false) Boolean domain,
                            @RequestParam(value = "name", required = false) String name,
                            @RequestParam(value = "parent_id", required = false) String parentId,
                            HttpServletRequest request) {
        LambdaQueryWrapper<Project> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(domainId),Project::getDomainId, domainId);
        queryWrapper.eq(enabled != null,Project::getEnabled, enabled);
        queryWrapper.eq(domain !=null,Project::getDomain, domain);
        queryWrapper.eq(StringUtils.isNotBlank(name),Project::getName, name);
        queryWrapper.eq(StringUtils.isNotBlank(parentId),Project::getParentId, parentId);
        List<Project> list = projectService.list(queryWrapper);
        List<ProjectNativeDetailVO> detailVOS = new ArrayList<>();
        Map links = Maps.newHashMap();
        for (Project project:list) {
            ProjectNativeDetailVO projectNativeDetailVO = new ProjectNativeDetailVO();
            BeanUtils.copyProperties(project, projectNativeDetailVO);
            String href = "http://"+ramIP+":"+ramPort+"/v3/projects/"+ project.getId();
            links.put("self", href);
            projectNativeDetailVO.setLinks(links);
            detailVOS.add(projectNativeDetailVO);
        }
        Map map = new HashMap();
        map.put("projects",detailVOS);
        String queryString = request.getQueryString() == null?"":"?"+request.getQueryString();
        String href = "http://"+ramIP+":"+ramPort+"/v3/projects/"+queryString;
        links.put("self", href);
        links.put("previous", null);
        links.put("next", null);
        map.put("links",links);
        return map;
    }

    @GetMapping("/{project_id}")
    @ApiOperation(value = "获取项目详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "project_id", value = "project_id", dataType = "string", paramType = "path"),
    })
    public Map<String, Object> getProject(@PathVariable("project_id") String projectId) {
        LambdaQueryWrapper<Project> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Project::getId, projectId);
        Project project = projectService.getOne(queryWrapper);
        if (project == null) throw new ProjectCannotFindException("{\n" +
                "    \"error\": {\n" +
                "        \"message\": \"Could not find project: "+projectId+".\",\n" +
                "        \"code\": 404,\n" +
                "        \"title\": \"Not Found\"\n" +
                "    }\n" +
                "}");
        Map links = Maps.newHashMap();
        String href = "http://"+ramIP+":"+ramPort+"/v3/projects/"+ projectId;
        links.put("self", href);
        ProjectNativeDetailVO projectNativeDetailVO = new ProjectNativeDetailVO();
        BeanUtils.copyProperties(project, projectNativeDetailVO);
        projectNativeDetailVO.setLinks(links);
        Map resultMap = new HashMap();
        resultMap.put("project", projectNativeDetailVO);
        return resultMap;
    }
}

