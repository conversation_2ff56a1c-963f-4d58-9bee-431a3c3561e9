package com.sugon.cloud.iam.api.service.resourceauth;

import cn.hutool.core.collection.CollectionUtil;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.entity.StrategyForm;
import com.sugon.cloud.iam.api.service.StrategyService;
import com.sugon.cloud.resource.auth.common.service.impl.BaseResourceAuthHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/17 14:37
 */
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class StrategyAuthHandler extends BaseResourceAuthHandler {

    private final StrategyService strategyService;

    private final SecurityUserTypeAuthHandler securityUserTypeAuthHandler;

    @Override
    public List<String> getUserResource(String userId) {
        List<String> userResource = securityUserTypeAuthHandler.getUserResource(userId);
        if (CollectionUtil.isNotEmpty(userResource)) {
            return userResource;
        }
        PageCL<StrategyForm> page = strategyService.findPage(userId, 1, 999999);
        List<StrategyForm> list = page.getList();
        return list.stream().map(StrategyForm::getId).collect(Collectors.toList());
    }
}
