package com.sugon.cloud.iam.api.controller.keystone;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Maps;
import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.iam.api.entity.exception.ProjectCannotFindException;
import com.sugon.cloud.iam.api.entity.exception.ResourceNotFoundException;
import com.sugon.cloud.iam.api.entity.exception.RoleCannotFindException;
import com.sugon.cloud.iam.api.entity.exception.UserCannotFindException;
import com.sugon.cloud.iam.api.entity.keystone.Assignment;
import com.sugon.cloud.iam.api.entity.keystone.Project;
import com.sugon.cloud.iam.api.entity.keystone.Role;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.entity.keystone.VO.DomainVO;
import com.sugon.cloud.iam.api.entity.keystone.VO.ProjectNativeDetailVO;
import com.sugon.cloud.iam.api.service.AssignmentService;
import com.sugon.cloud.iam.api.service.ProjectService;
import com.sugon.cloud.iam.api.service.RoleService;
import com.sugon.cloud.iam.api.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Slf4j
@RequestMapping("/v3/domains")
@RestController
@Api(tags = {"keystone-domain操作"})
@DocumentIgnore
public class DomainController {
    @Value("${vip.ip}")
    private String ramIP;
    @Value("${vip.port}")
    private String ramPort;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private RoleService roleService;
    @Autowired
    private UserService userService;
    @Autowired
    private AssignmentService assignmentService;

    @GetMapping("/{domain_id}")
    @ApiOperation(value = "获取域详情")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "domain_id", value = "域ID", required = true, dataType = "String")})
    public Map<String, Object> getDomainDetail(@PathVariable("domain_id") String domainId) {
        Project project = projectService.getOne(new QueryWrapper<Project>().lambda()
                .eq(Project::getId, domainId)
                .or(qw -> qw.eq(Project::getName, domainId))
                .eq(Project::getDomain, true));
        if (project == null) throw new ResourceNotFoundException("Could not find domain: "+domainId);
        DomainVO domainVO = new DomainVO();
        BeanUtils.copyProperties(project, domainVO);
        Map links = Maps.newHashMap();
        String href = "http://"+ramIP+":"+ramPort+"/v3/domains/"+ domainId;
        links.put("self", href);
        domainVO.setLinks(links);
        Map resultMap = new HashMap();
        resultMap.put("domain", domainVO);
        return resultMap;
    }

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @ApiOperation(value = "创建域")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "domain", value = "域实体", required = true, dataType = "JSONObject")})
    public Map<String, Object> createDomain(@RequestBody JSONObject jsonObject) {
        Map<String, String> o = (HashMap) jsonObject.get("domain");
        String name = o.get("name");
        Project project = new Project();
        List<Project> list = projectService.list(new QueryWrapper<Project>().lambda().eq(Project::getName, name));
        if (!CollectionUtils.isEmpty(list)) {
            project = list.get(0);
        }
        if (StringUtils.isNotBlank(project.getId())) {
            DomainVO domainVO = new DomainVO();
            BeanUtils.copyProperties(project, domainVO);
            Map links = Maps.newHashMap();
            String href = "http://"+ramIP+":"+ramPort+"/v3/domains/"+ project.getId();
            links.put("self", href);
            domainVO.setLinks(links);
            Map resultMap = new HashMap();
            resultMap.put("domain", domainVO);
            return resultMap;
        } else {
             project.setDomainId("<<keystone.domain.root>>");
             project.setName(name);
             project.setDomain(true);
             project.setEnabled(true);
             projectService.save(project);
             DomainVO domainVO = new DomainVO();
             BeanUtils.copyProperties(project, domainVO);
             domainVO.setEnabled(project.getDomain());
             String href = "http://"+ramIP+":"+ramPort+"/v3/domains/"+ project.getId();
             Map links = Maps.newHashMap();
             links.put("self", href);
             domainVO.setLinks(links);
             Map returnMap = new HashMap();
             returnMap.put("domain", domainVO);
             return returnMap;
        }
    }
    @PutMapping("{domain_id}/users/{user_id}/roles/{role_id}")
    @ResponseStatus(HttpStatus.OK)
    @ApiOperation(value = "为域下的用户分配角色")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "domain_id", value = "域ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "user_id", value = "用户ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "role_id", value = "角色ID", required = true, dataType = "String")})
    public void assignRole2UserOnDomain(@PathVariable("domain_id") String domainId,
                                         @PathVariable("user_id") String userId,
                                         @PathVariable("role_id") String roleId,
                                         HttpServletResponse response) {
        LambdaQueryWrapper<Project> projectWrapper = new LambdaQueryWrapper<>();
        projectWrapper.eq(Project::getId, domainId);
        Project project = projectService.getOne(projectWrapper);
        if (project == null) throw new ProjectCannotFindException("{\n" +
                "    \"error\": {\n" +
                "        \"message\": \"Could not find domian: "+domainId+".\",\n" +
                "        \"code\": 404,\n" +
                "        \"title\": \"Not Found\"\n" +
                "    }\n" +
                "}");

        LambdaQueryWrapper<User> userWrapper = new LambdaQueryWrapper<>();
        userWrapper.eq(User::getId, userId);
        User user = userService.getOne(userWrapper);
        if (user == null) throw new UserCannotFindException("{\n" +
                "    \"error\": {\n" +
                "        \"message\": \"Could not find user: "+userId+".\",\n" +
                "        \"code\": 404,\n" +
                "        \"title\": \"Not Found\"\n" +
                "    }\n" +
                "}");
        LambdaQueryWrapper<Role> roleWrapper = new LambdaQueryWrapper<>();
        roleWrapper.eq(Role::getId, roleId);
        Role role = roleService.getOne(roleWrapper);
        if (role == null) throw new RoleCannotFindException("{\n" +
                "    \"error\": {\n" +
                "        \"message\": \"Could not find role: "+roleId+".\",\n" +
                "        \"code\": 404,\n" +
                "        \"title\": \"Not Found\"\n" +
                "    }\n" +
                "}");
        response.setStatus(HttpServletResponse.SC_NO_CONTENT);
        LambdaQueryWrapper<Assignment> assignmentWrapper = new LambdaQueryWrapper<>();
        assignmentWrapper.eq(Assignment::getActorId, userId);
        assignmentWrapper.eq(Assignment::getRoleId, roleId);
        assignmentWrapper.eq(Assignment::getTargetId, domainId);
        Assignment assignment = assignmentService.getOne(assignmentWrapper);
        if (assignment != null) {
            return;
        }
        if (assignment == null) assignment = new Assignment();
        assignment.setActorId(userId);
        assignment.setRoleId(roleId);
        assignment.setTargetId(domainId);
        assignment.setType("UserDomain");
        assignment.setInherited(false);
        assignmentService.save(assignment);
    }

    @GetMapping
    @ApiOperation(value = "获取域列表")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "name", value = "域名称", required = false, dataType = "String")})
    public Map<String, Object> domains(@RequestParam(value = "name", required = false) String name) {
        List<Project> list = projectService.list(new QueryWrapper<Project>()
                .lambda()
                .eq(Project::getDomain, true)
                .eq(StringUtils.isNotBlank(name), Project::getName, name));
        List<DomainVO> domains = list.stream().map(project -> {
            DomainVO domainVO = new DomainVO();
            BeanUtils.copyProperties(project, domainVO);
            domainVO.setEnabled(project.getDomain());
            String href = "http://" + ramIP + ":" + ramPort + "/v3/domains/" + project.getId();
            Map links = Maps.newHashMap();
            links.put("self", href);
            domainVO.setLinks(links);
            return domainVO;
        }).collect(Collectors.toList());
        Map resultMap = Maps.newHashMap();
        resultMap.put("domains", domains);
        Map links = Maps.newHashMap();
        String href = "http://"+ramIP+":"+ramPort+"/v3/domains";
        links.put("self", href);
        links.put("next", null);
        links.put("previous", null);
        resultMap.put("links", links);
        return resultMap;
    }

}
