package com.sugon.cloud.iam.api.service;

import com.sugon.cloud.common.model.IdNameInfo;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.iam.api.entity.Department;
import com.sugon.cloud.iam.api.vo.CreateOrUpdateDepartmentVO;
import com.sugon.cloud.iam.api.vo.DepartmentDetailVO;
import com.sugon.cloud.iam.common.model.vo.DepartmentTreeDetailVO;

import java.util.List;

/**
 * 组织表(Department)表服务接口
 *
 * <AUTHOR>
 * @since 2021-04-07 10:51:58
 */
public interface DepartmentService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    DepartmentDetailVO queryById(String id);

    /**
     * 查询多条数据
     *
     * @param offset 查询起始位置
     * @param limit 查询条数
     * @return 对象列表
     */
    List<Department> queryAllByLimit(int offset, int limit);

    /**
     * 新增数据
     *
     * @param createOrUpdateDepartmentVO 实例对象
     * @return 实例对象
     */
    Department insert(CreateOrUpdateDepartmentVO createOrUpdateDepartmentVO);

    /**
     * 修改数据
     *
     * @param createOrUpdateDepartmentVO 实例对象
     * @return 实例对象
     */
    Department update(CreateOrUpdateDepartmentVO createOrUpdateDepartmentVO);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     */
    ResultModel deleteById(String id);

    /**
     * 通过用户Id查询组织
     * @param userId
     * @return
     */
    DepartmentTreeDetailVO findTreeDepartmentByUserId(String userId);

    com.sugon.cloud.iam.common.model.vo.DepartmentTreeDetailVO findTreeDepartmentAll();

    List<com.sugon.cloud.iam.common.model.vo.DepartmentTreeDetailVO> findDepartmentAll(String userId, String currentUserId, boolean queryDefaultDept);

    Integer findUserDepartmentTotalByUserId(String header);

    /**
     * 根组织的名称校验
     * @param name 根组织名称
     * @param id 根组织ID
     */
    boolean topParentDepartmentNameDuplication(String name, String id);


    List<IdNameInfo> deptInfos(List<String> ids);

    Department getRootDepartmentByDeptId(String deptId);

    /**
     * 根据组织ID查询当前组织的子组织及父组织
     * @param deptId
     * @return
     */
    List<DepartmentTreeDetailVO> getDeptsByDeptId(String deptId);

    /**
     * 根据用户id查询顶级组织
     *
     * @param userId
     * @return
     */
    DepartmentDetailVO queryTopDeptByUserId(String userId);

    /**
     * 根据用户id查询顶级组织
     *
     * @param projectId
     * @return
     */
    DepartmentDetailVO queryTopDeptByProjectId(String projectId);

    /**
     * bm组织三员获取部门id
     * @param userId
     * @return
     */
    List<String> getDeptsByUserId(String userId);
}
