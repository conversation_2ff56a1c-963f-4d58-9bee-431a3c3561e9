package com.sugon.cloud.iam.api.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sugon.cloud.common.mapper.ModelMapper;
import com.sugon.cloud.common.utils.IPUtil;
import com.sugon.cloud.common.utils.RequestUtil;
import com.sugon.cloud.common.utils.UUIDUtil;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.entity.UserAuthorizeSettings;
import com.sugon.cloud.iam.api.entity.keystone.LoginUser;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.enums.UserAuthorizeScope;
import com.sugon.cloud.iam.api.enums.UserAuthorizeType;
import com.sugon.cloud.iam.api.enums.UserSource;
import com.sugon.cloud.iam.api.jwt.JwtTokenUtils;
import com.sugon.cloud.iam.api.mapper.DepartmentMapper;
import com.sugon.cloud.iam.api.mapper.UserAuthorizeSettingsMapper;
import com.sugon.cloud.iam.api.mapper.UserMapper;
import com.sugon.cloud.iam.api.service.DepartmentService;
import com.sugon.cloud.iam.api.service.JwtService;
import com.sugon.cloud.iam.api.service.Oauth2AuthorizeService;
import com.sugon.cloud.iam.api.service.UserService;
import com.sugon.cloud.iam.api.utils.EncryptAndDecryptUtil;
import com.sugon.cloud.iam.api.vo.*;
import com.sugon.cloud.iam.common.model.vo.DepartmentTreeDetailVO;
import com.sugon.cloud.log.client.model.CommonBusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URLDecoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/05/19 09:03
 **/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class Oauth2AuthorizeServiceImpl implements Oauth2AuthorizeService {
    private final UserAuthorizeSettingsMapper userAuthorizeSettingsMapper;
    private final UserMapper userMapper;
    private final UserService userService;
    private final RestTemplate restTemplate;
    private final HttpServletRequest request;
    private final DepartmentMapper departmentMapper;
    private final DepartmentService departmentService;
    private final JwtTokenUtils jwtTokenUtils;
    private final EncryptAndDecryptUtil encryptAndDecryptUtil;
    private final JwtService jwtService;
    private final ModelMapper modelMapper;

    @Value("#{'${user.authorize.oauth2.support.user-type:master}'.split(',')}")
    private List<String> userTypeSupports;

    @Value("#{'${user.authorize.oauth2.support.username:admin}'.split(',')}")
    private List<String> usernameSupports;

    private final static String USER_ALIAS = "alias";
    private final static String USER_NAME = "name";
    private final static String USER_PHONE = "phone";
    private final static String USER_EMAIL = "email";

    @Override
    public OAuth2CallbackResponseVO callback(String code, String userId, String redirectUri) {
        try {
            UserAuthorizeSettings userAuthorizeSettings = userAuthorizeSettingsMapper.selectOne(new LambdaQueryWrapper<UserAuthorizeSettings>()
                    .eq(UserAuthorizeSettings::getUserId, userId)
                    .eq(UserAuthorizeSettings::getScope, RequestUtil.isInternetRequest(request) ? UserAuthorizeScope.OUTER : UserAuthorizeScope.INNER)
                    .eq(UserAuthorizeSettings::getType, UserAuthorizeType.OAUTH2));
            if (Objects.isNull(userAuthorizeSettings)) {
                return null;
            }
            String extra = userAuthorizeSettings.getExtra();
            OAuth2ExtraVO oAuth2ExtraVO = JSONObject.parseObject(extra, OAuth2ExtraVO.class);
            // 1、通过code获取token
            HttpHeaders httpHeaders = new HttpHeaders();
            Map<String, Object> urlParam = new HashMap<>();
            urlParam.put("client_id", oAuth2ExtraVO.getClientId());
            urlParam.put("client_secret", oAuth2ExtraVO.getClientSecret());
            urlParam.put("grant_type", "authorization_code");
            urlParam.put("code", code);
            urlParam.put("redirect_uri", redirectUri);
            ResponseEntity<JSONObject> tokenObj = restTemplate.exchange(uriWrapper(oAuth2ExtraVO.getTokenUrl(), urlParam),
                    Objects.requireNonNull(HttpMethod.resolve(oAuth2ExtraVO.getTokenGetMethod().toUpperCase())),
                    new HttpEntity<>(httpHeaders), JSONObject.class);
            String tokenObjStr = tokenObj.getBody().toString();
            if (!tokenObjStr.contains("access_token")) {
                log.error("callback getToken error:[{}]", tokenObj.getBody());
                throw new CommonBusinessException("获取access_token报错");
            }
            String accessToken = tokenObjStr.split("\"access_token\":")[1].split(",")[0].replaceAll("\"", "");

            // 2、通过token获取用户信息
            httpHeaders.set(HttpHeaders.AUTHORIZATION, accessToken);
            urlParam = new HashMap<>();
            urlParam.put("access_token", accessToken);
            ResponseEntity<JSONObject> oauthServerUserObj = restTemplate.exchange(uriWrapper(oAuth2ExtraVO.getUserInfoUrl(), urlParam), HttpMethod.GET,
                    new HttpEntity<>(httpHeaders), JSONObject.class);
            JSONObject oauthServerUser = oauthServerUserObj.getBody();
            Map<String, String> userInfoAttrMapping = oAuth2ExtraVO.getUserInfoAttrMapping();
            String userName = getValueByMappingKey(userInfoAttrMapping.get(USER_NAME), oauthServerUser);
            if (StringUtils.isEmpty(userName)) {
                log.info("oauthServerUser=[{}], usernameMappingKey=[{}]", oauthServerUser, userInfoAttrMapping.get(USER_NAME));
                throw new CommonBusinessException("没有找到账号信息");
            }
            User user = userMapper.selectOne(new LambdaQueryWrapper<User>()
                    .eq(User::getName, userName));
            UserCreateOrUpdateVO userCreateOrUpdateVO = new UserCreateOrUpdateVO();
            userCreateOrUpdateVO.setAlias(getValueByMappingKey(userInfoAttrMapping.get(USER_ALIAS), oauthServerUser));
            userCreateOrUpdateVO.setEmail(getValueByMappingKey(userInfoAttrMapping.get(USER_EMAIL), oauthServerUser));
            userCreateOrUpdateVO.setPhone(getValueByMappingKey(userInfoAttrMapping.get(USER_PHONE), oauthServerUser));
            if (Objects.nonNull(user)) {
                userCreateOrUpdateVO.setId(user.getId());
                // 开启每次修改和不为admin的用户需要修改别名、邮箱和电话.
                if (oAuth2ExtraVO.isAlwaysUpdateUserInfo() && !"admin".equals(userName)) {
                    User updateUser = new User();
                    BeanUtils.copyProperties(userCreateOrUpdateVO, updateUser);
                    userMapper.updateById(updateUser);
                    log.debug("[{}]通过oauth2.0登录并更新用户完毕.", userName);
                } else {
                    // 不做修改需要将alias改回去，否则前端解析token会显示最新的alias
                    userCreateOrUpdateVO.setAlias(user.getAlias());
                }
            } else {
                userCreateOrUpdateVO.setName(userName);
                userCreateOrUpdateVO.setId(syncUser(userCreateOrUpdateVO, userId));
            }
            // 生成token信息
            LoginUser userDetails = new LoginUser(userName, "password", Lists.newArrayList());
            // 生成token的User实体
            User tokenUser = userService.getById(userCreateOrUpdateVO.getId());
            userDetails.setUserId(userCreateOrUpdateVO.getId());
            userDetails.setAlias(userCreateOrUpdateVO.getAlias());
            userDetails.setProjectId(tokenUser.getDefaultProjectId());
            userDetails.setUserType(tokenUser.getType());
            userDetails.setDeptId(tokenUser.getDeptId());
            String token = jwtTokenUtils.generateToken(userDetails, false);
            jwtService.initOnePlaceLoginInfo(userDetails, token);
            String ipAddr = IPUtil.getIpAddr(request);
            // 更新登录IP和登录时间
            userService.update(null, new LambdaUpdateWrapper<User>()
                    .eq(User::getId, userCreateOrUpdateVO.getId())
                    .set(User::getLastActiveAt, new Date())
                    .set(User::getLastLoginIp, ipAddr));
            return OAuth2CallbackResponseVO.builder()
                    .passwordUpdate(StringUtils.isBlank(tokenUser.getPassword()))
                    .phoneOrEmailUpdate(StringUtils.isBlank(tokenUser.getPhone()) || StringUtils.isBlank(tokenUser.getEmail()))
                    .token(token)
                    .build();
        } catch (CommonBusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("callback error:", e);
            throw new CommonBusinessException("OAuth2认证失败");
        }
    }

    private String getValueByMappingKey(String originMappingKey, JSONObject obj) {
        if (Objects.isNull(obj)) {
            return null;
        }
        String[] keys = StringUtils.split(originMappingKey, ".");
        String currentKey = keys[0];
        if (!obj.containsKey(currentKey)) {
            return null;
        }
        if (keys.length == 1) {
            return obj.getString(currentKey);
        }
        return getValueByMappingKey(StringUtils.removeStart(originMappingKey, currentKey + "."), obj.getJSONObject(keys[0]));
    }

    private String syncUser(UserCreateOrUpdateVO userCreateOrUpdateVO, String userId) {
        User oauthOriginUser = userMapper.selectById(userId);
        String newUserId;
        if ("master".equals(oauthOriginUser.getType())) {
            String oauth2DepartmentId = getOauth2DepartmentId(oauthOriginUser.getDeptId());
            userCreateOrUpdateVO.setDeptId(oauth2DepartmentId);
            newUserId = userService.createSubUser(userCreateOrUpdateVO, false);
        } else {
            // 创建admin
            newUserId = userService.createAdmin(userCreateOrUpdateVO, false);
        }
        // 设置用户来源
        User newUser = userService.getById(newUserId);
        // USER_EXTRA_SOURCE_KEY
        User updateUserSource = new User();
        updateUserSource.setId(newUserId);
        String extra = newUser.getExtra();
        JSONObject extraObj = JSONObject.parseObject(extra);
        extraObj.put(CommonInstance.USER_EXTRA_SOURCE_KEY, UserSource.OAUTH2);
        updateUserSource.setExtra(extraObj.toString());
        userService.updateById(updateUserSource);
        return newUserId;
    }

    private String getOauth2DepartmentId(String parentDepartmentId) {
        String oauth2DefaultDeptName = "oauth2";
        DepartmentTreeDetailVO oauth2DefaultDept = departmentMapper.findTreeDepartment(parentDepartmentId).stream()
                .filter(e -> oauth2DefaultDeptName.equals(e.getName()))
                .findFirst()
                .orElse(null);
        if (Objects.nonNull(oauth2DefaultDept)) {
            return oauth2DefaultDept.getId();
        }
        CreateOrUpdateDepartmentVO createOrUpdateDepartmentVO = new CreateOrUpdateDepartmentVO();
        createOrUpdateDepartmentVO.setName(oauth2DefaultDeptName);
        createOrUpdateDepartmentVO.setParentId(parentDepartmentId);
        return departmentService.insert(createOrUpdateDepartmentVO).getId();
    }

    private static URI uriWrapper(String url, Map<String, Object> params) {
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(url);
        if (Objects.nonNull(params) && !params.isEmpty()) {
            params.forEach((k, v) -> {
                try {
                    if (Objects.nonNull(v)) {
                        urlBuilder.queryParam(k, URLDecoder.decode(v.toString(), "UTF-8"));
                    } else {
                        urlBuilder.queryParam(k, "");
                    }
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
            });
        }
        return urlBuilder.build().encode().toUri();
    }

    @Override
    public String oauth2Update(String userId, OAuth2DetailVO oauth2DetailVO) {
        User user = userMapper.selectById(userId);
        if (Objects.isNull(user)) {
            throw new CommonBusinessException("用户不存在");
        }
        String userName = user.getName();
        String userType = user.getType();
        if (!(userTypeSupports.contains(userType) || usernameSupports.contains(userName))) {
            throw new CommonBusinessException("不支持修改该用户的Oauth2配置", userName);
        }

        OAuth2ExtraVO oauth2Extra = oauth2DetailVO.getOauth2Extra();
        // clientId的验重
        if (checkClientIdExist(userId, oauth2Extra.getClientId())) {
            throw new CommonBusinessException("客户端Id已存在", userName);
        }
        UserAuthorizeScope scope = RequestUtil.isInternetRequest(request) ? UserAuthorizeScope.OUTER : UserAuthorizeScope.INNER;
        UserAuthorizeSettings userAuthorizeSettings = userAuthorizeSettingsMapper.selectOne(new LambdaQueryWrapper<UserAuthorizeSettings>()
                .eq(UserAuthorizeSettings::getUserId, userId)
                .eq(UserAuthorizeSettings::getScope, scope)
                .eq(UserAuthorizeSettings::getType, UserAuthorizeType.OAUTH2));

        if (Objects.isNull(userAuthorizeSettings)) {
            userAuthorizeSettings = new UserAuthorizeSettings();
            userAuthorizeSettings.setId(UUIDUtil.get32UUID());
            userAuthorizeSettings.setScope(scope);
            userAuthorizeSettings.setUserId(userId);
            userAuthorizeSettings.setType(UserAuthorizeType.OAUTH2);
            userAuthorizeSettings.setEnabled(oauth2DetailVO.isEnabled());
            try {
                oauth2Extra.setClientSecret(encryptAndDecryptUtil.decryptByPublickey(oauth2DetailVO.getPublickey(), oauth2Extra.getClientSecret()));
            } catch (Exception e) {
                log.error("oauth2Update error", e);
                throw new RuntimeException("机密失败");
            }
            userAuthorizeSettings.setExtra(JSONObject.toJSONString(oauth2Extra));
            userAuthorizeSettingsMapper.insert(userAuthorizeSettings);
        } else {
            userAuthorizeSettings.setEnabled(oauth2DetailVO.isEnabled());
            String newClientSecret = oauth2Extra.getClientSecret();
            if (StringUtils.isBlank(newClientSecret)) {
                // 修改时候没有录入clientSecret就用旧的来填充
                String clientSecret = JSONObject.parseObject(userAuthorizeSettings.getExtra()).getString("clientSecret");
                oauth2Extra.setClientSecret(clientSecret);
            } else {
                try {
                    oauth2Extra.setClientSecret(encryptAndDecryptUtil.decryptByPublickey(oauth2DetailVO.getPublickey(), newClientSecret));
                } catch (Exception e) {
                    log.error("oauth2Update error", e);
                    throw new RuntimeException("机密失败");
                }
            }

            userAuthorizeSettings.setExtra(JSONObject.toJSONString(oauth2Extra));
            userAuthorizeSettingsMapper.updateById(userAuthorizeSettings);
        }
        return userName;
    }

    private boolean checkClientIdExist(String userId, String clientId) {
        return userAuthorizeSettingsMapper.selectList(new LambdaQueryWrapper<UserAuthorizeSettings>()
                        .select(UserAuthorizeSettings::getExtra)
                        .ne(UserAuthorizeSettings::getUserId, userId)
                        .eq(UserAuthorizeSettings::getScope, RequestUtil.isInternetRequest(request) ? UserAuthorizeScope.OUTER : UserAuthorizeScope.INNER)
                        .eq(UserAuthorizeSettings::getType, UserAuthorizeType.OAUTH2))
                .stream()
                .map(e -> JSONObject.parseObject(e.getExtra(), OAuth2ExtraVO.class)).anyMatch(e -> e.getClientId().equals(clientId));
    }

    @Override
    public OAuth2DetailVO oauth2Detail(String userId) {
        User user = userMapper.selectById(userId);
        if (Objects.isNull(user)) {
            throw new CommonBusinessException("用户不存在");
        }

        UserAuthorizeSettings userAuthorizeSettings = userAuthorizeSettingsMapper.selectOne(new LambdaQueryWrapper<UserAuthorizeSettings>()
                .eq(UserAuthorizeSettings::getUserId, userId)
                .eq(UserAuthorizeSettings::getScope, RequestUtil.isInternetRequest(request) ? UserAuthorizeScope.OUTER : UserAuthorizeScope.INNER)
                .eq(UserAuthorizeSettings::getType, UserAuthorizeType.OAUTH2));

        OAuth2DetailVO result = new OAuth2DetailVO();
        if (Objects.nonNull(userAuthorizeSettings)) {
            result.setEnabled(userAuthorizeSettings.getEnabled());
            OAuth2ExtraVO oAuth2ExtraVO = JSONObject.parseObject(userAuthorizeSettings.getExtra(), OAuth2ExtraVO.class);
            // 隐藏密码
            oAuth2ExtraVO.setClientSecret(null);
            result.setOauth2Extra(oAuth2ExtraVO);
        } else {
            OAuth2ExtraVO oAuth2ExtraVO = new OAuth2ExtraVO();
            oAuth2ExtraVO.setTokenGetMethod("GET");
            Map<String, String> userInfoAttrMapping = Maps.newHashMap();
            userInfoAttrMapping.put("alias", "username");
            userInfoAttrMapping.put("name", "loginName");
            userInfoAttrMapping.put("email", "email");
            userInfoAttrMapping.put("phone", "phone");
            oAuth2ExtraVO.setUserInfoAttrMapping(userInfoAttrMapping);
            result.setOauth2Extra(oAuth2ExtraVO);
        }
        return result;
    }

    @Override
    public List<OAuth2ServerItem> oauth2List() {
        List<UserAuthorizeSettings> userAuthorizeSettings = userAuthorizeSettingsMapper.selectList(new LambdaQueryWrapper<UserAuthorizeSettings>()
                .eq(UserAuthorizeSettings::getEnabled, true)
                .eq(UserAuthorizeSettings::getScope, RequestUtil.isInternetRequest(request) ? UserAuthorizeScope.OUTER : UserAuthorizeScope.INNER)
                .eq(UserAuthorizeSettings::getType, UserAuthorizeType.OAUTH2));
        if (CollectionUtils.isEmpty(userAuthorizeSettings)) {
            return Lists.newArrayList();
        }
        List<String> userIds = userAuthorizeSettings.stream().map(UserAuthorizeSettings::getUserId).collect(Collectors.toList());
        Map<String, String> userIdNameMap = userMapper.selectList(new LambdaQueryWrapper<User>()
                        .select(User::getId, User::getName)
                        .in(User::getId, userIds))
                .stream()
                .collect(Collectors.toMap(User::getId, User::getName));
        return userAuthorizeSettings.stream().map(userAuthorizeSetting -> {
            OAuth2ExtraVO oAuth2ExtraVO = JSONObject.parseObject(userAuthorizeSetting.getExtra(), OAuth2ExtraVO.class);
            OAuth2ServerItem oAuth2ServerItem = modelMapper.map(oAuth2ExtraVO, OAuth2ServerItem.class);
            oAuth2ServerItem.setUsername(userIdNameMap.get(userAuthorizeSetting.getUserId()));
            oAuth2ServerItem.setUserId(userAuthorizeSetting.getUserId());
            return oAuth2ServerItem;
        }).collect(Collectors.toList());
    }
}
