package com.sugon.cloud.iam.api.service.resourceauth;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.beust.jcommander.internal.Lists;
import com.sugon.cloud.common.constants.HeaderParamConstant;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.entity.IamRole;
import com.sugon.cloud.iam.api.entity.keystone.Role;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.service.IamRoleService;
import com.sugon.cloud.iam.api.service.RoleService;
import com.sugon.cloud.iam.api.service.UserService;
import com.sugon.cloud.iam.common.model.vo.RoleResponseVO;
import com.sugon.cloud.resource.auth.common.service.impl.BaseResourceAuthHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/10 17:46
 */
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class RoleAuthHandler extends BaseResourceAuthHandler {

    private final SecurityUserTypeAuthHandler securityUserTypeAuthHandler;


    private final IamRoleService iamRoleService;

    @Override
    public List<String> getUserResource(String userId) {

        List<String> userResource = securityUserTypeAuthHandler.getUserResource(userId);
        if (CollectionUtil.isNotEmpty(userResource)) {
            return userResource;
        }

        PageCL<RoleResponseVO> roleResponseVOPageCL = iamRoleService.listAll(userId, null, 1, 9999);
        List<RoleResponseVO> list = roleResponseVOPageCL.getList();

        LambdaQueryWrapper<IamRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IamRole::getType, CommonInstance.ROLE_TYPE_DEFAULT).or().eq(IamRole::getType, TypeUtil.TYPE_COMMON);
        List<IamRole> list1 = iamRoleService.list(queryWrapper);
        ArrayList<String> objects = new ArrayList<>();
        objects.addAll(list.stream().map(RoleResponseVO::getId).collect(Collectors.toList()));
        objects.addAll(list1.stream().map(IamRole::getId).collect(Collectors.toList()));

        return objects;
    }
}
