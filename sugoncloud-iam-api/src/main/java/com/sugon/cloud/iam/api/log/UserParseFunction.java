package com.sugon.cloud.iam.api.log;

import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.service.UserService;
import com.sugon.cloud.log.client.service.IParseFunction;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Objects;

@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class UserParseFunction implements IParseFunction {

    private final UserService userService;

    @Override
    public String functionName() {
        return "USER";
    }
    @Override
    public String apply(String value) {
        User user;
        if (StringUtils.isEmpty(value) || Objects.isNull(user = userService.getById(value))) {
            return "";
        }
        return user.getName();
    }
}
