package com.sugon.cloud.iam.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sugon.cloud.common.model.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("user_micro")
@ApiModel("用户收藏实体")
public class UserMicroEntity extends BaseEntity {
    @TableId(type = IdType.UUID)
    @ApiModelProperty("主键")
    private String id;
    @JsonProperty("user_id")
    @ApiModelProperty("用户id")
    private String userId;
    @JsonProperty("micro_id")
    @ApiModelProperty("微服务id")
    private String microId;
    @ApiModelProperty("排序")
    private Integer sort;

}
