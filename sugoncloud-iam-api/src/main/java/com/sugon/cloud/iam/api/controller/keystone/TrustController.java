package com.sugon.cloud.iam.api.controller.keystone;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.google.common.collect.Maps;
import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.mapper.ModelMapper;
import com.sugon.cloud.iam.api.entity.exception.NativeBadRequestException;
import com.sugon.cloud.iam.api.entity.keystone.*;
import com.sugon.cloud.iam.api.entity.keystone.VO.TrustResultVO;
import com.sugon.cloud.iam.api.service.RoleService;
import com.sugon.cloud.iam.api.service.TrustRoleService;
import com.sugon.cloud.iam.api.service.TrustService;
import com.sugon.cloud.iam.common.model.vo.CreateResourceResponseLinksParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.ws.rs.ForbiddenException;
import javax.ws.rs.NotFoundException;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/v3/OS-TRUST/trusts")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Api(tags = {"keystone-租户授权"})
@Slf4j
@DocumentIgnore
public class TrustController {
    @Value("#{'${vip.ip}'.concat(':').concat('${vip.port}')}")
    private String linksPrefix;
    private final ModelMapper mapper;
    private final RoleService roleService;
    private final TrustService trustService;
    private static String defaultDomainId = "<<null>>";
    private final TrustRoleService trustRoleService;

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @ApiOperation(value = "创建租户授权")
    public Map<String, Object> createTrust (@RequestBody Map<String, TrustCreateOrUpdate> map) {
        TrustCreateOrUpdate trust = map.get("trust");
        if (Objects.isNull(trust))
            throw new ForbiddenException("{\n" +
                    "    \"error\": {\n" +
                    "        \"message\": \"You are not authorized to perform the requested action: identity:create_trust.\",\n" +
                    "        \"code\": 403,\n" +
                    "        \"title\": \"Forbidden\"\n" +
                    "    }\n" +
                    "}");
        Object roles = trust.getRoles();
        if (roles == null )
            throw new ForbiddenException("{\n" +
                    "    \"error\": {\n" +
                    "        \"message\": \"You are not authorized to perform the requested action: At least one role should be specified.\",\n" +
                    "        \"code\": 403,\n" +
                    "        \"title\": \"Forbidden\"\n" +
                    "    }\n" +
                    "}");

        try {
            List objects = (ArrayList) roles;
            if (CollectionUtils.isEmpty(objects))
                throw new ForbiddenException("{\n" +
                        "    \"error\": {\n" +
                        "        \"message\": \"You are not authorized to perform the requested action: At least one role should be specified.\",\n" +
                        "        \"code\": 403,\n" +
                        "        \"title\": \"Forbidden\"\n" +
                        "    }\n" +
                        "}");
            List<Role> list = new ArrayList<>();
           for (int i=0; i<objects.size(); i++) {
               String s = JSON.toJSONString(objects.get(i));
               JSONObject r = JSONObject.parseObject(s);
               String roleName = r.getString("id");
               List<Role> role = roleService.lambdaQuery().eq(Role::getId, roleName).or().eq(Role::getName, roleName).list();
               if (CollectionUtils.isEmpty(role))
                   throw new NotFoundException("{\n" +
                           "    \"error\": {\n" +
                           "        \"message\": \"Role "+roleName+" is not defined\",\n" +
                           "        \"code\": 404,\n" +
                           "        \"title\": \"Not Found\"\n" +
                           "    }\n" +
                           "}");
               list.add(role.get(0));
           }
            Trust tr = trustService.createTrust(mapper.map(trust, Trust.class), list);
            List<CreateRoleResponseParam> roleResult = list.stream().map(role -> role2CreateRoleResponseParam(role)).collect(Collectors.toList());
            TrustResultVO trustResultVO = mapper.map(tr, TrustResultVO.class);
            Map rolesLinks = Maps.newHashMap();
            rolesLinks.put("self", "http://" + linksPrefix+"/v3/OS-TRUST/trusts/"+tr.getId()+"/roles");
            rolesLinks.put("previous", null);
            rolesLinks.put("next", null);
            trustResultVO.setRolesLinks(rolesLinks);
            Map links = Maps.newHashMap();
            links.put("self","http://" + linksPrefix+"/v3/OS-TRUST/trusts/"+tr.getId());
            trustResultVO.setRoles(roleResult);
            trustResultVO.setLinks(links);
            Map result = Maps.newHashMap();
            result.put("trust", trustResultVO);
            return result;
        } catch (Exception e) {
            if (e instanceof NotFoundException) throw e;
            throw new NativeBadRequestException("{\n" +
                    "    \"error\": {\n" +
                    "        \"message\": \"Invalid input for field roles\",\n" +
                    "        \"code\": 400,\n" +
                    "        \"title\": \"Bad Request\"\n" +
                    "    }\n" +
                    "}");
        }
    }

    private CreateRoleResponseParam role2CreateRoleResponseParam(Role role){
        CreateRoleResponseParam roleResponseParam = mapper.map(role,CreateRoleResponseParam.class);
        roleResponseParam.setLinks(new CreateResourceResponseLinksParam("http://" + linksPrefix +
                "/v3/roles/" + role.getId()));
        if (defaultDomainId.equals(roleResponseParam.getDomainId())) {
            roleResponseParam.setDomainId(null);
        }
        return roleResponseParam;
    }

    @GetMapping("{trust_id}")
    @ApiOperation(value = "获取租户授权")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "trust_id", value = "租户授权ID", required = true, dataType = "String")})
    public Map<String, Object> getTrust(@PathVariable("trust_id") String trustId) {
        Trust trust = trustService.getById(trustId);
        if (Objects.isNull(trust))
            throw new NotFoundException("{\n" +
                    "    \"error\": {\n" +
                    "        \"message\": \"Could not find trust: "+trustId+".\",\n" +
                    "        \"code\": 404,\n" +
                    "        \"title\": \"Not Found\"\n" +
                    "    }\n" +
                    "}");
        List<TrustRole> trustRoles = trustRoleService.list(new QueryWrapper<TrustRole>()
                .lambda()
                .eq(TrustRole::getTrustId, trust.getId()));
        List<String> roleIds = trustRoles.stream().map(trustRole -> trustRole.getRoleId()).collect(Collectors.toList());
        List<Role> roles = roleService.list(new QueryWrapper<Role>().lambda().in(Role::getId,roleIds));
        List<CreateRoleResponseParam> roleResult = roles.stream().map(role -> role2CreateRoleResponseParam(role)).collect(Collectors.toList());
        TrustResultVO trustResultVO = mapper.map(trust, TrustResultVO.class);
        Map rolesLinks = Maps.newHashMap();
        rolesLinks.put("self", "http://" + linksPrefix+"/v3/OS-TRUST/trusts/"+trust.getId()+"/roles");
        rolesLinks.put("previous", null);
        rolesLinks.put("next", null);
        trustResultVO.setRolesLinks(rolesLinks);
        Map links = Maps.newHashMap();
        links.put("self","http://" + linksPrefix+"/v3/OS-TRUST/trusts/"+trust.getId());
        trustResultVO.setRoles(roleResult);
        Map result = Maps.newHashMap();
        result.put("trust", trustResultVO);
        return  result;
    }

    @GetMapping
    @ApiOperation(value = "获取租户授权列表")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "trustor_user_id", value = "租户授权ID", required = false, dataType = "String"),
            @ApiImplicitParam(name = "trustee_user_id", value = "租户授权ID", required = false, dataType = "String")})
    public Map<String, Object> trustList(@RequestParam(value = "trustor_user_id", required = false) String trustorUserId,
                         @RequestParam(value = "trustee_user_id", required = false) String trusteeUserId) {
        List<Trust> list = trustService.list(new QueryWrapper<Trust>()
                .lambda().isNull(Trust::getDeletedAt)
                .eq(StringUtils.isNotBlank(trustorUserId), Trust::getTrustorUserId, trustorUserId)
                .eq(StringUtils.isNotBlank(trusteeUserId), Trust::getTrusteeUserId, trusteeUserId));
        List<TrustResultVO> trustResultVOS = list.stream().map(trust -> trust2TrustResultVO(trust)).collect(Collectors.toList());
        Map links = Maps.newHashMap();
        links.put("self", "http://" + linksPrefix+"/v3/OS-TRUST/trusts");
        links.put("previous", null);
        links.put("next", null);
        Map map = Maps.newHashMap();
        map.put("links", links);
        map.put("trusts", trustResultVOS);
        return map;
    }

    private TrustResultVO trust2TrustResultVO(Trust trust) {
        TrustResultVO trustResultVO = mapper.map(trust, TrustResultVO.class);
        Map links = Maps.newHashMap();
        links.put("self","http://" + linksPrefix+"/v3/OS-TRUST/trusts/"+trust.getId());
        trustResultVO.setLinks(links);
        return trustResultVO;
    }

    @GetMapping("{trust_id}/roles")
    @ApiOperation(value = "获取租户授权角色列表")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "trust_id", value = "租户授权ID", required = true, dataType = "String")})
    public Map<String, Object> trustRoles(@PathVariable("trust_id") String trustId) {
        List<TrustRole> trustRoles = trustRoleService.list(new QueryWrapper<TrustRole>()
                .lambda()
                .eq(TrustRole::getTrustId, trustId));
        List<String> roleIds = trustRoles.stream().map(trustRole -> trustRole.getRoleId()).collect(Collectors.toList());
        List<Role> roles = roleService.list(new QueryWrapper<Role>().lambda().in(Role::getId,roleIds));
        List<CreateRoleResponseParam> roleResult = roles.stream().map(role -> role2CreateRoleResponseParam(role)).collect(Collectors.toList());
        Map rolesLinks = Maps.newHashMap();
        rolesLinks.put("self", "http://" + linksPrefix+"/v3/OS-TRUST/trusts/"+trustId+"/roles");
        rolesLinks.put("previous", null);
        rolesLinks.put("next", null);
        Map map = Maps.newHashMap();
        map.put("links", rolesLinks);
        map.put("roles", roleResult);
        return map;
    }

    @DeleteMapping("{trust_id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @ApiOperation(value = "删除租户授权")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "trust_id", value = "租户授权ID", required = true, dataType = "String")})
    public void deleteTrust(@PathVariable("trust_id") String trustId) {
        trustService.lambdaUpdate().eq(Trust::getId, trustId).set(Trust::getDeletedAt, new Date()).update();
    }
}
