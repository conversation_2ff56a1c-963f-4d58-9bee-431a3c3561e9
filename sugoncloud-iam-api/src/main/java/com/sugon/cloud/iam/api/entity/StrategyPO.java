package com.sugon.cloud.iam.api.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@Accessors(chain = true)
@TableName("strategy")
public class StrategyPO implements Serializable {

    private static final long serialVersionUID = -8886712512084851221L;
    @TableId(type = IdType.UUID)
    private String id;

    private String policy;

    private String name;

    private String description;

    private Date createAt;

    private String creatBy;

    private String catalog;
}
