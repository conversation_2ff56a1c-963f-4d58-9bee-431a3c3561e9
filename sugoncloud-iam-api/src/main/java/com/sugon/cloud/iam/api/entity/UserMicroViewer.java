package com.sugon.cloud.iam.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sugon.cloud.common.model.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("用户微服务收藏")
public class UserMicroViewer extends BaseEntity {

    @ApiModelProperty("主键")
    private String id;
    @JsonProperty("user_id")
    @ApiModelProperty("用户id")
    private String userId;
    @JsonProperty("micro_id")
    @ApiModelProperty("微服务id")
    private String microId;
    @ApiModelProperty("排序")
    private Integer sort;
    @JsonProperty("micro_name")
    @ApiModelProperty("微服务名称")
    private String microName;
    @ApiModelProperty("是否为第三方接入")
    @JsonProperty("third_part_access")
    private Boolean thirdPartAccess;

    /**
     * 微服务名称
     */
    @ApiModelProperty("微服务名称")
    private String name;

    @ApiModelProperty("微服务描述")
    private String description;
    /**
     * 微服务id
     */
    @ApiModelProperty("微服务id")
    private String serviceId;
    @ApiModelProperty("微服务链接")
    private String link;
    @ApiModelProperty("微服务分类id")
    private String categoryId;
    @ApiModelProperty("是否隐藏")
    private Boolean navHidden;
    @ApiModelProperty("最后请求时间")
    private Date latestRequestTime;
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty("收藏时间")
    private Date collectTime;

    @TableField(exist = false)
    @JsonIgnore
    @ApiModelProperty("微服务链接")
    private String publicLink;
}
