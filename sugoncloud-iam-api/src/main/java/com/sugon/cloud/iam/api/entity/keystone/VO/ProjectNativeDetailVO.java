package com.sugon.cloud.iam.api.entity.keystone.VO;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * openstack 原生返回对象
 */
@Data
public class ProjectNativeDetailVO {
    private String id;
    private String name;
    @JsonProperty("domain_id")
    private String domainId;
    private String description;
    private boolean enabled;
    @JsonProperty("is_domain")
    private boolean domain;
    private String parentId;
    private Object links;
    private List tags = new ArrayList();

}
