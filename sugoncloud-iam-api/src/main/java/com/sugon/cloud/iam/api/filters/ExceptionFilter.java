package com.sugon.cloud.iam.api.filters;

import com.auth0.jwt.exceptions.TokenExpiredException;
import com.sugon.cloud.iam.api.entity.exception.SubjectTokenAccessDeniedException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import java.io.IOException;
import java.lang.annotation.Annotation;

@Slf4j
@Component
@Order(Integer.MIN_VALUE)
public class ExceptionFilter implements Filter, Order {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        try {
            chain.doFilter(request, response);
        } catch (SubjectTokenAccessDeniedException | TokenExpiredException | UsernameNotFoundException e ) {
            // 异常捕获，发送到error controller
            request.setAttribute("filter.error", e);
            //将异常分发到/error/exthrow控制器
            request.getRequestDispatcher("/error/extThrow").forward(request, response);
        }
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void destroy() {

    }

    @Override
    public int value() {
        return Integer.MIN_VALUE;
    }

    @Override
    public Class<? extends Annotation> annotationType() {
        return null;
    }
}
