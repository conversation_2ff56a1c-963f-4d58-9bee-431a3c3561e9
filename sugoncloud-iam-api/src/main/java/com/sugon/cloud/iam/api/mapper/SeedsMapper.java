package com.sugon.cloud.iam.api.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import com.sugon.cloud.iam.api.entity.SeedEntity;

/**
 * (Seeds)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-07 10:45:24
 */
public interface SeedsMapper extends BaseMapper<SeedEntity> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<Seeds> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<SeedEntity> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<SeedEntity> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<SeedEntity> entities);

}

