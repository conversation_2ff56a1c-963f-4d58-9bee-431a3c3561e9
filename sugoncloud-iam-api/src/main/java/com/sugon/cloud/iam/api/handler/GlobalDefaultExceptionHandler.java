package com.sugon.cloud.iam.api.handler;


import com.auth0.jwt.exceptions.TokenExpiredException;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.iam.api.entity.exception.*;
import com.sugon.cloud.iam.api.exception.BusinessException;
import com.sugon.cloud.iam.api.exception.SCA4SignatureException;
import com.sugon.cloud.iam.api.utils.CommonUtils;
import com.sugon.cloud.log.client.model.CommonBusinessException;
import com.sugon.cloud.resource.auth.common.service.ResourceAuthException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.text.StrBuilder;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.RedisConnectionFailureException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.ForbiddenException;
import javax.ws.rs.NotFoundException;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;

@RestControllerAdvice
@Slf4j
public class GlobalDefaultExceptionHandler {

    @ExceptionHandler(IamLoginException.class)
    public ResultModel loginException(HttpServletResponse response, IamLoginException ex) {
        log.error("error:", ex);
        String msg = ex.getMessage();
        if (!CommonUtils.isContainChinese(msg)) {
            msg = "用户登录异常";
        }
        return ResultModel.error(msg);
    }

    @ExceptionHandler(SCA4SignatureException.class)
    public ResultModel sca4SignatureException(SCA4SignatureException ex) {
        log.error("error:", ex);
        String msg = ex.getMessage();
        if (!CommonUtils.isContainChinese(msg)) {
            msg = "用户登录异常";
        }
        return ResultModel.error(msg);
    }

    //声明要捕获的异常
    @ExceptionHandler(ResourceConflictException.class)
    public void resourceConflictExceptionHandler(HttpServletResponse response, ResourceConflictException ex)
            throws IOException {
        log.error("error:", ex);
        response.setContentType("application/json;charset=utf-8");
        response.setStatus(HttpServletResponse.SC_CONFLICT);
        PrintWriter out = response.getWriter();
        out.write("{\n" +
                "    \"error\": {\n" +
                "        \"message\": \"" + ex.getMessage() + "\",\n" +
                "        \"code\": 409,\n" +
                "        \"title\": \"Conflict\"\n" +
                "    }\n" +
                "}");
    }

    //声明要捕获的异常
    @ExceptionHandler(ResourceNotFoundException.class)
    public void resourceNotFoundExceptionHandler(HttpServletResponse response, ResourceNotFoundException ex)
            throws IOException {
        log.error("error:", ex);
        response.setContentType("application/json;charset=utf-8");
        response.setStatus(HttpServletResponse.SC_NOT_FOUND);
        PrintWriter out = response.getWriter();
        out.write("{\n" +
                "    \"error\": {\n" +
                "        \"message\": \"" + ex.getMessage() + "\",\n" +
                "        \"code\": 404,\n" +
                "        \"title\": \"Not Found\"\n" +
                "    }\n" +
                "}");
    }

    //声明要捕获的异常
    @ExceptionHandler(KeyStoneMethodArgumentNotValidException.class)
    public void keyStoneMethodArgumentExceptionHandler(HttpServletResponse response, KeyStoneMethodArgumentNotValidException ex)
            throws IOException {
        log.error("error:", ex);
        response.setContentType("application/json;charset=utf-8");
        response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        PrintWriter out = response.getWriter();
        out.write("{\n" +
                "    \"error\": {\n" +
                "        \"message\": \"" + ex.getMessage() + "\",\n" +
                "        \"code\": 400,\n" +
                "        \"title\": \"Bad Request\"\n" +
                "    }\n" +
                "}");
    }

    //声明要捕获的异常
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResultModel methodArgumentExceptionHandler(HttpServletResponse response, MethodArgumentNotValidException ex)
            throws IOException {
        log.error("error:", ex);
        BindingResult bindingResult = ex.getBindingResult();
        List<FieldError> errors = bindingResult.getFieldErrors();
        StrBuilder errorMsg = new StrBuilder();
        errorMsg.append("参数异常: ");
        for (FieldError error : errors) {
            errorMsg.append(error.getField());
            errorMsg.append(":");
            errorMsg.append(error.getDefaultMessage());
            errorMsg.append("; ");
        }
        response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        return ResultModel.error(errorMsg.toString());
    }

    @ExceptionHandler(CommonBusinessException.class)
    public ResultModel commonBusinessExceptionHandler(CommonBusinessException ex) {
        log.error("error:", ex);
        return ResultModel.error(ex.getMessage());
    }

    //声明要捕获的异常
    @ExceptionHandler(BusinessException.class)
    public ResultModel businessExceptionHandler(BusinessException ex) {
        log.error("error:", ex);
        return ResultModel.error(ex.getMessage());
    }

    @ExceptionHandler(ResourceAuthException.class)
    public ResultModel resourceAuthExceptionHandler(ResourceAuthException ex) {
        log.error("error:", ex);
        return ResultModel.error(ex.getMessage());
    }

    @ExceptionHandler(RedisConnectionFailureException.class)
    public ResultModel redisConnectionFailureExceptionHandler(RedisConnectionFailureException ex) {
        log.error("系统异常：redis connection error:", ex);
        String msg = ex.getMessage();
        if (!CommonUtils.isContainChinese(msg)) {
            msg = "系统异常";
        }
        return ResultModel.error(msg);
    }
    @ExceptionHandler(TokenExpiredException.class)
    public ResultModel tokenExpiredExceptionHandler(TokenExpiredException ex, HttpServletResponse response) {
        log.error("TokenExpiredException:", ex);
        String msg = ex.getMessage();
        if (!CommonUtils.isContainChinese(msg)) {
            msg = "认证无效或已过期";
        }
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        return ResultModel.error(msg);
    }
    @ExceptionHandler(RuntimeException.class)
    public ResultModel runtimeExceptionHandler(RuntimeException ex) {
        log.error("RuntimeException:", ex);
        String msg = ex.getMessage();
        if (!CommonUtils.isContainChinese(msg)) {
            msg = "系统异常";
        }
        return ResultModel.error(msg);
    }

    //声明要捕获的异常
    @ExceptionHandler(Exception.class)
    public void exceptionHandler(HttpServletResponse response,
                                 Exception ex) throws IOException {
        log.error("error:", ex);
        response.setContentType("application/json;charset=utf-8");
        PrintWriter out = response.getWriter();
        if (ex instanceof SubjectTokenAccessDeniedException) {
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            out.write(ex.getMessage());
            out.close();
            return;
        }
        if (ex instanceof V3DeniedException) {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            out.write(ex.getMessage());
            out.close();
            return;
        }
        if (ex instanceof UsernameNotFoundException || ex instanceof AccessDeniedException) {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            out.write(ex.getMessage());
            out.close();
            return;
        }

        if (ex instanceof EmptyInputException) {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            out.write(ex.getMessage());
            out.close();
            return;
        }
        if (ex instanceof DuplicateKeyException) {
            response.setStatus(HttpServletResponse.SC_CONFLICT);
            out.write(ex.getMessage());
            out.close();
            return;
        }
        if (ex instanceof NotFoundException) {
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            out.write(ex.getMessage());
            out.close();
            return;
        }
        if (ex instanceof NativeBadRequestException) {
            response.setContentType("application/json;charset=utf-8");
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            out.write(ex.getMessage());
            out.close();
            return;
        }
        if (ex instanceof ProjectCannotFindException) {
            response.setContentType("application/json;charset=utf-8");
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            out.write(ex.getMessage());
            out.close();
            return;
        }
        if (ex instanceof UserCannotFindException) {
            response.setContentType("application/json;charset=utf-8");
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            out.write(ex.getMessage());
            out.close();
            return;
        }
        if (ex instanceof RoleCannotFindException) {
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            out.write(ex.getMessage());
            out.close();
            return;
        }
        if (ex instanceof ForbiddenException) {
            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
            out.write(ex.getMessage());
            out.close();
            return;
        }
//        "-------------以上是为了满足OpenStack异常信息不要修改------------------"
        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        if (ex instanceof TokenExpiredException) {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        }
        String errMes = ex.getMessage();
        if (!CommonUtils.isContainChinese(ex.getMessage())) {
            log.error("系统异常", ex);
            errMes = "系统异常";
        }
        out.write("{\n" +
                "  \"content\": null,\n" +
                "  \"resouce\": null,\n" +
                "  \"status_code\": 0,\n" +
                "  \"status_mes\": \"" + errMes + "\",\n" +
                "  \"success\": false\n" +
                "}");
        out.close();
    }
}
