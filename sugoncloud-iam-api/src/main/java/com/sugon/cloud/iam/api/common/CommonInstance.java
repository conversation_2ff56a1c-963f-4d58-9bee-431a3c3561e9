package com.sugon.cloud.iam.api.common;

import com.google.common.collect.Lists;

import java.util.List;

public class CommonInstance {

    public static final String USER_NOT_EXIST = "用户不存在";

    public static final String PROJECT_ID = "projectId";

    public static final String USER_ALIAS = "alias";

    public static final String CREATED = "created";

    public static final Integer MASTER_DEPARTMENT_LEVEL = 0;

    public static final String AUTHORIZATION = "Authorization";

    public static final String X_SCA4_REGION = "X-SCA4-Region";

    public static final String X_SCA4_PROJECT = "X-SCA4-Project";

    public static final String X_SCA4_METHOD = "X-SCA4-Method";

    public static final String X_SCA4_URI = "X-SCA4-URI";

    public static final String X_SCA4_ACTION = "X-SCA4-Action";

    public static final String USER_EXTRA_DESCRIPTION_KEY = "description";

    public static final String USER_EXTRA_SOURCE_KEY = "source";

    public static final String ENCRYPTION_CONTENT_KEY = "encryption_content";

    public static final String CERT_CONTENT_KEY = "cert_content";

    public static final String ONE_PLACE_LOGIN = "one_place_login";

    public static final String POLICY_TYPE = "user";

    public static final String GLOBAL_SETTING_TRUE = "true";

    public static final String TOKEN = "token";

    public static final String EXPIRE_TOKEN = "expire_token:";

    public static final String TOKEN_EXPIRED_KEY = "token_expired";

    public static final String DEPT_ID = "dept_id";

    public static final String LICENSE = "license";

    public static final int ALG_ASYM_SM2 = 19;

    public static final String UNAME_ERROR_TIMES = "uname_error_times:";
    public static final String UNAME_ERROR_LOCK = "uname_error_lock:";
    public static final String UNAME_ERROR_COUNT = "user_error_count:";

    public static final String CLOBAL_SETTING_PASSWORD_ERROR_TIMES = "error_times";

    public static final String CLOBAL_SETTING_PASSWORD_LOCK_MINUTE = "lock_minute";

    public static final String CLOBAL_SETTING_PASSWORD_ERROR_TIME_INTERVAL = "error_time_interval";

    public static final String CLOBAL_SETTING_PASSWORD = "password";
    /**
     * 签名验签
     */
    public static final String SM_3 = "sm3";
    /**
     * 完整性校验采用hmac
     */
    public static final String HMAC = "hmac";

    /**
     * 加密存储
     */
    public static final String SM_4 = "sm4";
    /**
     * 加密存储需要传
     */
    public static final String IV = "iv";
    public static final String MODEL_CBC = "CBC";
    public static final String ENCRYPT = "encrypt";

    /**
     * 用户完整度校验
     */
    public static final String USER_INTEGRITY_CHECK_HANDLER = "USER_INTEGRITY_CHECK_HANDLER";

    public static final String REAL_IP = "real_ip";

    public static final String REAL_IP_SUFFIX = "_internet";

    public static final String ROLE_TYPE_DEFAULT = "default";

    public static final String VNC_IP = "vnc_ip";

    public static final String VNC_PUBLIC_IP = "vnc_public_ip";

    public static final String CCE_WS = "cce_ws";

    public static final String CCE_PUBLIC_WS = "cce_public_ws";

    public static final String OBS_KEY = "obs_key";

    public static final String OBS_TEM_KEY = "obs_tem_key";

    public static final String USER_ROLE_SYNC_HANDLER = "USER_ROLE_SYNC_HANDLER";

    public static final String SECRET_KEY_SYNC_HANDLER = "SECRET_KEY_SYNC_HANDLER";

    public static final String STRATEGY_KEY_SYNC_HANDLER = "STRATEGY_KEY_SYNC_HANDLER";

    public static final String USER_STRATEGY_KEY_SYNC_HANDLER = "USER_STRATEGY_KEY_SYNC_HANDLER";

    public static final String CATALOG_SYSTEM_TYPE = "SYSTEM";

    public static final String CATALOG_CUSTOMER_TYPE = "CUSTOMER";

    public static final String USER_TYPE_ADMIN = "admin";

    public static final String USER_TYPE_SECURITY = "security";

    public static final String USER_TYPE_AUDIT = "audit";

    public static final String USER_TYPE_MASTER = "master";

    public static final String USER_TYPE_DEPARTMENT_MASTER = "department_master";

    public static final List<String> PLATFORM_ROLES = Lists.newArrayList(USER_TYPE_ADMIN, USER_TYPE_AUDIT, USER_TYPE_MASTER, USER_TYPE_DEPARTMENT_MASTER);

    public static final String IAM_USERS_REDIS_KEY = "/iam/users/";

    public static final String IAM_ROLEDB_USERS = "/iam/roledb/users/";

    public static final String IAM_POLICIES = "/iam/policies/";

    public static final String  IAM_POLICYDB_USERS = "/iam/policydb/users/";

    public static final String IAM_STS_POLICIES = "/iam/sts/policies/";

    public static final String IAM_STS_USERS_REDIS_KEY = "/iam/sts/users/";

    public static final String IAM_POLICYDB_STS_USERS = "/iam/policydb/sts-users/";

    public static final String PASSWORD_ENCODE = "passwordEncode";


    public static final Integer ZERO = 0;

    public static final Integer ONE = 1;
    //歌尔登录header获取cn码
    public static final String KOAL_CERT_CN = "KOAL_CERT_CN";

    // micro_service类型
    public static final String MICRO_INNER = "inner";
    public static final String MICRO_CUSTOM = "custom";
    public static final String MICRO_HIDE = "hide";


    //组织系统管理员
    public static final String ORG_SYSADMIN = "org_sysadmin";
    //组织审计管理员
    public static final String ORG_SECAUDITOR = "org_secauditor";
    //组织安全管理员
    public static final String ORG_SECADMIN = "org_secadmin";
    //系统管理员
    public static final String SYS_ADMIN = "sys_admin";
    //审计管理员
    public static final String SEC_AUDITOR = "sec_auditor";
    //安全管理员
    public static final String SEC_ADMIN = "sec_admin";

    // 保密用户类型
    public static final List<String> BM_ROLES = Lists.newArrayList(
            CommonInstance.SYS_ADMIN,
            CommonInstance.SEC_AUDITOR,
            CommonInstance.SEC_ADMIN,
            CommonInstance.ORG_SECAUDITOR,
            CommonInstance.ORG_SYSADMIN,
            CommonInstance.ORG_SECADMIN);

    // 监控用户锁定状态1对应的原因
    public static final String MONITOR_USER_LOCK_REASON_ONE = "用户名/密码错误次数达到上限";

    // 监控用户密码/用户名错误导致锁定，锁定状态1
    public static final int MONITOR_USER_LOCK_STATUS_ONE = 1;

    public static final String DEFAULT_PROJECT_ID = "admin-inner-project";

}
