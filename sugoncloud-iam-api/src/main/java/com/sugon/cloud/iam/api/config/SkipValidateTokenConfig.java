package com.sugon.cloud.iam.api.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "jwt.skip")
@Setter
@Getter
public class SkipValidateTokenConfig {
    private List<String> uri;
}

