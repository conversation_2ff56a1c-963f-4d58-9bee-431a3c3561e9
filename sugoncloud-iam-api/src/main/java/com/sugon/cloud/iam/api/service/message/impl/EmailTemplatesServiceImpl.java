package com.sugon.cloud.iam.api.service.message.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sugon.cloud.iam.api.entity.EmailTemplates;
import com.sugon.cloud.iam.api.mapper.EmailTemplatesMapper;
import com.sugon.cloud.iam.api.service.message.EmailTemplatesService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: yangdingshan
 * @Date: 2025/2/10 16:26
 * @Description:
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class EmailTemplatesServiceImpl extends ServiceImpl<EmailTemplatesMapper, EmailTemplates> implements EmailTemplatesService {
}
