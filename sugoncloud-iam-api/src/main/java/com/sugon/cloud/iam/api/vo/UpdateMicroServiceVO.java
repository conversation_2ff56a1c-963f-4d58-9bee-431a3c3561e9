package com.sugon.cloud.iam.api.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel("修改微服务")
public class UpdateMicroServiceVO {
    @ApiModelProperty(value = "id")
    @NotBlank(message = "id不能为空")
    private String id;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "web链接")
    private String link;
    @ApiModelProperty(value = "分类")
    @JsonProperty("category_id")
    private String categoryId;
    @ApiModelProperty(value = "打开方式Internal:内部加载,external:外部跳转")
    @JsonProperty("open_mode")
    private String openMode;
    @ApiModelProperty("隐藏服务")
    @JsonProperty("nav_hidden")
    private Boolean navHidden;
    @ApiModelProperty("排序")
    private Double order;
    @ApiModelProperty("是否为第三方接入")
    @JsonProperty("third_part_access")
    private Boolean thirdPartAccess;
}