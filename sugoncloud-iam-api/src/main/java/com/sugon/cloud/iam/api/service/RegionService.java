package com.sugon.cloud.iam.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sugon.cloud.iam.api.entity.Region;
import com.sugon.cloud.iam.api.entity.RegionForm;
import com.sugon.cloud.iam.api.vo.UpdateRegionVO;

public interface RegionService extends IService<Region> {

    String createRegion(RegionForm regionForm);

    void updateRegion(String regionId, UpdateRegionVO updateRegionVO);
}
