package com.sugon.cloud.iam.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sugon.cloud.iam.api.entity.GlobalsettingsEntity;

public interface GlobalsettingsService extends IService<GlobalsettingsEntity> {
    /**
     * 根据类型和建获取全局实体
     * @param type
     * @param key
     * @return
     */
    GlobalsettingsEntity getGlobalsettingsEntity(String type,String key);

    boolean getBmStatus();

    boolean getSecurityAdminLoginStatus();
}
