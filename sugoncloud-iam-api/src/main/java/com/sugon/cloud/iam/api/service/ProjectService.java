package com.sugon.cloud.iam.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.entity.keystone.Project;
import com.sugon.cloud.iam.api.vo.CreateOrUpdateProjectVO;
import com.sugon.cloud.iam.api.vo.ProjectContractVO;
import com.sugon.cloud.iam.api.vo.ProjectResourceVO;
import com.sugon.cloud.iam.api.vo.UpdateProjectOperationTypeVO;
import com.sugon.cloud.iam.common.model.vo.CreateWithDepartProjectVO;
import com.sugon.cloud.iam.common.model.vo.ProjectDetailVO;
import com.sugon.cloud.iam.common.model.vo.ProjectUpdateByOperationVO;

import java.util.List;

public interface ProjectService extends IService<Project> {

    Project findByUserIdAndRoleId(String userId, String roleId);

    List<Project> findByUserId(String userId);

    void insert(CreateOrUpdateProjectVO createOrUpdateProjectVO);

    CreateWithDepartProjectVO insertProjectWithoutDepart(CreateWithDepartProjectVO createWithDepartProjectVO);

    void update(CreateOrUpdateProjectVO createOrUpdateProjectVO);

    ResultModel deleteById(String id, String regionId);

    ProjectDetailVO queryById(String id);

    /**
     * 根据组织ID查询项目list
     *
     * @param userId
     * @param deptId
     * @param name
     * @param pageNum
     * @param pageSize
     * @param querySub    是否查询子级
     * @param projectType
     * @param meterType
     * @return
     */
    PageCL<ProjectDetailVO> pageList(String userId, String deptId, String name, int pageNum, int pageSize, Boolean querySub, String projectType, String meterType);

    List<ProjectDetailVO> getAllProjects();

    PageCL<ProjectDetailVO> getAllProjectsByUserType(String currentUserId, int pageNum, int pageSize, String name, String projectType, String meterType);

    /**
     * 根据组织查询该组织以及所有子组织的所有项目
     *
     * @param departmentId 组织Id
     * @param pageNum      页码
     * @param pageSize     每页条数
     * @param name         根据名称查询
     * @param projectType
     * @param meterType
     * @return PageCL<ProjectDetailVO>
     */
    PageCL<ProjectDetailVO> listAllDepartment(String currentUserId, String departmentId, int pageNum, int pageSize, String name, String projectType, String meterType);

    List<String> getProjectIdsByUserId(String userId);


    List<ProjectDetailVO> listContractOptions(String userId, String name, String meterType, String departmentId);

    PageCL<ProjectContractVO> listProjectContractOptions(String projectId, String deptId, List<String> meterTypes,
                                                         String name, String code, int pageNum, int pageSize);


    void extension(String id, String extensionDate);

    void updateProjectByOperation(String id, ProjectUpdateByOperationVO projectUpdateByOperationVO);


    void updateOperationApproveType(UpdateProjectOperationTypeVO vo);

    List<ProjectResourceVO> getProjectResourceLink();
}
