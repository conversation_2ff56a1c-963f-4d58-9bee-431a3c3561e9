package com.sugon.cloud.iam.api.task;

import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.entity.StrategyPO;
import com.sugon.cloud.iam.api.service.StrategyService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;


@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
@RefreshScope
public class StrategySyncHandler {

    @Resource(name = "defaultRedisTemplate")
    private final RedisTemplate<String, Object> redisTemplate;

    private final StrategyService service;

    @XxlJob(CommonInstance.STRATEGY_KEY_SYNC_HANDLER)
    public void run() {
        log.info("::::::::::::::strategy_sync start");
        List<StrategyPO> list = service.list();
        list.forEach(secretKeyEntity -> {
            redisTemplate.opsForValue().set(CommonInstance.IAM_POLICIES+secretKeyEntity.getId(), secretKeyEntity.getPolicy());
        });
        log.info("::::::::::::::strategy_sync end");
    }
}
