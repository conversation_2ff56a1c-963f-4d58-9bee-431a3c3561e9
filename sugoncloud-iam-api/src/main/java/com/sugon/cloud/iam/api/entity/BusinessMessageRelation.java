package com.sugon.cloud.iam.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sugon.cloud.common.model.entity.BaseEntity;
import lombok.Data;

/**
 * @Author: yangdingshan
 * @Date: 2025/2/10 14:22
 * @Description: 业务消息模板绑定
 */
@Data
@TableName("business_message_relation")
public class BusinessMessageRelation extends BaseEntity {

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 消息类型
     */
    private String messageType;

    /**
     * 模板id
     */
    private String templateId;

    /**
     * 业务描述
     */
    private String businessDesc;

    /**
     * 模板类型 2表示通知邮件，3表示验证码邮件
     */
    private Integer templateType;

    /**
     * 是否启用
     */
    private Boolean enabled;
}
