package com.sugon.cloud.iam.api.config;

import com.sugon.cloud.iam.api.entity.MultiEsEntity;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/08/02 17:51
 **/

@Data
@Component
@ConfigurationProperties(prefix = "multiregion")
public class MultiRegionEsConfig {
    private List<MultiEsEntity> multiEsEntityList;
    private List<MultiEsEntity> multiMonitorEntityList;
}
