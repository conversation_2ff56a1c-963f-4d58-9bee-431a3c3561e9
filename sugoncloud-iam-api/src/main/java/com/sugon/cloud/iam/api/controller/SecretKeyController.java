package com.sugon.cloud.iam.api.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.constants.HeaderParamConstant;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.common.mapper.ModelMapper;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.common.utils.UUIDUtil;
import com.sugon.cloud.iam.api.common.AESUtil;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.entity.Department;
import com.sugon.cloud.iam.api.entity.SecretKeyEntity;
import com.sugon.cloud.iam.api.entity.keystone.Project;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.service.*;
import com.sugon.cloud.iam.api.service.resourceauth.SecretKeyResourceAuthHandler;
import com.sugon.cloud.iam.api.service.resourceauth.UserIdResourceAuthHandler;
import com.sugon.cloud.iam.common.model.vo.SecretKeyVO;
import com.sugon.cloud.iam.common.model.vo.UserViewVO;
import com.sugon.cloud.log.client.starter.annotation.LogRecordAnnotation;
import com.sugon.cloud.redis.distributed.lock.config.RedisDistributedLock;
import com.sugon.cloud.resource.auth.common.aop.ResourceAuth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags = "凭证API")
@RequestMapping(value = "/api/secret-key")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RestController
@Slf4j
@RefreshScope
public class SecretKeyController {

    private final SecretKeyService secretKeyService;

    @Resource(name = "defaultRedisTemplate")
    private final RedisTemplate<String, Object> redisTemplate;

    @Value("${obs.certificate.number}")
    private int certificateNumber = 3;

    private final UserService userService;

    /**
     * CSV文件列分隔符
     */
    private static final String CSV_COLUMN_SEPARATOR = ",";

    /**
     * CSV文件行分隔符
     */
    private static final String CSV_ROW_SEPARATOR = "\r\n";

    private final RedisDistributedLock redisDistributedLock;

    private final ProjectService projectService;

    private final DepartmentService departmentService;

    private final ModelMapper mapper;

    private final GlobalsettingsService globalsettingsService;

    @ApiOperation("创建凭证")
    @PostMapping("{user_id}")
    @LogRecordAnnotation(value = "创建凭证", detail = "创建凭证[{{#_ret.resource}}]")
    @ApiImplicitParams({@ApiImplicitParam(name = "user_id", value = "用户ID", required = true, dataType = "String", paramType = "path"),
                        @ApiImplicitParam(name = "describe", value = "描述(最多255分字符)", paramType = "query", dataType = "String")})
    @ResourceAuth(handler = UserIdResourceAuthHandler.class, resources = "#userId")
    public ResultModel<String> createAkSk(@PathVariable("user_id") String userId,
                                          @RequestParam(value = "describe", required = false) String describe) {
        // 解决条件竞争漏洞，多个线程访问增加访问凭证，导致会超出上线
        boolean locked = redisDistributedLock.lock(userId);
        if (!locked) {
            log.error("创建凭证获取lockKey=[{}]的redis锁超时", userId);
            return ResultModel.error("创建凭证获取失败，请稍后再试");
        }
        try {
            List<SecretKeyEntity> list = secretKeyService.list(new QueryWrapper<SecretKeyEntity>()
                    .eq("user_id", userId)
                    .eq("inner_key", false));
            if (list.size() >= certificateNumber ) {
                return  ResultModel.error("凭证个数已达上限");
            }
            String ak = UUIDUtil.get32UUID();
            String sk = AESUtil.encrypt(ak, CommonInstance.OBS_KEY);
            SecretKeyEntity secretKeyEntity = new SecretKeyEntity();
            secretKeyEntity.setEnabled(true)
                    .setUserId(userId)
                    .setCreateAt(new Date())
                    .setAccessKey(ak)
                    .setSecretKey(sk)
                    .setDescription(describe)
                    .setInnerKey(false);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("accessKey",ak);
            jsonObject.put("status",1);
            jsonObject.put("secretKey",sk);
            jsonObject.put("userId",userId);
            jsonObject.put("expiresAt", null);
            secretKeyService.save(secretKeyEntity);
            redisTemplate.opsForValue().set(CommonInstance.IAM_USERS_REDIS_KEY+ak, jsonObject.toString());
            return ResultModel.success("添加凭证成功", secretKeyEntity.getId());
        } finally {
            redisDistributedLock.unLock(userId);
        }
    }

    @ApiOperation("修改凭证描述")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "凭证id", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "describe", value = "描述(最多255分字符)", paramType = "query", dataType = "String")})
    @PutMapping("{id}")
    @LogRecordAnnotation(value = "修改凭证描述", detail = "创建凭证描述[{SecretKeyEntity{#id}}]")
    @ResourceAuth(handler = SecretKeyResourceAuthHandler.class, resources = "#id")
    public ResultModel<String> update(@RequestParam(value = "describe", required = false) String describe,
                              @PathVariable("id") String id) {
        secretKeyService.lambdaUpdate()
                .eq(SecretKeyEntity::getId, id)
                .set(SecretKeyEntity::getDescription, describe)
                .update();
        return ResultModel.success("更新描述成功");
    }

    @ApiOperation("修改凭证状态")
    @LogRecordAnnotation(value = "修改凭证状态", detail = "创建凭证状态[{SecretKeyEntity{#id}}]")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "凭证id", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "status", value = "启用/禁用", paramType = "Boolean", dataType = "String", required = true)})
    @PutMapping("{id}/{status}")
    @ResourceAuth(handler = SecretKeyResourceAuthHandler.class, resources = "#id")
    public ResultModel<String> updateStatus(@PathVariable("status") Boolean status,
                              @PathVariable("id") String id) {
        SecretKeyEntity secretKeyEntity = secretKeyService.getById(id);
        if (secretKeyEntity == null) {
            return ResultModel.error("凭证不存在");
        }
        secretKeyService.lambdaUpdate()
                .eq(SecretKeyEntity::getId, id)
                .eq(SecretKeyEntity::isInnerKey, false)
                .set(SecretKeyEntity::getEnabled, status)
                .update();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("accessKey",secretKeyEntity.getAccessKey());
        jsonObject.put("status", status?1:0);
        jsonObject.put("secretKey",secretKeyEntity.getSecretKey());
        jsonObject.put("userId",secretKeyEntity.getUserId());
        redisTemplate.opsForValue().set(CommonInstance.IAM_USERS_REDIS_KEY + secretKeyEntity.getAccessKey(), jsonObject.toString());
        return ResultModel.success("更新状态成功");
    }

    @ApiOperation("删除凭证")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "凭证id", required = true, dataType = "String", paramType = "path")})
    @DeleteMapping("{id}")
    @LogRecordAnnotation(value = "删除凭证", detail = "删除凭证[{SecretKeyEntity{#id}}]")
    @ResourceAuth(handler = SecretKeyResourceAuthHandler.class, resources = "#id")
    public ResultModel<String> delete(@PathVariable("id") String id) {
        SecretKeyEntity secretKeyEntity = secretKeyService.getById(id);
        if (secretKeyEntity == null) {
            return ResultModel.error("凭证不存在");
        }
        secretKeyService.remove(new LambdaQueryWrapper<SecretKeyEntity>()
                .eq(SecretKeyEntity::getId, id)
                .eq(SecretKeyEntity::isInnerKey, false));
        redisTemplate.delete(CommonInstance.IAM_USERS_REDIS_KEY + secretKeyEntity.getAccessKey());
        return ResultModel.success("删除凭证成功");
    }

    @ApiOperation("凭证列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "user_id", value = "用户Id", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "page_num", value = "页码", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "page_size", value = "每页显示条数", paramType = "query", dataType = "int")})
    @GetMapping("{user_id}")
    @ResourceAuth(handler = UserIdResourceAuthHandler.class, resources = "#userId")
    public ResultModel<PageCL<SecretKeyEntity>> list(@PathVariable("user_id") String userId,
                            @RequestParam(value = "page_num", defaultValue = "0", required = false) int pageNum,
                            @RequestParam(value = "page_size", defaultValue = "15", required = false) int pageSize) {
        //查出当前用户 下面的子账户
        PageCL<UserViewVO> userPage = userService.listAllUser(userId, 1, 999, userId);
        List<String> ids = userPage.getList().stream().map(UserViewVO::getId).collect(Collectors.toList());
        ids.add(userId);
        PageCL<SecretKeyEntity> secretKeyPage = secretKeyService.getSecretKeyPage(ids, pageNum, pageSize);
        return ResultModel.success("获取列表成功", secretKeyPage);
    }

    @ApiOperation("下载凭证")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "凭证Id", required = true, dataType = "String", paramType = "path")})
    @GetMapping("{id}/download")
    @LogRecordAnnotation(value = "下载凭证", detail = "下载凭证[{SecretKeyEntity{#id}}]")
    @ResourceAuth(handler = SecretKeyResourceAuthHandler.class, resources = "#id")
    public void download(@PathVariable("id") String id,
                         HttpServletResponse response) {
        SecretKeyEntity secretKeyEntity = secretKeyService.getById(id);

        String titles = "User ID,Access Key Id,Secret Access Key"; // 设置表头
        try (OutputStream os = response.getOutputStream()){
            this.responseSetProperties(response);
            this.doExport(secretKeyEntity, titles, os);
        } catch (Exception e) {
            log.error("下载凭证失败", e);
        }
    }

    @ApiOperation("cce csi内部使用")
    @ApiImplicitParams({@ApiImplicitParam(name = "projectId", value = "凭证Id", required = true, dataType = "String", paramType = "path")})
    @GetMapping("/{project_id}/secret")
    @DocumentIgnore
    public ResultModel<SecretKeyVO> getAkSkByProjectId(@PathVariable("project_id") String projectId, HttpServletRequest request) {
        String userName = request.getHeader(HeaderParamConstant.USER_NAME);
        if (!"inner".equals(userName)) {
            return ResultModel.error("非内部调用");
        }
        Project project = projectService.getById(projectId);
        if (project == null) {
            log.warn("通过该租户ID未查询到租户信息{}", projectId);
            return ResultModel.error("通过该租户ID未查询到租户信息");
        }
        //获取根部门信息
        Department rootDepartmentByDeptId = departmentService.getRootDepartmentByDeptId(project.getDeptId());
        if (rootDepartmentByDeptId == null) {
            log.warn("通过该部门ID未查询到部门信息{}", project.getDeptId());
            return ResultModel.error("通过该部门ID未查询到部门信息");
        }
        User user = null;
        if (CommonInstance.DEFAULT_PROJECT_ID.equals(projectId)) {
            //默认项目用admin账户或者sysadmin账户
            boolean bmStatus = globalsettingsService.getBmStatus();
            if (bmStatus) {//bm环境下默认用sysadmin账户
                user = userService.getOne(new LambdaQueryWrapper<User>()
                        .eq(User::getName, "sysadmin"));
            } else {//非bm状态默认用admin账户
                user = userService.getOne(new LambdaQueryWrapper<User>()
                        .eq(User::getName, "admin"));
            }
        } else {
            //通过根本部门ID+type=master查询用户信息
            user = userService.getOne(new LambdaQueryWrapper<User>()
                    .eq(User::getDeptId, rootDepartmentByDeptId.getId())
                    .eq(User::getType, TypeUtil.TYPE_MASTER));
        }
        if (user == null) {
            log.warn("通过该部门ID未查询到用户信息{}", rootDepartmentByDeptId.getId());
            return ResultModel.error("通过该部门ID未查询到用户信息");
        }
        if (!TypeUtil.TYPE_MASTER.equals(user.getType())) {
            return ResultModel.error("该账户不能通过");
        }
        List<SecretKeyEntity> list = secretKeyService.list(new LambdaQueryWrapper<SecretKeyEntity>()
                .eq(SecretKeyEntity::getUserId, user.getId())
                .eq(SecretKeyEntity::isInnerKey, true));
        SecretKeyEntity secretKeyEntity = null;
        if (list.size() > 0) {
            secretKeyEntity = list.get(0);
        } else {
            String ak = UUIDUtil.get32UUID();
            String sk = AESUtil.encrypt(ak, CommonInstance.OBS_KEY);
            secretKeyEntity = new SecretKeyEntity();
            secretKeyEntity.setEnabled(true)
                    .setUserId(user.getId())
                    .setCreateAt(new Date())
                    .setAccessKey(ak)
                    .setSecretKey(sk)
                    .setDescription("CSI创建")
                    .setInnerKey(true);
            secretKeyService.save(secretKeyEntity);

        }
        SecretKeyVO secretKeyVO = mapper.map(secretKeyEntity, SecretKeyVO.class);
        return ResultModel.success("获取凭证成功", secretKeyVO);
    }

    public  void doExport(SecretKeyEntity secretKeyEntity, String titles, OutputStream os)
            throws Exception {

        // 保证线程安全
        StringBuffer buffer = new StringBuffer();
        String[] titleArr = titles.split(",");
        // 组装表头
        for (String title : titleArr) {
            buffer.append(title).append(CSV_COLUMN_SEPARATOR);
        }
        buffer.append(CSV_ROW_SEPARATOR);
        buffer.append(secretKeyEntity.getUserId()).append(CSV_COLUMN_SEPARATOR);
        buffer.append(secretKeyEntity.getAccessKey()).append(CSV_COLUMN_SEPARATOR);
        buffer.append(secretKeyEntity.getSecretKey()).append(CSV_COLUMN_SEPARATOR);
        buffer.append(CSV_ROW_SEPARATOR);
        // 写出响应
        os.write(buffer.toString().getBytes(StandardCharsets.UTF_8));
        os.flush();
    }

    /**
     * 设置Header
     *
     * @param response
     * @throws UnsupportedEncodingException
     */
    public  void responseSetProperties(HttpServletResponse response)
            throws UnsupportedEncodingException {
        // 读取字符编码
        String utf = "UTF-8";
        // 设置响应
        response.setContentType("application/ms-txt.numberformat:@");
        response.setCharacterEncoding(utf);
        response.setHeader("Pragma", "public");
        response.setHeader("Cache-Control", "max-age=30");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("credentials.csv", utf));
    }
}
