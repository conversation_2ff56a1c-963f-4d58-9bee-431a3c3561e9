package com.sugon.cloud.iam.api.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.common.String2JSONMapper;
import com.sugon.cloud.iam.api.entity.StrategyForm;
import com.sugon.cloud.iam.api.entity.StrategyPO;
import com.sugon.cloud.iam.api.exception.BusinessException;
import com.sugon.cloud.iam.api.mapper.StrategyMapper;
import com.sugon.cloud.iam.api.service.StrategyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.glasnost.orika.MapperFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class StrategyServiceImpl extends ServiceImpl<StrategyMapper, StrategyPO> implements StrategyService {

    @Override
    public PageCL<StrategyForm> findByUserIdPage(String userId, int pageNumber, int pageSize) {
        try {
            IPage<StrategyPO> page = new Page(pageNumber, pageSize);
            page = this.baseMapper.findByUserIdPage(page, userId);
            MapperFacade mapper = String2JSONMapper.mapper();
            return new PageCL<StrategyForm>().getPageByPageHelper(page, mapper.mapAsList(page.getRecords(), StrategyForm.class));
        } catch (Exception e) {
            log.error("查询用户策略列表失败:", e);
            throw new BusinessException("查询列表报错");
        }
    }

    @Override
    public PageCL<StrategyForm> findPage(String userId, int pageNumber, int pageSize) {
        try {
            IPage<StrategyPO> page = new Page(pageNumber, pageSize);
            page = this.baseMapper.selectPage(page, new QueryWrapper<StrategyPO>().lambda()
                    .eq(StrategyPO::getCatalog, CommonInstance.CATALOG_SYSTEM_TYPE)
                    .or()
                    .eq(StrategyPO::getCreatBy, userId)
                    .orderByDesc(StrategyPO::getCreateAt));
            MapperFacade mapper = String2JSONMapper.mapper();
            return new PageCL<StrategyForm>().getPageByPageHelper(page, mapper.mapAsList(page.getRecords(), StrategyForm.class));
        } catch (Exception e) {
            log.error("查询策略列表失败:", e);
            throw new BusinessException("查询列表报错");
        }
    }
}
