package com.sugon.cloud.iam.api.service.resourceauth;

import com.beust.jcommander.internal.Lists;
import com.sugon.cloud.common.constants.HeaderParamConstant;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.resource.auth.common.service.impl.BaseResourceAuthHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/10 17:46
 */
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class SecurityUserTypeAuthHandler extends BaseResourceAuthHandler {

    private final HttpServletRequest request;

    @Override
    public List<String> getUserResource(String userId) {

        String header = request.getHeader(HeaderParamConstant.USER_TYPE);
        // 安全员权限直接放开
        if (TypeUtil.TYPE_SECURITY.equals(header) || TypeUtil.TYPE_SEC_ADMIN.equals(header) || TypeUtil.TYPE_SYS_ADMIN.equals(header)) {
            return Lists.newArrayList(TypeUtil.TYPE_ADMIN);
        }

        return null;
    }
}
