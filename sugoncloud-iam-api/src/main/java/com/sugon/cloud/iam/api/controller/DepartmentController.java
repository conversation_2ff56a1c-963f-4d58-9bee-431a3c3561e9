package com.sugon.cloud.iam.api.controller;

import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.constants.HeaderParamConstant;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.common.model.IdNameInfo;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.service.DepartmentService;
import com.sugon.cloud.iam.api.service.ProjectService;
import com.sugon.cloud.iam.api.service.resourceauth.DeptResourceAuthHandler;
import com.sugon.cloud.iam.api.service.resourceauth.ProjectResourceAuthHandler;
import com.sugon.cloud.iam.api.service.resourceauth.SecurityUserTypeAuthHandler;
import com.sugon.cloud.iam.api.service.resourceauth.UserCreateAuthHandler;
import com.sugon.cloud.iam.api.service.resourceauth.UserIdExtentResourceAuthHandler;
import com.sugon.cloud.iam.api.service.resourceauth.UserResourceAuthHandler;
import com.sugon.cloud.iam.api.vo.CreateOrUpdateDepartmentVO;
import com.sugon.cloud.iam.api.vo.DepartmentDetailVO;
import com.sugon.cloud.iam.common.model.vo.DepartmentTreeDetailVO;
import com.sugon.cloud.iam.common.model.vo.ProjectDetailVO;
import com.sugon.cloud.log.client.context.LogRecordContext;
import com.sugon.cloud.log.client.starter.annotation.LogRecordAnnotation;
import com.sugon.cloud.resource.auth.common.aop.ResourceAuth;
import com.sugon.cloud.resource.auth.common.aop.ResourceAuths;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 组织(Department)表控制层
 *
 * <AUTHOR>
 * @since 2021-04-07 10:51:58
 */
@RestController
@RequestMapping("/api/departments")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Api(tags = {"组织API"})
public class DepartmentController {
    /**
     * 服务对象
     */
    private final DepartmentService departmentService;
    private final ProjectService projectService;
    private final HttpServletRequest request;
    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/{id}")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "组织ID", required = true, dataType = "String", paramType = "path")})
    @ApiOperation("查询组织详情")
    @ResourceAuth(handler = DeptResourceAuthHandler.class, resources = "#id")
    public ResultModel<DepartmentDetailVO> detail(@PathVariable("id") String id) {
        return ResultModel.success(this.departmentService.queryById(id));
    }

    @PostMapping
    @ApiOperation(value = "创建组织")
    @LogRecordAnnotation(value = "创建组织", detail = "创建组织:{{#createOrUpdateDepartmentVO.name}}", resourceId = "{{#id}}", resource = "{{#createOrUpdateDepartmentVO.name}}")
    @ResourceAuths(value = {
            @ResourceAuth(handler = UserCreateAuthHandler.class, resources = TypeUtil.TYPE_ADMIN),
            @ResourceAuth(handler = DeptResourceAuthHandler.class, resources = "#createOrUpdateDepartmentVO.parentId"),
    })
    public ResultModel<CreateOrUpdateDepartmentVO> create(@Validated @RequestBody CreateOrUpdateDepartmentVO createOrUpdateDepartmentVO) {
        departmentService.insert(createOrUpdateDepartmentVO);
        LogRecordContext.putVariable("id", createOrUpdateDepartmentVO.getId());
        return ResultModel.success("创建组织成功", createOrUpdateDepartmentVO);
    }

    @PutMapping("/{id}")
    @ApiOperation("修改组织")
    @LogRecordAnnotation(value = "修改组织", detail = "修改组织:{{#createOrUpdateDepartmentVO.name}}", resourceId = "{{#id}}", resource = "{{#createOrUpdateDepartmentVO.name}}")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "组织ID", required = true, dataType = "String", paramType = "path")})
    @ResourceAuth(handler = DeptResourceAuthHandler.class, resources = "#id")
    public ResultModel<CreateOrUpdateDepartmentVO> update(@PathVariable("id") String id,
                                                          @Validated @RequestBody CreateOrUpdateDepartmentVO createOrUpdateDepartmentVO) {
        createOrUpdateDepartmentVO.setId(id);
        departmentService.update(createOrUpdateDepartmentVO);
        return ResultModel.success("修改组织成功", createOrUpdateDepartmentVO);
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @DeleteMapping("/{id}")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "组织ID", required = true, dataType = "String", paramType = "path")})
    @LogRecordAnnotation(value = "删除组织", detail = "删除组织:{DEPARTMENT{#id}}", resourceId = "{{#id}}", resource = "{DEPARTMENT{#id}}")
    @ApiOperation("删除组织")
    @ResourceAuth(handler = DeptResourceAuthHandler.class, resources = "#id")
    public ResultModel<String> delete(@PathVariable("id") String id) {
        return this.departmentService.deleteById(id);
    }

    /**
     * 通过userId查询组织及各个组织子节点
     *
     * @return 通过userId查询组织及各个组织子节点
     */
    @GetMapping
    @ApiOperation("查询组织及各个组织子节点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "master_id", value = "根账号ID", paramType = "query", dataType = "String")})
    public ResultModel<DepartmentTreeDetailVO> findByUserId(HttpServletRequest req,
                                                            @RequestParam(value = "master_id", required = false) String masterId) {
        if (StringUtils.isEmpty(masterId)) {
            return ResultModel.success(this.departmentService.findTreeDepartmentByUserId(req.getHeader(HeaderParamConstant.USER_ID)));
        } else {
            return ResultModel.success(this.departmentService.findTreeDepartmentByUserId(masterId));
        }
    }

    @GetMapping("/all")
    @ApiOperation("查询所有组织及各个组织子节点")
    @LogRecordAnnotation(value = "查询组织列表", detail = "查询组织列表")
    @ResourceAuth(handler = SecurityUserTypeAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    @DocumentIgnore
    public ResultModel<DepartmentTreeDetailVO> findAll() {
        return ResultModel.success(this.departmentService.findTreeDepartmentAll());
    }

    /**
     * 查询所有组织及各个组织子节点
     *
     * @return 查询所有组织及各个组织子节点
     */
    @GetMapping("/alls")
    @ApiOperation("查询所有组织节点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "user_id", value = "用户ID", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "query_default_dept", value = "是否查询admin的默认组织", paramType = "query", dataType = "Boolean", allowableValues = "true,false")})
    @ResourceAuth(handler = UserIdExtentResourceAuthHandler.class, resources = "#userId")
    public ResultModel<List<DepartmentTreeDetailVO>> findAlls(@RequestParam(value = "user_id", required = false) String userId,
                                                              @RequestParam(value = "query_default_dept", required = false, defaultValue = "true") boolean queryDefaultDept,
                                                              @RequestHeader(required = false, value= "userId", defaultValue = "1") String currentUserId) {
        return ResultModel.success(this.departmentService.findDepartmentAll(userId, currentUserId, queryDefaultDept));
    }

    /**
     * 通过userId查询组织及各子组织总数量
     *
     * @return 通过userId查询组织及各子部组织总数量
     */
    @GetMapping("/total-by-user")
    @ApiOperation("通过userId查询组织及各子组织总数量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "user_id", value = "用户ID", paramType = "query", dataType = "String")})
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    public ResultModel<Integer> findUserDepartmentTotalByUserId(@RequestParam(value = "user_id", required = false) String userId) {
        return ResultModel.success(this.departmentService.findUserDepartmentTotalByUserId(userId));
    }


    @GetMapping("/{id}/projects")
    @ApiOperation("组织项目列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "组织ID", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "name", value = "模糊查询名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "page_num", value = "页码", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "user_id", value = "用户ID", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "page_size", value = "每页显示条数", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "project_type", value = "项目类型：正式项目(formal)、试用项目(test)", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "meter_type", value = "计费类型（统一计费：contract_meter_unification | 合同计费：contract_meter_contract）", paramType = "query", dataType = "string"),
    }
    )
    @ResourceAuth(handler = DeptResourceAuthHandler.class, resources = "#deptId")
    public ResultModel<PageCL<ProjectDetailVO>> pageList(@PathVariable("id") String deptId,
                                                         @RequestParam(value = "name", required = false) String name,
                                                         @RequestParam(value = "query_sub", required = false, defaultValue = "false") Boolean querySub,
                                                         @RequestParam(value = "user_id", required = false) String userId,
                                                         @RequestParam(value = "page_num", defaultValue = "1", required = false) int pageNum,
                                                         @RequestParam(value = "page_size", defaultValue = "15", required = false) int pageSize
            , @RequestParam(value = "project_type", required = false) String projectType
            , @RequestParam(value = "meter_type", required = false) String meterType) {
        PageCL<ProjectDetailVO> pageResult = projectService.pageList(userId, deptId, name, pageNum, pageSize, querySub, projectType, meterType);
        return ResultModel.success(pageResult);
    }

    @ApiOperation("通过用户IDs查询名称")
    @PostMapping("/by-ids")
    @DocumentIgnore
    public ResultModel<List<IdNameInfo>> deptInfos(@RequestBody List<String> ids) {
        return ResultModel.success(departmentService.deptInfos(ids));
    }

    @GetMapping("/by/{dept_id}")
    @ApiOperation("根据组织ID查询当前组织的子组织及父组织，不包括根组织以及当前组织")
    @ResourceAuth(handler = UserCreateAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    public ResultModel<List<DepartmentTreeDetailVO>> getDeptsByDeptId(@PathVariable("dept_id") String deptId) {
        return ResultModel.success(departmentService.getDeptsByDeptId(deptId));
    }


    @GetMapping("/top-dept/{user_id}")
    @ApiImplicitParams({@ApiImplicitParam(name = "user_id", value = "用户id", required = true, dataType = "String", paramType = "path")})
    @ApiOperation("根据用户id查询顶级组织详情")
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    public ResultModel<DepartmentDetailVO> topDept(@PathVariable("user_id") String userId) {
        return ResultModel.success(departmentService.queryTopDeptByUserId(userId));
    }

    @GetMapping("/top-dept/by-project-id/{project_id}")
    @ApiImplicitParams({@ApiImplicitParam(name = "project_id", value = "项目id", required = true, dataType = "String", paramType = "path")})
    @ApiOperation("根据项目id查询顶级组织详情")
    @ResourceAuth(handler = ProjectResourceAuthHandler.class, resources = "#projectId")
    public ResultModel<DepartmentDetailVO> topDeptByProjectId(@PathVariable("project_id") String projectId) {
        return ResultModel.success(departmentService.queryTopDeptByProjectId(projectId));
    }

    @ApiOperation("BM组织安全管理有、审计管理员查询当前租户所有部门")
    @GetMapping("/org/dept-ids")
    @DocumentIgnore
    public ResultModel<List<String>> orgDeptIds() {
        return ResultModel.success(departmentService.getDeptsByUserId(request.getHeader(HeaderParamConstant.USER_ID)));
    }

}
