package com.sugon.cloud.iam.api.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sugon.cloud.iam.api.ann.CreateGroup;
import com.sugon.cloud.iam.api.ann.SubCreateGroup;
import com.sugon.cloud.iam.api.ann.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

@Data
@ApiModel("创建或者更新用户")
public class UserCreateOrUpdateVO {

    @ApiModelProperty("用户Id(创建、注册时用户可不传)")
    @NotBlank(message = "用户id不能为空", groups = UpdateGroup.class)
    private String id;
    @ApiModelProperty("用户名")
    @NotBlank(message = "用户名不能为空", groups = {UpdateGroup.class, CreateGroup.class})
    private String name;
    @ApiModelProperty(value = "用户别名")
    private String alias;
    @ApiModelProperty(value = "密码(修改可不传)")
    @NotBlank(message = "密码不能为空", groups = CreateGroup.class)
    private String password;
    @ApiModelProperty("邮箱")
    @NotBlank(message = "邮箱不能为空", groups = {UpdateGroup.class, CreateGroup.class})
    private String email;
    @ApiModelProperty("手机号码")
    @NotBlank(message = "手机号不能为空", groups = {UpdateGroup.class, CreateGroup.class})
    private String phone;
    @ApiModelProperty("描述")
    private String extra;
    @NotBlank(message = "组织id不能为空", groups = SubCreateGroup.class)
    @ApiModelProperty("组织Id(注册用户时可以不传)")
    @JsonProperty("dept_id")
    private String deptId;
    @JsonProperty("role_ids")
    @ApiModelProperty("角色id")
    private List<String> roleIds;
    @JsonProperty("role_project_ids")
    @ApiModelProperty("项目角色id project_id:role_id")
    private List<Map<String, String>> projectRoleIds;
    @ApiModelProperty("是否为组织管理员)")
    @JsonProperty("is_department_manager")
    private boolean departmentManager;
    @ApiModelProperty("密码公钥匙")
    private String publickey;
    @JsonProperty("department_name")
    @ApiModelProperty("组织名称")
    private String departmentName;
    @JsonProperty("user_type")
    @ApiModelProperty("用户类型")
    private String userType;
}
