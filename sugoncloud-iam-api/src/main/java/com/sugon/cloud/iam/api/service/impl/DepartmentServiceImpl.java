package com.sugon.cloud.iam.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.common.mapper.ModelMapper;
import com.sugon.cloud.common.model.IdNameInfo;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.common.utils.UUIDUtil;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.common.RecursionDeptIdUtils;
import com.sugon.cloud.iam.api.entity.Department;
import com.sugon.cloud.iam.api.entity.IamRole;
import com.sugon.cloud.iam.api.entity.keystone.Project;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.exception.BusinessException;
import com.sugon.cloud.iam.api.mapper.*;
import com.sugon.cloud.iam.api.service.DepartmentService;
import com.sugon.cloud.iam.api.service.QuotaService;
import com.sugon.cloud.iam.api.utils.DepartmentPathUtils;
import com.sugon.cloud.iam.api.vo.CreateOrUpdateDepartmentVO;
import com.sugon.cloud.iam.api.vo.DepartmentDetailVO;
import com.sugon.cloud.iam.common.model.vo.DepartmentTreeDetailVO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 组织表(Department)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-04-07 10:51:58
 */
@Service("departmentService")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RefreshScope
public class DepartmentServiceImpl implements DepartmentService {
    private final DepartmentMapper departmentMapper;
    private final ProjectMapper projectMapper;
    private final IamRoleMapper iamRoleMapper;
    private final UserMapper userMapper;
    private final ModelMapper mapper;
    private final QuotaService quotaService;
    @Value("${department.max.level:5}")
    private Integer maxLevel;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public DepartmentDetailVO queryById(String id) {
        return mapper.map(this.departmentMapper.selectById(id), DepartmentDetailVO.class);
    }

    /**
     * 查询多条数据
     *
     * @param offset 查询起始位置
     * @param limit  查询条数
     * @return 对象列表
     */
    @Override
    public List<Department> queryAllByLimit(int offset, int limit) {
        return null;
    }

    /**
     * 新增数据
     *
     * @param createOrUpdateDepartmentVO 实例对象
     * @return 实例对象
     */
    @Override
    public Department insert(CreateOrUpdateDepartmentVO createOrUpdateDepartmentVO) {
        Department parentDepartment = checkSaveOrUpdateBusinessParam(createOrUpdateDepartmentVO);
        createOrUpdateDepartmentVO.setId(UUIDUtil.get32UUID());
        Department department = mapper.map(createOrUpdateDepartmentVO, Department.class);
        department.setDomainId(parentDepartment.getDomainId());
        department.setLevel(parentDepartment.getLevel() + 1);
        this.departmentMapper.insert(department);
        return department;
    }

    /**
     * 新增、修改业务参数的校验
     *
     * @param createOrUpdateDepartmentVO
     * @return masterId
     */
    private Department checkSaveOrUpdateBusinessParam(CreateOrUpdateDepartmentVO createOrUpdateDepartmentVO) {
        Department parentDepartment = checkParentDepartment(createOrUpdateDepartmentVO.getParentId());
        String name = createOrUpdateDepartmentVO.getName();
        if (isNameDuplication(parentDepartment.getDomainId(), name, createOrUpdateDepartmentVO.getId())) {
            throw new BusinessException("[" + name + "]组织已经存在");
        }
        return parentDepartment;
    }

    /**
     * 校验父级组织
     *
     * @param parentId
     * @return
     */
    private Department checkParentDepartment(String parentId) {
        Department parentDepartment = departmentMapper.selectById(parentId);
        if (Objects.isNull(parentDepartment)) {
            throw new BusinessException("parentId无效,没有查到对应的父级组织");
        }
        if (parentDepartment.getLevel() + 1 > maxLevel) {
            throw new BusinessException("组织的层级最多为" + maxLevel + "级");
        }
        return parentDepartment;
    }

    /**
     * 校验名称
     *
     * @param domainId
     * @param name
     * @param id
     * @return
     */
    private boolean isNameDuplication(String domainId, String name, String id) {
        return departmentMapper.selectCount(new QueryWrapper<Department>().lambda()
                .eq(Department::getDomainId, domainId)
                .eq(Department::getName, name)
                .ne(StringUtils.isNotBlank(id), Department::getId, id)) > 0;
    }

    /**
     * 修改数据
     *
     * @param createOrUpdateDepartmentVO 实例对象
     * @return 实例对象
     */
    @Override
    public Department update(CreateOrUpdateDepartmentVO createOrUpdateDepartmentVO) {
        String id = createOrUpdateDepartmentVO.getId();
        Department originDept = departmentMapper.selectById(id);
        if (Objects.isNull(originDept)) {
            throw new BusinessException("修改失败，该id对应的组织不存在");
        }
        // 重名校验
        if (CommonInstance.MASTER_DEPARTMENT_LEVEL.equals(originDept.getLevel())) {
            // 顶级组织只需要验证顶级组织间的组织名称
            if (topParentDepartmentNameDuplication(createOrUpdateDepartmentVO.getName(), id)) {
                throw new BusinessException("组织名称已存在");
            }
        } else {
            // 子级组织只需要验证同一顶级组织下的组织名称
            if (isNameDuplication(originDept.getDomainId(), createOrUpdateDepartmentVO.getName(), id)) {
                throw new BusinessException("组织名称已存在");
            }
        }
        Department department = mapper.map(createOrUpdateDepartmentVO, Department.class);
        this.departmentMapper.updateById(department);
        return department;
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public ResultModel deleteById(String id) {
        DepartmentDetailVO departmentDetailVO = this.queryById(id);
        if (Objects.isNull(departmentDetailVO)) {
            throw new BusinessException("删除失败，该组织对应的组织不存在");
        }
        if ("admin".equals(departmentDetailVO.getId())) {
            throw new BusinessException("默认组织禁止删除");
        }
        //校验是否有子组织存在
        Integer total = departmentMapper.selectCount(new QueryWrapper<Department>().lambda().eq(Department::getParentId, departmentDetailVO.getId()));
        if (Objects.nonNull(total) && total > 0) {
            throw new BusinessException("删除失败，该组织对应有子组织存在");
        }
        //校验是否有人员存在
        total = userMapper.selectCount(new QueryWrapper<User>().lambda().eq(User::getDeptId, id));
        if (Objects.nonNull(total) && total > 0) {
            throw new BusinessException("删除失败，该组织对应有用户存在");
        }
        //校验是否有项目存在
        total = projectMapper.selectCount(new QueryWrapper<Project>().lambda().eq(Project::getDeptId, id).eq(Project::getEnabled,true));
        if (Objects.nonNull(total) && total > 0) {
            throw new BusinessException("删除失败，该组织对应有项目存在");
        }
        //校验是否有角色存在
        total = iamRoleMapper.selectCount(new QueryWrapper<IamRole>().lambda().eq(IamRole::getDeptId, id));
        if (Objects.nonNull(total) && total > 0) {
            throw new BusinessException("删除失败，该组织对应有角色存在");
        }
        this.departmentMapper.deleteById(id);
        //删除组织的配额并更新父级组织使用量
        quotaService.deleteDepartmentQuota(departmentDetailVO);
        return ResultModel.success("删除组织成功", departmentDetailVO.getName(),null);
    }

    @Override
    public DepartmentTreeDetailVO findTreeDepartmentByUserId(String userId) {
        User user;
        if (StringUtils.isBlank(userId) || Objects.isNull(user = userMapper.selectById(userId))) {
            return null;
        }
        String currentDeptId = user.getDeptId();
        Department department = departmentMapper.selectById(currentDeptId);
        DepartmentTreeDetailVO result = mapper.map(department, DepartmentTreeDetailVO.class);
        // 非根账号或者非组织管理员就不查询子级组织
        if (Objects.isNull(department) || (
                department.getLevel() > CommonInstance.MASTER_DEPARTMENT_LEVEL
                        && !TypeUtil.TYPE_DEPT_MASTER.equals(user.getType())
        )) {
            return result;
        }
        //查询子级组织
        result.setChildren(departmentMapper.findTreeDepartment(currentDeptId));
        return result;
    }

    @Override
    public com.sugon.cloud.iam.common.model.vo.DepartmentTreeDetailVO findTreeDepartmentAll() {
        Department department = new Department();
        department.setName("根部门");
        com.sugon.cloud.iam.common.model.vo.DepartmentTreeDetailVO result = mapper.map(department, com.sugon.cloud.iam.common.model.vo.DepartmentTreeDetailVO.class);
        //查询子级组织
        result.setChildren(departmentMapper.findTreeAllDepartment());
        return result;
    }

    @Override
    public List<DepartmentTreeDetailVO> findDepartmentAll(String userId, String currentUserId, boolean queryDefaultDept) {
        String userType = "master";
        if(!StringUtils.isEmpty(userId)){
            User user = userMapper.selectById(userId);
            if (Objects.isNull(user)) {
                throw new BusinessException("用户不存在");
            }
            // 保密系统管理员和安全管理员，将userId设置为空，不然没法查询所有组织
            // 全栈的环境前端admin账号没有传userId
            if (TypeUtil.TYPE_SYS_ADMIN.equals(user.getType()) ||
                    TypeUtil.TYPE_SEC_ADMIN.equals(user.getType()) ||
                    TypeUtil.TYPE_SEC_AUDITOR.equals(user.getType())) {
                userId = "";
            } else {
                userType = user.getType();
            }
        }
        boolean getAdminDept = false;

        if (currentUserId != null && !"1".equals(currentUserId)) {
            User user = userMapper.selectById(currentUserId);
            //只有admin自己登录查询自己的组织信息时才查询admin组织
            //userId不为空表示查询指定用户的组织信息(admin 调用这个接口查询 指定用户的组织)
            if (user != null
                    && (TypeUtil.TYPE_ADMIN.equals(user.getType()) || TypeUtil.TYPE_SYS_ADMIN.equals(user.getType()))
                    && queryDefaultDept) {
                if (StrUtil.isBlank(userId) || userId.equals(currentUserId)) {
                    getAdminDept = true;
                }
            }
        }
        return departmentMapper.findAllDepartment(userId, userType, getAdminDept);
    }

    @Override
    public Integer findUserDepartmentTotalByUserId(String userId) {
        String userType = "master";
        if(!StringUtils.isEmpty(userId)){
            User user = userMapper.selectById(userId);
            if (Objects.isNull(user)) {
                throw new BusinessException("用户不存在");
            }
            // 保密系统管理员和安全管理员，将userId设置为空，不然没法查询所有组织
            if (TypeUtil.TYPE_SYS_ADMIN.equals(user.getType()) || TypeUtil.TYPE_SEC_ADMIN.equals(user.getType())) {
                userId = "";
            } else {
                userType = user.getType();
            }
        }
        List<DepartmentTreeDetailVO> results = departmentMapper.findAllDepartment(userId, userType, false);
        List<String> deptIds = Lists.newArrayList();
        RecursionDeptIdUtils.getDeptIds(results, deptIds);
        return deptIds.size();
    }

    @Override
    public boolean topParentDepartmentNameDuplication(String name, String id) {
        return departmentMapper.selectCount(new QueryWrapper<Department>().lambda()
                .eq(Department::getLevel, CommonInstance.MASTER_DEPARTMENT_LEVEL)
                .eq(Department::getName, name)
                .ne(StringUtils.isNotBlank(id), Department::getId, id)) > 0;
    }

    @Override
    public List<IdNameInfo> deptInfos(List<String> ids) {
        List<DepartmentTreeDetailVO> departments = departmentMapper.findAllDepartmentList();
        return ids.stream().map(id -> {
            IdNameInfo idNameInfo = new IdNameInfo();
            idNameInfo.setId(id);
            idNameInfo.setName(DepartmentPathUtils.buildDeptPath(id, departments));
            return idNameInfo;
        }).collect(Collectors.toList());
    }

    @Override
    public Department getRootDepartmentByDeptId(String deptId) {
        // 查询当前组织
        Department department = departmentMapper.selectById(deptId);
        if (department == null) {
            throw new BusinessException("组织不存在");
        }
        // 循环查找父组织，直到找到根组织
        while (department.getParentId() != null) {
            Department parentDept = departmentMapper.selectOne(
                    new LambdaQueryWrapper<Department>()
                            .eq(Department::getId, department.getParentId())
            );
            if (parentDept == null) {
                throw new BusinessException("父组织不存在");
            }
            department = parentDept;
        }
        return department;
    }

    /**
     * 根据组织id，获取当前组织的子组织及父组织(不包括根组织)
     * @param deptId
     * @return
     */
    @Override
    public  List<DepartmentTreeDetailVO> getDeptsByDeptId(String deptId) {
        Department rootDepartmentByDeptId = this.getRootDepartmentByDeptId(deptId);
       /* List<String> deptIds = RecursionDeptIdUtils.getDeptIds(allDepartmentByRootDeptId, new ArrayList<>());
        //排除当前组织以及根组织
        List<String> ids = deptIds.stream().filter(id -> !id.equals(deptId) && !id.equals(rootDepartmentByDeptId.getId())).collect(Collectors.toList());
        return departmentMapper.selectList(new LambdaQueryWrapper<Department>()
                .in(Department::getId, ids));*/
        return departmentMapper.findAllDepartmentByRootDeptId(rootDepartmentByDeptId.getId());
    }

    @Override
    public DepartmentDetailVO queryTopDeptByUserId(String userId) {
        User user = userMapper.selectById(userId);
        if (Objects.isNull(user)) {
            throw new BusinessException("用户不存在");
        }
        if (StrUtil.isBlank(user.getDeptId())) {
            return null;
        }
        return getTopDepartmentDetailVO(user.getDeptId());
    }

    @Override
    public DepartmentDetailVO queryTopDeptByProjectId(String projectId) {
        Project project = projectMapper.selectById(projectId);
        if (Objects.isNull(project)) {
            throw new BusinessException("项目不存在");
        }
        if (StrUtil.isBlank(project.getDeptId())) {
            return null;
        }

        return getTopDepartmentDetailVO(project.getDeptId());
    }

    @Override
    public List<String> getDeptsByUserId(String userId) {
        User user = userMapper.selectById(userId);
        // 保密->组织系统管理员/组织安全管理员/阻住审计管理员
        if (TypeUtil.TYPE_ORG_SYSADMIN.equals(user.getType())
                || TypeUtil.TYPE_ORG_SECADMIN.equals(user.getType())
                || TypeUtil.TYPE_ORG_SECAUDITOR.equals(user.getType())) {
            // 查询根部门
            Department rootDep = this.getRootDepartmentByDeptId(user.getDeptId());
            // 查询根部门下的子部门
            List<DepartmentTreeDetailVO> subDeps = this.getDeptsByDeptId(rootDep.getId());
            List<String> deptIds = new ArrayList<>();
            //添加根部门id
            deptIds.add(rootDep.getId());
            RecursionDeptIdUtils.getDeptIds(subDeps, deptIds);
           return deptIds;
        }
        return new ArrayList<>();
    }

    /**
     * 根据部门id获取顶级部门信息
     *
     * @param deptId
     * @return
     */
    private DepartmentDetailVO getTopDepartmentDetailVO(String deptId) {
        Department department = departmentMapper.selectById(deptId);
        if (Objects.isNull(department)) {
            throw new BusinessException("用户组织不存在");
        }
        // 获取顶级部门
        String parentId = department.getParentId();
        if (StrUtil.isBlank(department.getParentId())) {
            return mapper.map(department, DepartmentDetailVO.class);
        }
        while (StrUtil.isNotBlank(parentId)) {
            department = departmentMapper.selectById(parentId);
            parentId = department.getParentId();
        }
        return mapper.map(department, DepartmentDetailVO.class);
    }
}
