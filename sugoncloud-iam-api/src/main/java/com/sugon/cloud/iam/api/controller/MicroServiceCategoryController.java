package com.sugon.cloud.iam.api.controller;


import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.entity.MicroServiceCategory;
import com.sugon.cloud.iam.api.service.MicroServiceCategoryService;
import com.sugon.cloud.iam.api.service.resourceauth.AdminUserTypeAuthHandler;
import com.sugon.cloud.iam.api.vo.CreateOrUpdateMicroServiceCategoryVO;
import com.sugon.cloud.iam.api.vo.MicroServiceCategoryDetailVO;
import com.sugon.cloud.log.client.context.LogRecordContext;
import com.sugon.cloud.log.client.starter.annotation.LogRecordAnnotation;
import com.sugon.cloud.resource.auth.common.aop.ResourceAuth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

/**
 * (MicroServiceCategory)表控制层
 *
 * <AUTHOR>
 * @since 2021-04-23 09:25:37
 */
@RestController
@RequestMapping("/api/micro-service-categories")
@Api(tags = "微服务类别API")
@Slf4j
public class MicroServiceCategoryController {
    /**
     * 服务对象
     */
    @Resource
    private MicroServiceCategoryService microServiceCategoryService;

    /**
     * 查询列表数据
     *
     * @return 数据列表
     */
    @GetMapping
    @ApiOperation("查询微服务类别信息")
    @ApiImplicitParams({@ApiImplicitParam(name = "name", value = "微服务类别名称",dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "page_num", value = "当前页",defaultValue = "1",dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "page_size", value = "每页数量", defaultValue = "15", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "check_child", value = "是否检查子类", defaultValue = "true", dataType = "boolean", paramType = "query"),
    })
    public ResultModel<PageCL<MicroServiceCategoryDetailVO>> list(@RequestParam(value = "name", required = false) String name,
                                                                  @RequestParam(value = "page_num", defaultValue = "1", required = false) int pageNum,
                                                                  @RequestParam(value = "page_size", defaultValue = "15" ,required = false) int pageSize,
                                                                  @RequestParam(value = "check_child", defaultValue = "true" ,required = false) boolean checkChild,
                                                                  HttpServletRequest request) {

        String internetAccess = request.getHeader("InternetAccess");
        log.info("internetAccess {} ", internetAccess);
        boolean isInternet = false;
        if (Objects.nonNull(internetAccess)) isInternet = Boolean.parseBoolean(internetAccess);
        PageCL<MicroServiceCategoryDetailVO> pageResult = this.microServiceCategoryService.pageList(name,pageNum,pageSize, isInternet, checkChild);
        return ResultModel.success(pageResult);
    }

    @GetMapping("by-quota")
    @ApiOperation("查询所有配额的分类")
    @DocumentIgnore
    public ResultModel<List<MicroServiceCategoryDetailVO>> ListAllCategoryByQuota() {
        List<MicroServiceCategoryDetailVO> microServiceCategoryDetailVOList = this.microServiceCategoryService.ListAllCategoryByQuota();
        return ResultModel.success(microServiceCategoryDetailVOList);
    }

    /**
     * 查询列表数据
     *
     * @return 数据列表
     */
    @GetMapping("/management")
    @ApiOperation("查询所有服务类型信息")
    @ApiImplicitParams({@ApiImplicitParam(name = "name", value = "微服务类别名称",dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "page_num", value = "当前页",defaultValue = "1",dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "page_size", value = "每页数量", defaultValue = "15", dataType = "int", paramType = "query")})
    public ResultModel<PageCL<MicroServiceCategoryDetailVO>> listAll(@RequestParam(value = "name", required = false) String name,
                                                                  @RequestParam(value = "page_num", defaultValue = "1", required = false) int pageNum,
                                                                  @RequestParam(value = "page_size", defaultValue = "15" ,required = false) int pageSize,
                                                                     HttpServletRequest request) {
        String internetAccess = request.getHeader("InternetAccess");
        log.info("internetAccess {} ", internetAccess);
        boolean isInternet = false;
        if (Objects.nonNull(internetAccess)) isInternet = Boolean.parseBoolean(internetAccess);
        PageCL<MicroServiceCategoryDetailVO> pageResult = this.microServiceCategoryService.pageListAll(name,pageNum,pageSize, isInternet);
        return ResultModel.success(pageResult);
    }

    @PostMapping
    @ApiOperation("创建微服务类别信息")
    @DocumentIgnore
    @ResourceAuth(handler = AdminUserTypeAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    @LogRecordAnnotation(value = "创建微服务类别", detail = "创建微服务类别{{#createOrUpdateMicroServiceCategoryVO.name}}", resourceId = "{{#id}}", resource = "{{#createOrUpdateMicroServiceCategoryVO.name}}", auditFlag = "true")
    public ResultModel save(@Validated @RequestBody CreateOrUpdateMicroServiceCategoryVO createOrUpdateMicroServiceCategoryVO) {
        MicroServiceCategory insert = this.microServiceCategoryService.insert(createOrUpdateMicroServiceCategoryVO);
        LogRecordContext.putVariable("id", insert.getId());
        return ResultModel.success("创建微服务类别信息成功", "");
    }

    @PutMapping
    @ApiOperation("更新微服务类别信息")
    @LogRecordAnnotation(value = "更新微服务类别信息", detail = "更新微服务类别信息{{#_ret.resource}}", resourceId = "{{#microServiceCategoryDetailVO.id}}", resource = "{{#microServiceCategoryDetailVO.name}}", auditFlag = "true")
    @DocumentIgnore
    @ResourceAuth(handler = AdminUserTypeAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    public ResultModel update(@Validated @RequestBody MicroServiceCategoryDetailVO microServiceCategoryDetailVO) {
        this.microServiceCategoryService.update(microServiceCategoryDetailVO);
        return ResultModel.success("更新微服务类别信息成功", "");
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除微服务类别信息")
    @LogRecordAnnotation(value = "删除微服务类别信息", detail = "删除微服务类别信息{{#name}}", resourceId = "{{#id}}", resource = "{{#name}}", auditFlag = "true")
    @DocumentIgnore
    @ResourceAuth(handler = AdminUserTypeAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    public ResultModel delete(@PathVariable String id) {
        MicroServiceCategory microServiceCategory = microServiceCategoryService.queryById(id);
        if (microServiceCategory == null) {
            return ResultModel.error("微服务类别不存在");
        }
        if (CommonInstance.MICRO_INNER.equals(microServiceCategory.getType()) || CommonInstance.MICRO_HIDE.equals(microServiceCategory.getType())) {
            return ResultModel.error("不能删除内置类别");
        }
        LogRecordContext.putVariable("name", microServiceCategory.getName());
        microServiceCategoryService.deleteById(id);
        return ResultModel.success("删除微服务类别信息成功", "");
    }

}
