package com.sugon.cloud.iam.api.service.ukeyImpl;

import com.alibaba.fastjson.JSONObject;
import com.sugon.cloud.iam.api.config.MFAMappingJackson2HttpMessageConverter;
import com.sugon.cloud.iam.api.exception.BusinessException;
import com.sugon.cloud.iam.api.service.UkeyService;
import com.sugon.cloud.iam.common.model.vo.MfaVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.ssl.TrustStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.PostConstruct;
import javax.net.ssl.SSLContext;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URLDecoder;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 奥联uk
 */
@Service
@Slf4j
@ConditionalOnExpression("'${mfa.type}'.contains('aolian')")
@RefreshScope
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AolianUkeyServiceImpl implements UkeyService {

    private RestTemplate mfaRestTemplate;
    private RestTemplate httpsRestTemplate;
    @Value("${user.authorize.mfa.server:127.0.0.1:80}")
    private String server;
    @Override
    public boolean verifyData(MfaVo mfaVo) {
        Map<String, Object> map = new HashMap();
        map.put("version", 0);
        map.put("reqType", "verifySignedData");
        map.put("reqTime", getRreqType());
        Map<String, Object> innerMap = new HashMap();
        innerMap.put("signMethod", "131585");
        innerMap.put("type", 1);
        innerMap.put("cert",mfaVo.getCert());
        //signerIDLen signerID 默认
        String signerID = Base64.getEncoder().encodeToString("1234567812345678".getBytes());
        innerMap.put("signerIDLen", "1234567812345678".length());
        innerMap.put("signerID", signerID);
        String inData = Base64.getEncoder().encodeToString(mfaVo.getInData().getBytes());
        innerMap.put("inDataLen", mfaVo.getInData().length());
        innerMap.put("inData", inData);
        innerMap.put("signature", mfaVo.getSignNature());
        innerMap.put("verifyLevel", 0);
        map.put("request", innerMap);
        log.info("call aolian  req {}", JSONObject.toJSONString(map));
        JSONObject result = null;
        String url = server+"/cloud_sign_svs/cert/VerifySignedData";
        log.info("url {}", url);

        try {
            if (url.startsWith("https")) {
                result = httpsRestTemplate.postForObject(url, map, JSONObject.class);
            } else {
                result = mfaRestTemplate.postForObject(url, map, JSONObject.class);
            }
            log.info("call aolian  result {}", result);
            return  result.getJSONObject("respond").getInteger("respValue") == 0;
        } catch (Exception e) {
            log.error("call aolian server error", e);
            throw new BusinessException("aolian server");
        }
    }
    
    private  String getRreqType() {
        // 获取当前时间
        Date currentTime = new Date();
        // 创建 SimpleDateFormat 对象并设置日期格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss'Z'Z");
        // 设置时区为东八区（北京时间）
        sdf.setTimeZone(TimeZone.getTimeZone("GMT+8"));

        // 格式化时间
        return sdf.format(currentTime);

    }

    @PostConstruct
    public void initMFARestTemplate() {
        HttpComponentsClientHttpRequestFactory HTTP_REQUEST_FACTORY = new HttpComponentsClientHttpRequestFactory();
        HTTP_REQUEST_FACTORY.setConnectTimeout(5000);
        // 自定义超时时间
        HTTP_REQUEST_FACTORY.setReadTimeout(30000);
        mfaRestTemplate = new RestTemplate(HTTP_REQUEST_FACTORY);
        mfaRestTemplate.getMessageConverters().add(new MFAMappingJackson2HttpMessageConverter());
    }

    @PostConstruct
    public void initHttpsRestTemplate() throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
        TrustStrategy acceptingTrustStrategy = (x509Certificates, authType) -> true;
        SSLContext sslContext = SSLContexts.custom()
                .loadTrustMaterial(null, acceptingTrustStrategy)
                .build();
        SSLConnectionSocketFactory sslFactory = new SSLConnectionSocketFactory(
                sslContext, new NoopHostnameVerifier());
        CloseableHttpClient httpClient = HttpClients.custom()
                .setSSLSocketFactory(sslFactory)
                .build();
        HttpComponentsClientHttpRequestFactory factory =
                new HttpComponentsClientHttpRequestFactory();
        factory.setConnectTimeout(3600000);
        factory.setReadTimeout(3600000);
        factory.setHttpClient(httpClient);
        httpsRestTemplate = new RestTemplate(factory);
    }

    private URI uriWrapper(String url, Map<String, Object> params) {
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(url);
        if (Objects.nonNull(params) && !params.isEmpty()) {
            params.forEach((k, v) -> {
                try {
                    if (Objects.nonNull(v)) {
                        urlBuilder.queryParam(k, URLDecoder.decode(v.toString(), "UTF-8"));
                    } else {
                        urlBuilder.queryParam(k, "");
                    }
                } catch (UnsupportedEncodingException e) {
                    log.error("url decode error", e);
                }
            });
        }
        return urlBuilder.build().encode().toUri();
    }

}
