package com.sugon.cloud.iam.api.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.sugon.cloud.common.constants.HeaderParamConstant;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.entity.GlobalsettingsEntity;
import com.sugon.cloud.iam.api.entity.UserAccessEntity;
import com.sugon.cloud.iam.api.entity.keystone.LoginUser;
import com.sugon.cloud.iam.api.exception.BusinessException;
import com.sugon.cloud.iam.api.jwt.JwtTokenUtils;
import com.sugon.cloud.iam.api.service.*;
import com.sugon.cloud.iam.api.utils.DateUtils;
import com.sugon.cloud.iam.common.model.vo.ValidateTokenUserResponseVO;
import io.jsonwebtoken.Claims;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Log4j2
@RefreshScope
public class JwtServiceImpl implements JwtService {

    private final AuthenticationManager authenticationManager;

    private final JwtTokenUtils jwtTokenUtils;

    private final RedisTemplate redisTemplate;

    private final GlobalsettingsService globalsettingsService;

    private final UserAccessService userAccessService;

    @Value("${inner:inner}")
    private String inner;
//    @Value("${token.expireTime}")
//    private Integer expiration;

    @Override
    public String login(String username, String password, boolean isNative) {
        //用户验证
        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(username, password));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        String token = jwtTokenUtils.generateToken(loginUser, isNative);
        if (!isNative) {
            initOnePlaceLoginInfo(loginUser, token);
        }
        return token;
    }

    /**
     * 设置用户是否只能在一个地方登录
     */
    @Override
    public void initOnePlaceLoginInfo(LoginUser loginUser, String token) {
        String username = loginUser.getUsername();
        long expiration = this.getExpiredTime();
        try {
            Object onePlaceLogin = redisTemplate.opsForValue().get(CommonInstance.ONE_PLACE_LOGIN);
            boolean flag = false;
            if (Objects.nonNull(onePlaceLogin)) {
                if (CommonInstance.GLOBAL_SETTING_TRUE.equals(String.valueOf(onePlaceLogin))) flag = true;
            } else {
                GlobalsettingsEntity globalsettingsEntity = globalsettingsService.getGlobalsettingsEntity(CommonInstance.POLICY_TYPE, CommonInstance.ONE_PLACE_LOGIN);
                if (Objects.nonNull(globalsettingsEntity)) {
                    String value = globalsettingsEntity.getPolicyDocument();
                    redisTemplate.opsForValue().set(CommonInstance.ONE_PLACE_LOGIN, value);
                    if (CommonInstance.GLOBAL_SETTING_TRUE.equals(value)) flag = true;
                }
            }
            //设置只允许在一个地方登录，（内置用户不做这个判断）
            if (flag && !inner.contains(username)) {
                Set keys = redisTemplate.keys(username + ":" + "*");
                if (Objects.nonNull(keys)) {
                    keys.stream().forEach(key -> {
                        String existToken = String.valueOf(key).split(":")[1];
                        redisTemplate.opsForValue().set(CommonInstance.EXPIRE_TOKEN + existToken, "N/A", expiration, TimeUnit.SECONDS);
                    });
                }
            }
        } catch (Exception e) {
            log.error("get one place login error", e);
        }
        //filter 中校验 直接比对
        redisTemplate.opsForValue().set(username + ":" + token, "N/A", expiration, TimeUnit.SECONDS);
    }

    public static boolean isLegalDate(int length, String sDate,String format) {
        int legalLen = length;
        if ((sDate == null) || (sDate.length() != legalLen)) {
            return false;
        }
        DateFormat formatter = new SimpleDateFormat(format);
        try {
            Date date = formatter.parse(sDate);
            return sDate.equals(formatter.format(date));
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public ValidateTokenUserResponseVO validateToken(String token, HttpServletRequest httpRequest) {
        if (StringUtils.isBlank(token)) {
            return null;
        }
        token = token.replace("Bearer ", "");
        Claims claims = jwtTokenUtils.getClaimsFromToken(token);
        if (Objects.isNull(claims)) {
            return null;
        }
        String username = claims.get(HeaderParamConstant.USER_NAME, String.class);
        log.debug("start validateToken redis...");
        if (!validateTokenRedis(username, token, System.currentTimeMillis() + 60 * 1000)) {
            return null;
        }
        try {
            checkTime(claims.get(HeaderParamConstant.USER_ID, String.class));
        }catch (BusinessException e) {
            log.error("checkTime error", e);
            return null;
        }

        ValidateTokenUserResponseVO.ValidateTokenUserResponseVOBuilder builder = ValidateTokenUserResponseVO.builder();
        boolean loginElsewhere = this.checkOnePlaceLogin(token);
        builder.loginElsewhere(loginElsewhere);
        return builder.userId(claims.get(HeaderParamConstant.USER_ID, String.class))
                .username(claims.get(HeaderParamConstant.USER_NAME, String.class))
                .userType(claims.get(HeaderParamConstant.USER_TYPE, String.class))
                .build();
    }


    private boolean validateTokenRedis(String username, String token, long timeout) {
        try {
            if (System.currentTimeMillis() > timeout) {
                return false;
            }
            if (redisTemplate.hasKey(username + ":" + token)) {
                long expiration = this.getExpiredTime();
                redisTemplate.opsForValue().set(username + ":" + token, "N/A", expiration, TimeUnit.SECONDS);
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error("start validateToken redis error", e);
            return validateTokenRedis(username, token, timeout);
        }
    }

    private boolean checkOnePlaceLogin(String token) {
        Object onePlaceLogin = redisTemplate.opsForValue().get(CommonInstance.ONE_PLACE_LOGIN);
        boolean exist = false;
        if (CommonInstance.GLOBAL_SETTING_TRUE.equals(String.valueOf(onePlaceLogin))) {
            exist = redisTemplate.hasKey(CommonInstance.EXPIRE_TOKEN + token);
        }
        return exist;
    }

    @Override
    public void setBlack(String token) {
        token = token.replace("Bearer ", "");
        Claims claims = jwtTokenUtils.getClaimsFromToken(token);
        if (Objects.isNull(claims)) return;
        String username = claims.get(HeaderParamConstant.USER_NAME, String.class);
        redisTemplate.delete(username + ":" + token);
    }

    private boolean getLock(String userName) {
        String lockKey = "token-refresh:" + userName;
        Long exp = redisTemplate.getExpire(lockKey);
        if (exp == -1) {
            redisTemplate.delete(lockKey);
        }
        return redisTemplate.opsForValue().setIfAbsent(lockKey, userName);
    }

    @Override
    public long getExpiredTime() {
        Object tokenExpiredTime = redisTemplate.opsForValue().get(CommonInstance.TOKEN_EXPIRED_KEY);
        if (Objects.nonNull(tokenExpiredTime)) {
            return Integer.parseInt(String.valueOf(tokenExpiredTime));
        } else {
            int expiredTime = 1800;
            GlobalsettingsEntity globalsettingsEntity = globalsettingsService.getGlobalsettingsEntity(CommonInstance.TOKEN, CommonInstance.TOKEN_EXPIRED_KEY);
            if (Objects.nonNull(globalsettingsEntity)) {
                expiredTime = Integer.parseInt(globalsettingsEntity.getPolicyDocument());
            }
            redisTemplate.opsForValue().set(CommonInstance.TOKEN_EXPIRED_KEY, expiredTime);
            return expiredTime;
        }
    }

    @Override
    public Long currentUserExpiredDate(String token) {
        token = token.replace("Bearer ", "");
        try {
            Claims claims = jwtTokenUtils.getClaimsFromToken(token);
            if (Objects.isNull(claims)) return -2L;
            String username = claims.get(HeaderParamConstant.USER_NAME, String.class);
            return redisTemplate.opsForValue().getOperations().getExpire(username + ":" + token);
        } catch (Exception e) {
            log.info("token expired", e);
            return -2L;
        }
    }

    @Override
    public void checkTime(String userId) {
        UserAccessEntity userAccess = userAccessService.getAccessByUserId(userId);
        // 如果userAccess为空，则证明未设置登录时间限制，直接返回
        if (Objects.isNull(userAccess)) {
            return;
        }
        LocalDate nowDate = LocalDate.now();
        // 允许登录开始时间之前
        if (StringUtils.isNotBlank(userAccess.getStartDate()) && nowDate.compareTo(DateUtils.parseDate(userAccess.getStartDate())) < 0) {
            throw new BusinessException("当前用户不在允许登录时间范围");
        }
        // 允许登录结束时间之后
        if (StringUtils.isNotBlank(userAccess.getEndDate()) && nowDate.compareTo(DateUtils.parseDate(userAccess.getEndDate())) > 0) {
            throw new BusinessException("当前用户不在允许登录时间范围");
        }
        // 不进行具体时间限制
        if (Objects.isNull(userAccess.getLimitFlag()) || !userAccess.getLimitFlag()) {
            return;
        }
        // 所有时间点均无法登录
        if (StringUtils.isBlank(userAccess.getLimitTime())) {
            throw new BusinessException("当前用户不在允许登录时间范围");
        }
        JSONObject limitJson = JSONObject.parseObject(userAccess.getLimitTime());
        if (Objects.isNull(limitJson) || limitJson.size() == 0) {
            throw new BusinessException("当前用户不在允许登录时间范围");
        }

        // 获取今天为周几
        String week = DateUtils.getWeekByDate(nowDate);
        // 今天无法进行登录
        if (!limitJson.containsKey(week)) {
            throw new BusinessException("当前用户不在允许登录时间范围");
        }
        String limitTime = limitJson.getString(week);
        // 今天没有允许登录的时间点
        if (StringUtils.isBlank(limitTime)) {
            throw new BusinessException("当前用户不在允许登录时间范围");
        }
        List<String> limitTimeArray = Arrays.asList(limitTime.split(","));
        // 当前时间点无法登录
        if (!limitTimeArray.contains(String.valueOf(LocalDateTime.now().getHour()))) {
            throw new BusinessException("当前用户不在允许登录时间范围");
        }
        return;
    }
}
