package com.sugon.cloud.iam.api.service.resourceauth;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/24 14:24
 */
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class RoleAuthExtentHandler extends BaseResourceExtentAuthHandler {



    private final RoleAuthHandler roleAuthHandler;

    @Override
    public List<String> getUserResource(String userId) {

        return roleAuthHandler.getUserResource(userId);
    }

}
