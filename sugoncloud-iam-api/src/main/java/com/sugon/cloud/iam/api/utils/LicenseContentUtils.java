package com.sugon.cloud.iam.api.utils;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sugon.cloud.iam.api.entity.LicenseInfo;

/**
 * 授权工具类
 *
 * <AUTHOR>
 *
 */
public class LicenseContentUtils {
	/**
	 * 解密License信息，作为LicenseInfo对象输出
	 *
	 * @param licenseCyptoContent
	 * @return
	 * @throws Exception
	 */
	public static LicenseInfo decodeLicenseContent(String licenseCyptoContent) throws Exception {
		try {
			String licenseOriContent = LicenseCyptoUtils.decode(LicenseConstants.SECRET_KEY, licenseCyptoContent);
			LicenseInfo licenseInfo = (LicenseInfo) JSONObject.toJavaObject((JSON) JSONObject.parse(licenseOriContent),
					LicenseInfo.class);
			return licenseInfo;
		} catch (Exception e) {
			throw new Exception("无法解析授权文件内容：请上传正确LICENSE文件。", e);
		}
	}

	/**
	 * 加密License信息作为字符串输出
	 *
	 * @param licenseInfo
	 * @return
	 */
	public static String encodeLicenseContent(LicenseInfo licenseInfo) {
		String licenseOriContent = JSONObject.toJSONString(licenseInfo);

		String licenseCyptoContent = LicenseCyptoUtils.encode(LicenseConstants.SECRET_KEY, licenseOriContent);
		return licenseCyptoContent;
	}
}
