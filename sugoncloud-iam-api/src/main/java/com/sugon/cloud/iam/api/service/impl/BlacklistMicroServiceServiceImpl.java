package com.sugon.cloud.iam.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sugon.cloud.iam.api.entity.BlacklistMicroService;
import com.sugon.cloud.iam.api.mapper.BlacklistMicroServiceMapper;
import com.sugon.cloud.iam.api.service.BlacklistMicroServiceService;
import com.sugon.cloud.iam.common.enums.BlacklistTypeEnum;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【blacklist_micro_service】的数据库操作Service实现
* @createDate 2024-02-05 15:54:15
*/
@Service
public class BlacklistMicroServiceServiceImpl extends ServiceImpl<BlacklistMicroServiceMapper, BlacklistMicroService>
    implements BlacklistMicroServiceService{

    @Value("${operations.enabled:false}")
    private boolean operationEnable;

    @Override
    public List<BlacklistMicroService> listByType(BlacklistTypeEnum type) {
        LambdaQueryWrapper<BlacklistMicroService> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(type != null, BlacklistMicroService::getType, type);
        if (operationEnable) {
            queryWrapper.isNull(BlacklistMicroService::getExtra);
        }
        return list(queryWrapper);
    }

    @Override
    public List<BlacklistMicroService> listByTypes(List<BlacklistTypeEnum> types) {
        LambdaQueryWrapper<BlacklistMicroService> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollUtil.isNotEmpty(types), BlacklistMicroService::getType, types);
        if (operationEnable) {
            queryWrapper.isNull(BlacklistMicroService::getExtra);
        }
        return list(queryWrapper);
    }


}




