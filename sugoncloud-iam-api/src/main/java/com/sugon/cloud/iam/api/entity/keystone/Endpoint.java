package com.sugon.cloud.iam.api.entity.keystone;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("endpoint")
@ApiModel("endpoint")
public class Endpoint {
    private static final long serialVersionUID = 1234567L;
    @ApiModelProperty("区域id")
    private String regionId;
    @ApiModelProperty("服务id")
    private String serviceId;
    @ApiModelProperty("链接")
    private String url;
    @JsonProperty(value = "interface")
    @TableField("interface")
    @ApiModelProperty("接口")
    private String interfaceParam;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("是否启用")
    private Boolean enabled;
    @ApiModelProperty("扩展信息")
    private String extra;
    private String legacyEndpointId;
    @TableField(exist = false)
    @ApiModelProperty("区域")
    private String region;

}
