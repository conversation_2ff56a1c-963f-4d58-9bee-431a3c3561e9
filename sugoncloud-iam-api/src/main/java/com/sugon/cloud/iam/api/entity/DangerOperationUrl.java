package com.sugon.cloud.iam.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sugon.cloud.common.model.entity.BaseEntity;
import lombok.Data;

/**
 * @Author: yangdingshan
 * @Date: 2025/2/9 14:44
 * @Description:
 */
@Data
@TableName("danger_operation_url")
public class DangerOperationUrl extends BaseEntity {

    /**
     * 接口地址
     */
    private String url;

    /**
     * 接口请求方式
     */
    private String method;

    /**
     * 接口描述
     */
    private String description;

    /**
     * 是否使验证码失效
     */
    private Boolean expired;

    /**
     * 是否启用
     */
    private Boolean enabled;
}
