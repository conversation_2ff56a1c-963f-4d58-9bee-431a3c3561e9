package com.sugon.cloud.iam.api.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.entity.StrategyForm;
import com.sugon.cloud.iam.api.entity.StrategyPO;

public interface StrategyService extends IService<StrategyPO> {

    PageCL<StrategyForm> findByUserIdPage(String userId, int pageNumber, int pageSize);

    PageCL<StrategyForm> findPage(String userId, int pageNumber, int pageSize);
}
