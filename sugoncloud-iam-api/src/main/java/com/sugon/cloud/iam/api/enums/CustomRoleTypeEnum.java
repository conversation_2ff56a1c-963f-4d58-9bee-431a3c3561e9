package com.sugon.cloud.iam.api.enums;


public enum CustomRoleTypeEnum {

    PLATFORM("platform", "平台"),
    ROOT_DEPARTMENT("root_department", "根部门"),
    REGULAR("regular", "普通用户");
    private String type;
    private String desc;

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    CustomRoleTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
