package com.sugon.cloud.iam.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sugon.cloud.common.model.entity.BaseEntity;
import lombok.Data;

/**
 * @Author: yangdingshan
 * @Date: 2025/2/10 16:23
 * @Description:
 */
@Data
@TableName("email_templates")
public class EmailTemplates extends BaseEntity {

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板类型 2表示通知邮件，3表示验证码邮件
     */
    private Integer templateType;

    /**
     * 模板内容
     */
    private String content;

    /**
     * 邮件主题
     */
    private String subject;
}
