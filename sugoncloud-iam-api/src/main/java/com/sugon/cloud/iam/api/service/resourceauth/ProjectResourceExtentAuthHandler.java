package com.sugon.cloud.iam.api.service.resourceauth;

import com.sugon.cloud.resource.auth.common.configuration.AuthHandlerConfig;
import com.sugon.cloud.resource.auth.common.service.ResourceAuthException;
import com.sugon.cloud.resource.auth.common.service.impl.BaseResourceAuthHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/24 14:30
 */
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ProjectResourceExtentAuthHandler extends BaseResourceExtentAuthHandler {


    private final ProjectResourceAuthHandler projectResourceAuthHandler;
    @Override
    public List<String> getUserResource(String userId) {
        return projectResourceAuthHandler.getUserResource(userId);
    }



}
