package com.sugon.cloud.iam.api.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("用户绑定角色和项目FORM")
public class UserBindRolesVO {
    @ApiModelProperty("项目Id")
    @JsonProperty("project_id")
    private String projectId;
    @ApiModelProperty("绑定的角色ID集合")
    @JsonProperty("role_ids")
    private List<String> roleIds;
}