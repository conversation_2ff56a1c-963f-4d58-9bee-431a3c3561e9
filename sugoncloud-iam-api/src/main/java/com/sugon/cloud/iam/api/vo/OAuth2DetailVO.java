package com.sugon.cloud.iam.api.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/05/23 09:42
 **/
@Data
@ApiModel("OAuth2详情")
public class OAuth2DetailVO {
    @ApiModelProperty("是否启用")
    private boolean enabled;
    @ApiModelProperty("公钥")
    public String publickey;
    @JsonProperty("oauth2_extra")
    @ApiModelProperty("服务提供商")
    @Valid
    @NotNull(message = "不能为空")
    private OAuth2ExtraVO oauth2Extra;
}
