package com.sugon.cloud.iam.api.controller;

import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.iam.api.service.SyncDataBaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/sync-data")
@Api(tags = "数据库迁移",hidden = true)
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@DocumentIgnore
public class SyncDataBaseController {

    private final SyncDataBaseService syncDataBaseService;

    @GetMapping("/userName")
    @ApiOperation("同步用户名称")
    public void syncUserNameExtra() {
//        syncDataBaseService.syncUserName();
    }

    @GetMapping("/userExtra")
    @ApiOperation("同步用户扩展信息")
    public void syncUserExtra() {
//        syncDataBaseService.syncUserExtra();
    }

    /**
     * 同步gateway的数据
     */
    @GetMapping("/gateway-user")
    @ApiOperation("同步gateway用户")
    public String syncGatewayUser() {
        syncDataBaseService.syncGatewayUser();
        return "done";
    }

    /**
     * 同步gateway的数据
     */
    @GetMapping("/gateway-quota")
    @ApiOperation("同步gateway用户配额")
    public String syncGatewayQuota() {
        syncDataBaseService.syncGatewayQuota();
        return "done";
    }
}
