package com.sugon.cloud.iam.api.service.impl;

import com.sugon.cloud.iam.api.entity.keystone.Project;
import com.sugon.cloud.iam.api.service.ProjectService;
import com.sugon.cloud.iam.api.service.UpdateProjectByOperationService;
import com.sugon.cloud.iam.common.constants.ProjectType;
import com.sugon.cloud.iam.common.model.vo.ProjectUpdateByOperationVO;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/12/19 17:25
 **/
@Service(ProjectType.STATUS_FORMAL_APPLY_COMMIT)
public class FormalApplyUpdateProjectByOperationServiceImpl implements UpdateProjectByOperationService {

    @Resource
    @Lazy
    private ProjectService projectService;
    @Override
    public void apply(String projectId, ProjectUpdateByOperationVO projectUpdateByOperationVO) {
        projectService.lambdaUpdate()
                .set(Project::getTaskStatus, "")
                .set(Project::getType, ProjectType.FORMAL)
                .eq(Project::getId, projectId)
                .update();
    }
}
