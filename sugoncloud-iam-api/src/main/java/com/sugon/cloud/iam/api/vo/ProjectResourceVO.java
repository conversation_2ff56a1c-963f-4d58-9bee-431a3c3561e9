package com.sugon.cloud.iam.api.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: yangdingshan
 * @Date: 2024/7/23 9:44
 * @Description:
 */
@Data
@ApiModel("项目资源")
public class ProjectResourceVO {

    @JsonProperty("id")
    @ApiModelProperty("服务id")
    private String id;

    @JsonProperty("resources")
    @ApiModelProperty("项目资源详情")
    private List<ResourceDetail> resources;

    @Data
    @ApiModel("项目资源详情")
    public static class ResourceDetail {

        @JsonProperty("black_id")
        private String blackId;

        @ApiModelProperty("资源名称")
        private String name;

        @ApiModelProperty("资源列表链接")
        private String link;

        @JsonProperty("detail_url")
        @ApiModelProperty("资源详情链接")
        private String detailUrl;
    }
}




