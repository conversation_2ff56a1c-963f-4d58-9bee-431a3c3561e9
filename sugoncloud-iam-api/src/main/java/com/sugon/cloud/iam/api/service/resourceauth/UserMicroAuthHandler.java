package com.sugon.cloud.iam.api.service.resourceauth;

import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.iam.api.controller.UserMicroController;
import com.sugon.cloud.iam.api.entity.UserMicroViewer;
import com.sugon.cloud.resource.auth.common.service.impl.BaseResourceAuthHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/15 10:20
 */
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class UserMicroAuthHandler extends BaseResourceAuthHandler {

    private final UserMicroController userMicroController;

    private final HttpServletRequest request;


    @Override
    public List<String> getUserResource(String userId) {
        ResultModel byUser = userMicroController.getByUser(request);
        Object content = byUser.getContent();
        List<UserMicroViewer> list = (List<UserMicroViewer>) content;

        return list.stream().map(UserMicroViewer::getId).collect(Collectors.toList());
    }
}
