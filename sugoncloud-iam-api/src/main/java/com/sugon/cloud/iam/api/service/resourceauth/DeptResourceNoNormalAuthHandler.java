package com.sugon.cloud.iam.api.service.resourceauth;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.iam.api.common.RecursionDeptIdUtils;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.mapper.UserMapper;
import com.sugon.cloud.iam.api.service.DepartmentService;
import com.sugon.cloud.iam.common.model.vo.DepartmentTreeDetailVO;
import com.sugon.cloud.resource.auth.common.service.impl.BaseResourceAuthHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: yangdingshan
 * @Date: 2024/1/10 16:20
 * @Description:
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class DeptResourceNoNormalAuthHandler extends BaseResourceAuthHandler {

    private final UserMapper userMapper;

    private final DeptResourceAuthHandler deptResourceAuthHandler;

    @Override
    public List<String> getUserResource(String userId) {
        User user = userMapper.selectById(userId);
        // 普通用户无权访问
        if (StrUtil.isBlank(user.getType())) {
            return null;
        }
        return deptResourceAuthHandler.getUserResource(userId);
    }

}
