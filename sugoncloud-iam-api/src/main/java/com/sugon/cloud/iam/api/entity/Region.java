package com.sugon.cloud.iam.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("region")
@ApiModel(value = "region")
public class Region {
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("描述")
    private String description;
    @JsonProperty("parent_region_id")
    @ApiModelProperty("父级regionId")
    private String parentRegionId;
    @ApiModelProperty("名称")
    private String extra;
    @ApiModelProperty("类型")
    private String type;
}
