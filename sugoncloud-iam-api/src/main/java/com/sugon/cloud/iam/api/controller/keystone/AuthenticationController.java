package com.sugon.cloud.iam.api.controller.keystone;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.constants.HeaderParamConstant;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.entity.SecretKeyEntity;
import com.sugon.cloud.iam.api.entity.exception.KeyStoneMethodArgumentNotValidException;
import com.sugon.cloud.iam.api.entity.exception.SubjectTokenAccessDeniedException;
import com.sugon.cloud.iam.api.entity.keystone.*;
import com.sugon.cloud.iam.api.entity.keystone.VO.*;
import com.sugon.cloud.iam.api.jwt.JwtTokenUtils;
import com.sugon.cloud.iam.api.sca4.SCA4Match;
import com.sugon.cloud.iam.api.service.*;
import io.jsonwebtoken.Claims;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.sugon.cloud.iam.api.sca4.SCA4Match.signV4Algorithm;

/**
 * openstack Native oath
 */
@RestController
@RequestMapping("/v3/auth")
@Log4j2
@Api(tags = {"keystone-权限认证"})
@RefreshScope
@DocumentIgnore
public class AuthenticationController {
    @Autowired
    private UserService userService;
    @Autowired
    private CatalogService catalogService;
    @Autowired
    private RoleService roleService;
    @Autowired
    private JwtService jwtService;
    @Autowired
    private JwtTokenUtils jwtTokenUtils;
    @Autowired
    private ProjectService projectService;
//    @Value("${token.expireTime}")
//    private Integer expiration;
    @Autowired
    private TrustService trustService;

    @Autowired
    private  SecretKeyService secretKeyService;
    @Value("#{'${vip.ip}'.concat(':').concat('${vip.port}')}")
    private String linksPrefix;

    @PostMapping("/tokens")
    @ResponseStatus(HttpStatus.CREATED)
    @ApiOperation(value = "认证")
    public Map<String,Object> createToken (@RequestBody AuthVO auth, HttpServletResponse response) {
        log.debug("-------------create token------------");
        response.setHeader("Content-Type", "application/json");
        response.addHeader("x-openstack-request-id", "req-" + UUID.randomUUID().toString());
        response.addHeader("Vary", "X-Auth-Token");
        if (auth.getAuth() == null) {
            throw new KeyStoneMethodArgumentNotValidException("Expecting to find auth in request body. The server could not comply with the request since it is either malformed or otherwise incorrect. The client is assumed to be in error.");
        }
        if (Objects.isNull(auth.getAuth().getIdentity())) {
            throw new KeyStoneMethodArgumentNotValidException("'identity' is a required property");
        }
        if (Objects.isNull(auth.getAuth().getIdentity().getMethods())) {
            throw new KeyStoneMethodArgumentNotValidException("Invalid input for field 'identity': 'methods' is a required property");
        }

        String tokenStr;
        String userName;
        List<String> methods = new ArrayList<>();
        if (Objects.nonNull(auth.getAuth().getIdentity().getToken())) {
            if (Objects.isNull(auth.getAuth().getIdentity().getToken().getId())) {
                throw new KeyStoneMethodArgumentNotValidException("Invalid input for field 'identity/token': 'id' is a required property");
            }
            tokenStr = auth.getAuth().getIdentity().getToken().getId();
            Claims claims = jwtTokenUtils.getClaimsFromToken(tokenStr);
            if (Objects.isNull(claims)) {
                throw new SubjectTokenAccessDeniedException("{\n" +
                        "    \"error\": {\n" +
                        "        \"message\": \"This is not a recognized Fernet token " + tokenStr + "\",\n" +
                        "        \"code\": 404,\n" +
                        "        \"title\": \"Not Found\"\n" +
                        "    }\n" +
                        "}");
            }
            methods.add("token");
            userName = claims.get(HeaderParamConstant.USER_NAME, String.class);
            if (Objects.nonNull(auth.getAuth().getScope())) {
                try {
                    //兼容 OS-TRUST API
                    log.debug("scope content: {} ",auth.getAuth().getScope());
                    JSONObject jsonObject = JSONObject.parseObject(auth.getAuth().getScope().toString());
                    JSONObject scope = jsonObject.getJSONObject("scope");
                    JSONObject trust = scope.getJSONObject("OS-TRUST:trust");
                    if (trust != null) {
                        String trustId = trust.getString("id");
                        Map trustMap = trustInfo(trustId);
                        User user = userService.getByName(userName);
                        UserVO userVO = initUserVO(user);
                        Map resultMap = Maps.newHashMap();
                        resultMap.put("OS-TRUST:trust", trustMap);
                        resultMap.put("user", userVO);
                        Date date = claims.getExpiration();
                        long expiration = jwtService.getExpiredTime();
                        if (Objects.isNull(date)) date = new Date(System.currentTimeMillis()+expiration*1000);
                        resultMap.put("expires_at", date);
                        resultMap.put("methods", methods);
                        Map tokenVO = Maps.newHashMap();
                        tokenVO.put("token", resultMap);
                        return tokenVO;
                    }
                } catch (Exception e) {
                    log.error("Consuming a trust error", e);
                }
            }
        } else {
            if (Objects.isNull(auth.getAuth().getIdentity().getPassword())) {
                throw new KeyStoneMethodArgumentNotValidException("Expecting to find password in identity. The server could not comply with the request since it is either malformed or otherwise incorrect. The client is assumed to be in error.");
            }
            if (Objects.isNull(auth.getAuth().getIdentity().getPassword().getUser())) {
                throw new KeyStoneMethodArgumentNotValidException("Expecting to find user in None. The server could not comply with the request since it is either malformed or otherwise incorrect. The client is assumed to be in error.");
            }
            if (Objects.isNull(auth.getAuth().getIdentity().getPassword().getUser().getName())) {
                throw new KeyStoneMethodArgumentNotValidException("Invalid input for field identity/password/user: id or name must be present.");
            }
            if (Objects.isNull(auth.getAuth().getIdentity().getPassword().getUser().getPassword())) {
                throw new KeyStoneMethodArgumentNotValidException("Invalid input for field identity/password/user: password must be present.");
            }
            methods.add("password");
            userName = auth.getAuth().getIdentity().getPassword().getUser().getName();
            String password = auth.getAuth().getIdentity().getPassword().getUser().getPassword();
            try {
                tokenStr = jwtService.login(userName,password, true);
            } catch (AuthenticationException e) {
                log.info("creatToken error:[{}]", e);
                throw new UsernameNotFoundException("{\n" +
                        "    \"error\": {\n" +
                        "        \"message\": \"The request you have made requires authentication.\",\n" +
                        "        \"code\": 401,\n" +
                        "        \"title\": \"Unauthorized\"\n" +
                        "    }\n" +
                        "}");
            }
        }
        response.addHeader("X-Subject-Token", tokenStr);
        User user = userService.getByName(userName);
        List<Catalog> list = catalogService.getAll();
        List<Role> roles = roleService.findByUserId(user.getId());
        List<CatalogVO> catalogVOS = initCatalog(list, user.getDefaultProjectId());
        return initTokenVO(user, catalogVOS, roles, methods, tokenStr);
    }

    @GetMapping("/tokens")
    @ApiOperation(value = "验证token")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "X-Auth-Token", value = "masterToken", required = true, dataType = "String", paramType = "header")
            , @ApiImplicitParam(name = "X-Subject-Token", value = "subToken", required = true, dataType = "String", paramType = "header")})
    public Map<String, Object> checkToken(@RequestHeader("X-Auth-Token") String masterToken, @RequestHeader("X-Subject-Token") String subToken,
                              HttpServletResponse response){
        log.debug("-------------check token------------");
        response.setHeader("Content-Type","application/json");
        response.addHeader("x-openstack-request-id", "req-" + UUID.randomUUID().toString());
        String masterUsername = null;
        if (masterToken.startsWith(signV4Algorithm)) {
            SCA4Match.SignValues signValues = null;
            try {
                signValues = SCA4Match.parseSignV4(masterToken);
                String accessKey = signValues.getCredential().getAccessKey();
                SecretKeyEntity secretKeyEntity = secretKeyService.getOne(new LambdaQueryWrapper<SecretKeyEntity>()
                        .eq(SecretKeyEntity::getAccessKey, accessKey));
                String userId = secretKeyEntity.getUserId();
                User user = userService.getById(userId);
                if (user != null) {
                    masterUsername = user.getName();
                }
            } catch (Exception e) {
                throw new SubjectTokenAccessDeniedException("{\n" +
                        "    \"error\": {\n" +
                        "        \"message\": \"This is not a recognized Fernet token " + subToken + "\",\n" +
                        "        \"code\": 404,\n" +
                        "        \"title\": \"Not Found\"\n" +
                        "    }\n" +
                        "}");
            }
        } else {
            masterUsername = jwtTokenUtils.getUsernameFromToken(masterToken);
        }
        if (masterUsername == null) {
            throw new AccessDeniedException("{\n" +
                    "    \"error\": {\n" +
                    "        \"message\": \"The request you have made requires authentication.\",\n" +
                    "        \"code\": 401,\n" +
                    "        \"title\": \"Unauthorized\"\n" +
                    "    }\n" +
                    "}");
        }
        response.addHeader("X-Subject-Token", subToken);
        response.addHeader("x-openstack-request-id", "req-" + UUID.randomUUID().toString());
        response.addHeader("Vary", "X-Auth-Token");
        User user = null;
        if (subToken.startsWith(signV4Algorithm)) {
            SCA4Match.SignValues signValues = null;
            try {
                signValues = SCA4Match.parseSignV4(subToken);
                String accessKey = signValues.getCredential().getAccessKey();
                SecretKeyEntity secretKeyEntity = secretKeyService.getOne(new LambdaQueryWrapper<SecretKeyEntity>()
                        .eq(SecretKeyEntity::getAccessKey, accessKey));
                String userId = secretKeyEntity.getUserId();
                user = userService.getById(userId);
                //需要联合查询出project信息
                user = userService.getByName(user.getName());
            } catch (Exception e) {
                throw new SubjectTokenAccessDeniedException("{\n" +
                        "    \"error\": {\n" +
                        "        \"message\": \"This is not a recognized Fernet token " + subToken + "\",\n" +
                        "        \"code\": 404,\n" +
                        "        \"title\": \"Not Found\"\n" +
                        "    }\n" +
                        "}");
            }
        } else {
            String subName  = jwtTokenUtils.getUsernameFromToken(subToken);
            user = userService.getByName(subName);
        }
        if (Objects.isNull(user)) {
            throw new SubjectTokenAccessDeniedException("{\n" +
                    "    \"error\": {\n" +
                    "        \"message\": \"This is not a recognized Fernet token " + subToken + "\",\n" +
                    "        \"code\": 404,\n" +
                    "        \"title\": \"Not Found\"\n" +
                    "    }\n" +
                    "}");
        }
        List<Role> roles = roleService.findByUserId(user.getId());
        List<Catalog> list = catalogService.getAll();
        List<CatalogVO> catalogVOS = initCatalog(list, user.getDefaultProjectId());
        List<String> methods = new ArrayList<>();
        methods.add("password");
        return initTokenVO(user, catalogVOS, roles, methods, subToken);
    }


    private List<CatalogVO> initCatalog(List<Catalog> catalogs,final String projectId) {
        List<CatalogVO> list = catalogs.stream().map(catalog -> initCatalogVO(catalog, projectId)).collect(Collectors.toList());
        return list;
    }

    private CatalogVO initCatalogVO(Catalog catalog,final String project) {
        CatalogVO catalogVO = new CatalogVO();
        BeanUtils.copyProperties(catalog, catalogVO);
        List<EndpointVO> endpointVOs = catalog.getEndpoints().stream().map(endpoint -> replaceTenantId(endpoint, project)).collect(Collectors.toList());
        catalogVO.setEndpoints(endpointVOs);
        return catalogVO;
    }

    private EndpointVO replaceTenantId (Endpoint endpoint,final String project) {
        EndpointVO endpointVO = new EndpointVO();
        if (endpoint.getUrl().contains("%(tenant_id)s")) {
            endpoint.setUrl(endpoint.getUrl().replace("%(tenant_id)s", project));
        }
        BeanUtils.copyProperties(endpoint, endpointVO);
        return endpointVO;
    }

    private Map initTokenVO(User user, List<CatalogVO> list, List<Role> roles ,List<String> methods, String tokenStr) {
        Map tokenVO = Maps.newHashMap();
        Token token = new Token();
        UserVO userVO = new UserVO();
        Project project = user.getProject();
        BeanUtils.copyProperties(user, userVO);
        if (Objects.nonNull(project)) {
            LambdaQueryWrapper<Project> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Project::getId, project.getDomainId());
            Project domainProject = projectService.getOne(queryWrapper);
            Domain domain = new Domain();
            domain.setId(domainProject.getId());
            domain.setName(domainProject.getName());
            userVO.setDomain(domain);
            ProjectNativeVO projectNativeVO = new ProjectNativeVO();
            BeanUtils.copyProperties(project, projectNativeVO);
            projectNativeVO.setDomain(domain);
            token.setProject(projectNativeVO);
        }
        token.setUser(userVO);
        token.setCatalog(list);
        List<RoleVO> roleVOS = new ArrayList<>();
        for (Role role:roles) {
            RoleVO roleVO = new RoleVO();
            BeanUtils.copyProperties(role, roleVO);
            roleVOS.add(roleVO);
        }
        token.setRoles(roleVOS);
        token.setMethods(methods);
        token.setIssuedAt(new Date());
        Date date = null;
        if (tokenStr.startsWith(signV4Algorithm)) {
            date = null;
        } else {
            Claims claims = new JwtTokenUtils().getClaimsFromToken(tokenStr);
            //底层创建的token 存在 Expiration， iam创建的token 不存在
            date = claims.getExpiration();
        }
        long expiration = jwtService.getExpiredTime();
        if (Objects.isNull(date)) date = new Date(System.currentTimeMillis()+expiration*1000);
        token.setExpiresAt(date);
        tokenVO.put("token", token);
        return tokenVO;
    }
    @GetMapping("/projects")
    @ApiOperation(value = "获取用户项目信息", notes = "获取用户项目信息")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "X-Auth-Token", value = "masterToken", required = true, dataType = "String", paramType = "header")})
    public Map<String, Object> getProjectFromToken(@RequestHeader("X-Auth-Token") String masterToken, HttpServletResponse response) {
        response.setHeader("Content-Type","application/json");
        response.addHeader("x-openstack-request-id", "req-" + UUID.randomUUID().toString());
        String userName = jwtTokenUtils.getUsernameFromToken(masterToken);
        if (userName == null) {
            throw new AccessDeniedException("{\n" +
                    "    \"error\": {\n" +
                    "        \"message\": \"The request you have made requires authentication.\",\n" +
                    "        \"code\": 401,\n" +
                    "        \"title\": \"Unauthorized\"\n" +
                    "    }\n" +
                    "}");
        }
        response.addHeader("X-Subject-Token", masterToken);
        response.addHeader("x-openstack-request-id", "req-" + UUID.randomUUID().toString());
        response.addHeader("Vary", "X-Auth-Token");
        User user = userService.getByName(userName);
        List<Project> list = projectService.findByUserId(user.getId());
        Map map = new HashMap();
        map.put("projects", list);
        return map;
    }

    private Map trustInfo(String trustId) {
        Trust trust = trustService.getById(trustId);
        Map trustMap = Maps.newHashMap();
        trustMap.put("trustor_user", initMap(trust.getTrustorUserId()));
        trustMap.put("trustee_user", initMap(trust.getTrusteeUserId()));
        trustMap.put("id", trust.getId());
        trustMap.put("impersonation", trust.isImpersonation());
        Map trustlinks = Maps.newHashMap();
        trustlinks.put("self","http://" + linksPrefix+"/v3/OS-TRUST/trusts/"+trustId);
        trustlinks.put("links", trustlinks);
        return trustMap;
    }

    private Map initMap(String id) {
        Map map = Maps.newHashMap();
        map.put("id", id);
        Map links = Maps.newHashMap();
        links.put("self","http://" + linksPrefix+"/v3/users/"+id);
        map.put("links", links);
        return map;
    }

    private UserVO initUserVO(User user) {
        UserVO userVO = new UserVO();
        BeanUtils.copyProperties(user, userVO);
        LambdaQueryWrapper<Project> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Project::getId, user.getDomainId());
        Project domainProject = projectService.getOne(queryWrapper);
        if (Objects.nonNull(domainProject)) {
            Domain domain = new Domain();
            domain.setId(domainProject.getId());
            domain.setName(domainProject.getName());
            userVO.setDomain(domain);
        }
        return userVO;
    }
}

