package com.sugon.cloud.iam.api.controller.keystone;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;

import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.entity.keystone.Project;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.entity.keystone.VO.UserDetailNativeVO;
import com.sugon.cloud.iam.api.entity.keystone.VO.UserInfoEntityVO;
import com.sugon.cloud.iam.api.entity.exception.EmptyInputException;
import com.sugon.cloud.iam.api.entity.exception.NativeBadRequestException;
import com.sugon.cloud.iam.api.entity.exception.UserCannotFindException;
import com.sugon.cloud.iam.api.service.ProjectService;
import com.sugon.cloud.iam.api.service.UserService;
import com.sugon.cloud.iam.api.utils.EncryptAndDecryptUtil;
import com.sugon.cloud.tools.service.EncAndDecFeignService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpStatus;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.NotFoundException;
import java.time.DateTimeException;
import java.util.*;

/**
 * OpenStack原生创建用户
 */
@RestController
@RequestMapping("/v3/users")
@Api(tags = {"keystone-用户操作"})
@Log4j2
@DocumentIgnore
public class UserNativeController {
    @Value("${vip.ip}")
    private String ramIP;
    @Value("${vip.port}")
    private String ramPort;
    @Autowired
    private UserService userService;
    @Autowired
    private ProjectService projectService;
    @Value("${encryption.enabled:false}")
    private boolean encryptionEnabled;
    @Autowired
    private EncryptAndDecryptUtil encryptAndDecryptUtil;


    @PostMapping()
    @ResponseStatus(HttpStatus.CREATED)
    @ApiOperation(value = "创建用户")
    public Map<String, Object> create(@RequestBody UserInfoEntityVO userInfoEntityVO) {
        User user = userInfoEntityVO.getUser();
        if (user == null) {
            throw new EmptyInputException("{\n" +
                    "    \"error\": {\n" +
                    "        \"message\": \"create_user() takes exactly 3 arguments (2 given)\",\n" +
                    "        \"code\": 400,\n" +
                    "        \"title\": \"Bad Request\"\n" +
                    "    }\n" +
                    "}");
        }
        String name = user.getName();
        User result = userService.getByName(name);
        if (result != null) {
            throw new DuplicateKeyException("{\n" +
                    "    \"error\": {\n" +
                    "        \"message\": \"Conflict occurred attempting to store user - Duplicate entry found with name "+name+".\",\n" +
                    "        \"code\": 409,\n" +
                    "        \"title\": \"Conflict\"\n" +
                    "    }\n" +
                    "}");
        }
        String domainId = user.getDomainId();
        if (StringUtils.isNotBlank(domainId)) {
            LambdaQueryWrapper<Project> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Project::getDomainId, domainId);
            queryWrapper.or().eq(Project::getId, domainId);
            List<Project> projects = projectService.list(queryWrapper);
            if (projects == null || projects.size() == 0) {
                throw new NotFoundException("{\n" +
                        "    \"error\": {\n" +
                        "        \"message\": \"Could not find domain: "+domainId+".\",\n" +
                        "        \"code\": 404,\n" +
                        "        \"title\": \"Not Found\"\n" +
                        "    }\n" +
                        "}");
            }
        }
        if (StringUtils.isBlank(domainId)) user.setDomainId("default");
        Object federated = user.getFederated();
        String email = user.getEmail();
        Object description = user.getDescription();
        JSONObject extraJson = new JSONObject();
        if (federated != null) extraJson.put("federated", federated);
        if (StringUtils.isNotBlank(email)) extraJson.put("email", email);
        if (description != null) extraJson.put("description", description);
        user.setCreatedAt(new Date());
        if (StringUtils.isNotBlank(user.getPassword())) {
            String password = new BCryptPasswordEncoder().encode(user.getPassword());
            if (encryptionEnabled) {
                String iv = encryptAndDecryptUtil.getIv();
                user.setPassword (encryptAndDecryptUtil.encryptByIv(password, iv));
                user.setIv(iv);
                extraJson.put(CommonInstance.PASSWORD_ENCODE, true);
            } else {
                user.setPassword(password);
            }
        }
        user.setExtra(extraJson.toJSONString());
        userService.save(user);
        UserDetailNativeVO userDetailNativeVO = new UserDetailNativeVO();
        BeanUtils.copyProperties(user, userDetailNativeVO);
        String href = "http://"+ramIP+":"+ramPort+"/v3/users/"+ user.getId();
        Map links = Maps.newHashMap();
        links.put("self", href);
        userDetailNativeVO.setLinks(links);
        Map resultMap = new HashMap();
        resultMap.put("user", userDetailNativeVO);
        return resultMap;

    }

    @GetMapping("/{user_id}")
    @ApiOperation(value = "获取用户详情")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "user_id", value = "用户ID", required = true, dataType = "String")})
    public Map<String, Object> getUser(@PathVariable("user_id") String userId) {
        LambdaQueryWrapper<User> userQueryWrapper = new LambdaQueryWrapper<>();
        userQueryWrapper.eq(User::getId, userId);
        User user = userService.getOne(userQueryWrapper);
        if (user == null) throw new UserCannotFindException("{\n" +
                "    \"error\": {\n" +
                "        \"message\": \"Could not find user: "+userId+".\",\n" +
                "        \"code\": 404,\n" +
                "        \"title\": \"Not Found\"\n" +
                "    }\n" +
                "}");
        UserDetailNativeVO userDetailNativeVO = new UserDetailNativeVO();
        BeanUtils.copyProperties(user, userDetailNativeVO);
        if (StringUtils.isNotBlank(user.getExtra())) {
            JSONObject jsonObject = JSON.parseObject(user.getExtra());
            Object federated = jsonObject.get("federated");
            userDetailNativeVO.setFederated(federated);
        }
        Map links = Maps.newHashMap();
        String href = "http://"+ramIP+":"+ramPort+"/v3/users/"+ userId;
        links.put("self", href);
        userDetailNativeVO.setLinks(links);
        Map resultMap = new HashMap();
        resultMap.put("user", userDetailNativeVO);
        return resultMap;
    }

    @GetMapping
    @ApiOperation(value = "获取用户列表")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "domain_id", value = "域ID", required = false, dataType = "String"),
            @ApiImplicitParam(name = "enabled", value = "是否启用", required = false, dataType = "Boolean"),
            @ApiImplicitParam(name = "idp_id", value = "身份提供商ID", required = false, dataType = "String"),
            @ApiImplicitParam(name = "name", value = "用户名", required = false, dataType = "String"),
            @ApiImplicitParam(name = "password_expires_at", value = "密码过期时间", required = false, dataType = "String"),
            @ApiImplicitParam(name = "protocol_id", value = "协议ID", required = false, dataType = "String"),
            @ApiImplicitParam(name = "unique_id", value = "唯一ID", required = false, dataType = "String")})
    public Map<String, Object> userList(@RequestParam(value = "domain_id", required = false) String domainId,
                        @RequestParam(value = "enabled", required = false) Boolean enabled,
                        @RequestParam(value = "idp_id", required = false) String idpId,
                        @RequestParam(value = "name", required = false) String name,
                        @RequestParam(value = "password_expires_at", required = false) String passwordExpiresAt,
                        @RequestParam(value = "protocol_id", required = false) String protocolId,
                        @RequestParam(value = "unique_id", required = false) String uniqueId,
                        HttpServletRequest request) {
        LambdaQueryWrapper<User> userQueryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(domainId)) {
            userQueryWrapper.eq(User::getDomainId, domainId);
        }
        if (enabled != null) {
            userQueryWrapper.eq(User::getEnabled, enabled);
        }
        if (StringUtils.isNotBlank(name)) {
            userQueryWrapper.like(User::getName, name);
        }
        if (StringUtils.isNotBlank(passwordExpiresAt)) {
            if (passwordExpiresAt.contains(":")) {
                String operators = passwordExpiresAt.split(":")[0];
                String date = passwordExpiresAt.substring(operators.length()+1);
                Date expired = null;
                try {
                     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
                     expired = simpleDateFormat.parse(date);
                } catch (Exception e) {
                    throw new DateTimeException("{\n" +
                            "    \"error\": {\n" +
                            "        \"message\": \"Timestamp not in expected format. The server could not comply with the request since it is either malformed or otherwise incorrect. The client is assumed to be in error.\",\n" +
                            "        \"code\": 400,\n" +
                            "        \"title\": \"Bad Request\"\n" +
                            "    }\n" +
                            "}");
                }
                boolean flag = true;
                if ("lt".equals(operators)) {
                    flag = false;
                    userQueryWrapper.lt(User::getPasswordExpiresAt, expired);
                }
                if ("lte".equals(operators)) {
                    flag = false;
                    userQueryWrapper.le(User::getPasswordExpiresAt, expired);
                }
                if ("gt".equals(operators)) {
                    flag = false;
                    userQueryWrapper.gt(User::getPasswordExpiresAt, expired);
                }
                if ("gte".equals(operators)) {
                    flag = false;
                    userQueryWrapper.ge(User::getPasswordExpiresAt, expired);
                }
                if ("eq".equals(operators)) {
                    flag = false;
                    userQueryWrapper.eq(User::getPasswordExpiresAt, expired);
                }
                if ("neq".equals(operators)) {
                    flag = false;
                    userQueryWrapper.ne(User::getPasswordExpiresAt, expired);
                }
                if (flag) throw new NativeBadRequestException("{\n" +
                        "    \"error\": {\n" +
                        "        \"message\": \""+operators+"\",\n" +
                        "        \"code\": 400,\n" +
                        "        \"title\": \"Bad Request\"\n" +
                        "    }\n" +
                        "}");
            }
        }
        List<User> list = userService.list(userQueryWrapper);
        List<UserDetailNativeVO> vos = new ArrayList<>();
        Map links = Maps.newHashMap();
        for(User user:list){
            UserDetailNativeVO userDetailNativeVO = new UserDetailNativeVO();
            BeanUtils.copyProperties(user, userDetailNativeVO);
            if (StringUtils.isNotBlank(user.getExtra())) {
                try {
                    JSONObject jsonObject = JSON.parseObject(user.getExtra());
                    Object federated = jsonObject.get("federated");
                    Object options = jsonObject.get("options");
                    Object description = jsonObject.get("description");
                    userDetailNativeVO.setFederated(federated);
                    userDetailNativeVO.setOptions(options);
                    userDetailNativeVO.setDescription(description);
                } catch (Exception e) {

                }
            }
            String href = "http://"+ramIP+":"+ramPort+"/v3/users/"+ user.getId();
            links.put("self", href);
            userDetailNativeVO.setLinks(links);
            vos.add(userDetailNativeVO);
        }
        Map map = new HashMap();
        map.put("users",vos);
        String queryString = request.getQueryString() == null?"":"?"+request.getQueryString();
        String href = "http://"+ramIP+":"+ramPort+"/v3/users/"+queryString;
        links.put("self", href);
        links.put("previous", null);
        links.put("next", null);
        map.put("links",links);

        return map;
    }

    @PostMapping("{user_id}/password")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @ApiOperation(value = "更新用户密码")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "user_id", value = "用户ID", required = true, dataType = "String")})
    public void updatePassword(@PathVariable("user_id") String userId,
                               @RequestBody JSONObject jsonObject) {
        LambdaQueryWrapper<User> userQueryWrapper = new LambdaQueryWrapper<>();
        userQueryWrapper.eq(User::getId, userId);
        User user = userService.getOne(userQueryWrapper);
        if (user == null) throw new UserCannotFindException("{\n" +
                "    \"error\": {\n" +
                "        \"message\": \"The request you have made requires authentication.\",\n" +
                "        \"code\": 401,\n" +
                "        \"title\": \"Unauthorized\"\n" +
                "    }\n" +
                "}");
        Map<String, String> o = (HashMap) jsonObject.get("user");
        String password = o.get("password");
        String original_password = o.get("original_password");
        BCryptPasswordEncoder bcryptPasswordEncoder = new BCryptPasswordEncoder();
        String newPassword = new BCryptPasswordEncoder().encode(password);
        String presentedPassword = user.getPassword();
        if (encryptionEnabled) {
            newPassword = encryptAndDecryptUtil.encryptByIv(newPassword, user.getIv());
            presentedPassword = encryptAndDecryptUtil.decryptByIv(presentedPassword, user.getIv());
        }
        boolean matches =  bcryptPasswordEncoder.matches(original_password, presentedPassword);
        if (!matches) {
            throw new UserCannotFindException("{\n" +
                    "    \"error\": {\n" +
                    "        \"message\": \"The request you have made requires authentication.\",\n" +
                    "        \"code\": 401,\n" +
                    "        \"title\": \"Unauthorized\"\n" +
                    "    }\n" +
                    "}");
        }
        userService.lambdaUpdate()
                .eq(User::getId, userId)
                .set(User::getPassword, newPassword)
                .update();
    }

    @PatchMapping("{user_id}")
    @ApiOperation(value = "更新用户信息")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "user_id", value = "用户ID", required = true, dataType = "String")})
    public void updateUser(@PathVariable("user_id")String userId, @RequestBody JSONObject jsonObject) {
        Map<String, String> o = (HashMap) jsonObject.get("user");
        String password = o.get("password");
        LambdaQueryWrapper<User> userQueryWrapper = new LambdaQueryWrapper<>();
        userQueryWrapper.eq(User::getId, userId);
        User user = userService.getOne(userQueryWrapper);
        if (user == null) {
            throw new UserCannotFindException("{\n" +
                    "    \"error\": {\n" +
                    "        \"message\": \"Could not find user: "+userId+".\",\n" +
                    "        \"code\": 404,\n" +
                    "        \"title\": \"Not Found\"\n" +
                    "    }\n" +
                    "}");
        }
        BCryptPasswordEncoder bcryptPasswordEncoder = new BCryptPasswordEncoder();
        String newPassword = bcryptPasswordEncoder.encode(password);
        if (encryptionEnabled) {
             newPassword = encryptAndDecryptUtil.encryptByIv(newPassword, user.getIv());
        }
        userService.lambdaUpdate()
                .eq(User::getId, userId)
                .set(User::getPassword, newPassword)
                .update();
    }
}
