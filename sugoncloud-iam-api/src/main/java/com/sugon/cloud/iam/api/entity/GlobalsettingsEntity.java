package com.sugon.cloud.iam.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("globalsettings")
public class GlobalsettingsEntity {
    @TableId(type = IdType.UUID)
    private String uuid;
    @ApiModelProperty("键")
    @JsonProperty("policy_name")
    private String policyName;
    @ApiModelProperty("值")
    @JsonProperty("policy_document")
    private String policyDocument;
    @ApiModelProperty("类型")
    @JsonProperty("policy_type")
    private String policyType;
    @ApiModelProperty("描述")
    @JsonProperty("retained_field")
    private String retainedField;
    @ApiModelProperty("名称")
    @JsonProperty("policy_display_name")
    private String policyDisplayName;
    @ApiModelProperty("类型 env:环境参数, sys:系统参数, sec:安全参数")
    private String type;
}
