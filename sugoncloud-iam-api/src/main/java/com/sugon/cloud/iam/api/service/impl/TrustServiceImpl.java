package com.sugon.cloud.iam.api.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sugon.cloud.iam.api.entity.keystone.Role;
import com.sugon.cloud.iam.api.entity.keystone.Trust;
import com.sugon.cloud.iam.api.entity.keystone.TrustRole;
import com.sugon.cloud.iam.api.mapper.TrustMapper;
import com.sugon.cloud.iam.api.service.TrustRoleService;
import com.sugon.cloud.iam.api.service.TrustService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class TrustServiceImpl extends ServiceImpl<TrustMapper, Trust> implements TrustService {

    private final TrustRoleService trustRoleService;

    @Transactional
    @Override
    public Trust createTrust(Trust trust, List<Role> roles) {
        if (trust.getExpiresAt() != null) trust.setExpiresAtInt(trust.getExpiresAt().getTime());
        this.save(trust);
        List<TrustRole> trustRoles = new ArrayList<>();
        roles.stream().forEach(role -> {
            TrustRole tr = new TrustRole();
            tr.setTrustId(trust.getId());
            tr.setRoleId(role.getId());
            trustRoles.add(tr);
        });
        trustRoleService.saveBatch(trustRoles);
        return trust;
    }
}
