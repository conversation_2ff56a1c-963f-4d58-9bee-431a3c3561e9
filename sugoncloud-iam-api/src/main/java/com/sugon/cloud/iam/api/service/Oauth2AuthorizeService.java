package com.sugon.cloud.iam.api.service;

import com.sugon.cloud.iam.api.vo.OAuth2CallbackResponseVO;
import com.sugon.cloud.iam.api.vo.OAuth2DetailVO;
import com.sugon.cloud.iam.api.vo.OAuth2ServerItem;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/05/16 14:10
 **/
public interface Oauth2AuthorizeService {
    OAuth2CallbackResponseVO callback(String code, String userId, String redirectUri);

    String oauth2Update(String userId, OAuth2DetailVO oauth2DetailVO);

    OAuth2DetailVO oauth2Detail(String userId);

    List<OAuth2ServerItem> oauth2List();
}
