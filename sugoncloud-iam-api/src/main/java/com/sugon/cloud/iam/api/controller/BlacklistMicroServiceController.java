package com.sugon.cloud.iam.api.controller;

import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.iam.api.entity.BlacklistMicroService;
import com.sugon.cloud.iam.api.service.BlacklistMicroServiceService;
import com.sugon.cloud.iam.api.service.MicroServiceService;
import com.sugon.cloud.iam.api.vo.PolicyFilterVO;
import com.sugon.cloud.iam.common.enums.BlacklistTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/5 16:13
 */
@DocumentIgnore
@RestController
@RequestMapping(value = "/api/blacklist-micro-service")
@Api(value = "黑名单控制器", tags = "黑名单控制器")
@Slf4j
public class BlacklistMicroServiceController {

    @Resource
    private BlacklistMicroServiceService service;

    @Resource
    private MicroServiceService microServiceService;

    @Value("${black.enable:false}")
    private boolean blackEnable;


    @GetMapping(value = "list")
    @ApiOperation("获取黑名单列表")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "type", value = "类型, MENU 菜单, MICRO 微服务, CATEGORY 微服务类型", required = true, dataType = "BlacklistTypeEnum", paramType = "path")})
    public List<BlacklistMicroService> getList(BlacklistTypeEnum type) {
        if (!blackEnable) {
            return Collections.emptyList();
        }
        return service.listByType(type);
    }

    @GetMapping(value = "all")
    @ApiOperation("获取黑名单列表")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "type", value = "类型, MENU 菜单, MICRO 微服务, CATEGORY 微服务类型", required = true, dataType = "BlacklistTypeEnum", paramType = "path")})
    public ResultModel<List<BlacklistMicroService>> all(BlacklistTypeEnum type) {
        if (!blackEnable) {
            return ResultModel.success(Collections.emptyList());
        }
        return ResultModel.success(service.listByType(type));
    }

    @GetMapping(value = "/policy")
    @ApiOperation("策略专用接口")
    @DocumentIgnore
    public ResultModel<PolicyFilterVO> policy() {
        return ResultModel.success(microServiceService.getPolicyFilter());
    }
}
