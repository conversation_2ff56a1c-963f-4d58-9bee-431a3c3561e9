package com.sugon.cloud.iam.api.service.resourceauth;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.beust.jcommander.internal.Lists;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.iam.api.entity.SecretKeyEntity;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.mapper.UserMapper;
import com.sugon.cloud.iam.api.service.SecretKeyService;
import com.sugon.cloud.resource.auth.common.service.impl.BaseResourceAuthHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: yangdingshan
 * @Date: 2024/1/10 16:08
 * @Description:
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class SecretKeyResourceAuthHandler extends BaseResourceAuthHandler {

    private final SecretKeyService secretKeyService;

    private final UserMapper userMapper;

    @Override
    public List<String> getUserResource(String userId) {
        User user = userMapper.selectById(userId);
        if (TypeUtil.TYPE_ADMIN.equals(user.getType())) {
            return Lists.newArrayList(TypeUtil.TYPE_ADMIN);
        }
        List<SecretKeyEntity> list = secretKeyService.list(new QueryWrapper<SecretKeyEntity>().eq("user_id", userId));
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return list.stream().map(SecretKeyEntity::getId).collect(Collectors.toList());
    }
}
