package com.sugon.cloud.iam.api.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.entity.ActionForm;
import com.sugon.cloud.iam.api.entity.StrategyForm;
import com.sugon.cloud.iam.api.entity.StrategyPO;
import com.sugon.cloud.iam.api.exception.BusinessException;
import com.sugon.cloud.iam.api.mapper.ServiceActionMapper;
import com.sugon.cloud.iam.api.service.ServiceActionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class ServiceActionServiceImpl extends ServiceImpl<ServiceActionMapper, ActionForm> implements ServiceActionService {


    @Override
    public PageCL<ActionForm> actionPage(String serviceId, String action, String description, int pageNum, int pageSize) {
        try {
            IPage<ActionForm> page = new Page(pageNum, pageSize);
            page = this.baseMapper.selectPage(page, new LambdaQueryWrapper<ActionForm>()
                    .eq(ActionForm::getServiceId, serviceId)
                    .and(StringUtils.isNotBlank(action) || StringUtils.isNotBlank(description), actionFormLambdaQueryWrapper -> {
                        actionFormLambdaQueryWrapper.like(StringUtils.isNotBlank(action), ActionForm::getAction, action)
                                .or()
                                .like(StringUtils.isNotBlank(description), ActionForm::getDescription, description);
                    }));
            return new PageCL<ActionForm>().getPageByPageHelper(page, page.getRecords());
        } catch (Exception e) {
            log.error("查询操作列表失败:", e);
            throw new BusinessException("查询列表报错");
        }
    }
}
