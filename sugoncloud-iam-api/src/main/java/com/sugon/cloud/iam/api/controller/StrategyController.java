package com.sugon.cloud.iam.api.controller;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.common.String2JSONMapper;
import com.sugon.cloud.iam.api.entity.*;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.exception.BusinessException;
import com.sugon.cloud.iam.api.service.BlacklistMicroServiceService;
import com.sugon.cloud.iam.api.service.StrategyService;
import com.sugon.cloud.iam.api.service.UserService;
import com.sugon.cloud.iam.api.service.UserStrategyService;
import com.sugon.cloud.iam.api.service.resourceauth.SecurityUserTypeAuthHandler;
import com.sugon.cloud.iam.api.service.resourceauth.StrategyAuthHandler;
import com.sugon.cloud.iam.api.service.resourceauth.UserResourceAuthHandler;
import com.sugon.cloud.iam.common.enums.BlacklistTypeEnum;
import com.sugon.cloud.log.client.context.LogRecordContext;
import com.sugon.cloud.log.client.starter.annotation.LogRecordAnnotation;
import com.sugon.cloud.resource.auth.common.aop.ResourceAuth;
import com.sugon.cloud.resource.auth.common.aop.ResourceAuths;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.glasnost.orika.MapperFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;


@Api(tags = "策略API")
@RequestMapping(value = "/api/strategy")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RestController
@Slf4j
@RefreshScope
@DocumentIgnore
public class StrategyController {

    @Resource(name = "defaultRedisTemplate")
    private final RedisTemplate defaultRedisTemplate;
    private final StrategyService strategyService;
    private final UserStrategyService userStrategyService;
    private final UserService userService;

    @Value("${black.enable:false}")
    private boolean blackEnable;

    private final BlacklistMicroServiceService blacklistMicroServiceService;

    @ApiOperation("创建策略")
    @PostMapping("{user_id}")
    @LogRecordAnnotation(value = "创建策略", detail = "创建策略[{{#_ret.resource}}]", resource = "{{#strategyForm.name}}")
    @ApiImplicitParams({@ApiImplicitParam(name = "user_id", value = "用户ID", required = true, dataType = "String", paramType = "path")})
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    public ResultModel createStrategy (@RequestBody StrategyForm strategyForm,
                                       @PathVariable("user_id") String userId) {


        JSONObject jsonObject = strategyForm.getPolicy();
        if (jsonObject.toString().length() > 32768)
            throw new BusinessException("策略长度超过最大长度32768");
        StrategyPO strategyPO = new StrategyPO();
        strategyPO.setDescription(strategyForm.getDescription())
                .setName(strategyForm.getName())
                .setPolicy(jsonObject.toString())
                .setCreateAt(new Date());
        strategyPO.setCreatBy(userId);
        User user = userService.getById(userId);
        if (CommonInstance.USER_TYPE_ADMIN.equals(user.getType())
                ||CommonInstance.USER_TYPE_AUDIT.equals(user.getType())
                ||CommonInstance.USER_TYPE_SECURITY.equals(user.getType())) {
            strategyPO.setCatalog(CommonInstance.CATALOG_SYSTEM_TYPE);
        } else {
            strategyPO.setCatalog(CommonInstance.CATALOG_CUSTOMER_TYPE);
        }
        strategyService.save(strategyPO);
        defaultRedisTemplate.opsForValue().set(CommonInstance.IAM_POLICIES+strategyPO.getId(), jsonObject.toString());
        return ResultModel.success("创建策略成功", strategyPO.getName(),"");
    }

    @ApiOperation("用户绑定策略")
    @PostMapping("/user-bind-strategy")
    @ResourceAuths(value = {@ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userStrategyForm.userId")
            , @ResourceAuth(handler = StrategyAuthHandler.class, resources = "#userStrategyForm.strategyId")})
    public ResultModel userBindStrategy(@RequestBody UserStrategyForm userStrategyForm) {
       /* userStrategyService.remove(new LambdaQueryWrapper<UserStrategyEntity>()
                .eq(UserStrategyEntity::getUserId, userStrategyForm.getUserId()));*/
        userStrategyForm.getStrategyId().forEach(id->{
            UserStrategyEntity entity = new UserStrategyEntity();
            entity.setUserId(userStrategyForm.getUserId());
            entity.setStrategyId(id);
            if(userStrategyService.lambdaQuery()
                    .eq(UserStrategyEntity::getUserId,userStrategyForm.getUserId())
                    .eq(UserStrategyEntity::getStrategyId, id).list().size() == 0) {
                userStrategyService.save(entity);
            }
        });
        userStrategyService.syncStrategy2Redis(userStrategyForm.getUserId());
        return ResultModel.success("用户绑定策略成功");
    }

    @ApiOperation("用户解绑策略")
    @PostMapping("/user-unbind-strategy")
    @ResourceAuths(value = {@ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userStrategyForm.userId")
            , @ResourceAuth(handler = StrategyAuthHandler.class, resources = "#userStrategyForm.strategyId")})
    public ResultModel userUnbindStrategy(@RequestBody UserStrategyForm userStrategyForm) {

        userStrategyForm.getStrategyId().forEach(id->{
            UserStrategyEntity entity = new UserStrategyEntity();
            entity.setUserId(userStrategyForm.getUserId());
            entity.setStrategyId(id);
            userStrategyService.remove(new LambdaQueryWrapper<UserStrategyEntity>().eq(UserStrategyEntity::getStrategyId, id)
                    .eq(UserStrategyEntity::getUserId, userStrategyForm.getUserId()));
        });
        userStrategyService.syncStrategy2Redis(userStrategyForm.getUserId());
        return ResultModel.success("用户解除绑策略成功");
    }

    @ApiOperation("删除策略")
    @DeleteMapping("{user_id}/{id}")
    @LogRecordAnnotation(value = "删除策略", detail = "删除策略{{#name}}", resourceId = "{{#id}}", resource = "{{#name}}")
    @ResourceAuth(handler = StrategyAuthHandler.class, resources = "#id")
    public ResultModel deleteStrategy(@PathVariable("user_id") String userId, @PathVariable("id") String id){

        List<UserStrategyEntity> list = userStrategyService.lambdaQuery().eq(UserStrategyEntity::getStrategyId, id).list();
        if (CollectionUtil.isNotEmpty(list))
            throw  new BusinessException("该策略已被引用，不可删除请取消所有与该策略的绑定之后再执行删除操作");
        StrategyPO strategyPO = strategyService.getById(id);
        LogRecordContext.putVariable("name", Objects.isNull(strategyPO) ? "" : strategyPO.getName());
        if (CommonInstance.CATALOG_SYSTEM_TYPE.equals(strategyPO.getCatalog())) {
            if (!userId.equals(strategyPO.getCreatBy())) {
                return ResultModel.error("该策略您不能删除");
            }
            /*User user = userService.getById(id);
            if (!CommonInstance.USER_TYPE_SECURITY.equals(user.getType())) {
                return ResultModel.error("内置策略不能删除");
            }*/
        }
        strategyService.removeById(id);
        defaultRedisTemplate.delete(CommonInstance.IAM_POLICIES+id);
        return ResultModel.success("删除策略成功");
    }

    @ApiOperation("策略详情")
    @GetMapping(value = "{id}")
    @LogRecordAnnotation(value = "策略详情", detail = "查看策略详情", resourceId = "{{#id}}", resource = "{{#name}}")
    @ResourceAuth(handler = StrategyAuthHandler.class, resources = "#id")
    public ResultModel<StrategyForm> getStrategy(@PathVariable("id") String id){
        StrategyPO strategyPO = strategyService.getById(id);
        LogRecordContext.putVariable("name", Objects.isNull(strategyPO) ? "" : strategyPO.getName());
        MapperFacade mapper = String2JSONMapper.mapper();
        return ResultModel.success("查询策略成功", mapper.map(strategyPO, StrategyForm.class));
    }

    @ApiOperation("用户策略(用户已经绑定的策略)")
    @GetMapping(value = "{user_id}/list")
    @ApiImplicitParams({@ApiImplicitParam(name = "user_id", value = "用户Id", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "page_num", value = "页码", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "page_size", value = "每页显示条数", paramType = "query", dataType = "int")})
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    public ResultModel<PageCL<StrategyForm>> getUserStrategy(@PathVariable("user_id") String userId,
                                                             @RequestParam(value = "page_num", defaultValue = "0", required = false) int pageNum,
                                                             @RequestParam(value = "page_size", defaultValue = "15", required = false) int pageSize){
        if (blackEnable) {
            List<BlacklistMicroService> blacklistMicroServices = blacklistMicroServiceService.listByType(BlacklistTypeEnum.MENU);
            long count = blacklistMicroServices.stream().filter(p -> p.getId().equals("6c6dafc2f8127287e43dee8f3c9ec412")).count();
            if (count > 0) {
                return ResultModel.success("Strategy_Hidden", null);
            }
        }
        return ResultModel.success("查询策略成功", strategyService.findByUserIdPage(userId,pageNum,pageSize));
    }

    @ApiOperation("策略列表")
    @GetMapping(value = "{user_id}/list-all")
    @ApiImplicitParams({@ApiImplicitParam(name = "user_id", value = "用户Id", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "page_num", value = "页码", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "page_size", value = "每页显示条数", paramType = "query", dataType = "int")})
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    public ResultModel<PageCL<StrategyForm>> getStrategyList(@PathVariable("user_id") String userId,
                                       @RequestParam(value = "page_num", defaultValue = "0", required = false) int pageNum,
                                       @RequestParam(value = "page_size", defaultValue = "15", required = false) int pageSize){
        if (blackEnable) {
            List<BlacklistMicroService> blacklistMicroServices = blacklistMicroServiceService.listByType(BlacklistTypeEnum.MENU);
            long count = blacklistMicroServices.stream().filter(p -> p.getId().equals("6c6dafc2f8127287e43dee8f3c9ec412")).count();
            if (count > 0) {
                return ResultModel.success("Strategy_Hidden", null);
            }
        }
        return ResultModel.success("查询策略成功", strategyService.findPage(userId,pageNum,pageSize));
    }


    @ApiOperation("编辑策略")
    @PutMapping()
    @LogRecordAnnotation(value = "编辑策略", detail = "编辑策略{{#_ret.resource}}", resourceId = "{{#strategyForm.id}}", resource = "{{#strategyForm.name}}")
    @ResourceAuth(handler = StrategyAuthHandler.class, resources = "#strategyForm.id")
    public ResultModel updateStrategy (@RequestBody StrategyForm strategyForm) {
        JSONObject jsonObject = strategyForm.getPolicy();
        if (jsonObject.toString().length() > 32768)
            throw new BusinessException("策略长度超过最大长度32768");
        strategyService.lambdaUpdate().set(Objects.nonNull(strategyForm.getName()), StrategyPO::getName, strategyForm.getName())
                .set(Objects.nonNull(strategyForm.getPolicy()), StrategyPO::getPolicy, jsonObject.toString())
                .set(Objects.nonNull(strategyForm.getDescription()), StrategyPO::getDescription, strategyForm.getDescription())
                .eq(StrategyPO::getId, strategyForm.getId())
                .update();
        defaultRedisTemplate.opsForValue().set(CommonInstance.IAM_POLICIES+strategyForm.getId(), jsonObject.toString());
        return ResultModel.success("编辑策略成功", strategyForm.getName(),"");
    }

    /*private void syncStrategy2Redis(String userId) {
        List<UserStrategyEntity> userStrategies= userStrategyService.lambdaQuery()
                .eq(UserStrategyEntity::getUserId, userId)
                .list();
        List<String> strategyIds = userStrategies.stream().map(UserStrategyEntity::getStrategyId).collect(Collectors.toList());
        StringBuilder ids = new StringBuilder();
        for (int i = 0; i < strategyIds.size(); i++) {
            ids.append(strategyIds.get(i));
            if (i < strategyIds.size() -1) {
                ids.append(",");
            }
        }
        JSONObject jsonIds = new JSONObject();
        jsonIds.put("policy", ids);
        defaultRedisTemplate.opsForValue().set(CommonInstance.IAM_POLICYDB_USERS+userId, jsonIds.toString());

    }*/
}
