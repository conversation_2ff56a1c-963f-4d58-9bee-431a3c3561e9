package com.sugon.cloud.iam.api.entity.keystone;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.Map;

@Data
@TableName(value = "trust")
@ApiModel(value = "创建或更新租户授权")
public class TrustCreateOrUpdate {
    @ApiModelProperty("主键")
    private String id;
    @JsonProperty(value = "trustor_user_id")
    @ApiModelProperty("委托用户ID")
    private String trustorUserId;
    @JsonProperty(value = "trustee_user_id")
    @ApiModelProperty("受托人用户ID")
    private String trusteeUserId;
    @JsonProperty(value = "project_id")
    @ApiModelProperty("项目ID")
    private String projectId;
    @ApiModelProperty("委托类型")
    private boolean impersonation;
    @JsonProperty(value = "deletedAt")
    @ApiModelProperty("删除时间")
    private Date deletedAt;
    @JsonProperty(value = "expires_at")
    @ApiModelProperty("过期时间")
    private Date expiresAt;
    @JsonProperty(value = "remaining_uses")
    @ApiModelProperty("剩余使用次数")
    private int remainingUses;
    @ApiModelProperty("扩展信息")
    private String extra;
    @JsonProperty(value = "expires_at_int")
    @ApiModelProperty("过期时间")
    private long expiresAtInt;
    @ApiModelProperty("角色")
    private Object roles;
    @JsonProperty(value = "allow_redelegation")
    @ApiModelProperty("是否允许重委托")
    private boolean allowRedelegation;
}
