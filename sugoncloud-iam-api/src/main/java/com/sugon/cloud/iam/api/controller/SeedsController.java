package com.sugon.cloud.iam.api.controller;



import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.api.ApiController;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ft.otp.core.ReturnResult;
import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.iam.api.entity.SeedEntity;
import com.sugon.cloud.iam.api.service.SeedsService;
import java.io.IOException;
import javax.xml.parsers.ParserConfigurationException;

import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;
import org.xml.sax.SAXException;

/**
 * (Seeds)表控制层
 *
 * <AUTHOR>
 * @since 2024-08-07 10:45:22
 */
@RestController
@RequestMapping("/api/seeds")
@ConditionalOnExpression("${mfa.enabled} &&'${mfa.type}'.contains('ftng')")
@DocumentIgnore
public class SeedsController extends ApiController {
    /**
     * 服务对象
     */
    @Resource
    private SeedsService seedsService;

    /**
     * 分页查询所有数据
     *
     *
     * @param page 分页对象
     * @param seeds 查询实体
     * @return 所有数据
     */
    @GetMapping
    public ResultModel selectAll(Page<SeedEntity> page, SeedEntity seeds) {
        return ResultModel.success(this.seedsService.page(page, new QueryWrapper<>(seeds)));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("{id}")
    public ResultModel selectOne(@PathVariable Serializable id) {
        return ResultModel.success(this.seedsService.getById(id));
    }

    /**
     * 新增数据
     *
     * @param seeds 实体对象
     * @return 新增结果
     */
    @PostMapping
    public ResultModel insert(@RequestBody SeedEntity seeds) {
        return ResultModel.success(this.seedsService.save(seeds));
    }

    /**
     * 修改数据
     *
     * @param seeds 实体对象
     * @return 修改结果
     */
    @PutMapping
    public ResultModel update(@RequestBody SeedEntity seeds) {
        return ResultModel.success(this.seedsService.updateById(seeds));
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @DeleteMapping
    public ResultModel delete(@RequestParam("idList") List<Long> idList) {
        return ResultModel.success(this.seedsService.removeByIds(idList));
    }

    @PostMapping("/upload")
    public ResultModel<ReturnResult> upload(@RequestParam("file1") MultipartFile file1,
                                            @RequestParam("file2") MultipartFile file2) throws IOException, ParserConfigurationException, SAXException {
        ReturnResult upload = this.seedsService.upload(file1, file2);
        logger.info("upload:" + upload);
        return ResultModel.success("","导入密钥成功");
    }
}

