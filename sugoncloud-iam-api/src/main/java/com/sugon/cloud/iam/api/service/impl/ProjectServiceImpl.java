package com.sugon.cloud.iam.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sugon.cloud.common.constants.HeaderParamConstant;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.common.mapper.ModelMapper;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.common.utils.UUIDUtil;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.common.RecursionDeptIdUtils;
import com.sugon.cloud.iam.api.config.MultiRegionEsConfig;
import com.sugon.cloud.iam.api.entity.BlacklistMicroService;
import com.sugon.cloud.iam.api.entity.Department;
import com.sugon.cloud.iam.api.entity.GlobalsettingsEntity;
import com.sugon.cloud.iam.api.entity.MultiEsEntity;
import com.sugon.cloud.iam.api.entity.UserRoleMapping;
import com.sugon.cloud.iam.api.entity.keystone.Project;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.exception.BusinessException;
import com.sugon.cloud.iam.api.mapper.DepartmentMapper;
import com.sugon.cloud.iam.api.mapper.ProjectMapper;
import com.sugon.cloud.iam.api.mapper.UserMapper;
import com.sugon.cloud.iam.api.mapper.UserRoleMappingMapper;
import com.sugon.cloud.iam.api.service.BlacklistMicroServiceService;
import com.sugon.cloud.iam.api.service.GlobalsettingsService;
import com.sugon.cloud.iam.api.service.OperationsService;
import com.sugon.cloud.iam.api.service.ProjectService;
import com.sugon.cloud.iam.api.service.QuotaService;
import com.sugon.cloud.iam.api.service.UpdateProjectByOperationService;
import com.sugon.cloud.iam.api.utils.DepartmentPathUtils;
import com.sugon.cloud.iam.api.utils.HttpTemplateUtils;
import com.sugon.cloud.iam.api.utils.JacksonUtils;
import com.sugon.cloud.iam.api.vo.CreateOrUpdateProjectVO;
import com.sugon.cloud.iam.api.vo.ProjectContractOptionQueryParamVO;
import com.sugon.cloud.iam.api.vo.ProjectContractVO;
import com.sugon.cloud.iam.api.vo.ProjectResourceVO;
import com.sugon.cloud.iam.api.vo.UpdateProjectOperationTypeVO;
import com.sugon.cloud.iam.common.constants.ProjectType;
import com.sugon.cloud.iam.common.enums.BlacklistTypeEnum;
import com.sugon.cloud.iam.common.model.vo.CreateWithDepartProjectVO;
import com.sugon.cloud.iam.common.model.vo.DepartmentTreeDetailVO;
import com.sugon.cloud.iam.common.model.vo.ProjectDetailVO;
import com.sugon.cloud.iam.common.model.vo.ProjectUpdateByOperationVO;
import com.sugon.cloud.log.client.context.LogRecordContext;
import com.sugon.cloud.log.client.model.CommonBusinessException;
import com.sugon.cloud.log.client.service.MessageFeignService;
import com.sugon.cloud.log.common.model.vo.MessageVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class ProjectServiceImpl extends ServiceImpl<ProjectMapper, Project> implements ProjectService {

    private final ProjectMapper projectMapper;
    private final UserMapper userMapper;
    private final DepartmentMapper departmentMapper;
    private final UserRoleMappingMapper userRoleMappingMapper;
    private final QuotaService quotaService;
    private final ModelMapper mapper;
    private final MessageFeignService messageFeignService;
    @Autowired
    private HttpServletRequest request;
    private final Map<String, UpdateProjectByOperationService> updateProjectByOperationServiceMap;
    @Value("${operations.enabled:true}")
    private boolean operationsEnabled;
    @Value("${black.enable:true}")
    private boolean blackEnable;
    private final OperationsService operationsService;
    private final GlobalsettingsService globalsettingsService;
    private final BlacklistMicroServiceService blacklistMicroServiceService;
    private final MultiRegionEsConfig multiRegionEsConfig;


    @Override
    public Project findByUserIdAndRoleId(String userId, String roleId) {
        return projectMapper.findByUserIdAndRoleId(userId, roleId);
    }

    @Override
    public List<Project> findByUserId(String userId) {
        return projectMapper.findByUserId(userId);
    }

    @Override
    public void insert(CreateOrUpdateProjectVO createOrUpdateProjectVO) {
        createOrUpdateProjectVO.setId(UUIDUtil.get32UUID());
        LogRecordContext.putVariable("id", createOrUpdateProjectVO.getId());
        Project project = checkSaveOrUpdateBusinessParam(createOrUpdateProjectVO);
        project.setName(createOrUpdateProjectVO.getName() + "_" + UUIDUtil.get8UUID());
        if (operationsEnabled) {
            project.setOperationApproveType("REQUIRE");
        }
        this.projectMapper.insert(project);
    }

    @Override
    public CreateWithDepartProjectVO insertProjectWithoutDepart(CreateWithDepartProjectVO createWithDepartProjectVO) {
        List<Project> projects = this.projectMapper.selectList(new LambdaQueryWrapper<Project>().eq(Project::getName, createWithDepartProjectVO.getName()));
        if (!CollectionUtils.isEmpty(projects)) {
            createWithDepartProjectVO.setId(projects.get(0).getId());
            return createWithDepartProjectVO;
        }
        createWithDepartProjectVO.setId(UUIDUtil.get32UUID());
        Project project = mapper.map(createWithDepartProjectVO, Project.class);
        project.setDomain(false);
        project.setEnabled(true);
        project.setDomainId("<<keystone.domain.root>>");
//        project.setParentId(department.getDomainId());
        project.setAlias(createWithDepartProjectVO.getName());
        // 防止修改时候被修改（为null时候，update 不会更新）
        project.setName(null);

        project.setName(createWithDepartProjectVO.getName() + "_" + UUIDUtil.get8UUID());
        this.projectMapper.insert(project);
        return createWithDepartProjectVO;
    }

    @Override
    public void update(CreateOrUpdateProjectVO createOrUpdateProjectVO) {
        // 判断项目计费类型是否允许修改
        if (operationsEnabled) {
            ResultModel<PageCL<ProjectContractVO>> list = operationsService.list(1, 10000, null, null, null, null
                    , null, null, null, createOrUpdateProjectVO.getId()
                    , null, null, null);
            PageCL<ProjectContractVO> content = list.getContent();
            List<ProjectContractVO> list1 = content.getList();
            List<String> meterTypes = createOrUpdateProjectVO.getMeterTypes();
            List<ProjectContractVO> collect = list1.stream().filter(p -> !meterTypes.contains(p.getMeterType())).collect(Collectors.toList());
            if (!collect.isEmpty()) {
                throw new BusinessException("该项目下存在合同，不允许修改计费类型");
            }
        }
        Project byId = getById(createOrUpdateProjectVO.getId());
        if (StrUtil.isNotBlank(byId.getType()) && !byId.getType().equals(createOrUpdateProjectVO.getType())) {
            throw new BusinessException("该项目已经有项目类型，不允许修改其项目类型");
        }
        this.projectMapper.updateById(checkSaveOrUpdateBusinessParam(createOrUpdateProjectVO));
    }

    @Override
    @Transactional
    public ResultModel deleteById(String id, String regionId) {
        ProjectDetailVO projectDetailVO = this.queryById(id);
        if (Objects.isNull(projectDetailVO)) {
            throw new BusinessException("删除失败，该id对应的项目不存在");
        }
        if ("admin-inner-project".equals(projectDetailVO.getId())) {
            throw new BusinessException("默认项目禁止删除");
        }
        //Long count = esSearchService.searchResourceCount(regionId, id, null);
        Long count = searchResourceCount(id);
        // 解绑项目合同
        if (operationsEnabled) {
            ResultModel<PageCL<ProjectContractVO>> list = operationsService.list(1, 1, null, null, null, null
                    , null, null, null, id
                    , null, null, null);
            PageCL<ProjectContractVO> content = list.getContent();
            List<ProjectContractVO> list1 = content.getList();
            boolean empty = CollectionUtil.isEmpty(list1);
            if (count > 0 && !empty) {
                throw new BusinessException("该项目已申请资源并绑定了合同，不能进行删除");
            } else if (count > 0) {
                throw new BusinessException("项目中存在资源，不能删除");
            }
            if (!empty) {
                throw new BusinessException("该项目下存在合同，不允许删除");
            }

            /*BindOrUnbindContractVO bindOrUnbindContractVO = new BindOrUnbindContractVO();
            bindOrUnbindContractVO.setResourceId(id);
            bindOrUnbindContractVO.setType("project");
            contractService.unbindContracts(bindOrUnbindContractVO);*/
        } else {
            if (count > 0) {
                throw new BusinessException("项目中存在资源，不能删除");
            }
        }
        Project project = new Project();
        project.setId(id);
        // 状态删除
        project.setEnabled(false);
        projectMapper.updateById(project);
        //删除项目的配额并更新所在组织配额的使用量
        quotaService.deleteProjectQuota(projectDetailVO);
        //删除项目配置的角色
        userRoleMappingMapper.delete(new LambdaUpdateWrapper<UserRoleMapping>().eq(UserRoleMapping::getProjectId, id));

        try {
            MessageVo messageVo = new MessageVo();
            messageVo.setSvc("sugoncloud-iam-api");
            messageVo.setEmailEnable(false);
            messageVo.setProject(id);
            messageVo.setContent("项目[" + projectDetailVO.getName() + "]已被删除");
            messageFeignService.createMessage(messageVo);
        } catch (Exception e) {
            log.error("删除项目发送消息失败！", e);
        }
        return ResultModel.success("删除项目成功", projectDetailVO.getName(), null);
    }

    private Long searchResourceCount(String id) {
        String sugoncloudMonitorApi = "";
        for(MultiEsEntity met : multiRegionEsConfig.getMultiMonitorEntityList()){
            if(request.getHeader(HeaderParamConstant.REGION_ID).equals(met.getName())){
                sugoncloudMonitorApi = met.getIp() + ":" + met.getPort() + "/sugoncloud-monitor-api";
            }
        }

        // 查询monitor接口获取项目所有资源
        String url = sugoncloudMonitorApi + "/api/monitor/overview-user/all-resource?project_id={project_id}";
        if (!url.startsWith("http")) {
            url = "http://" + url;
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("project_id", id);
        long count = 0L;
        ResultModel resultModel = HttpTemplateUtils.get(url, paramMap, null, new TypeReference<ResultModel>() {});
        if (!resultModel.isSuccess()) {
            log.error("请求云监控服务失败:{}", JSONObject.toJSONString(resultModel));
            throw new BusinessException("请求云监控服务失败");
        }

        if (Objects.isNull(resultModel.getContent())) {
            return count;
        }
        List<Map<String, Object>> list = (List<Map<String, Object>>) resultModel.getContent();
        for (Map<String, Object> map : list) {
            List<Map<String, Object>> children = (List<Map<String, Object>>) map.get("children");
            for (Map<String, Object> child : children) {
                String name = (String) child.get("name");
                // 对象存储 OSS桶是用户类资源，删除项目时不统计
                if ("对象存储 OSS桶".equals(name)) {
                    continue;
                }
                List<Map<String, Object>> childList = (List<Map<String, Object>>) child.get("list");
                if ("镜像服务 IMS".equals(name)) {
                    count += childList.stream().filter(t -> "private".equals(t.get("visibility"))).count();
                } else if ("安全组 SG".equals(name) || "网络 ACL".equals(name)) {
                    count += childList.stream().filter(t -> !"default".equals(t.get("name"))).count();
                } else {
                    // 存在其他资源，立刻结束循环
                    count += childList.size();
                    break;
                }
            }
            // count已经大于，不用执行后续操作
            if (count > 0) {
                break;
            }
        }
        return count;
    }

    @Override
    public ProjectDetailVO queryById(String id) {
        LambdaQueryWrapper<Project> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Project::getId, id);
        Project project = this.getOne(queryWrapper);
        if (Objects.isNull(project)) {
            return null;
        }
        ProjectDetailVO result = mapper.map(project, ProjectDetailVO.class);
        result.setName(project.getAlias());
        // 环境未打开计费审批
        result.convertApproveType(operationsEnabled);
        return result;
    }

    @Override
    public PageCL<ProjectDetailVO> pageList(String userId, String deptId, String name, int pageNum,
                                            int pageSize, Boolean querySub, String projectType, String meterType) {
        List<String> deptIds = null;
        if (StringUtils.isNotBlank(deptId)) {
            checkDepartmentIsExist(deptId);
            deptIds = Lists.newArrayList();
            deptIds.add(deptId);
            if (Objects.nonNull(querySub) && querySub) {
                List<DepartmentTreeDetailVO> treeDepartments = departmentMapper.findTreeDepartment(deptId);
                deptIds = RecursionDeptIdUtils.getDeptIds(treeDepartments, deptIds);
            }
        }
        List<String> projectIds = null;
        boolean isAdmin = false;
        if (StringUtils.isNotBlank(userId)) {
            projectIds = getProjectIdsByUserId(userId);
            // 避免sql报错添 加一个特殊值
            if (CollectionUtils.isEmpty(projectIds)) {
                projectIds.add("-1");
            }
            // admin用户查询所有项目（project.dept_id is not null）
            if (projectIds.contains(TypeUtil.TYPE_ADMIN)) {
                isAdmin = true;
            }
        }
        IPage<Project> page = new Page(pageNum, pageSize);
        IPage<Project> projectIPage = projectMapper.selectPage(page, new QueryWrapper<Project>().lambda()
                .in(StringUtils.isNotBlank(deptId), Project::getDeptId, deptIds)
                .in(!isAdmin && StringUtils.isNotBlank(userId), Project::getId, projectIds)
                .eq(Project::getEnabled, true)
                .isNotNull(isAdmin, Project::getDeptId)
                .isNotNull(Project::getParentId)
                .like(StringUtils.isNotBlank(name), Project::getAlias, name)
                .eq(StrUtil.isNotBlank(projectType), Project::getType, projectType)
                .like(StrUtil.isNotBlank(meterType), Project::getMeterTypes, StrUtil.format("%{}%", meterType))
                .orderByDesc(Project::getCreateTime));
        List<Project> records = projectIPage.getRecords();
        //查询组织list
        List<DepartmentTreeDetailVO> departments = departmentMapper.findAllDepartmentList();
        Map<String, String> departmentIdNameMap = departments.stream()
                .collect(Collectors.toMap(DepartmentTreeDetailVO::getId, DepartmentTreeDetailVO::getName));
        // alias转为name
        for (Project project : records) {
            project.setName(project.getAlias());
        }
        List<ProjectDetailVO> projectDetailVOS = mapper.mapList(records, ProjectDetailVO.class);
        projectDetailVOS.forEach(p -> p.convertApproveType(operationsEnabled));
        String defaultResourceProject = userMapper.getUserDefaultProjectId(request.getHeader(HeaderParamConstant.USER_ID));
        for (ProjectDetailVO projectDetailVO : projectDetailVOS) {
            projectDetailVO.setDeptPath(DepartmentPathUtils.buildDeptPath(projectDetailVO.getDeptId(), departments));
            projectDetailVO.setDeptName(departmentIdNameMap.get(projectDetailVO.getDeptId()));
            if (StringUtils.isNotEmpty(defaultResourceProject)) {
                if (defaultResourceProject.equals(projectDetailVO.getId())) {
                    projectDetailVO.setCurrentUserDefault(true);
                }
            }
        }
        try {
            return new PageCL<ProjectDetailVO>().
                    getPageByPageHelper(page, projectDetailVOS);
        } catch (Exception e) {
            log.error("com.sugon.cloud.iam.api.service.impl.ProjectServiceImpl.pageList error:", e);
            throw new BusinessException("查询列表报错");
        }
    }

    @Override
    public List<ProjectDetailVO> getAllProjects() {
        List<Project> projectList = projectMapper.selectList(new QueryWrapper<Project>().lambda().eq(Project::getEnabled, true));
        projectList.forEach(record -> {
            record.setName(record.getAlias());
        });
        List<ProjectDetailVO> projectDetailVOS = mapper.mapList(projectList, ProjectDetailVO.class);
        List<DepartmentTreeDetailVO> departments = departmentMapper.findAllDepartmentList();
        for (ProjectDetailVO projectDetailVO : projectDetailVOS) {
            projectDetailVO.setDeptPath(DepartmentPathUtils.buildDeptPath(projectDetailVO.getDeptId(), departments));
            departments.stream().filter(p -> StrUtil.equals(p.getId(), projectDetailVO.getDeptId())).findFirst()
                    .ifPresent(p -> projectDetailVO.setDeptName(p.getName()));

        }
        projectDetailVOS.forEach(p -> p.convertApproveType(operationsEnabled));
        return projectDetailVOS;
    }

    /**
     * 根据userId查询所属的项目
     *
     * @param userId
     * @return
     */
    @Override
    public List<String> getProjectIdsByUserId(String userId) {
        User user = userMapper.selectById(userId);
        if (Objects.isNull(user)) {
            log.error("userId[{}]这个用户不存在", userId);
            throw new BusinessException("该用户不存在或已被删除");
        }
        String userType = user.getType();
        // 普通用户只查询自己所属的项目集合
        if (StringUtils.isBlank(userType)) {
            return userRoleMappingMapper.selectList(new QueryWrapper<UserRoleMapping>().lambda()
                            .eq(UserRoleMapping::getUserId, userId))
                    .stream().map(UserRoleMapping::getProjectId).collect(Collectors.toList());
        }
        // 组织管理员/根账号查询自己所属组织及子级组织的项目集合
        if (TypeUtil.TYPE_DEPT_MASTER.equals(userType)
                || TypeUtil.TYPE_MASTER.equals(userType)
                || CommonInstance.ORG_SYSADMIN.equals(userType)
                || CommonInstance.ORG_SECAUDITOR.equals(userType)
                || CommonInstance.ORG_SECADMIN.equals(userType)) {
            List<String> deptIds = Lists.newArrayList();
            String deptId = user.getDeptId();
            deptIds.add(deptId);
            List<DepartmentTreeDetailVO> treeDepartments = departmentMapper.findTreeDepartment(user.getDeptId());
            deptIds = RecursionDeptIdUtils.getDeptIds(treeDepartments, deptIds);
            return projectMapper.selectList(new QueryWrapper<Project>().lambda()
                            .in(Project::getDeptId, deptIds)
                            // 排除内置的project
                            .ne(Project::getId, user.getDefaultProjectId()))
                    .stream().map(Project::getId).collect(Collectors.toList());
        }
        // admin账户
        if (TypeUtil.TYPE_ADMIN.equals(userType)
                || CommonInstance.SYS_ADMIN.equals(user.getType())
                || CommonInstance.SEC_ADMIN.equals(user.getType())
                || CommonInstance.SEC_AUDITOR.equals(user.getType())) {
            return Lists.newArrayList(TypeUtil.TYPE_ADMIN);
        }
        return Lists.newArrayList();
    }

    private Project checkSaveOrUpdateBusinessParam(CreateOrUpdateProjectVO createOrUpdateProjectVO) {
        String deptId = createOrUpdateProjectVO.getDeptId();
        Department department = checkDepartmentIsExist(deptId);
        if (isNameDuplication(department,
                createOrUpdateProjectVO.getName(), createOrUpdateProjectVO.getId())) {
            throw new BusinessException("已有组织存在项目[" +
                    createOrUpdateProjectVO.getName() + "]");
        }
        Project project = mapper.map(createOrUpdateProjectVO, Project.class);
        project.setDomain(false);
        project.setEnabled(true);
        project.setDomainId(department.getDomainId());
        project.setParentId(department.getDomainId());
        project.setAlias(createOrUpdateProjectVO.getName());
        // 防止修改时候被修改（为null时候，update 不会更新）
        project.setName(null);
        return project;
    }

    private Department checkDepartmentIsExist(String deptId) {
        Department department = departmentMapper.selectById(deptId);
        if (Objects.isNull(department)) {
            throw new BusinessException("操作失败，组织不存在");
        }
        return department;
    }

    /**
     * 校验名称
     *
     * @param department
     * @param name
     * @param id
     * @return
     */
    private boolean isNameDuplication(Department department, String name, String id) {
        // 获取顶级组织
        Department parentDepartmentTemp = mapper.map(department, Department.class);
        String parentId = parentDepartmentTemp.getParentId();
        while (StringUtils.isNotBlank(parentId)) {
            parentDepartmentTemp = departmentMapper.selectById(parentId);
            parentId = parentDepartmentTemp.getParentId();
        }
        // 所有的组织ID集合
        List<String> deptIds = Lists.newArrayList();
        RecursionDeptIdUtils.getDeptIds(departmentMapper.findTreeDepartment(parentDepartmentTemp.getId()), deptIds);
        return projectMapper.selectCount(new QueryWrapper<Project>().lambda()
                .eq(Project::getEnabled, true)
                .in(Project::getDeptId, deptIds)
                .eq(Project::getAlias, name)
                .ne(StringUtils.isNotBlank(id), Project::getId, id)) > 0;
    }

    @Override
    public PageCL<ProjectDetailVO> getAllProjectsByUserType(String currentUserId, int pageNum, int pageSize, String name, String projectType, String meterType) {
        IPage<Project> page = new Page(pageNum, pageSize);
        IPage<Project> projectIPage = new Page(pageNum, pageSize);
        User user = userMapper.selectById(currentUserId);
        // 根账号
        if (TypeUtil.TYPE_MASTER.equals(user.getType())
                || TypeUtil.TYPE_DEPT_MASTER.equals(user.getType())
                || CommonInstance.ORG_SYSADMIN.equals(user.getType())
                || CommonInstance.ORG_SECAUDITOR.equals(user.getType())
                || CommonInstance.ORG_SECADMIN.equals(user.getType())) {
            List<String> deptIds = Lists.newArrayList();
            deptIds.add(user.getDeptId());
            List<DepartmentTreeDetailVO> treeDepartments = departmentMapper.findTreeDepartment(user.getDeptId());
            deptIds = RecursionDeptIdUtils.getDeptIds(treeDepartments, deptIds);
            projectIPage = projectMapper.selectPage(page, new QueryWrapper<Project>().lambda()
                    .like(StringUtils.isNotEmpty(name), Project::getAlias, name)
                    .in(Project::getDeptId, deptIds)
                    .eq(Project::getDomain, false)
                    .eq(Project::getEnabled, true)
                    .eq(StrUtil.isNotBlank(projectType), Project::getType, projectType)
                    .like(StrUtil.isNotBlank(meterType), Project::getMeterTypes, StrUtil.format("%{}%", meterType))
                    .orderByDesc(Project::getCreateTime));
        }
        // 管理员
        if (TypeUtil.TYPE_ADMIN.equals(user.getType())
                || TypeUtil.TYPE_SECURITY.equals(user.getType())
                || TypeUtil.TYPE_SYS_ADMIN.equals(user.getType())
                || TypeUtil.TYPE_SEC_ADMIN.equals(user.getType())
                || TypeUtil.TYPE_SEC_AUDITOR.equals(user.getType())) {
            projectIPage = projectMapper.selectPage(page, new QueryWrapper<Project>().lambda()
                    .like(StringUtils.isNotEmpty(name), Project::getAlias, name)
                    .eq(Project::getDomain, false)
                    .eq(Project::getEnabled, true)
                    .isNotNull(Project::getDeptId)
                    .eq(StrUtil.isNotBlank(projectType), Project::getType, projectType)
                    .like(StrUtil.isNotBlank(meterType), Project::getMeterTypes, StrUtil.format("%{}%", meterType))
                    .orderByDesc(Project::getCreateTime));
        }

        // 普通用户 bug:414212
        if (StrUtil.isBlank(user.getType())) {
            List<String> projectIds = userRoleMappingMapper.selectList(new QueryWrapper<UserRoleMapping>()
                            .lambda()
                            .eq(UserRoleMapping::getUserId, currentUserId))
                    .stream().map(UserRoleMapping::getProjectId).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
            if (CollUtil.isNotEmpty(projectIds)) {
                projectIPage = projectMapper.selectPage(page, new QueryWrapper<Project>().lambda()
                        .in(Project::getId, projectIds)
                        .like(StringUtils.isNotEmpty(name), Project::getAlias, name)
                        .eq(Project::getDomain, false)
                        .eq(Project::getEnabled, true)
                        .isNotNull(Project::getDeptId)
                        .eq(StrUtil.isNotBlank(projectType), Project::getType, projectType)
                        .like(StrUtil.isNotBlank(meterType), Project::getMeterTypes, StrUtil.format("%{}%", meterType))
                        .orderByDesc(Project::getCreateTime));
            }
        }
        //查询组织list
        List<ProjectDetailVO> projectDetailVOS = transProject(projectIPage.getRecords());
        try {
            return new PageCL<ProjectDetailVO>().
                    getPageByPageHelper(page, projectDetailVOS);
        } catch (Exception e) {
            log.error("com.sugon.cloud.iam.api.service.impl.ProjectServiceImpl.getAllProjectsByUserType error:", e);
            throw new BusinessException("通过用户类型查询项目列表");
        }
    }

    /**
     * 根据组织查询该组织以及所有子组织的所有项目
     *
     * @param departmentId 组织Id
     * @param pageNum      页码
     * @param pageSize     每页条数
     * @param name         根据名称查询
     * @param projectType
     * @param meterType
     * @return PageCL<ProjectDetailVO>
     */
    @Override
    public PageCL<ProjectDetailVO> listAllDepartment(String currentUserId, String departmentId, int pageNum, int pageSize, String name, String projectType, String meterType) {
        IPage<Project> projectIPage = new Page(pageNum, pageSize);
        List<String> projectIds = null;
        boolean isAdmin = false;
        if (StringUtils.isNotBlank(currentUserId)) {
            projectIds = getProjectIdsByUserId(currentUserId);
            // 避免sql报错添 加一个特殊值
            if (CollectionUtils.isEmpty(projectIds)) {
                projectIds.add("-1");
            }
            // admin用户查询所有项目（project.dept_id is not null）
            if (projectIds.contains(TypeUtil.TYPE_ADMIN)) {
                isAdmin = true;
            }
        }
        List<DepartmentTreeDetailVO> treeDepartments = departmentMapper.findTreeDepartmentAndOwner(departmentId);
        List<String> deptIds = Lists.newArrayList();
        deptIds = RecursionDeptIdUtils.getDeptIds(treeDepartments, deptIds);
        projectMapper.selectPage(projectIPage, new QueryWrapper<Project>().lambda()
                .in(StringUtils.isNotBlank(departmentId), Project::getDeptId, deptIds)
                .in(!isAdmin && StringUtils.isNotBlank(currentUserId), Project::getId, projectIds)
                .eq(Project::getEnabled, true)
                .isNotNull(isAdmin, Project::getDeptId)
                // bugfix:403234
                .isNotNull(Project::getParentId)
                .like(StringUtils.isNotEmpty(name), Project::getAlias, name)
                .eq(StrUtil.isNotBlank(projectType), Project::getType, projectType)
                .like(StrUtil.isNotBlank(meterType), Project::getMeterTypes, StrUtil.format("%{}%", meterType))
                .orderByDesc(Project::getCreateTime));
        //查询组织list
        List<ProjectDetailVO> projectDetailVOS = transProject(projectIPage.getRecords());
        try {
            return new PageCL<ProjectDetailVO>().
                    getPageByPageHelper(projectIPage, projectDetailVOS);
        } catch (Exception e) {
            log.error("com.sugon.cloud.iam.api.service.impl.ProjectServiceImpl.listAllDepartment error:", e);
            throw new BusinessException("通过组织查询项目列表");
        }
    }

    private List<ProjectDetailVO> transProject(List<Project> records) {
        List<DepartmentTreeDetailVO> departments = departmentMapper.findAllDepartmentList();
        List<ProjectDetailVO> projectDetailVOS = Lists.newArrayList();
        for (Project project : records) {
            ProjectDetailVO projectDetailVO = mapper.map(project, ProjectDetailVO.class);
            projectDetailVO.setName(project.getAlias());
            projectDetailVO.setDeptPath(DepartmentPathUtils.buildDeptPath(projectDetailVO.getDeptId(), departments));
            projectDetailVO.convertApproveType(operationsEnabled);
            projectDetailVOS.add(projectDetailVO);
        }
        return projectDetailVOS;
    }

    @Override
    public List<ProjectDetailVO> listContractOptions(String userId, String name, String meterType, String departmentId) {
        Department department = departmentMapper.selectById(departmentId);
        if (Objects.isNull(department)) {
            throw new CommonBusinessException("组织不存在或已被删除");
        }
        List<String> deptIds = Lists.newArrayList(departmentId);
        String parentId = department.getParentId();
        // 统一计费需要往上级找到所有的组织
        while ("contract_meter_unification".equalsIgnoreCase(meterType)
                && StringUtils.isNotBlank(parentId)) {
            department = departmentMapper.selectById(parentId);
            parentId = department.getParentId();
            deptIds.add(department.getId());
        }
        List<Project> projects = projectMapper.selectList(new QueryWrapper<Project>().lambda()
                .in(StringUtils.isNotBlank(departmentId), Project::getDeptId, deptIds)
                .eq(Project::getEnabled, true)
                .eq(Project::getOperationApproveType, "REQUIRE")
                .isNotNull(Project::getParentId)
                .like(StringUtils.isNotEmpty(name), Project::getAlias, name)
                .like(StringUtils.isNotEmpty(meterType), Project::getMeterTypes, meterType)
                .orderByDesc(Project::getCreateTime));

        return transProject(projects);
    }


    @Override
    public PageCL<ProjectContractVO> listProjectContractOptions(String projectId, String deptId, List<String> meterTypes,
                                                                String name, String code, int pageNum, int pageSize) {
        if (StringUtils.isNotBlank(projectId)) {
            Project project = this.getById(projectId);
            if (Objects.isNull(project)) {
                throw new CommonBusinessException("项目不存在或已被删除");
            }
            deptId = project.getDeptId();
            meterTypes = project.getMeterTypes();
        }
        ProjectContractOptionQueryParamVO projectContractOptionQueryParamVO = new ProjectContractOptionQueryParamVO();
        projectContractOptionQueryParamVO.setPageNum(pageNum);
        projectContractOptionQueryParamVO.setPageSize(pageSize);
        projectContractOptionQueryParamVO.setMeterTypes(meterTypes);
        projectContractOptionQueryParamVO.setName(name);
        projectContractOptionQueryParamVO.setCode(code);
        // 如果有统一计费项，需要找到项目所在根账号下的的所有统一计费合同
        if (meterTypes != null) {
            if (meterTypes.contains("contract_meter_unification")) {
                List<String> deptIds = Lists.newArrayList();
                deptIds.add(deptId);
                Department departmentTemp = departmentMapper.selectById(deptId);
                String parentId = departmentTemp.getParentId();
                while (StringUtils.isNotBlank(parentId)) {
                    departmentTemp = departmentMapper.selectById(parentId);
                    parentId = departmentTemp.getParentId();
                    deptIds.add(departmentTemp.getId());
                }
                projectContractOptionQueryParamVO.setUnificationOrgIds(deptIds);
            }
            if (meterTypes.contains("contract_meter_contract")) {
                projectContractOptionQueryParamVO.setContractOrgId(deptId);
            }
        }

        return operationsService.projectContractOptions(projectContractOptionQueryParamVO).getContent();
    }

    @Override
    public void extension(String id, String extensionDate) {
        Project project = this.getById(id);
        if (Objects.isNull(project)) {
            throw new CommonBusinessException("项目不存在或已被删除");
        }
        String alias = project.getAlias();
        List<String> testTypes = Lists.newArrayList(ProjectType.TEST);
        if (!testTypes.contains(project.getType())) {
            throw new CommonBusinessException("非测试项目不需要延期", alias);
        }
        DateTime extension = DateUtil.parse(extensionDate, "yyyy-MM-dd");
        if (extension.isBeforeOrEquals(project.getEndTime())) {
            throw new CommonBusinessException("延期时间需要在当前测试到期时间之后", alias);
        }
        this.lambdaUpdate()
                .set(Project::getEndTime, extensionDate)
                .eq(Project::getId, id)
                .update();
    }

    @Override
    public void updateProjectByOperation(String id, ProjectUpdateByOperationVO projectUpdateByOperationVO) {
        Project project = this.getById(id);
        if (Objects.isNull(project)) {
            throw new CommonBusinessException("项目不存在或已被删除");
        }
        String action = projectUpdateByOperationVO.getAction();
        UpdateProjectByOperationService updateProjectByOperationService = updateProjectByOperationServiceMap.get(action);
        if (Objects.isNull(updateProjectByOperationService)) {
            updateProjectByOperationService = updateProjectByOperationServiceMap.get(ProjectType.COMMON_APPLY);
        }
        updateProjectByOperationService.apply(id, projectUpdateByOperationVO);
    }

    @Override
    public void updateOperationApproveType(UpdateProjectOperationTypeVO vo) {
        Assert.isTrue(operationsEnabled, "环境未开启计费功能");
        // 判断项目下是否有还在进行中的流程，有则不允许关闭
        if (!"REQUIRE".equals(vo.getOperationApproveType())) {
            ResultModel resultModel = operationsService.getAllApplyListByProjectId(vo.getId(), "PROCESS");
            List list = (List) resultModel.getContent();
            Assert.isFalse(CollUtil.isNotEmpty(list), "项目下还有正在审批中的流程，请先完成所有流程，再关闭计费审批");
        }
        Project project = this.getById(vo.getId());
        if (Objects.isNull(project)) {
            throw new CommonBusinessException("项目不存在或已被删除");
        }
        project.setOperationApproveType(vo.getOperationApproveType());
        LogRecordContext.putVariable("project", project);
        LogRecordContext.putVariable("approveType", "REQUIRE".equals(vo.getOperationApproveType()) ? "开启" : "关闭");
        projectMapper.updateById(project);
    }

    @Override
    public List<ProjectResourceVO> getProjectResourceLink() {
        GlobalsettingsEntity entity = globalsettingsService.getGlobalsettingsEntity("micro_service", "micro_service_list_link");
        if (Objects.isNull(entity) || StrUtil.isBlank(entity.getPolicyDocument())) {
            return null;
        }
        String document = entity.getPolicyDocument();
        List<ProjectResourceVO> result = JacksonUtils.parseObject(document, new TypeReference<List<ProjectResourceVO>>() {});
        List<String> blackMenuIds = Collections.emptyList();
        if (blackEnable) {
            List<BlacklistMicroService> blacklistMicroServices = blacklistMicroServiceService.listByTypes(Collections.singletonList(BlacklistTypeEnum.MENU));
            blackMenuIds = blacklistMicroServices.stream().filter(p -> BlacklistTypeEnum.MENU.equals(p.getType())).map(BlacklistMicroService::getId).collect(Collectors.toList());
        }
        if (CollUtil.isEmpty(blackMenuIds)) {
            return  result;
        }
        for (ProjectResourceVO vo : result) {
            Iterator<ProjectResourceVO.ResourceDetail> iterator = vo.getResources().iterator();
            while (iterator.hasNext()) {
                ProjectResourceVO.ResourceDetail next = iterator.next();
                if (StrUtil.isBlank(next.getBlackId())) {
                    continue;
                }
                String[] blackIds = next.getBlackId().split(",");
                for (String blackId : blackIds) {
                    if (blackMenuIds.contains(blackId)) {
                        iterator.remove();
                    }
                }
            }
        }
        return result;
    }
}
