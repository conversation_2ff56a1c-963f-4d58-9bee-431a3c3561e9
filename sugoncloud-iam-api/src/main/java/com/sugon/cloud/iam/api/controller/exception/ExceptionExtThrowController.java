package com.sugon.cloud.iam.api.controller.exception;

import com.sugon.cloud.common.annotations.DocumentIgnore;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 异常重定向controller
 */
@RestController
@DocumentIgnore
public class ExceptionExtThrowController {
    /**
     * 重新抛出异常
     */
    @GetMapping("/error/extThrow")
    public void getRethrow(HttpServletRequest request) throws Exception {
        throw ((Exception) request.getAttribute("filter.error"));
    }

    /**
     * 重新抛出异常
     */
    @PostMapping("/error/extThrow")
    public void postRethrow(HttpServletRequest request) throws Exception {
        throw ((Exception) request.getAttribute("filter.error"));
    }

    @PutMapping("/error/extThrow")
    public void putRethrow(HttpServletRequest request) throws Exception {
        throw ((Exception) request.getAttribute("filter.error"));
    }

    @DeleteMapping("/error/extThrow")
    public void deleteRethrow(HttpServletRequest request) throws Exception {
        throw ((Exception) request.getAttribute("filter.error"));
    }
}
