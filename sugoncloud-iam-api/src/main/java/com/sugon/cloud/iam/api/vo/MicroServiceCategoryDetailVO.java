package com.sugon.cloud.iam.api.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sugon.cloud.iam.common.model.vo.MicroServiceDetailVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * (MicroService)实体类
 *
 * <AUTHOR>
 * @since 2021-04-15 09:35:33
 */
@Data
@ApiModel("产品微服务分类")
public class MicroServiceCategoryDetailVO {
    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "名称")
    @NotBlank(message = "微服务类别名称不能为空")
    private String name;
    @ApiModelProperty(value = "描述")
    private String description;
    @ApiModelProperty(value = "创建时间")
    @JsonProperty("create_time")
    private Date createTime;
    @ApiModelProperty(value = "更新时间")
    @JsonProperty("modify_time")
    private Date modifyTime;
    @TableField("`order`")
    private Double order;
    @ApiModelProperty(value = "隐藏分类")
    @JsonProperty("nav_hidden")
    private Boolean navHidden;
    @ApiModelProperty(value = "类型")
    private String type;
    @ApiModelProperty(value = "子分类")
    private List<MicroServiceDetailVO> children;
}
