package com.sugon.cloud.iam.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sugon.cloud.iam.api.entity.UserAccessEntity;
import com.sugon.cloud.iam.api.mapper.UserAccessMapper;
import com.sugon.cloud.iam.api.service.UserAccessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class UserAccessServiceImpl extends ServiceImpl<UserAccessMapper, UserAccessEntity> implements UserAccessService {

    @Resource
    private UserAccessMapper userAccessMapper;

    @Override
    public UserAccessEntity getAccessByUserId(String userId) {
        LambdaQueryWrapper<UserAccessEntity> wrapper = new LambdaQueryWrapper<UserAccessEntity>()
                .eq(UserAccessEntity::getUserId, userId)
                .eq(UserAccessEntity::getIsDelete, Boolean.FALSE);

        return userAccessMapper.selectOne(wrapper);
    }

    @Override
    public void updateAccessById(UserAccessEntity userAccessEntity) {
        LambdaUpdateWrapper<UserAccessEntity> wrapper = new LambdaUpdateWrapper<UserAccessEntity>()
                .eq(UserAccessEntity::getId, userAccessEntity.getId())
                .set(UserAccessEntity::getStartDate, userAccessEntity.getStartDate())
                .set(UserAccessEntity::getEndDate, userAccessEntity.getEndDate())
                .set(UserAccessEntity::getLimitFlag, userAccessEntity.getLimitFlag())
                .set(UserAccessEntity::getLimitTime, userAccessEntity.getLimitTime());
        userAccessMapper.update(null, wrapper);
        return;
    }
}
