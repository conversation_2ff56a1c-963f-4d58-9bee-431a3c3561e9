package com.sugon.cloud.iam.api.service;

/**
 * @Author: yang<PERSON><PERSON>
 * @Date: 2025/4/18 15:37
 * @Description: 推送监控服务
 */
public interface MonitorService {

    /**
     * 用户锁定推送监控
     */
    void userLockAlarm(String userId, String userName, String reason, int value);

    /**
     * 登录失败用户锁定推送监控
     */
    void userLockAlarm(String userName, String reason, int value);

    /**
     * 用户解锁推送监控
     */
    void userUnLockAlarm(String userId, String userName, String reason);

    /**
     * 用户解锁推送指标
     *
     */
    void userUnLockAlarm(String userName, String reason);
}
