package com.sugon.cloud.iam.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@TableName("license_info")
public class LicenseInfoEntity {
	private static final long serialVersionUID = -5804179385700352317L;

	@TableId(type = IdType.UUID)
	private String id;

	@JsonProperty("json_value")
	private String jsonValue;

	@JsonProperty("effective_flag")
	private int effectiveFlag = 1;//1有效，  2 无效

	@JsonProperty("create_time")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date createTime;

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getJsonValue() {
		return jsonValue;
	}

	public void setJsonValue(String jsonValue) {
		this.jsonValue = jsonValue;
	}

	public int getEffectiveFlag() {
		return effectiveFlag;
	}

	public void setEffectiveFlag(int effectiveFlag) {
		this.effectiveFlag = effectiveFlag;
	}
}
