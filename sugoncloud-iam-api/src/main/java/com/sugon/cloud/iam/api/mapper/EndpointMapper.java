package com.sugon.cloud.iam.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sugon.cloud.iam.api.entity.keystone.Endpoint;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EndpointMapper extends BaseMapper<Endpoint> {
    List<Endpoint> findByServiceId(String id);

    Endpoint findByServiceNameAndRegionId (String serviceName, String regionId);
}
