package com.sugon.cloud.iam.api.service.impl;

import cn.com.infosec.netpass.authapi.CardAuthManager;
import cn.com.infosec.netpass.common.base.QueryCardInfo;
import cn.com.infosec.netpass.managementapi.ManagementAPI;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.common.mapper.ModelMapper;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.common.utils.UUIDUtil;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.common.MFAResultCodeTransUtil;
import com.sugon.cloud.iam.api.common.SecretCertificate;
import com.sugon.cloud.iam.api.config.MFAMappingJackson2HttpMessageConverter;
import com.sugon.cloud.iam.api.entity.SeedEntity;
import com.sugon.cloud.iam.api.entity.UserAuthorizeSettings;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.enums.InfoSecCardStatusEnum;
import com.sugon.cloud.iam.api.enums.UserAuthorizeType;
import com.sugon.cloud.iam.api.mapper.UserAuthorizeSettingsMapper;
import com.sugon.cloud.iam.api.mapper.UserMapper;
import com.sugon.cloud.iam.api.service.MFAAuthorizeService;
import com.sugon.cloud.iam.api.service.SeedsService;
import com.sugon.cloud.iam.api.service.UserAuthorizeSettingsService;
import com.sugon.cloud.iam.api.service.UserService;
import com.sugon.cloud.iam.api.utils.JacksonUtils;
import com.sugon.cloud.iam.api.vo.MFADetailVO;
import com.sugon.cloud.iam.api.vo.MfaResultVO;
import com.sugon.cloud.iam.common.model.vo.UserViewVO;
import com.sugon.cloud.log.client.model.CommonBusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.ssl.TrustStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.PostConstruct;
import javax.net.ssl.SSLContext;
import java.io.ByteArrayOutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URLDecoder;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/05/16 14:10
 **/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
@RefreshScope
public class MFAAuthorizeServiceImpl implements MFAAuthorizeService {

    @Value("${user.authorize.mfa.server}")
    private String mfaOTPServer;
    @Value("${mfa.type}")
    private String mfaType;
    @Value("${mfa.enabled}")
    private boolean mfaEnabled;
    @Value("${mfa.swxa.strategy:SM3}")
    private String strategy;
    @Value("${mfa.swxa.wordSource:1}")
    private String wordSource;
    @Value("${mfa.swxa.companyId:100022}")
    private String companyId;
    private final UserMapper userMapper;
    private final UserAuthorizeSettingsMapper userAuthorizeSettingsMapper;
    private RestTemplate mfaRestTemplate;
    private RestTemplate httpsRestTemplate;
    private final String resultCodeKey = "resultCode";
    private final String resultDataKey = "resultData";
    private final UserService userService;
    private final ModelMapper modelMapper;
    private final UserAuthorizeSettingsService userAuthorizeSettingsService;
    @Autowired
    private SeedsService seedsService;
    @PostConstruct
    public void initMFARestTemplate() {
        HttpComponentsClientHttpRequestFactory HTTP_REQUEST_FACTORY = new HttpComponentsClientHttpRequestFactory();
        HTTP_REQUEST_FACTORY.setConnectTimeout(5000);
        // 自定义超时时间
        HTTP_REQUEST_FACTORY.setReadTimeout(30000);
        mfaRestTemplate = new RestTemplate(HTTP_REQUEST_FACTORY);
        mfaRestTemplate.getMessageConverters().add(new MFAMappingJackson2HttpMessageConverter());
    }

    @PostConstruct
    public void initHttpsRestTemplate() throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
        TrustStrategy acceptingTrustStrategy = (x509Certificates, authType) -> true;
        SSLContext sslContext = SSLContexts.custom()
                .loadTrustMaterial(null, acceptingTrustStrategy)
                .build();
        SSLConnectionSocketFactory sslFactory = new SSLConnectionSocketFactory(
                sslContext, new NoopHostnameVerifier());
        CloseableHttpClient httpClient = HttpClients.custom()
                .setSSLSocketFactory(sslFactory)
                .build();
        HttpComponentsClientHttpRequestFactory factory =
                new HttpComponentsClientHttpRequestFactory();
        factory.setConnectTimeout(3600000);
        factory.setReadTimeout(3600000);
        factory.setHttpClient(httpClient);
        httpsRestTemplate = new RestTemplate(factory);
    }

    @Override
    public MFADetailVO get(String userId, boolean queryQr) {
        User user = checkUserExist(userId);

        UserAuthorizeSettings userAuthorizeSettings = userAuthorizeSettingsMapper.selectOne(new LambdaQueryWrapper<UserAuthorizeSettings>()
                .eq(UserAuthorizeSettings::getUserId, userId)
                .eq(UserAuthorizeSettings::getEnabled,true)
                .eq(UserAuthorizeSettings::getType, UserAuthorizeType.MFA));
        if (Objects.isNull(userAuthorizeSettings)) {
            return null;
        }
        MFADetailVO mfaDetailVO = new MFADetailVO();
        mfaDetailVO.setUserId(userId);
        mfaDetailVO.setTokenSN(userAuthorizeSettings.getExtra());
        if (queryQr && !SecretCertificate.SMS.equals(mfaType) && !SecretCertificate.EMAIL.equals(mfaType)) {
            // 查询
            Map<String, Object> genAcData = getUrlParam("genAcData");
            genAcData.put("tokenSN", mfaDetailVO.getTokenSN());
            genAcData.put("userId", user.getName());
            try {
                String genAcDataResult = null;
                if (mfaOTPServer.startsWith("https")) {
                    genAcDataResult = httpsRestTemplate.getForObject(uriWrapper(mfaOTPServer, genAcData), String.class);
                } else {
                    genAcDataResult = mfaRestTemplate.getForObject(uriWrapper(mfaOTPServer, genAcData), String.class);
                }

                Integer resultCode = JSONObject.parseObject(genAcDataResult).getInteger(resultCodeKey);
                String msg = MFAResultCodeTransUtil.errCodeMap.get(resultCode);
                if (StringUtils.isNotBlank(msg)) {
                    throw new CommonBusinessException(msg);
                }
                mfaDetailVO.setQr(getQRCodeImage(JSONObject.parseObject(genAcDataResult).getString(resultDataKey)));
            } catch (Exception e) {
                log.error("调用otp server失败", e);
            }
        }
        return mfaDetailVO;
    }

    /**
     * 1 查询opt server有没有此用户
     * 1.1 用户不存在，直接调用绑定并创建的接口
     * 1.2 用户存在，需要判断是否绑定了令牌
     * 1.2.1 已绑定，直接返回令牌号
     * 1.2.2 未绑定，绑定后返回令牌号
     * 2 录入数据库
     *
     * @param userId 用户ID
     * @param login
     * @param danger
     * @return 令牌号
     */
    @Override
    public MfaResultVO open(String userId, String certificate, Integer operateType, boolean login, boolean danger) {
        User user = checkUserExist(userId);
        UserAuthorizeSettings userAuthorizeSettingsCheck = userAuthorizeSettingsMapper.selectOne(new LambdaQueryWrapper<UserAuthorizeSettings>()
                .eq(UserAuthorizeSettings::getUserId, userId)
                .eq(UserAuthorizeSettings::getType, UserAuthorizeType.MFA));
        if (Objects.nonNull(userAuthorizeSettingsCheck)) {
            // 如果是短信和邮箱认证的话，就不必抛异常了
            if ("sms".equals(mfaType) || "email".equals(mfaType)) {
                Map<String, Boolean> detailMap = new HashMap<>();
                detailMap.put("login", login);
                detailMap.put("danger", danger);
                userAuthorizeSettingsCheck.setExtra(JacksonUtils.writeAsJsonString(detailMap));
                userAuthorizeSettingsMapper.updateById(userAuthorizeSettingsCheck);
                subAccountHandler(user, userAuthorizeSettingsCheck);
                MfaResultVO mfaResultVO = new MfaResultVO();
                mfaResultVO.setUserName(user.getName());
                mfaResultVO.setMfaResult(CommonInstance.ZERO);
                return mfaResultVO;
            }
            throw new CommonBusinessException("用户已开通认证", user.getName());
        }
        MfaResultVO mfaResultVO = new MfaResultVO();
        mfaResultVO.setUserName(user.getName());
        String tokenSN = null;

        if(mfaEnabled) {
            if ("fisherman".equals(mfaType) || "dean".equals(mfaType)
                    || "swxa".equals(mfaType) || "aolian".equals(mfaType)) {
                // 录入证书
                tokenSN = Base64.decodeStr(certificate);
//            }else if ("dean".equals(mfaType)){
//                tokenSN = Base64.decodeStr(certificate);
            } else if ("infoSec".equals(mfaType)){
                // 如果是信安世纪的双因子的话，需要先查询当前令牌是否绑定
                try {
                    tokenSN = Base64.decodeStr(certificate);
                    QueryCardInfo cardInfo = CardAuthManager.getCardInfo(tokenSN);
                    log.debug("信安世纪令牌信息，令牌：{}，令牌信息:{}", tokenSN, cardInfo);
                    if (!InfoSecCardStatusEnum.UNUSE.getCode().equals(cardInfo.getStatus()) && !InfoSecCardStatusEnum.NORMAL.getCode().equals(cardInfo.getStatus())) {
                        throw new CommonBusinessException("当前令牌状态有问题，请联系管理员");
                    }
                    // 如果当前令牌是未使用状态，查看当前的操作类型，如果是0-mfa开启，则获取激活码返回，如果是1-激活令牌，则进行激活，然后完成用户绑定
                    if (InfoSecCardStatusEnum.UNUSE.getCode().equals(cardInfo.getStatus())) {
                        if (Objects.isNull(operateType) || CommonInstance.ZERO.equals(operateType)) {
                            String activeCode = ManagementAPI.getActiveCode(tokenSN);
                            log.debug("信安世纪令牌信息，令牌：{}，激活码:{}", tokenSN, activeCode);
                            mfaResultVO.setMfaResult(CommonInstance.ONE);
                            mfaResultVO.setActiveCode(activeCode);
                            return mfaResultVO;
                        } else if (CommonInstance.ONE.equals(operateType)) {
                            CardAuthManager.activeCard(tokenSN);
                        }
                    }
                }catch (Exception e) {
                    log.error("信安世纪双因子绑定流程发生异常", e);
                    throw new CommonBusinessException("MFA服务端不可用，请联系管理员", user.getName());
                }


            }else if ("opt-server".equals(mfaType)) {
                // 查询
                Map<String, Object> urlParams = getUrlParam("getBindToken");
                urlParams.put("userId", user.getName());
                try {
                    String result = null;
                    if (mfaOTPServer.startsWith("https")) {
                        result = httpsRestTemplate.getForObject(uriWrapper(mfaOTPServer, urlParams), String.class);
                    } else {
                        result = mfaRestTemplate.getForObject(uriWrapper(mfaOTPServer, urlParams), String.class);
                    }
                    log.debug("call opt server result");
                    log.debug(result);
                    Integer resultCode = JSONObject.parseObject(result).getInteger(resultCodeKey);

                    if (resultCode == 12) {
                        //1.1 用户不存在，直接调用绑定并创建的接口
                        tokenSN = bindMobile(user.getName());
                    } else {
                        String msg = MFAResultCodeTransUtil.errCodeMap.get(resultCode);
                        if (StringUtils.isNotBlank(msg)) {
                            throw new CommonBusinessException(msg);
                        }
                        // 1.2 用户存在，需要判断是否绑定了令牌
                        String tokenSnRemote = JSONObject.parseObject(result).getString("tokenSn");
                        if (StringUtils.isNotBlank(tokenSnRemote)) {
                            // 1.2.1 已绑定，直接返回令牌号
                            tokenSN = tokenSnRemote;
                        } else {
                            //1.2.2 未绑定，绑定后返回令牌号
                            tokenSN = bindMobile(user.getName());
                        }
                    }
                } catch (CommonBusinessException e) {
                    log.error("mfa error", e);
                    throw e;
                } catch (Exception e) {
                    log.error("调用otp server失败", e);
                    throw new CommonBusinessException("MFA服务端不可用，请联系管理员", user.getName());
                }
            } else if ("sms".equals(mfaType) || "email".equals(mfaType)) {
                Map<String, Boolean> detailMap = new HashMap<>();
                detailMap.put("login", login);
                detailMap.put("danger", danger);
                tokenSN = JacksonUtils.writeAsJsonString(detailMap);
            }else if ("ftng".equals(mfaType)){
                String a = Base64.decodeStr(certificate);
                SeedEntity seed = seedsService.getById(a);
                if (seed == null) {
                    throw new CommonBusinessException("未找到指定的令牌");
                }
                tokenSN = seed.getPrivateKey();
            }
        }

        UserAuthorizeSettings userAuthorizeSettings = new UserAuthorizeSettings();
        userAuthorizeSettings.setId(UUIDUtil.get32UUID());
        userAuthorizeSettings.setUserId(userId);
        userAuthorizeSettings.setType(UserAuthorizeType.MFA);
        userAuthorizeSettings.setEnabled(true);
        userAuthorizeSettings.setExtra(tokenSN);
        // 2 录入数据库
        userAuthorizeSettingsMapper.insert(userAuthorizeSettings);
        // 子账号插入
        subAccountHandler(user, userAuthorizeSettings);
        mfaResultVO.setMfaResult(CommonInstance.ZERO);
        return mfaResultVO;
    }

    private void subAccountHandler(User user, UserAuthorizeSettings userAuthorizeSettingsCheck) {
        if (!TypeUtil.TYPE_MASTER.equals(user.getType())) {
           return;
        }

        if (!("sms".equals(mfaType) || "email".equals(mfaType))) {
            return;
        }

        PageCL<UserViewVO> userPage = userService.listAllUser(user.getId(), 1, 1000, null);
        List<UserViewVO> userList = userPage.getList();
        if (CollUtil.isEmpty(userList)) {
            return;
        }
        List<String> userIds = userList.stream().map(UserViewVO::getId).collect(Collectors.toList());
        List<UserAuthorizeSettings> userAuthorizeSettingsList = userAuthorizeSettingsMapper.selectList(new LambdaQueryWrapper<UserAuthorizeSettings>()
                .in(UserAuthorizeSettings::getUserId, userIds)
                .eq(UserAuthorizeSettings::getType, UserAuthorizeType.MFA));
        Map<String, String> userIdToIdMap = userAuthorizeSettingsList.stream()
                .collect(Collectors.toMap(UserAuthorizeSettings::getUserId, UserAuthorizeSettings::getId, (o1, o2) -> o1));
        List<UserAuthorizeSettings> updateList = new ArrayList<>();
        for (String userId : userIds) {
            UserAuthorizeSettings userAuthorizeSettings = new UserAuthorizeSettings();
            userAuthorizeSettings.setId(userIdToIdMap.getOrDefault(userId, UUIDUtil.get32UUID()));
            userAuthorizeSettings.setUserId(userId);
            userAuthorizeSettings.setType(UserAuthorizeType.MFA);
            userAuthorizeSettings.setEnabled(true);
            userAuthorizeSettings.setExtra(userAuthorizeSettingsCheck.getExtra());
            updateList.add(userAuthorizeSettings);
        }
        userAuthorizeSettingsService.saveOrUpdateBatch(updateList);
    }

    @Override
    public boolean auth(String userId, String tokenSN, String mfaCode) {
        Map<String, Object> urlParams = getUrlParam("auth");
        urlParams.put("userId", userId);
        urlParams.put("tokenSN", tokenSN);
        urlParams.put("otp", mfaCode);
        try {
            String result = null;
            if (mfaOTPServer.startsWith("https")) {
                result = httpsRestTemplate.getForObject(uriWrapper(mfaOTPServer, urlParams), String.class);
            } else {
                result = mfaRestTemplate.getForObject(uriWrapper(mfaOTPServer, urlParams), String.class);
            }

            Integer resultCode = JSONObject.parseObject(result).getInteger(resultCodeKey);
            String msg = MFAResultCodeTransUtil.errCodeMap.get(resultCode);
            if (StringUtils.isNotBlank(msg)) {
                throw new CommonBusinessException(msg);
            }
            return true;
        } catch (Exception e) {
            log.error("mfa auth error:", e);
            return false;
        }
    }

    @Override
    public String preClose(String userId, User deletedUser) {
        User user;
        if (Objects.nonNull(deletedUser)) {
            user = deletedUser;
        } else {
            user = checkUserExist(userId);
        }
        UserAuthorizeSettings userAuthorizeSettingsCheck = userAuthorizeSettingsMapper.selectOne(new LambdaQueryWrapper<UserAuthorizeSettings>()
                .eq(UserAuthorizeSettings::getUserId, userId)
                .eq(UserAuthorizeSettings::getType, UserAuthorizeType.MFA));
        if (Objects.isNull(userAuthorizeSettingsCheck)) {
            // 如果是删除用户的话，就不必抛异常
            if (Objects.isNull(deletedUser)) {
                throw new CommonBusinessException("用户已关闭认证", user.getAlias());
            }
            return user.getName();
        }
        if( "opt-server".equals(mfaType)){
            Map<String, Object> urlParams = getUrlParam("unBind");
            urlParams.put("userId", user.getName());
            urlParams.put("tokenSN", userAuthorizeSettingsCheck.getExtra());
            String result = null;
            if (mfaOTPServer.startsWith("https")) {
                result = httpsRestTemplate.getForObject(uriWrapper(mfaOTPServer, urlParams), String.class);
            } else {
                result = mfaRestTemplate.getForObject(uriWrapper(mfaOTPServer, urlParams), String.class);
            }
            Integer resultCode = JSONObject.parseObject(result).getInteger(resultCodeKey);
            String msg = MFAResultCodeTransUtil.errCodeMap.get(resultCode);
            if (StringUtils.isNotBlank(msg) && resultCode != 55) {
                throw new CommonBusinessException(msg);
            }
        }
        return user.getName();
    }

    @Override
    public String close(String userId, User deletedUser) {
        String userName = preClose(userId, deletedUser);
        User user = userMapper.selectById(userId);
        List<String> deleteUserIds = new ArrayList<>();
        deleteUserIds.add(userId);
        if (TypeUtil.TYPE_MASTER.equals(user.getType())
                && ("sms".equals(mfaType) || "email".equals(mfaType))) {
            PageCL<UserViewVO> userPage = userService.listAllUser(user.getId(), 1, 1000, null);
            List<UserViewVO> subUserList = userPage.getList();
            if (CollUtil.isNotEmpty(subUserList)) {
                deleteUserIds.addAll(subUserList.stream().map(UserViewVO::getId).collect(Collectors.toList()));
            }
        }
        // 删除记录
        userAuthorizeSettingsMapper.delete(new LambdaUpdateWrapper<UserAuthorizeSettings>()
                .in(UserAuthorizeSettings::getUserId, deleteUserIds)
                .eq(UserAuthorizeSettings::getType, UserAuthorizeType.MFA));

        return userName;
    }

    private String bindMobile(String userId){
        Map<String, Object> urlParams = getUrlParam("bindMobile");
        urlParams.put("userId", userId);
        String result = null;
        if (mfaOTPServer.startsWith("https")) {
            result = httpsRestTemplate.getForObject(uriWrapper(mfaOTPServer, urlParams), String.class);
        } else {
            result = mfaRestTemplate.getForObject(uriWrapper(mfaOTPServer, urlParams), String.class);
        }
        Integer resultCode = JSONObject.parseObject(result).getInteger(resultCodeKey);
        String msg = MFAResultCodeTransUtil.errCodeMap.get(resultCode);
        if (StringUtils.isNotBlank(msg)) {
            throw new CommonBusinessException(msg);
        }
        return JSONObject.parseObject(result).getString(resultDataKey);
    }

    private User checkUserExist(String userId) {
        User user = userMapper.selectById(userId);
        if (Objects.isNull(user)) {
            throw new CommonBusinessException("用户不存在或已被删除");
        }
        return user;
    }

    private Map<String, Object> getUrlParam(String method) {
        Map<String, Object> urlParam = new HashMap<>();
        urlParam.put("method", method);
        return urlParam;
    }

    private String getQRCodeImage(String content) {
        try {
            QRCodeWriter qrCodeWriter = new QRCodeWriter();
            BitMatrix bitMatrix = qrCodeWriter.encode(content, BarcodeFormat.QR_CODE, 300, 300);
            ByteArrayOutputStream pngOutputStream = new ByteArrayOutputStream();
            MatrixToImageWriter.writeToStream(bitMatrix, "PNG", pngOutputStream);
            return "data:image/png;base64," + Base64.encode(pngOutputStream.toByteArray());
        } catch (Exception e) {
            log.error("生成二维码失败", e);
        }
        return null;
    }

    private URI uriWrapper(String url, Map<String, Object> params) {
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(url);
        if (Objects.nonNull(params) && !params.isEmpty()) {
            params.forEach((k, v) -> {
                try {
                    if (Objects.nonNull(v)) {
                        urlBuilder.queryParam(k, URLDecoder.decode(v.toString(), "UTF-8"));
                    } else {
                        urlBuilder.queryParam(k, "");
                    }
                } catch (UnsupportedEncodingException e) {
                   log.error("url decode error", e);
                }
            });
        }
        return urlBuilder.build().encode().toUri();
    }

    @Override
    public Map<String, Boolean> getUserMFAMap() {
        return userAuthorizeSettingsMapper.selectList(new LambdaQueryWrapper<UserAuthorizeSettings>()
                        .select(UserAuthorizeSettings::getUserId, UserAuthorizeSettings::getEnabled)
                        .eq(UserAuthorizeSettings::getEnabled, true)
                        .eq(UserAuthorizeSettings::getType, UserAuthorizeType.MFA))
                .stream()
                .collect(Collectors.toMap(UserAuthorizeSettings::getUserId, UserAuthorizeSettings::getEnabled, (o1, o2) -> o2));
    }

    @Override
    public boolean checkValidateCode(String businessType, MFADetailVO mfaDetailVO) {
        if (Objects.isNull(mfaDetailVO)) {
            return false;
        }
        Map<String, Boolean> detailMap = JacksonUtils.parseObject(mfaDetailVO.getTokenSN(), new TypeReference<Map<String, Boolean>>() {});
        // 危险操作gateway调用这个接口
        return detailMap.getOrDefault(businessType, false);
    }

}
