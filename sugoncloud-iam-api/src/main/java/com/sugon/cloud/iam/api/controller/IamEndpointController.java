package com.sugon.cloud.iam.api.controller;

import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.common.utils.UUIDUtil;
import com.sugon.cloud.iam.api.entity.EndpointForm;
import com.sugon.cloud.iam.api.entity.Region;
import com.sugon.cloud.iam.api.entity.keystone.CreateEndpointResponse;
import com.sugon.cloud.iam.api.entity.keystone.Endpoint;
import com.sugon.cloud.iam.common.model.vo.CreateEndpointResponseParam;
import com.sugon.cloud.iam.api.service.EndpointService;
import com.sugon.cloud.log.client.starter.annotation.LogRecordAnnotation;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "endpoint操作")
@RequestMapping(value = "/api/endpoint")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RestController
@DocumentIgnore
public class IamEndpointController {

    private final EndpointService endpointService;

    @GetMapping("{service_name}/{region_id}")
    @ApiOperation("查询endpoint")
    @ApiImplicitParams({@ApiImplicitParam(name = "service_name", value = "服务名", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "region_id", value = "区域id", paramType = "query", dataType = "string")})
    public ResultModel<CreateEndpointResponseParam> findByServiceNameAndRegionId(@PathVariable("service_name") String serviceName,
                                                                                 @PathVariable("region_id") String regionId) {
        return ResultModel.success(endpointService.findByServiceNameAndRegionId(serviceName, regionId));
    }

    @PostMapping()
    @ApiOperation("创建endpoint")
    @LogRecordAnnotation(value = "创建endpoint",detail = "创建endpoint:{{#_ret.resource}}")
    public ResultModel<Integer> createEndpoint(@RequestBody @Validated EndpointForm endpointForm) {
        Endpoint endpoint = new Endpoint();
        endpoint.setEnabled(endpointForm.getEnabled());
        endpoint.setExtra("{}");
        endpoint.setRegionId(endpointForm.getRegionId());
        endpoint.setServiceId(endpointForm.getServiceId());
        endpoint.setUrl(endpointForm.getUrl());
        endpoint.setInterfaceParam(endpointForm.getInterfaceParam());
        endpoint.setLegacyEndpointId(null);
        endpoint.setId(UUIDUtil.get32UUID());
        return ResultModel.success("创建endpoint成功",endpointForm.getUrl(),endpointService.createEndpoint(endpoint));
    }

    @PutMapping()
    @ApiOperation("修改endpoint")
    @LogRecordAnnotation(value = "修改endpoint",detail = "修改endpoint:{{#_ret.resource}}")
    public ResultModel<Integer> updateEndpoint(@RequestBody @Validated EndpointForm endpointForm) {
        return ResultModel.success("创建endpoint成功",endpointForm.getUrl(),endpointService.updateEndpoint(endpointForm));
    }

    @ApiOperation("查询Endpoint列表")
    @GetMapping("list")
    @ApiImplicitParams({@ApiImplicitParam(name = "page_num", value = "页码", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "page_size", value = "每页显示条数", paramType = "query", dataType = "int")})
    public ResultModel<PageCL<Endpoint>> list(@RequestParam(value = "page_num", defaultValue = "0", required = false) int pageNum,
                                              @RequestParam(value = "page_size", defaultValue = "15", required = false) int pageSize) {

        return ResultModel.success("查询列表成功", endpointService.list(pageNum, pageSize));
    }

    @DeleteMapping("{id}")
    @LogRecordAnnotation(value = "删除endpoint",detail = "删除endpoint:{{#_ret.resource}}")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "域ID", required = true, dataType = "String", paramType = "path")})
    @ApiOperation("删除endpoint")
    public ResultModel<Integer> deleteEndpoint(@PathVariable("id") String id) {
        CreateEndpointResponse createEndpointResponse = endpointService.endpointDetail(id);
        if (createEndpointResponse == null)
            return ResultModel.error("endpoint不存在");
       return ResultModel.success("删除endpoint成功",createEndpointResponse.getEndpoint().getUrl(),
               endpointService.deleteEndpoint(id));
    }
};