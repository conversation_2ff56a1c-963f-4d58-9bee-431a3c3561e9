package com.sugon.cloud.iam.api.controller;


import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.common.utils.IPUtil;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.entity.ImportUserEntity;
import com.sugon.cloud.iam.api.entity.exception.IamLoginException;
import com.sugon.cloud.iam.api.entity.keystone.LoginUser;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.exception.BusinessException;
import com.sugon.cloud.iam.api.jwt.JwtTokenUtils;
import com.sugon.cloud.iam.api.service.JwtService;
import com.sugon.cloud.iam.api.service.UserService;
import com.sugon.cloud.iam.api.vo.UserCreateOrUpdateVO;
import com.sugon.cloud.log.client.context.LogRecordContext;
import com.sugon.cloud.log.client.starter.annotation.LogRecordAnnotation;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Api(tags = "对接外部登录")
@RequestMapping(value = "/api/third-party")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RestController
@Slf4j
@DocumentIgnore
public class ThirdPartySignOnController {


    private final UserService userService;
    private final JwtTokenUtils jwtTokenUtils;
    @Value("${thirdPartySignOn:false}")
    private boolean thirdPartySignOn;
    private final JwtService jwtService;
    @PostMapping("login")
    @ApiOperation("格尔登录")
    @LogRecordAnnotation(value = "用户登录", operatorId = "{{#user.id}}", operatorName = "{{#user.name}}", detail = "{{#_ret.resource}}:用户登录")
    public ResultModel<String> signOn(HttpServletRequest request) {
        if (!thirdPartySignOn) throw new IamLoginException("请联系管理员切换到格尔登录模式");
        String cn = null;
        try {
            Cookie[] cookies = request.getCookies();
            if(ArrayUtils.isNotEmpty(cookies)){
                for (Cookie cookie : cookies) {
                    log.info("cookie name {}, cookie value {}",
                            URLDecoder.decode(cookie.getName(), StandardCharsets.UTF_8.name()),
                            URLDecoder.decode(cookie.getValue(), StandardCharsets.UTF_8.name()));
                    if(CommonInstance.KOAL_CERT_CN.equals(cookie.getName())){
                        cn = URLDecoder.decode(cookie.getValue(), StandardCharsets.UTF_8.name());
                        break;
                    }
                }
            }
        }catch (Exception e){
            log.error("analysis cookie error", e);
        }
        log.info("KOAL_CERT_CN:" + cn);
        if (StringUtils.isEmpty(cn)) throw new IamLoginException("未获取有效的CN码");
        User user = userService.getOne(new LambdaQueryWrapper<User>().eq(User::getName, cn));
        LogRecordContext.putVariable("user", user);
        if (user == null){
            log.error("{}不存在", cn);
            throw new IamLoginException(cn+"不存在");
        }
        LoginUser userDetails = new LoginUser(user.getName(), user.getPassword(),new ArrayList<>());
        userDetails.setUserId(user.getId());
        userDetails.setAlias(user.getAlias());
        userDetails.setUserType(user.getType());
        userDetails.setDeptId(user.getDeptId());
        userDetails.setProjectId(user.getDefaultProjectId());
        String token = jwtTokenUtils.generateToken(userDetails, false);
        jwtService.initOnePlaceLoginInfo(userDetails, token);
        String ipAddr = IPUtil.getIpAddr(request);
        userService.update(null, new LambdaUpdateWrapper<User>()
                .eq(User::getId, user.getId())
                .set(User::getLastActiveAt, new Date())
                .set(User::getLastLoginIp, ipAddr));
        ResultModel<String> resultModel = new ResultModel<>();
        resultModel.setContent(token);
        resultModel.setResource(user.getName());
        return resultModel;
    }

    @PostMapping("import-user")
    public ResultModel<String> importUser(@RequestPart(value ="file") MultipartFile file) {
        StringBuilder  stringBuilder = new StringBuilder();
        try {
            List<ImportUserEntity> importUsers = EasyExcel.read(file.getInputStream())
                    .head(ImportUserEntity.class).sheet().doReadSync();
            importUsers.forEach(e -> {
                log.info(JSONObject.toJSONString(e));
                UserCreateOrUpdateVO userVO = new UserCreateOrUpdateVO();
                try {
                    User user = userService.getOne(new LambdaQueryWrapper<User>().eq(User::getName, e.getName()));
                    //不存在加入
                    if (user == null) {
                        userVO.setPassword("importUser@!#123");
                        userVO.setName(e.getName());
                        userVO.setAlias(e.getAlias());
                        userVO.setEmail(e.getEmail());
                        userVO.setPhone(e.getPhone());
                        userVO.setDepartmentManager(e.isDepartmentManager());
                        userVO.setDeptId(e.getDeptId());
                        userService.createSubUser(userVO, true);
                    }else {
                        stringBuilder.append("用户 "+user.getName() + "已经存在");
                        stringBuilder.append("---------");
                        stringBuilder.append("\n");
                    }
                } catch (BusinessException businessException) {
                    log.error("import user error", businessException);
                    stringBuilder.append(userVO.getName());
                    stringBuilder.append(businessException.getMessage());
                    stringBuilder.append("---------");
                    stringBuilder.append("\n");
                }
             });
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        ResultModel<String> resultModel = new ResultModel<>();
        resultModel.setContent(stringBuilder.toString());
        return resultModel;
    }

    @GetMapping("cn")
    @ApiOperation("获取cn码")
    public ResultModel<String> cn(HttpServletRequest request) {
        if (!thirdPartySignOn) throw new IamLoginException("请联系管理员切换到格尔登录模式");
        String cn = null;
        try {
            Cookie[] cookies = request.getCookies();
            if(ArrayUtils.isNotEmpty(cookies)){
                for (Cookie cookie : cookies) {
                    log.info("cookie name {}, cookie value {}",
                            URLDecoder.decode(cookie.getName(), StandardCharsets.UTF_8.name()),
                            URLDecoder.decode(cookie.getValue(), StandardCharsets.UTF_8.name()));
                    if(CommonInstance.KOAL_CERT_CN.equals(cookie.getName())){
                        cn = URLDecoder.decode(cookie.getValue(), StandardCharsets.UTF_8.name());
                        break;
                    }
                }
            }
        }catch (Exception e){
            log.error("analysis cookie error", e);
        }
        log.info("KOAL_CERT_CN:" + cn);
        ResultModel<String> resultModel = new ResultModel<>();
        resultModel.setContent(cn);
        return resultModel;
    }
}
