package com.sugon.cloud.iam.api.controller;


import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.service.UserService;
import com.sugon.cloud.common.annotations.DocumentIgnore;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.sugon.cloud.common.constants.HeaderParamConstant;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.common.utils.UUIDUtil;
import com.sugon.cloud.iam.api.common.AESUtil;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.entity.TemporarySecretKeyEntity;
import com.sugon.cloud.iam.api.jwt.JwtTokenUtils;
import io.jsonwebtoken.Claims;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.concurrent.TimeUnit;

@Api(tags = "生成临时凭证API")
@RequestMapping(value = "/api/temporary-secret-key")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RestController
@Slf4j
@RefreshScope
@DocumentIgnore
public class TemporarySecretKsyController {

    @Resource(name = "defaultRedisTemplate")
    private final RedisTemplate defaultRedisTemplate;
    private final JwtTokenUtils jwtTokenUtils;
    private final UserService userService;
    @PostMapping()
    @ApiOperation("生成临时凭证")
    @ApiImplicitParams({@ApiImplicitParam(name = "time", value = "设置token过期时间(分钟)", dataType = "String", paramType = "query")})
    public ResultModel<TemporarySecretKeyEntity> creatTemporarySecretKey(@RequestParam("time") Integer minute,
                                               @RequestParam(value = "policy",required = false) String policy,
                                               HttpServletRequest request) {
        String ak = UUIDUtil.get32UUID();
        String sk = AESUtil.encrypt(ak, CommonInstance.OBS_KEY);
        String securityToken = AESUtil.encrypt(ak, CommonInstance.OBS_TEM_KEY);
        String token = request.getHeader(CommonInstance.AUTHORIZATION).substring(7);
        Claims claimsFromToken = jwtTokenUtils.getClaimsFromToken(token);
        String userId = claimsFromToken.get(HeaderParamConstant.USER_ID,String.class);
        if (minute == null || minute == 0) minute = 15;
        JSONObject json = new JSONObject();
        json.put("accessKey",ak);
        json.put("status",1);
        json.put("secretKey",sk);
        json.put("userId",userId);
        json.put("securityToken",securityToken);
        json.put("expiresAt", new Date(new Date().getTime()+ minute*60*1000));
        //fix bug 868ba4c8
        User user = userService.getById(userId);
        assert user != null;
        json.put("userType", user.getType() == null?"-":user.getType());
        String id = UUIDUtil.get32UUID();
        defaultRedisTemplate.opsForValue().set(CommonInstance.IAM_STS_USERS_REDIS_KEY+ak, json.toString(),minute, TimeUnit.MINUTES);
        if (StringUtils.isNotBlank(policy)){
            defaultRedisTemplate.opsForValue().set(CommonInstance.IAM_STS_POLICIES+id, policy, minute,TimeUnit.MINUTES);
            JSONObject jsonIds = new JSONObject();
            jsonIds.put("policy", id);
            defaultRedisTemplate.opsForValue().set(CommonInstance.IAM_POLICYDB_STS_USERS+userId,jsonIds.toString(), minute,TimeUnit.MINUTES);
        }

        TemporarySecretKeyEntity temporarySecretKeyEntity = new TemporarySecretKeyEntity();
        temporarySecretKeyEntity.setAccessKey(ak);
        temporarySecretKeyEntity.setSecurityToken(securityToken);
        temporarySecretKeyEntity.setSecretKey(sk);
        temporarySecretKeyEntity.setExpiresAt(new Date(new Date().getTime()+ minute*60*1000));
        return ResultModel.success("获取临时凭证成功", temporarySecretKeyEntity);
    }
}
