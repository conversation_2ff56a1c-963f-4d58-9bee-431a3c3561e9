package com.sugon.cloud.iam.api.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sugon.cloud.common.constants.HeaderParamConstant;
import com.sugon.cloud.common.mapper.ModelMapper;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.common.utils.I18nUtil;
import com.sugon.cloud.common.utils.UUIDUtil;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.config.MultiRegionEsConfig;
import com.sugon.cloud.iam.api.entity.BlacklistMicroService;
import com.sugon.cloud.iam.api.entity.LicenseInfo;
import com.sugon.cloud.iam.api.entity.LicenseServiceInfo;
import com.sugon.cloud.iam.api.entity.MicroService;
import com.sugon.cloud.iam.api.entity.MicroServiceCategory;
import com.sugon.cloud.iam.api.exception.BusinessException;
import com.sugon.cloud.iam.api.mapper.MicroServiceCategoryMapper;
import com.sugon.cloud.iam.api.mapper.MicroServiceMapper;
import com.sugon.cloud.iam.api.service.BlacklistMicroServiceService;
import com.sugon.cloud.iam.api.service.MicroServiceService;
import com.sugon.cloud.iam.api.utils.HttpTemplateUtils;
import com.sugon.cloud.iam.api.utils.LicenseContentUtils;
import com.sugon.cloud.iam.api.vo.CreateMicroServiceVO;
import com.sugon.cloud.iam.api.vo.PolicyFilterVO;
import com.sugon.cloud.iam.api.vo.UpdateMicroServiceVO;
import com.sugon.cloud.iam.common.enums.BlacklistTypeEnum;
import com.sugon.cloud.iam.common.model.vo.MicroServiceDetailVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * (MicroService)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-04-15 09:35:39
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MicroServiceServiceImpl extends ServiceImpl<MicroServiceMapper, MicroService> implements MicroServiceService {
    private final MicroServiceMapper microServiceMapper;
    private final MicroServiceCategoryMapper microServiceCategoryMapper;
    private final ModelMapper mapper;
    private final HttpServletRequest request;
    private final BlacklistMicroServiceService blacklistMicroServiceService;
    private final MultiRegionEsConfig multiRegionEsConfig;
    @Value("${black.enable:false}")
    private boolean blackEnable;
    /**
     * 52f2f86b-8423-11ee-9090-02420a43c727：计费管理
     * d3a89727-26a7-11ee-9065-0242bd4205e4：资源管理
     */
    @Value("#{'${noCloudProduct:52f2f86b-8423-11ee-9090-02420a43c727,d3a89727-26a7-11ee-9065-0242bd4205e4,f72db00f-4914-4bdf-9161-7cc5977edfsd}'.split(',')}")
    private List<String> noCloudProduct;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public MicroService queryById(String id) {
        return this.microServiceMapper.selectById(id);
    }

    /**
     * 查询多条数据
     *
     * @param name     查询名称
     * @param pageNum  查询起始位置
     * @param pageSize 查询条数
     * @return 对象列表
     */
    @Override
    public PageCL<MicroServiceDetailVO> pageList(String name, int pageNum, int pageSize, String collected, boolean needPolicy, String isHidden, String isCloudProduct) {
        String internetAccess = request.getHeader("InternetAccess");
        log.info("internetAccess {} ", internetAccess);
        boolean isInternet = false;
        if (Objects.nonNull(internetAccess)) isInternet = Boolean.parseBoolean(internetAccess);
        List<String> blackMicroIds = Collections.emptyList();
        List<String> cateGoryIds = Collections.emptyList();
        if (blackEnable) {
            List<BlacklistMicroService> blacklistMicroServices = blacklistMicroServiceService.listByTypes(Arrays.asList(BlacklistTypeEnum.MICRO, BlacklistTypeEnum.CATEGORY));
            blackMicroIds = blacklistMicroServices.stream().filter(p -> BlacklistTypeEnum.MICRO.equals(p.getType())).map(BlacklistMicroService::getId).collect(Collectors.toList());
            cateGoryIds = blacklistMicroServices.stream().filter(p -> BlacklistTypeEnum.CATEGORY.equals(p.getType())).map(BlacklistMicroService::getId).collect(Collectors.toList());
        }
        IPage<MicroService> page = new Page(pageNum, pageSize);
        new QueryWrapper<MicroService>().lambda()
                .like(StringUtils.isNotBlank(name), MicroService::getName, name);
        IPage<MicroService> projectIPage = microServiceMapper.selectPage(page, new QueryWrapper<MicroService>().lambda()
                .like(StringUtils.isNotBlank(name), MicroService::getName, name)
                .eq(MicroService::getThirdPartAccess, 0)
                .eq(MicroService::getNeedPolicy, needPolicy)
                .eq(StringUtils.isNotBlank(isHidden), MicroService::getNavHidden, BooleanUtils.toBoolean(isHidden))
                .isNotNull("true".equals(collected), MicroService::getCollectTime)
                .notIn(blackEnable && CollectionUtil.isNotEmpty(blackMicroIds), MicroService::getId, blackMicroIds)
                .notIn(blackEnable && CollectionUtil.isNotEmpty(cateGoryIds), MicroService::getCategoryId, cateGoryIds)
                .notIn(StringUtils.isNotBlank(isCloudProduct) && Boolean.valueOf(isCloudProduct), MicroService::getId, noCloudProduct)
                .orderByDesc("true".equals(collected), MicroService::getCollectTime)
                .orderByDesc(!"true".equals(collected), MicroService::getModifyTime));
        if (isInternet) {
            projectIPage.getRecords().forEach(s -> {
                // 国际化处理
                s.setName(I18nUtil.getMessageByKey(s.getName(), s.getName()));
                s.setDescription(I18nUtil.getMessageByKey(s.getDescription(), s.getDescription()));
                if (!StringUtils.isEmpty(s.getPublicLink())) {
                    s.setLink(s.getPublicLink());
                }
            });
        } else {
            projectIPage.getRecords().forEach(s -> {
                // 国际化处理
                s.setName(I18nUtil.getMessageByKey(s.getName(), s.getName()));
                s.setDescription(I18nUtil.getMessageByKey(s.getDescription(), s.getDescription()));
            });
        }
        try {
            return new PageCL<MicroServiceDetailVO>().
                    getPageByPageHelper(page, mapper.mapList(projectIPage.getRecords(), MicroServiceDetailVO.class));
        } catch (Exception e) {
            log.error("com.sugon.cloud.iam.api.service.impl.MicroServiceServiceImpl.pageList error:", e);
            throw new BusinessException("查询列表报错");
        }
    }

    @Override
    public List<MicroService> getAllMicroService() {
        // 查询分类下的所有微服务
        List<String> blackMicroIds = Collections.emptyList();
        List<String> cateGoryIds = Collections.emptyList();
        if (blackEnable) {
            List<BlacklistMicroService> blacklistMicroServices = blacklistMicroServiceService.listByTypes(Arrays.asList(BlacklistTypeEnum.MICRO, BlacklistTypeEnum.CATEGORY));
            blackMicroIds = blacklistMicroServices.stream().filter(p -> BlacklistTypeEnum.MICRO.equals(p.getType())).map(BlacklistMicroService::getId).collect(Collectors.toList());
            cateGoryIds = blacklistMicroServices.stream().filter(p -> BlacklistTypeEnum.CATEGORY.equals(p.getType())).map(BlacklistMicroService::getId).collect(Collectors.toList());
        }
        List<MicroService> microServices = microServiceMapper.selectList(new QueryWrapper<MicroService>().lambda()
                .notIn(blackEnable && CollectionUtil.isNotEmpty(blackMicroIds), MicroService::getId, blackMicroIds)
                .notIn(blackEnable && CollectionUtil.isNotEmpty(cateGoryIds), MicroService::getCategoryId, cateGoryIds)

        );
        return microServices;
    }

    @Override
    public void collectOrUnCollect(String id, boolean isCollected) {
        MicroService microService = this.queryById(id);
        if (Objects.isNull(microService)) {
            throw new BusinessException("微服务id[" + "id" + "]不存在");
        }
        microService.setCollectTime(isCollected ? new Date() : null);
        microServiceMapper.updateById(microService);
    }

    /**
     * 新增数据
     *
     * @param microService 实例对象
     * @return 实例对象
     */
    @Override
    public MicroService insert(CreateMicroServiceVO microService) {
        Integer count = microServiceCategoryMapper.selectCount(new LambdaQueryWrapper<MicroServiceCategory>().eq(MicroServiceCategory::getId, microService.getCategoryId()));
        if (count == 0) {
            throw new BusinessException("微服务类别id[" + microService.getCategoryId() + "]不存在");
        }
        // 判断重名
        count = microServiceMapper.selectCount(new LambdaQueryWrapper<MicroService>().eq(MicroService::getCategoryId, microService.getCategoryId()).eq(MicroService::getName, microService.getName()));
        if (count > 0) {
            throw new BusinessException("微服务名称[" + microService.getName() + "]已存在");
        }
        MicroService entity = mapper.map(microService, MicroService.class);
        entity.setId(UUIDUtil.get32UUID());
        entity.setThirdPartAccess(true);
        entity.setServiceId("sugoncloud-micro-custom");
        entity.setDescription(entity.getName());
        entity.setPublicLink(entity.getLink());
        // 设置排序
        List<Map<String, Object>> maps = microServiceMapper.selectMaps(new QueryWrapper<MicroService>().select("MAX(`order`) as maxOrder").lambda().eq(MicroService::getCategoryId, microService.getCategoryId()));
        if (CollectionUtil.isNotEmpty(maps)) {
            entity.setOrder(10d);
        } else {
            Double maxOrder = (Double) maps.get(0).get("maxOrder");
            entity.setOrder(maxOrder + 10);
        }
        entity.setType(CommonInstance.MICRO_CUSTOM);
        this.microServiceMapper.insert(entity);
        return entity;
    }

    /**
     * 修改数据
     *
     * @param vo 实例对象
     * @return 实例对象
     */
    @Override
    public MicroService update(UpdateMicroServiceVO vo) {
        MicroService microService = microServiceMapper.selectById(vo.getId());
        if (Objects.isNull(microService)) {
            throw new BusinessException("微服务id[" + vo.getId() + "]不存在");
        }
        Integer count = microServiceMapper.selectCount(new LambdaQueryWrapper<MicroService>()
                .ne(MicroService::getId, vo.getId())
                .eq(MicroService::getCategoryId, vo.getCategoryId())
                .eq(MicroService::getName, vo.getName()));
        if (count > 0) {
            throw new BusinessException("微服务名称[" + microService.getName() + "]已存在");
        }
        if (CommonInstance.MICRO_CUSTOM.equals(microService.getType())) {
            // 自定义微服务菜单
            microService.setName(vo.getName());
            microService.setLink(vo.getLink());
            microService.setCategoryId(vo.getCategoryId());
            if (StrUtil.isNotBlank(vo.getOpenMode())) {
                microService.setOpenMode(vo.getOpenMode());
            }
        }
        if (Objects.nonNull(vo.getNavHidden())) {
            microService.setNavHidden(vo.getNavHidden());
        }
        if (Objects.nonNull(vo.getThirdPartAccess())) {
            microService.setThirdPartAccess(vo.getThirdPartAccess());
        }
        if (Objects.nonNull(vo.getOrder())) {
            microService.setOrder(vo.getOrder());
        }
        microServiceMapper.updateById(microService);
        return microService;
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public void deleteById(String id) {
        microServiceMapper.deleteById(id);
    }


    @Override
    public LicenseInfo upload(String id, String licenseStr) throws Exception {
        ResultModel resultModel = new ResultModel();
        resultModel.setStatusCode(1);
        //校验license
        LicenseInfo licenseInfo = LicenseContentUtils.decodeLicenseContent(licenseStr);
        saveLicense(id, licenseInfo);
        return licenseInfo;
    }

    @Override
    public void saveLicense(String microserviceId, LicenseInfo licenseInfo) {
        //保存数据库
        List<LicenseServiceInfo> serviceInfos = licenseInfo.getLicenseServiceInfoList();
        MicroService microService = microServiceMapper.selectById(microserviceId);
        if (!CollectionUtils.isEmpty(serviceInfos)) {
            List<LicenseServiceInfo> services = serviceInfos.stream().filter(s -> microService.getServiceId().equals(s.getSoftName())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(services)) {
                LicenseServiceInfo serviceInfo = services.get(0);
                microService.setVersion(serviceInfo.getSoftLicenseVersion());
                microServiceMapper.updateById(microService);
            }
        }
    }

    @Override
    public PolicyFilterVO getPolicyFilter() {
        PolicyFilterVO vo = new PolicyFilterVO();
        List<String> hiddenMicroServiceIds = new ArrayList<>();
        
        // 获取当前请求上下文
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        
        try {
            // 创建三个异步任务
            CompletableFuture<Void> task1 = CompletableFuture.runAsync(() -> getMicroServiceIds(hiddenMicroServiceIds));
            
            CompletableFuture<Void> task2 = CompletableFuture.runAsync(() -> {
                if (blackEnable) {
                    getBlackListIds(vo, hiddenMicroServiceIds);
                }
            });
            
            CompletableFuture<Void> task3 = CompletableFuture.runAsync(() -> {
                // 设置request上下文到当前线程
                RequestContextHolder.setRequestAttributes(attributes);
                try {
                    getLicenseKeyMicroServiceIds(hiddenMicroServiceIds);
                } finally {
                    // 清理request上下文
                    RequestContextHolder.resetRequestAttributes();
                }
            });
            
            // 等待所有异步任务完成
            try {
                CompletableFuture.allOf(task1, task2, task3).join();
            } catch (Exception e) {
                log.warn("异步任务执行超时或异常: {}", e.getMessage());
            }
        } catch (Exception e) {
            log.error("获取隐藏的微服务类别失败", e);
        }
        
        vo.setHiddenMicroServiceIds(hiddenMicroServiceIds.stream().distinct().collect(Collectors.toList()));
        return vo;
    }

    /**
     * 获取黑名单隐藏菜单和服务
     *
     * @param vo
     * @param hiddenMicroServiceIds
     */
    private void getBlackListIds(PolicyFilterVO vo, List<String> hiddenMicroServiceIds) {
        List<BlacklistMicroService> blackList = blacklistMicroServiceService.listByTypes(Arrays.asList(BlacklistTypeEnum.MICRO, BlacklistTypeEnum.CATEGORY, BlacklistTypeEnum.MENU));
        // 黑名单菜单
        vo.setBlacklistIds(blackList.stream().filter(t -> t.getType().equals(BlacklistTypeEnum.MENU)).map(BlacklistMicroService::getId).collect(Collectors.toList()));
        // 黑名单微服务
        hiddenMicroServiceIds.addAll(blackList.stream().filter(t -> !t.getType().equals(BlacklistTypeEnum.MENU)).map(BlacklistMicroService::getId).collect(Collectors.toList()));
    }

    /**
     * 获取未授权的微服务
     *
     * @param hiddenMicroServiceIds
     */
    private void getLicenseKeyMicroServiceIds(List<String> hiddenMicroServiceIds) {
        try {
            // 获取region ID
            String regionId = request.getHeader(HeaderParamConstant.REGION_ID);

            // 预构建URL，避免循环查找
            String sugoncloudOpsApi = multiRegionEsConfig.getMultiMonitorEntityList().stream()
                    .filter(met -> regionId.equals(met.getName()))
                    .findFirst()
                    .map(met -> met.getIp() + ":" + met.getPort() + "/sugoncloud-ops-api")
                    .orElse("");

            if (sugoncloudOpsApi.isEmpty()) {
                log.warn("未找到对应的region配置: {}", regionId);
                return;
            }

            // 查询ops获取当前授权信息
            String url = sugoncloudOpsApi + "/api/licenses/current";
            ResultModel resultModel = HttpTemplateUtils.get(url, null, null, new TypeReference<ResultModel>() {});
            if (!resultModel.isSuccess()) {
                log.error("查询ops获取当前授权信息:{}", JSONObject.toJSONString(resultModel));
                throw new BusinessException("请求ops服务失败");
            }
            Map<String, Object> map = (Map<String, Object>) resultModel.getContent();
            List<Map<String, Object>> list = (List<Map<String, Object>>) map.get("productAuthorizationInfos");
            // 获取未授权的license_key
            List<String> licenseKeys = list.stream().filter(t -> t.get("authorized").equals(false)).map(t -> (String) t.get("license_key")).collect(Collectors.toList());
            // 从数据库查询未授权的服务
            List<MicroService> noLicenseMicroServices = microServiceMapper.selectList(new LambdaQueryWrapper<MicroService>()
                    .in(MicroService::getLicenseKey, licenseKeys));
            hiddenMicroServiceIds.addAll(noLicenseMicroServices.stream().map(MicroService::getId).collect(Collectors.toList()));
        } catch (Exception e) {
            // 只记录错误日志不能影响正常功能
            log.error("查询ops获取当前授权信息失败", e);
        }
    }

    /**
     * 获取隐藏的微服务类别
     *
     * @param hiddenMicroServiceIds
     */
    private void getMicroServiceIds(List<String> hiddenMicroServiceIds) {
        // 获取隐藏的微服务类别
        List<String> categoryIds = microServiceCategoryMapper.selectList(new LambdaQueryWrapper<MicroServiceCategory>()
                        .ne(MicroServiceCategory::getType, CommonInstance.MICRO_HIDE)
                        .eq(MicroServiceCategory::getNavHidden, true))
                .stream()
                .map(MicroServiceCategory::getId)
                .collect(Collectors.toList());
        List<MicroService> microServices = microServiceMapper.selectList(new LambdaQueryWrapper<MicroService>()
                .in(MicroService::getCategoryId, categoryIds)
                .or()
                .eq(MicroService::getNavHidden, true));
        hiddenMicroServiceIds.addAll(microServices.stream().map(MicroService::getId).collect(Collectors.toList()));
        hiddenMicroServiceIds.addAll(categoryIds);
    }

    public static String readFileGetStr(InputStream inputStream) throws IOException {
        String license = "";
        BufferedReader br = null;
        LicenseInfo licenseInfo = new LicenseInfo();
        try {
            br = new BufferedReader(new InputStreamReader(inputStream));
            String line = null;
            while ((line = br.readLine()) != null) {
                license += line;
            }
        } catch (IOException e) {
            throw e;
        } finally {
            try {
                if (inputStream != null)
                    inputStream.close();
                if (br != null)
                    br.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return license;
    }
}
