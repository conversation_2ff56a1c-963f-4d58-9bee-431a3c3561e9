package com.sugon.cloud.iam.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("用户策略FORM")
public class UserStrategyForm implements Serializable {


    private static final long serialVersionUID = -3221369757550107170L;
    @ApiModelProperty("策略Id")
    @NotNull(message = "策略ID不能为空")
    private List<String> strategyId;

    @NotBlank(message = "用户Id不能为空")
    @Size(min = 2, max = 64)
    @ApiModelProperty("用户Id")
    private String userId;


}
