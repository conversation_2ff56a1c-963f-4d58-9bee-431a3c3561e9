package com.sugon.cloud.iam.api.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sugon.cloud.iam.api.entity.keystone.Catalog;
import com.sugon.cloud.iam.api.mapper.CatalogMapper;
import com.sugon.cloud.iam.api.service.CatalogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CatalogServiceImpl  extends ServiceImpl<CatalogMapper, Catalog> implements CatalogService {
    @Autowired
    private CatalogMapper catalogMapper;
    @Override
    public List<Catalog> getAll() {
        return catalogMapper.getAll();
    }
}
