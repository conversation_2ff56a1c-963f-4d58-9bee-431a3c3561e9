package com.sugon.cloud.iam.api.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel("创建微服务")
public class CreateMicroServiceVO {
    @ApiModelProperty(value = "名称")
    @NotBlank(message = "微服务名称不能为空")
    private String name;
    @ApiModelProperty(value = "web链接")
    private String link;
    @ApiModelProperty(value = "分类")
    @JsonProperty("category_id")
    @NotBlank(message = "微服务类别不能为空")
    private String categoryId;
    @ApiModelProperty(value = "是否隐藏")
    @JsonProperty("nav_hidden")
    private Boolean navHidden;
    @ApiModelProperty(value = "打开方式Internal:内部加载,external:外部跳转")
    @JsonProperty("open_mode")
    @NotBlank(message = "打开方式不能为空")
    private String openMode;
}