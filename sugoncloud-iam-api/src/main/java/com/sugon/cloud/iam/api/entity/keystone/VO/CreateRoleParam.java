package com.sugon.cloud.iam.api.entity.keystone.VO;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "创建角色信息")
public class CreateRoleParam {
    @ApiModelProperty("角色名称")
    private String name ;
    @JsonProperty("domain_id")
    @ApiModelProperty("域ID")
    private String domainId;
    @ApiModelProperty("描述")
    private String description;
    @ApiModelProperty("操作")
    private String options;
}
