package com.sugon.cloud.iam.api.listener;

import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.entity.GlobalsettingsEntity;
import com.sugon.cloud.iam.api.entity.exception.IamLoginException;
import com.sugon.cloud.iam.api.service.GlobalsettingsService;
import com.sugon.cloud.iam.api.service.MonitorService;
import com.sugon.cloud.iam.api.service.impl.MessageSendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.event.AuthenticationFailureBadCredentialsEvent;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import java.util.concurrent.TimeUnit;


@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class ApplicationListenerAuthenticationFailed implements ApplicationListener<AuthenticationFailureBadCredentialsEvent> {

    Logger logger = LoggerFactory.getLogger(ApplicationListenerAuthenticationFailed.class);

    private static final String className = "org.springframework.security.authentication.UsernamePasswordAuthenticationToken";

    private final RedisTemplate redisTemplate;

    private final GlobalsettingsService globalsettingsService;

    private final MessageSendService messageSendService;

    private final MonitorService monitorService;


    @Override
    public void onApplicationEvent(AuthenticationFailureBadCredentialsEvent event) {
        Authentication authentication =event.getAuthentication();
        String targetClassName = event.getSource().getClass().getName();
        if(className.equals(targetClassName)) {
            this.checkPassword(authentication.getName());
        }
    }

    private void checkPassword (String username) {
        // inner用户不锁定账号 不发消息 422137
        if ("inner".equals(username)) {
            logger.error("inner登录失败");
            return;
        }
        // 发送消息
        messageSendService.sendMessageByUserName(username, "用户[" + username + "]登录失败");
        int passwordErrorTimes = this.initPasswordErrorTimes();
        int lockTime = this.initPasswordErrorLockTime();
        int errorTimeInterval = this.initErrorTimeInterval();
        String lockUser = CommonInstance.UNAME_ERROR_LOCK+username;
        String lockCount = CommonInstance.UNAME_ERROR_COUNT + username;
        String tempUsername = username;
        username = CommonInstance.UNAME_ERROR_TIMES+username;
        if (passwordErrorTimes == 1) {//锁定用户
            redisTemplate.opsForValue().set(lockUser, true, lockTime, TimeUnit.MINUTES);
            monitorService.userLockAlarm(tempUsername, CommonInstance.MONITOR_USER_LOCK_REASON_ONE, CommonInstance.MONITOR_USER_LOCK_STATUS_ONE);
            throw new IamLoginException("用户名/密码错误次数达上限，锁定"+lockTime+"分钟");
        }
        boolean hasKey = redisTemplate.hasKey(username);
        if (hasKey) {
            Object times = redisTemplate.opsForValue().get(username);
            int count = Integer.valueOf(times.toString());

            int lockCountNum = count;
            if (redisTemplate.hasKey(lockCount)) {
                lockCountNum = Integer.parseInt(String.valueOf(redisTemplate.opsForValue().get(lockCount)));
            }
            if (count +1 >= passwordErrorTimes) {
                redisTemplate.opsForValue().set(lockUser, true, lockTime, TimeUnit.MINUTES);
                redisTemplate.opsForValue().set(lockCount, lockCountNum+1);
                redisTemplate.delete(username);
                monitorService.userLockAlarm(tempUsername, CommonInstance.MONITOR_USER_LOCK_REASON_ONE, CommonInstance.MONITOR_USER_LOCK_STATUS_ONE);
                throw new IamLoginException("用户名/密码错误次数达到上限，锁定"+lockTime+"分钟");
            }
            //bug 411549 start
            //返回的时间单位为秒
            Long expire = redisTemplate.getExpire(username);
            if (expire != null && expire != -1) {
                redisTemplate.opsForValue().set(username, count+1, expire, TimeUnit.SECONDS);
            }
            //bug 411549 end
            redisTemplate.opsForValue().set(lockCount, lockCountNum+1);
        } else {
            int lockCountNum = 1;
            if (redisTemplate.hasKey(lockCount)) {
                lockCountNum = Integer.parseInt(String.valueOf(redisTemplate.opsForValue().get(lockCount)));
                if (lockCountNum < 1) {
                    lockCountNum = 1;
                } else {
                    lockCountNum += 1;
                }
            }
            redisTemplate.opsForValue().set(username,1,  errorTimeInterval, TimeUnit.MINUTES);
            redisTemplate.opsForValue().set(lockCount, lockCountNum);
        }
        Object times = redisTemplate.opsForValue().get(username);
        int errorCount = Integer.valueOf(times.toString());
        throw new IamLoginException(errorTimeInterval+"分钟内，用户名/密码连续错误"+passwordErrorTimes+"次"+"将被锁定"+lockTime+"分钟，" + "已错误"+ errorCount +"次");
    }

    private int initPasswordErrorTimes () {
        try {
            GlobalsettingsEntity entity = globalsettingsService.getGlobalsettingsEntity(CommonInstance.CLOBAL_SETTING_PASSWORD, CommonInstance.CLOBAL_SETTING_PASSWORD_ERROR_TIMES);
            return Integer.parseInt(entity.getPolicyDocument());
        } catch (Exception e) {
            logger.error("init Password ErrorTimes", e);
        }
        return 10086;
    }

    private int initPasswordErrorLockTime () {
        try {
            GlobalsettingsEntity entity = globalsettingsService.getGlobalsettingsEntity(CommonInstance.CLOBAL_SETTING_PASSWORD, CommonInstance.CLOBAL_SETTING_PASSWORD_LOCK_MINUTE);
            return Integer.parseInt(entity.getPolicyDocument());
        } catch (Exception e) {
            logger.error("init lock time", e);
        }
        return 1;
    }

    private int initErrorTimeInterval () {
        try {
            GlobalsettingsEntity entity = globalsettingsService.getGlobalsettingsEntity(CommonInstance.CLOBAL_SETTING_PASSWORD, CommonInstance.CLOBAL_SETTING_PASSWORD_ERROR_TIME_INTERVAL);
            return Integer.parseInt(entity.getPolicyDocument());
        } catch (Exception e) {
            logger.error("init Password ErrorTimes", e);
        }
        return 10;
    }
}
