package com.sugon.cloud.iam.api.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
@ApiModel("endpoint FORM")
public class EndpointForm implements Serializable {

    private static final long serialVersionUID = -2324689785156627803L;
    @ApiModelProperty("区域id")
    private String regionId;
    @ApiModelProperty("服务id")
    private String serviceId;
    @Size(min = 4, max = 255)
    @ApiModelProperty("url")
    private String url;
    @ApiModelProperty("接口类型(admin、public、internal)")
    @JsonProperty(value = "interface")
    @TableField("interface")
    private String interfaceParam;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("是否启用")
    private Boolean enabled;

}
