package com.sugon.cloud.iam.api.service.resourceauth;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.service.UserService;
import com.sugon.cloud.iam.common.model.vo.UserViewVO;
import com.sugon.cloud.resource.auth.common.service.impl.BaseResourceAuthHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/10 17:46
 */
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class UserResourceAuthHandler extends BaseResourceAuthHandler {

    private final UserService userService;


    private final SecurityUserTypeAuthHandler securityUserTypeAuthHandler;

    @Override
    public List<String> getUserResource(String userId) {
        List<String> userResource = securityUserTypeAuthHandler.getUserResource(userId);
        if (CollectionUtil.isNotEmpty(userResource)) {
            return userResource;
        }
        userResource = Lists.newArrayList();
        userResource.add(userId);
        PageCL<UserViewVO> userViewVOPageCL = userService.listAllUser(userId, 1, 9999, null);
        userResource.addAll(userViewVOPageCL.getList().stream().map(UserViewVO::getId).collect(Collectors.toList()));
        return userResource;
    }
}
