package com.sugon.cloud.iam.api.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sugon.cloud.common.mapper.ModelMapper;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.common.utils.UUIDUtil;
import com.sugon.cloud.iam.api.entity.EndpointForm;
import com.sugon.cloud.iam.api.entity.keystone.*;
import com.sugon.cloud.iam.api.entity.keystone.VO.CreateEndpointParam;
import com.sugon.cloud.iam.api.entity.keystone.VO.CreateEndpointVO;
import com.sugon.cloud.iam.api.entity.exception.KeyStoneMethodArgumentNotValidException;
import com.sugon.cloud.iam.api.entity.exception.ResourceNotFoundException;
import com.sugon.cloud.iam.api.exception.BusinessException;
import com.sugon.cloud.iam.api.mapper.CatalogMapper;
import com.sugon.cloud.iam.api.mapper.EndpointMapper;
import com.sugon.cloud.iam.api.service.EndpointService;
import com.sugon.cloud.iam.common.model.vo.CreateEndpointResponseParam;
import com.sugon.cloud.iam.common.model.vo.CreateResourceResponseLinksParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RefreshScope
@Slf4j
public class EndpointServiceImpl implements EndpointService {

    private final EndpointMapper endpointMapper;
    private final CatalogMapper catalogMapper;
    private final ModelMapper mapper;

    @Value("#{'${vip.ip}'.concat(':').concat('${vip.port}')}")
    private String linksPrefix;

    @Override
    public CreateEndpointResponse createEndpoint(CreateEndpointVO createEndpointVO) {
        CreateEndpointParam endpoint = createEndpointVO.getEndpoint();
        Catalog catalog = catalogMapper.selectById(endpoint.getServiceId());
        if (Objects.isNull(catalog)) {
            throw new KeyStoneMethodArgumentNotValidException("Expecting to find endpoint service_id in " +
                    "service table. The server could not comply with the request since it is either malformed " +
                    "or otherwise incorrect. The client is assumed to be in error.");
        }
        String id = UUIDUtil.get32UUID();
        Endpoint endpointEntity = mapper.map(endpoint, Endpoint.class);
        endpointEntity.setId(id);
        endpointEntity.setExtra(new JSONObject().toString());
        endpointEntity.setEnabled(true);
        if (StringUtils.isEmpty(endpoint.getRegionId())) endpointEntity.setRegionId(endpoint.getRegion());
        endpointMapper.insert(endpointEntity);
        CreateEndpointResponse result = new CreateEndpointResponse();
        result.setEndpoint(endpoint2CreateEndpointResponseParam(endpointEntity));
        return result;
    }

    @Override
    public CreateEndpointResponse endpointDetail(String endpointId) {
        Endpoint endpointEntity = endpointMapper.selectById(endpointId);
        if(Objects.isNull(endpointEntity)){
            throw new ResourceNotFoundException("Could not find endpoint: " + endpointId + ".");
        }
        CreateEndpointResponse result = new CreateEndpointResponse();
        result.setEndpoint(endpoint2CreateEndpointResponseParam(endpointEntity));
        return result;
    }

    @Override
    public List<CreateEndpointResponseParam> listEndpoint(String interfaceParam, String serviceId, String regionId) {
        List<Endpoint> endpoints = endpointMapper.selectList(new QueryWrapper<Endpoint>().lambda()
                .eq(!StringUtils.isEmpty(interfaceParam), Endpoint::getInterfaceParam, interfaceParam)
                .eq(!StringUtils.isEmpty(serviceId), Endpoint::getServiceId, serviceId)
                .eq(!StringUtils.isEmpty(regionId), Endpoint::getRegionId, regionId));
        return endpoints.stream().map(endpoint -> endpoint2CreateEndpointResponseParam(endpoint)).collect(Collectors.toList());

    }

    @Override
    public CreateEndpointResponseParam findByServiceNameAndRegionId(String serviceName, String regionId) {
        Endpoint endpoint = endpointMapper.findByServiceNameAndRegionId(serviceName, regionId);
        CreateEndpointResponseParam endpointResponseParam = mapper.map(endpoint,CreateEndpointResponseParam.class);
        Catalog catalog = catalogMapper.selectById(endpoint.getServiceId());
        endpointResponseParam.setServiceName(catalog != null?catalog.getName():"");
        return endpointResponseParam;
    }

    @Override
    public int createEndpoint(Endpoint endpoint) {
        Catalog catalog = catalogMapper.selectById(endpoint.getServiceId());
        if (Objects.isNull(catalog)) {
            throw new BusinessException("Expecting to find endpoint service_id in " +
                    "service table. The server could not comply with the request since it is either malformed " +
                    "or otherwise incorrect. The client is assumed to be in error.");
        }
        Endpoint exist = endpointMapper.selectOne(new LambdaQueryWrapper<Endpoint>()
                .eq(Endpoint::getServiceId, endpoint.getServiceId())
                .eq(Endpoint::getRegionId, endpoint.getRegionId())
                .eq(Endpoint::getUrl, endpoint.getUrl())
                .eq(Endpoint::getInterfaceParam, endpoint.getInterfaceParam()));
        if (exist != null) {
            throw new BusinessException("endpoint已存在");
        }
        return endpointMapper.insert(endpoint);
    }

    @Override
    public int updateEndpoint(EndpointForm endpointForm) {
        Endpoint endpoint = endpointMapper.selectOne(new LambdaQueryWrapper<Endpoint>()
                .eq(Endpoint::getServiceId, endpointForm.getServiceId())
                .eq(Endpoint::getRegionId, endpointForm.getRegionId())
                .eq(Endpoint::getUrl, endpointForm.getUrl())
                .eq(Endpoint::getInterfaceParam, endpointForm.getInterfaceParam()));
        //判断修改后的Endpoint 在数据库中是否存在
        if (endpoint != null && !endpointForm.getId().equals(endpoint.getId()))
            throw new BusinessException("endpoint已经存在");
        return endpointMapper.update(null, new LambdaUpdateWrapper<Endpoint>()
                .set(Endpoint::getServiceId, endpointForm.getServiceId())
                .set(Endpoint::getEnabled, endpointForm.getEnabled())
                .set(Endpoint::getRegionId, endpointForm.getRegionId())
                .set(Endpoint::getInterfaceParam, endpointForm.getInterfaceParam())
                .set(Endpoint::getUrl, endpointForm.getUrl())
                .eq(Endpoint::getId, endpointForm.getId()));
    }

    @Override
    public int deleteEndpoint(String id) {
        return endpointMapper.deleteById(id);
    }

    @Override
    public PageCL<Endpoint> list(int pageNum, int pageSize) {
        try {
            IPage<Endpoint> page = new Page(pageNum, pageSize);
            page = endpointMapper.selectPage(page, new QueryWrapper<Endpoint>().lambda().orderByDesc(Endpoint::getRegionId));
            return new PageCL<Endpoint>().getPageByPageHelper(page, page.getRecords());
        } catch (Exception e) {
            log.error("error: ",e);
        }
        return null;
    }

    /**
     * 将数据库查询到的endPoint数据结构转为需要展示的数据结构
     * @param endpoint
     * @return
     */
    private CreateEndpointResponseParam endpoint2CreateEndpointResponseParam(Endpoint endpoint){
        CreateEndpointResponseParam endpointResponseParam = mapper.map(endpoint,CreateEndpointResponseParam.class);
        endpointResponseParam.setRegion(endpoint.getRegionId());
        endpointResponseParam.setLinks(new CreateResourceResponseLinksParam("http://" + linksPrefix + "/v3/endpoints/" + endpoint.getId()));
        return endpointResponseParam;
    }
}
