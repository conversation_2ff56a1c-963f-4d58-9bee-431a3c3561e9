package com.sugon.cloud.iam.api.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.iam.api.config.MultiRegionEsConfig;
import com.sugon.cloud.iam.api.entity.MultiEsEntity;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.service.MonitorService;
import com.sugon.cloud.iam.api.service.UserService;
import com.sugon.cloud.iam.api.utils.HttpTemplateUtils;
import com.sugon.cloud.iam.common.model.dto.UserLoginAlarmMetricDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @Author: yangdingshan
 * @Date: 2025/4/18 16:04
 * @Description:
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class MonitorServiceImpl implements MonitorService {

    private final MultiRegionEsConfig multiRegionEsConfig;

    private final UserService userService;

    @Override
    public void userLockAlarm(String userId, String userName, String reason, int value) {
        sendMetric(userId, userName, reason, value);
    }

    @Override
    public void userLockAlarm(String userName, String reason, int value) {
        User user = userService.getOne(new LambdaQueryWrapper<User>().eq(User::getName, userName));
        if (Objects.isNull(user)) {
            return;
        }
        sendMetric(user.getId(), userName, reason, value);
    }

    @Override
    public void userUnLockAlarm(String userId, String userName, String reason) {
        sendMetric(userId, userName, reason, 0);
    }

    @Override
    public void userUnLockAlarm(String userName, String reason) {
        User user = userService.getOne(new LambdaQueryWrapper<User>().eq(User::getName, userName));
        if (Objects.isNull(user)) {
            return;
        }
        sendMetric(user.getId(), userName, reason, 0);
    }

    /**
     * 推送指标
     *
     * @param userId
     * @param userName
     * @param reason
     * @param value
     */
    private void sendMetric(String userId, String userName, String reason, int value) {
        List<MultiEsEntity> monitorAddressList = multiRegionEsConfig.getMultiMonitorEntityList();
        for (MultiEsEntity met : monitorAddressList) {
            try {
                String url = met.getIp() + ":" + met.getPort() + "/sugoncloud-monitor-api/api/alarm/metric/data";
                if (!url.startsWith("http")) {
                    url = "http://" + url;
                }
                UserLoginAlarmMetricDTO dto = UserLoginAlarmMetricDTO.build(value, userId, userName, reason, met.getName());
                ResultModel resultModel = HttpTemplateUtils.post(url, dto, null, new TypeReference<ResultModel>() {});
                if (!resultModel.isSuccess()) {
                    log.error("请求云监控服务失败:{}", JSONObject.toJSONString(resultModel));
                }
            } catch (Exception e) {
                log.error("推送用户锁定监控失败，userId:{}, userName:{}, reason:{}, regionId:{}", userId, userName, reason, met.getName(), e);
            }
        }
    }
}
