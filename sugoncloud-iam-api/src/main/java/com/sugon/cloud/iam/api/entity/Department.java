package com.sugon.cloud.iam.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.sugon.cloud.common.model.entity.BaseEntity;
import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * 组织表(Department)实体类
 *
 * <AUTHOR>
 * @since 2021-04-07 10:51:53
 */
@Data
@TableName("department")
public class Department extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 290954149493544258L;
    /**
    * 名称
    */
    private String name;
    /**
    * 描述
    */
    private String description;
    /**
     * 父级组织id
     */
    private String parentId;
    /**
     * 层级
     */
    private Integer level;
    /**
     * project中is_domain=1的id
     */
    private String domainId;

}