package com.sugon.cloud.iam.api.controller;

import com.alibaba.fastjson.JSONObject;
import com.sugon.cloud.common.annotations.DocumentIgnore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

@RestController
@RequestMapping("/micro-healthy")
@DocumentIgnore
public class HeathyController {
    @Autowired
    @Lazy
    private RestTemplate restTemplate;

    @GetMapping
    public String health() throws Exception{
        int code = 404;
        try {
            String url = "http://sugoncloud-gateway-http.micro-service.svc.cluster.local:8080/sugoncloud-iam-api/actuator/health";
            ResponseEntity<JSONObject> responseEntity = restTemplate.exchange(url, HttpMethod.GET,new HttpEntity<>(new HttpHeaders()),JSONObject.class);
            code = responseEntity.getStatusCode().value();
        } catch (Exception e){
            e.printStackTrace();
        }
        if(code>=400){
            throw new Exception("iam service is unhealthy");
        }
        return "iam service is healthy";
    }
}
