package com.sugon.cloud.iam.api.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Author : <PERSON> / Peixq
 * Email : <EMAIL>
 * Create : 2018/3/14 上午11:31
 */
public class CommonUtils
{
    private static final Logger logger = LoggerFactory.getLogger(CommonUtils.class);
    /**
     * 检查字符串是否为空
     *
     * @param str
     * @return boolean
     */
    public static boolean isNotNullOrEmpty(String str)
    {
        return ((str != null) && (!str.isEmpty()));
    }


    /**
     * 格式化日期MM月dd日 HH:mm
     *
     * @param date
     * @return
     */
    public static String formatForLog(Date date)
    {
        SimpleDateFormat log_date = new SimpleDateFormat("MM月dd日 HH:mm");
        if (date == null) { return null; }
        return log_date.format(date);
    }

    /**
     * 格式化日期yyyy-MM-dd'T'HH:mm:ss.SSS+0800
     *
     * @param date
     * @return
     */
    public static String formatForEsLog(Date date)
    {
        String FULL_FORMAT = "yyyy-MM-dd\'T\'HH:mm:ss.SSS+0800";

        SimpleDateFormat log_es_date = new SimpleDateFormat(FULL_FORMAT);
        if (date == null) { return null; }
        return log_es_date.format(date);
    }

    /**
     * 获取访问ip地址
     *
     * @param request
     * @return
     */
    public static String getIpAddr(HttpServletRequest request)
    {
        String ipAddress = null;
        try {
            ipAddress = request.getHeader("x-forwarded-for");
            if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getHeader("Proxy-Client-IP");
            }
            if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getHeader("WL-Proxy-Client-IP");
            }
            if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getRemoteAddr();
                if (ipAddress.equals("127.0.0.1") || ipAddress.equals("0:0:0:0:0:0:0:1")) {
                    // 根据网卡取本机配置的IP
                    InetAddress inet = null;
                    try {
                        inet = InetAddress.getLocalHost();
                    }
                    catch (UnknownHostException e) {
                        e.printStackTrace();
                    }
                    ipAddress = inet.getHostAddress();
                }
            }
            // 对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
            if (ipAddress != null && ipAddress.length() > 15) { // "***.***.***.***".length()
                // = 15
                if (ipAddress.indexOf(",") > 0) {
                    ipAddress = ipAddress.substring(0, ipAddress.indexOf(","));
                }
            }
        }
        catch (Exception e) {
            ipAddress = "";
        }
        // ipAddress = this.getRequest().getRemoteAddr();
        return ipAddress;
    }

    /**
     * 判断字符串中是否包含中文
     * @param str
     * 待校验字符串
     * @return 是否为中文
     * @warn 不能校验是否为中文标点符号
     */
    public static boolean isContainChinese(String str) {
        if(StringUtils.isEmpty(str)) return false;
        Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
        Matcher m = p.matcher(str);
        if (m.find()) {
            return true;
        }
        return false;
    }

    public static boolean isContainString(String str1,String str2) {
        if(!StringUtils.isEmpty(str1) && str1.contains(str2)){
            return true;
        }
        return false;
    }

    public static String FormatUrl(String path, Object... params) {
        if (params.length == 0)
            return path;
        return String.format(path, params);
    }

    public static String objectToJsonString(Object obj){
        String targetString = "";
        try {
            if(Objects.nonNull(obj)){
                targetString = new ObjectMapper().writeValueAsString(obj);
            }
        } catch (Exception e){
            logger.error("objectToJsonString error");
        }
        return targetString;
    }

    //当前时间与传入时间的间隔天数
    public static int calculateTotalDay(Date date) {
        long l = 0L;
        if (date == null) date = new Date();
        Date currentDate = new Date();
        try {
            l = (currentDate.getTime() - date.getTime()) / (1000 * 60 * 60 * 24);
            return (int) l;
        } catch (Exception e) {
            logger.error("calculateTotalDay error",e);
        }
        return 0;
    }
    /**
     * 获取指定长度随机字符串
     * @param length 指定长度
     * @return 随机字符串
     */
    public static String getRandomCharacters(int length){
        return UUID.randomUUID().toString().substring(0, length);
    }

    /**
     * 通过header头判断是否从浏览器发起请求
     *
     * @param request
     * @return
     */
    public static boolean isFromWeb(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        String referer = request.getHeader("Referer");
        logger.debug("userAgent:{}，referer:{}", userAgent, referer);
        if (userAgent != null && (userAgent.contains("Mozilla") || userAgent.contains("Chrome") || userAgent.contains("Safari"))) {
            return true;
        }
        return referer != null && !referer.isEmpty();
    }

    /**
     * 手机号脱敏
     * @param phoneNumber
     * @return
     */
    public static String maskPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.length() < 7) {
            return phoneNumber; // 长度不够则不处理
        }
        return phoneNumber.substring(0, 3) + "****" + phoneNumber.substring(phoneNumber.length() - 4);
    }

    /**
     * 邮箱脱敏
     * @param email
     * @return
     */
    public static String maskEmail(String email) {
        if (email == null || !email.contains("@")) {
            return email; // 不是有效邮箱则不处理
        }
        String[] parts = email.split("@");
        String prefix = parts[0];
        String domain = parts[1];

        if (prefix.length() <= 3) {
            return prefix.charAt(0) + generateAsterisks(prefix.length() - 1) + "@" + domain;
        }

        int maskLength = prefix.length() - 3;
        return prefix.substring(0, 3) + generateAsterisks(maskLength) + "@" + domain;
    }

    // 手动生成指定长度的 * 字符串
    private static String generateAsterisks(int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append('*');
        }
        return sb.toString();
    }

    public static String bytesToHexString(byte[] src) {
        StringBuilder stringBuilder = new StringBuilder();
        if (src == null || src.length <= 0) {
            return null;
        }
        for (int i = 0; i < src.length; i++) {
            int v = src[i] & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString();
    }

}

