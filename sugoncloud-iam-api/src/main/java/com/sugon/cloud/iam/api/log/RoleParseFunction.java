package com.sugon.cloud.iam.api.log;

import com.sugon.cloud.iam.api.entity.IamRole;
import com.sugon.cloud.iam.api.service.IamRoleService;
import com.sugon.cloud.log.client.service.IParseFunction;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Objects;

@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class RoleParseFunction implements IParseFunction {

    private final IamRoleService roleService;

    @Override
    public String functionName() {
        return "ROLE";
    }

    @Override
    public String apply(String value) {
        IamRole iamRole;
        if (StringUtils.isEmpty(value) || Objects.isNull(iamRole = roleService.getById(value))) {
            return "";
        }
        return iamRole.getName();
    }
}
