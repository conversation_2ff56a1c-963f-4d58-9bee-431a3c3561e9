package com.sugon.cloud.iam.api.entity;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;

/**
 * (Seeds)表实体类
 *
 * <AUTHOR>
 * @since 2024-08-07 10:45:29
 */
@SuppressWarnings("serial")
@TableName("seeds")
public class SeedEntity extends Model<SeedEntity> {

    @TableId
    private String code;

    private String privateKey;

    private Integer seedLen;

    private String seed;

    private Integer codeLen;

    private Integer codeTime;


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    public Integer getSeedLen() {
        return seedLen;
    }

    public void setSeedLen(Integer seedLen) {
        this.seedLen = seedLen;
    }

    public String getSeed() {
        return seed;
    }

    public void setSeed(String seed) {
        this.seed = seed;
    }

    public Integer getCodeLen() {
        return codeLen;
    }

    public void setCodeLen(Integer codeLen) {
        this.codeLen = codeLen;
    }

    public Integer getCodeTime() {
        return codeTime;
    }

    public void setCodeTime(Integer codeTime) {
        this.codeTime = codeTime;
    }

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    protected Serializable pkVal() {
        return this.code;
    }
}

