package com.sugon.cloud.iam.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sugon.cloud.iam.api.enums.UserAuthorizeScope;
import com.sugon.cloud.iam.api.enums.UserAuthorizeType;
import lombok.Data;

/**
 * (UserAuthorizeSettings)实体类
 *
 * <AUTHOR>
 * @since 2023-05-16 14:12:50
 */
@Data
@TableName("user_authorize_settings")
public class UserAuthorizeSettings {
    /**
     * 主键Id
     */
    @TableId(type = IdType.INPUT)
    private String id;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 认证模式:UserAuthorizeType
     */
    private UserAuthorizeType type;
    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 访问途径：UserAuthorizeScope
     */
    private UserAuthorizeScope scope;
    /**
     * 额外参数
     */
    private String extra;
}

