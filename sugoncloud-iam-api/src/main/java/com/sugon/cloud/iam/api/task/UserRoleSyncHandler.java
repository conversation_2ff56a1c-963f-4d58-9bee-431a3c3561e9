package com.sugon.cloud.iam.api.task;

import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.service.UserRoleMappingService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;



@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
@RefreshScope
public class UserRoleSyncHandler {

    private final UserRoleMappingService userRoleMappingService;

    @XxlJob(CommonInstance.USER_ROLE_SYNC_HANDLER)
    public void run() {
        log.info("::::::::::::::user_role_sync start");
        userRoleMappingService.syncUserRole2Redis();
        log.info("::::::::::::::user_role_sync end");
    }
}
