package com.sugon.cloud.iam.api.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

@Setter
@Getter
@ApiModel("修改用户密码")
public class UpdateUserPwdVO {
    @JsonProperty("old_password")
    @ApiModelProperty("旧密码")
    @NotBlank(message = "旧密码能为空")
    private String oldPassword;
    @ApiModelProperty("新密码")
    @NotBlank(message = "新密码不能为空")
    private String password;
    @ApiModelProperty("公钥")
    private String publickey;
}
