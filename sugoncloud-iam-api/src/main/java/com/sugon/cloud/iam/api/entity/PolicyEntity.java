package com.sugon.cloud.iam.api.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;

import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
public class PolicyEntity implements Serializable {

    private static final long serialVersionUID = -8409886611028760083L;

    @ApiModelProperty("版本")
    @Size(max = 8)
    private String version;

    @Valid
    @ApiModelProperty("策略详情")
    private StatementEntity statement;

}
