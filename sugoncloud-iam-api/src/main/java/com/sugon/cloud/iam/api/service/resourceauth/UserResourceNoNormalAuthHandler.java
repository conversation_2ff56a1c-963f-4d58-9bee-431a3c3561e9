package com.sugon.cloud.iam.api.service.resourceauth;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.service.UserService;
import com.sugon.cloud.iam.common.model.vo.UserViewVO;
import com.sugon.cloud.resource.auth.common.service.impl.BaseResourceAuthHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/10 17:46
 */
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class UserResourceNoNormalAuthHandler extends BaseResourceAuthHandler {

    private final UserService userService;

    private final UserResourceAuthHandler userResourceAuthHandler;

    @Override
    public List<String> getUserResource(String userId) {
        User user = userService.getById(userId);
        // 普通用户无权访问
        if (StrUtil.isBlank(user.getType())) {
            return null;
        }
        return userResourceAuthHandler.getUserResource(userId);
    }
}
