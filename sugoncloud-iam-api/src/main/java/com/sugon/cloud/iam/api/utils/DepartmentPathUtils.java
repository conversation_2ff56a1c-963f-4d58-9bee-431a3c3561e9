package com.sugon.cloud.iam.api.utils;

import com.sugon.cloud.iam.api.entity.Department;
import com.sugon.cloud.iam.common.model.vo.DepartmentTreeDetailVO;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

public class DepartmentPathUtils {
    public static String buildDeptPath(String currentDeptId, List<DepartmentTreeDetailVO> departments){
        String deptPath = "";
        if(StringUtils.isNotEmpty(currentDeptId)){
            for(DepartmentTreeDetailVO department : departments){
                if(currentDeptId.equals(department.getId())){
                    deptPath = recursionDeptPath(deptPath,department,departments);
                }
            }
        }
        return deptPath;
    }

    /**
     * 递归查找组织path: 例如：sugon_dept >> 二级组织 >> 三级组织
     * @param department
     * @param departments
     * @return
     */
    private static String recursionDeptPath(String deptPath,DepartmentTreeDetailVO department,List<DepartmentTreeDetailVO> departments){
        String targetStr = department.getName();
        if(StringUtils.isEmpty(deptPath)){
            deptPath = targetStr;
        }else {
            deptPath = targetStr + " -> " + deptPath;
        }
        if(!StringUtils.isEmpty(department.getParentId())){
            List<DepartmentTreeDetailVO> parentDept = departments.stream().filter(d -> d.getId().equals(department.getParentId())).collect(Collectors.toList());
            deptPath = recursionDeptPath(deptPath,parentDept.get(0),departments);
        }
        return deptPath;
    }

}
