package com.sugon.cloud.iam.api.service;

import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.vo.MFADetailVO;
import com.sugon.cloud.iam.api.vo.MfaResultVO;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/05/16 14:10
 **/
public interface MFAAuthorizeService extends UserAuthorizeService {
    MFADetailVO get(String userId, boolean queryQr);

    MfaResultVO open(String userId, String certificate, Integer operateType, boolean login, boolean danger);

    String close(String userId, User deletedUser);

    boolean auth(String userId, String tokenSN, String mfaCode);

    Map<String, Boolean> getUserMFAMap();

    boolean checkValidateCode(String businessType, MFADetailVO mfaDetailVO);

}
