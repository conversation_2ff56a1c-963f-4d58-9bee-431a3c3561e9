package com.sugon.cloud.iam.api.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sugon.cloud.common.constant.SgCommonConstant;
import com.sugon.cloud.common.constants.HeaderParamConstant;
import com.sugon.cloud.common.utils.I18nUtil;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.common.RecursionDeptIdUtils;
import com.sugon.cloud.iam.api.entity.BlacklistMicroService;
import com.sugon.cloud.iam.api.entity.Department;
import com.sugon.cloud.iam.api.entity.keystone.Project;
import com.sugon.cloud.iam.api.exception.BusinessException;
import com.sugon.cloud.iam.api.mapper.DepartmentMapper;
import com.sugon.cloud.iam.api.mapper.ProjectMapper;
import com.sugon.cloud.iam.api.mapper.QuotaMetricMapper;
import com.sugon.cloud.iam.api.mapper.QuotaTypeMapper;
import com.sugon.cloud.iam.api.service.BlacklistMicroServiceService;
import com.sugon.cloud.iam.api.service.DepartmentService;
import com.sugon.cloud.iam.api.service.QuotaService;
import com.sugon.cloud.iam.api.vo.DepartmentDetailVO;
import com.sugon.cloud.iam.common.enums.BlacklistTypeEnum;
import com.sugon.cloud.iam.common.model.vo.*;
import com.sugon.cloud.log.client.service.MessageFeignService;
import com.sugon.cloud.log.common.model.vo.MessageVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.mail.Quota;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RefreshScope
@Slf4j
public class QuotaServiceImpl implements QuotaService {

    private final QuotaTypeMapper quotaTypeMapper;
    private final QuotaMetricMapper quotaMetricMapper;
    private final DepartmentMapper departmentMapper;
    private final ProjectMapper projectMapper;
    private final MessageFeignService messageFeignService;
    private final HttpServletRequest request;

    private final BlacklistMicroServiceService blacklistMicroServiceService;
    private final DepartmentService departmentService;

    @Value("${black.enable:false}")
    private boolean blackEnable;

    private static Lock quotaUsedLock = new ReentrantLock();

    private String getRegionIdByHeader() {
        String regionId = request.getHeader(HeaderParamConstant.REGION_ID);
        if (StringUtils.isEmpty(regionId)) {
            throw new BusinessException("Header必须传regionId");
        }
        return regionId;

    }

    @Override
    public List<QuotaType> getQuotaByDeparmentId(String departmentId, String typeName, String categoryId) {
        String regionId = this.getRegionIdByHeader();
        Map<String, Object> typeParam = Maps.newHashMap();
        if (StringUtils.isNotEmpty(typeName)) {
            typeParam.put("typeName", typeName);
        }
        if (StringUtils.isNotEmpty(categoryId)) {
            typeParam.put("categoryId", categoryId);
        }
        if (blackEnable) {
            List<BlacklistMicroService> blacklistMicroServices = blacklistMicroServiceService.listByTypes(Arrays.asList(BlacklistTypeEnum.MICRO, BlacklistTypeEnum.CATEGORY));
            typeParam.put("microIds", blacklistMicroServices.stream().filter(p -> BlacklistTypeEnum.MICRO.equals(p.getType())).map(BlacklistMicroService::getId).collect(Collectors.toList()));
            typeParam.put("categoryIds", blacklistMicroServices.stream().filter(p -> BlacklistTypeEnum.CATEGORY.equals(p.getType())).map(BlacklistMicroService::getId).collect(Collectors.toList()));
        }
        List<QuotaType> quotaTypeList = quotaTypeMapper.getQuotaTypes(typeParam);
        Map<String, String> param = Maps.newHashMap();
        param.put("departmentId", departmentId);
        param.put("regionId", regionId);
        List<QuotaMetric> quotaMetricList = quotaMetricMapper.getDepartmentQuotaByParam(param);
        List<QuotaMetric> parentMetricList = Lists.newArrayList();
        Department department = departmentMapper.selectById(departmentId);
        if (!CommonInstance.MASTER_DEPARTMENT_LEVEL.equals(department.getLevel())) {
            param.put("departmentId", department.getParentId());
            parentMetricList.addAll(quotaMetricMapper.getDepartmentQuotaByParam(param));
        }
        for (QuotaType qt : quotaTypeList) {
            qt.setDescription(I18nUtil.getMessageByKey(qt.getDescription(), qt.getDescription()));
            List<QuotaMetric> typeMetrics = quotaMetricList.stream().filter(quotaMetric -> quotaMetric.getTypeName().equals(qt.getName())).collect(Collectors.toList());
            for (QuotaMetric qm : typeMetrics) {
                qm.setDepartmentId(departmentId);
                qm.setParentAvailable(getParentAvailable(parentMetricList, qm.getName()));
            }
            qt.setQuotaMetricList(typeMetrics);
        }
        return quotaTypeList;
    }

    private Long getParentAvailable(List<QuotaMetric> parentMetricList, String quotaMetric) {
        Long parentAvailable = 0L;
        List<QuotaMetric> targetMetrics = parentMetricList.stream().filter(qm -> quotaMetric.equals(qm.getName())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(targetMetrics)) {
            parentAvailable = targetMetrics.get(0).getTotalValue() - targetMetrics.get(0).getUsedValue();
        }
        return parentAvailable;
    }

    @Override
    public List<QuotaType> getQuotaByProjectId(String projectId, String typeName, String categoryId) {
        String regionId = this.getRegionIdByHeader();
        Map<String, Object> typeParam = Maps.newHashMap();
        if (StringUtils.isNotEmpty(typeName)) {
            typeParam.put("typeName", typeName);
        }
        if (StringUtils.isNotEmpty(categoryId)) {
            typeParam.put("categoryId", categoryId);
        }
        if (blackEnable) {
            List<BlacklistMicroService> blacklistMicroServices = blacklistMicroServiceService.listByTypes(Arrays.asList(BlacklistTypeEnum.MICRO, BlacklistTypeEnum.CATEGORY));
            typeParam.put("microIds", blacklistMicroServices.stream().filter(p -> BlacklistTypeEnum.MICRO.equals(p.getType())).map(BlacklistMicroService::getId).collect(Collectors.toList()));
            typeParam.put("categoryIds", blacklistMicroServices.stream().filter(p -> BlacklistTypeEnum.CATEGORY.equals(p.getType())).map(BlacklistMicroService::getId).collect(Collectors.toList()));
        }
        List<QuotaType> quotaTypeList = quotaTypeMapper.getQuotaTypes(typeParam);
        Map<String, String> param = Maps.newHashMap();
        param.put("projectId", projectId);
        param.put("regionId", regionId);
        List<QuotaMetric> quotaMetricList = quotaMetricMapper.getProjectQuotaByParam(param);
        Map<String, String> deptParam = Maps.newHashMap();
        //404626 从根部分组织获取配额
        if (StrUtil.isNotBlank(projectId)) {
            Project project = projectMapper.selectById(projectId);
            Assert.isTrue(Objects.nonNull(project), "项目不存在");
            Department rootDepartmentByDeptId = departmentService.getRootDepartmentByDeptId(project.getDeptId());
            deptParam.put("departmentId", rootDepartmentByDeptId.getId());
        }
        deptParam.put("regionId", regionId);
        List<QuotaMetric> parentMetricList = quotaMetricMapper.getDepartmentQuotaByParam(deptParam);
        for (QuotaType qt : quotaTypeList) {
            qt.setDescription(I18nUtil.getMessageByKey(qt.getDescription(), qt.getDescription()));
            List<QuotaMetric> typeMetrics = quotaMetricList.stream().filter(quotaMetric -> quotaMetric.getTypeName().equals(qt.getName())).collect(Collectors.toList());
            for (QuotaMetric qm : typeMetrics) {
                qm.setProjectId(projectId);
                qm.setParentAvailable(getParentAvailable(parentMetricList, qm.getName()));
            }
            qt.setQuotaMetricList(typeMetrics);
        }
        return quotaTypeList;
    }

    /**
     * 更新组织的配额
     *
     * @param departmentId
     * @param quotaType
     * @return
     */
    @Override
    public QuotaType updateDepartmentQuota(String departmentId, QuotaType quotaType) {
        //判断组织是否存在
        Department department = departmentMapper.selectById(departmentId);
        if (Objects.isNull(department)) {
            throw new BusinessException("组织不存在");
        }

        //判断总量不能小于使用量
        for (QuotaMetric qm : quotaType.getQuotaMetricList()) {
            if (Objects.isNull(qm.getTotalValue())) {
                throw new BusinessException(qm.getTotalName() + "不能为空");
            }
            // 使用量通过查数据库来获取（fix bug-3298）
            checkMetric(getMetricByNameAndDepartmentId(qm.getName(), departmentId), qm);
        }
        if (CommonInstance.MASTER_DEPARTMENT_LEVEL.equals(department.getLevel())) {
            this.updateTopDepartmentQuota(quotaType.getQuotaMetricList(), department);
        } else {
            this.updateChildDepartmentQuota(quotaType.getQuotaMetricList(), department);
        }
        return quotaType;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public QuotaType updateDepartmentQuotaAndUsed(String departmentId, QuotaType quotaType) {
        //判断组织是否存在
        Department department = departmentMapper.selectById(departmentId);
        if (Objects.isNull(department)) {
            throw new BusinessException("组织不存在");
        }
        if (!CommonInstance.MASTER_DEPARTMENT_LEVEL.equals(department.getLevel())) {
            throw new BusinessException("非根组织不允许更新配额");
        }
        //判断总量不能小于使用量
        for (QuotaMetric qm : quotaType.getQuotaMetricList()) {
            if (Objects.isNull(qm.getTotalValue())) {
                throw new BusinessException(qm.getTotalName() + "不能为空");
            }
            if (Objects.isNull(qm.getUsedValue())) {
                throw new BusinessException(qm.getUsedName() + "不能为空");
            }
            if (qm.getTotalValue() < qm.getUsedValue()) {
                throw new BusinessException(qm.getTotalName() + "不能小于" + qm.getUsedName());
            }
        }
        String regionId = this.getRegionIdByHeader();

        for (QuotaMetric newMetric : quotaType.getQuotaMetricList()) {
            QuotaMetric oldMetric = this.getMetricByNameAndDepartmentId(newMetric.getName(), departmentId);
            newMetric.setRegionId(regionId);
            if (Objects.isNull(oldMetric)) {
                quotaMetricMapper.insertDepartmentQuota(newMetric);
            } else {
                quotaMetricMapper.updateDepartmentQuotaAndUsed(newMetric);
            }
        }
        return quotaType;
    }

    /**
     * 更新项目的配额
     *
     * @param projectId
     * @param quotaType
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public QuotaType updateProjectQuota(String projectId, QuotaType quotaType) {
        String regionId = this.getRegionIdByHeader();
        //判断项目是否存在
        Project project = projectMapper.selectById(projectId);
        if (Objects.isNull(project)) {
            throw new BusinessException("项目不存在");
        }
        Department rootDepartmentByDeptId = departmentService.getRootDepartmentByDeptId(project.getDeptId());
        //判断总量不能小于使用量
        for (QuotaMetric newMetric : quotaType.getQuotaMetricList()) {
            if (Objects.isNull(newMetric.getTotalValue())) {
                throw new BusinessException(newMetric.getTotalName() + "不能为空");
            }
            // 使用量通过查数据库来获取（fix bug-3298）
            checkMetric(getMetricByNameAndProjectId(newMetric.getName(), projectId), newMetric);
        }
        for (QuotaMetric newMetric : quotaType.getQuotaMetricList()) {
            QuotaMetric oldMetric = this.getMetricByNameAndProjectId(newMetric.getName(), projectId);
            //获取根组织的配额
            QuotaMetric departmentMetric = this.getMetricByNameAndDepartmentId(newMetric.getName(), rootDepartmentByDeptId.getId());
            //从查询组织配额修改为查询根组织配额 如果根组织配额为null，则初始化根组织配额
            if (Objects.isNull(departmentMetric)) {
                QuotaMetric initalMetric = new QuotaMetric();
                //初始化根部门的指标（之前更新到了项目部门的指标，新的配额逻辑是根部门->项目，不涉及到了二级部门）
                initalMetric.setDepartmentId(rootDepartmentByDeptId.getId());
                initalMetric.setName(newMetric.getName());
                initalMetric.setTotalValue(0L);
                initalMetric.setUsedValue(0L);
                initalMetric.setRegionId(regionId);
                quotaMetricMapper.insertDepartmentQuota(initalMetric);
                departmentMetric = initalMetric;
            }
            Long incQuota;
            //获取增量配额
            if (Objects.isNull(oldMetric)) {
                incQuota = newMetric.getTotalValue();
            } else {
                incQuota = newMetric.getTotalValue() - oldMetric.getTotalValue();
            }
            //判断根组织配额 与 增量+已使用量 大小
            if (departmentMetric.getTotalValue() < (incQuota + departmentMetric.getUsedValue())) {
                throw new BusinessException("项目所属组织" + newMetric.getTotalName() + "已满足不了申请");
            }
        }
        for (QuotaMetric newMetric : quotaType.getQuotaMetricList()) {
            QuotaMetric oldMetric = this.getMetricByNameAndProjectId(newMetric.getName(), projectId);
            //获取根组织的配额
            QuotaMetric departmentMetric = this.getMetricByNameAndDepartmentId(newMetric.getName(), rootDepartmentByDeptId.getId());
            Long incQuota = 0L;
            departmentMetric.setRegionId(regionId);
            newMetric.setRegionId(regionId);
            if (Objects.isNull(oldMetric)) {
                incQuota = newMetric.getTotalValue();
                quotaMetricMapper.insertProjectQuota(newMetric);
            } else {
                incQuota = newMetric.getTotalValue() - oldMetric.getTotalValue();
                quotaMetricMapper.updateProjectQuota(newMetric);
            }
            departmentMetric.setUsedValue(departmentMetric.getUsedValue() + incQuota);
            quotaMetricMapper.updateDepartmentUsed(departmentMetric);
        }
        MessageVo messageVo = new MessageVo();
        messageVo.setSvc("sugoncloud-iam-api");
        messageVo.setEmailEnable(false);
        messageVo.setProject(projectId);
        messageVo.setContent("项目[" + project.getAlias() + "]的[" + quotaType.getDescription() + "]配额已被修改");
        messageFeignService.createMessage(messageVo);
        return quotaType;
    }

    private void checkMetric(QuotaMetric originMetric, QuotaMetric newMetric) {
        if (Objects.isNull(originMetric) || Objects.isNull(originMetric.getUsedValue())) {
            newMetric.setUsedValue(0L);
        } else {
            newMetric.setUsedValue(originMetric.getUsedValue());
        }
        if (newMetric.getTotalValue() < newMetric.getUsedValue()) {
            throw new BusinessException(newMetric.getTotalName() + "不能小于" + newMetric.getUsedName());
        }
    }

    /**
     * 更新项目的配额的使用量
     *
     * @param quotaProjectValue
     * @return
     */
    @Override
    public QuotaProjectValue updateProjectQuotaUsed(QuotaProjectValue quotaProjectValue) {
        log.info("更新配额使用量");
        try {
            String regionId = StringUtils.isEmpty(quotaProjectValue.getRegionId()) ? request.getHeader(HeaderParamConstant.REGION_ID) : quotaProjectValue.getRegionId();
            //判断项目是否存在
            Project project = projectMapper.selectById(quotaProjectValue.getProjectId());
            if (Objects.isNull(project)) {
                throw new BusinessException("项目不存在");
            }
            if (quotaProjectValue.getUsedValue() < 0) {
                quotaProjectValue.setUsedValue(0L);
            }
            quotaProjectValue.setRegionId(regionId);
            quotaMetricMapper.updateProjectUsed(quotaProjectValue);
        } catch (Exception e) {
            log.error("更新项目配额使用量失败", e);
            throw new BusinessException("更新项目配额使用量失败");
        }
        return quotaProjectValue;
    }

    private QuotaMetric getMetricByNameAndDepartmentId(String metricName, String departmentId) {
        String regionId = this.getRegionIdByHeader();
        Map<String, String> param = Maps.newHashMap();
        param.put("departmentId", departmentId);
        param.put("metricName", metricName);
        param.put("regionId", regionId);
        return quotaMetricMapper.getDepartmentQuotaValue(param);
    }

    private QuotaMetric getMetricByNameAndProjectId(String metricName, String projectId) {
        String regionId = this.getRegionIdByHeader();
        Map<String, String> param = Maps.newHashMap();
        param.put("projectId", projectId);
        param.put("metricName", metricName);
        param.put("regionId", regionId);
        return quotaMetricMapper.getProjectQuotaValue(param);
    }

    /**
     * 通过组织ID + metricName 获取配额
     *
     * @param metricName
     * @param departmentId
     * @return
     */
    private QuotaMetric getMetricByNameAndDeptId(String metricName, String departmentId) {
        String regionId = this.getRegionIdByHeader();
        Map<String, String> param = Maps.newHashMap();
        param.put("departmentId", departmentId);
        param.put("metricName", metricName);
        param.put("regionId", regionId);
        return quotaMetricMapper.getDepartmentQuotaValue(param);
    }

    private QuotaMetric getMetricByNameAndProjectIdNoRegion(String metricName, String projectId, String regionId) {
        Map<String, String> param = Maps.newHashMap();
        param.put("projectId", projectId);
        param.put("metricName", metricName);
        param.put("regionId", regionId);
        return quotaMetricMapper.getProjectQuotaValue(param);
    }

    //更新根组织的配额
    @Transactional(propagation = Propagation.REQUIRED)
    public void updateTopDepartmentQuota(List<QuotaMetric> quotaMetrics, Department department) {
        String regionId = this.getRegionIdByHeader();
        for (QuotaMetric newMetric : quotaMetrics) {
            QuotaMetric oldMetric = this.getMetricByNameAndDepartmentId(newMetric.getName(), department.getId());
            newMetric.setRegionId(regionId);
            if (Objects.isNull(oldMetric)) {
                quotaMetricMapper.insertDepartmentQuota(newMetric);
            } else {
                quotaMetricMapper.updateDepartmentQuota(newMetric);
            }
        }
    }

    //更新根组织的配额
    public void updateTopDepartmentQuotaTotalAndUsed(List<QuotaMetric> quotaMetrics, Department department) {
        String regionId = this.getRegionIdByHeader();
        for (QuotaMetric newMetric : quotaMetrics) {
            QuotaMetric oldMetric = this.getMetricByNameAndDepartmentId(newMetric.getName(), department.getId());
            newMetric.setRegionId(regionId);
            if (Objects.isNull(oldMetric)) {
                quotaMetricMapper.insertDepartmentQuota(newMetric);
            } else {
                quotaMetricMapper.updateDepartmentQuotaAndUsed(newMetric);
            }
        }
    }

    //更新非根组织的配额
    @Transactional(propagation = Propagation.REQUIRED)
    public void updateChildDepartmentQuota(List<QuotaMetric> quotaMetrics, Department department) {
        String regionId = this.getRegionIdByHeader();
        //检查父级组织的配额是否足够申请
        for (QuotaMetric newMetric : quotaMetrics) {
            if (Objects.isNull(newMetric.getTotalValue())) {
                throw new BusinessException(newMetric.getTotalName() + "不能为空");
            }
            QuotaMetric oldMetric = this.getMetricByNameAndDepartmentId(newMetric.getName(), department.getId());
            QuotaMetric parentMetric = this.getMetricByNameAndDepartmentId(newMetric.getName(), department.getParentId());
            if (Objects.isNull(parentMetric)) {
                QuotaMetric initalMetric = new QuotaMetric();
                initalMetric.setDepartmentId(department.getParentId());
                initalMetric.setName(newMetric.getName());
                initalMetric.setTotalValue(0L);
                initalMetric.setUsedValue(0L);
                initalMetric.setRegionId(regionId);
                quotaMetricMapper.insertDepartmentQuota(initalMetric);
                parentMetric = initalMetric;
            }
            Long incQuota;
            if (Objects.isNull(oldMetric)) {
                incQuota = newMetric.getTotalValue();
            } else {
                incQuota = newMetric.getTotalValue() - oldMetric.getTotalValue();
            }
            if (parentMetric.getTotalValue() < (incQuota + parentMetric.getUsedValue())) {
                throw new BusinessException("父级组织的" + newMetric.getTotalName() + "已满足不了申请");
            }
        }
        for (QuotaMetric newMetric : quotaMetrics) {
            QuotaMetric oldMetric = this.getMetricByNameAndDepartmentId(newMetric.getName(), department.getId());
            QuotaMetric parentMetric = this.getMetricByNameAndDepartmentId(newMetric.getName(), department.getParentId());
            Long incQuota;
            newMetric.setRegionId(regionId);
            if (Objects.isNull(oldMetric)) {
                incQuota = newMetric.getTotalValue();
                quotaMetricMapper.insertDepartmentQuota(newMetric);
            } else {
                incQuota = newMetric.getTotalValue() - oldMetric.getTotalValue();
                quotaMetricMapper.updateDepartmentQuota(newMetric);
            }
            parentMetric.setUsedValue(parentMetric.getUsedValue() + incQuota);
            quotaMetricMapper.updateDepartmentUsed(parentMetric);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void deleteProjectQuota(ProjectDetailVO projectDetailVO) {
        String regionId = this.getRegionIdByHeader();
        Map<String, String> projectParam = Maps.newHashMap();
        projectParam.put("projectId", projectDetailVO.getId());
        projectParam.put("regionId", regionId);
        List<QuotaMetric> projectMetricList = quotaMetricMapper.getProjectQuotaByParam(projectParam);
        //bug 416577
        Department rootDepartmentByDeptId = departmentService.getRootDepartmentByDeptId(projectDetailVO.getDeptId());
        Map<String, String> deptParam = Maps.newHashMap();
        deptParam.put("departmentId", rootDepartmentByDeptId.getId());
        deptParam.put("regionId", regionId);
        List<QuotaMetric> deptMetricList = quotaMetricMapper.getDepartmentQuotaByParam(deptParam);
        quotaMetricMapper.deleteProjectQuota(projectDetailVO.getId(), regionId);
        for (QuotaMetric projectQm : projectMetricList) {
            for (QuotaMetric deptQm : deptMetricList) {
                if (projectQm.getName().equals(deptQm.getName())) {
                    long newUsed = deptQm.getUsedValue() - projectQm.getTotalValue();
                    if (newUsed < 0) {
                        newUsed = 0L;
                    }
                    deptQm.setRegionId(regionId);
                    deptQm.setUsedValue(newUsed);
                    deptQm.setDepartmentId(rootDepartmentByDeptId.getId());
                    quotaMetricMapper.updateDepartmentUsed(deptQm);
                }
            }
        }
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void deleteDepartmentQuota(DepartmentDetailVO departmentDetailVO) {
        String regionId = this.getRegionIdByHeader();
        Map<String, String> selfParam = Maps.newHashMap();
        selfParam.put("departmentId", departmentDetailVO.getId());
        selfParam.put("regionId", regionId);
        List<QuotaMetric> selfMetricList = quotaMetricMapper.getDepartmentQuotaByParam(selfParam);

        Map<String, String> parentParam = Maps.newHashMap();
        parentParam.put("departmentId", departmentDetailVO.getParentId());
        parentParam.put("regionId", regionId);
        List<QuotaMetric> parentMetricList = quotaMetricMapper.getDepartmentQuotaByParam(parentParam);

        quotaMetricMapper.deleteDepartmentQuota(departmentDetailVO.getId(), regionId);
        for (QuotaMetric selfQm : selfMetricList) {
            for (QuotaMetric parentQm : parentMetricList) {
                if (selfQm.getName().equals(parentQm.getName())) {
                    long newUsed = parentQm.getUsedValue() - selfQm.getTotalValue();
                    if (newUsed < 0) {
                        newUsed = 0L;
                    }
                    parentQm.setRegionId(regionId);
                    parentQm.setUsedValue(newUsed);
                    parentQm.setDepartmentId(departmentDetailVO.getParentId());
                    quotaMetricMapper.updateDepartmentUsed(parentQm);
                }
            }
        }
    }

    @Override
    public List<QuotaType> getQuotaOverview(String categoryId) {
        String regionId = this.getRegionIdByHeader();
        Map<String, Object> typeParam = Maps.newHashMap();
        if (StringUtils.isNotEmpty(categoryId)) {
            typeParam.put("categoryId", categoryId);
        }
        if (blackEnable) {
            List<BlacklistMicroService> blacklistMicroServices = blacklistMicroServiceService.listByTypes(Arrays.asList(BlacklistTypeEnum.MICRO, BlacklistTypeEnum.CATEGORY));
            typeParam.put("microIds", blacklistMicroServices.stream().filter(p -> BlacklistTypeEnum.MICRO.equals(p.getType())).map(BlacklistMicroService::getId).collect(Collectors.toList()));
            typeParam.put("categoryIds", blacklistMicroServices.stream().filter(p -> BlacklistTypeEnum.CATEGORY.equals(p.getType())).map(BlacklistMicroService::getId).collect(Collectors.toList()));
        }
        List<QuotaType> quotaTypeList = quotaTypeMapper.getQuotaTypes(typeParam);
        typeParam.put("regionId", regionId);
        List<QuotaMetric> quotaMetricList = quotaMetricMapper.getQuotaOverview(regionId);
        for (QuotaType qt : quotaTypeList) {
            qt.setDescription(I18nUtil.getMessageByKey(qt.getDescription(), qt.getDescription()));
            //生产环境存在QuotaMetric 指标 数据不存在，quota_department_value存在指标
            //导致 quotaMetric.getTypeName() == null
            List<QuotaMetric> typeMetrics = quotaMetricList.stream()
                    .filter(quotaMetric -> quotaMetric.getTypeName() != null && quotaMetric.getTypeName().equals(qt.getName()))
                    .collect(Collectors.toList());
            qt.setQuotaMetricList(typeMetrics);
        }
        return quotaTypeList;
    }

    @Override
    public List<QuotaRootUserEntity> getRootUserQuotaList(String typeName, String departmentName) {
        String regionId = this.getRegionIdByHeader();
        log.info("regionId: {}", regionId);
        Map<String, String> param = Maps.newHashMap();
        param.put("typeName", typeName);
        param.put("departmentName", departmentName);
        param.put("region_id", regionId);
        return quotaMetricMapper.getRootUserQuota(param);
    }


    @Override
    public List<QuotaMetric> updateRootUserQuota(List<QuotaMetric> quotaMetrics) throws Exception {
        String regionId = this.getRegionIdByHeader();
        //判断总量不能小于使用量
        for (QuotaMetric qm : quotaMetrics) {
            if (Objects.isNull(qm.getTotalValue())) {
                throw new BusinessException(qm.getTotalName() + "不能为空");
            }
            if (qm.getTotalValue() < qm.getUsedValue()) {
                throw new Exception(qm.getTotalName() + "不能小于" + qm.getUsedName());
            }
        }
        for (QuotaMetric qm : quotaMetrics) {
            QuotaMetric oldMetric = this.getMetricByNameAndDepartmentId(qm.getName(), qm.getDepartmentId());
            Long incQuota = 0L;
            if (Objects.isNull(oldMetric)) {
                incQuota = qm.getTotalValue();
            } else {
                if (qm.getTotalValue() < oldMetric.getUsedValue()) {
                    throw new Exception(qm.getTotalName() + "不能小于" + qm.getUsedName());
                }
                incQuota = qm.getTotalValue() - oldMetric.getTotalValue();
            }
            if (incQuota > qm.getParentAvailable()) {
                throw new Exception("平台的" + qm.getTotalName() + "已满足不了申请");
            }
            qm.setRegionId(regionId);
            if (Objects.isNull(oldMetric)) {
                quotaMetricMapper.insertDepartmentQuota(qm);
            } else {
                quotaMetricMapper.updateDepartmentQuota(qm);
            }
        }
        return quotaMetrics;
    }

    @Override
    public List<QuotaMetricTopology> getTopology(String typeName, String departmentId) {
        String regionId = this.getRegionIdByHeader();
        Map<String, String> param = Maps.newHashMap();
        param.put("typeName", typeName);
        param.put("departmentId", departmentId);
        param.put("regionId", regionId);
        return quotaMetricMapper.getTopology(param);
    }

    @Override
    public void resetTotalQuotaByProjects(List<QuotaMetricAndProjectsUsedVO> quotaMetricAndProjectsUsedVOs) {
        if (CollectionUtils.isEmpty(quotaMetricAndProjectsUsedVOs)) {
            return;
        }
        List<String> regionIds = quotaMetricAndProjectsUsedVOs.stream().map(QuotaMetricAndProjectsUsedVO::getRegionId).distinct().collect(Collectors.toList());
        List<String> metricNames = quotaMetricAndProjectsUsedVOs.stream().map(QuotaMetricAndProjectsUsedVO::getMetricName).collect(Collectors.toList());
        if (regionIds.size() != 1) {
            throw new BusinessException("单次重置只允许设置一个区域的配额");
        }
        quotaMetricAndProjectsUsedVOs.forEach(e -> {
            /************ 1.初始化部门指标 *************/
            this.initDepartQuotaByMetricName(e.getRegionId(), e.getMetricName());
            /************ 2.初始化项目指标 *************/
            List<QuotaMetricAndProjectsUsedVO.QuotaProjectsUsed> quotaProjectsUseds = e.getQuotaProjectsUseds();
            List<String> projectIds = quotaProjectsUseds.stream().map(QuotaMetricAndProjectsUsedVO.QuotaProjectsUsed::getProjectId).collect(Collectors.toList());
            //project id is null 不执行后续操作
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(projectIds)) return;
            //先删除指定项目的指标配额
            quotaMetricMapper.deleteProjectQuotaByProjectsAndMetric(projectIds, e.getRegionId(), e.getMetricName());
            //插入项目配额
            List<QuotaMetric> quotaMetrics = quotaProjectsUseds.stream().map(quotaProjectsUsed -> QuotaMetric.builder()
                    .projectId(quotaProjectsUsed.getProjectId())
                    .usedValue(quotaProjectsUsed.getUseValue())
                    .totalValue(Objects.equals(quotaProjectsUsed.getProjectId(), SgCommonConstant.ADMIN_INNER_PROJECT) ? 999999999 : quotaProjectsUsed.getUseValue())
                    .name(e.getMetricName())
                    .regionId(e.getRegionId())
                    .build()).collect(Collectors.toList());
            if(org.apache.commons.collections4.CollectionUtils.isEmpty(quotaMetrics)) return;
            quotaMetricMapper.insertBatchProjectQuota(quotaMetrics);
        });
        /************ 3.重置部门使用量和总量 *************/
        this.resetQuota(regionIds.get(0), metricNames);
    }

    @Override
    public void resetQuotaRootGb(String regionId, String metricName) {
        this.proessDepart(regionId, metricName);
        this.proessProject(regionId, metricName);
        /*List<Department> departments = departmentMapper.selectList(new LambdaQueryWrapper<Department>()
                .ne(Department::getId,"admin"));
        List<Department> level4Depts = departments.stream().filter(d -> d.getLevel() == 4).collect(Collectors.toList());
        List<Department> level3Depts = departments.stream().filter(d -> d.getLevel() == 3).collect(Collectors.toList());
        List<Department> level2Depts = departments.stream().filter(d -> d.getLevel() == 2).collect(Collectors.toList());
        List<Department> level1Depts = departments.stream().filter(d -> d.getLevel() == 1).collect(Collectors.toList());
        List<Department> level0Depts = departments.stream().filter(d -> d.getLevel() == 0).collect(Collectors.toList());
        this.proessLevelBottom(level4Depts,regionId,metricName);
        this.proessLevelOther(level3Depts,regionId,metricName);
        this.proessLevelOther(level2Depts,regionId,metricName);
        this.proessLevelOther(level1Depts,regionId,metricName);
        this.proessLevelOther(level0Depts,regionId,metricName);*/
        this.resetQuota(regionId);
    }

    private void proessDepart(String regionId, String metricName) {
        List<Department> departments = departmentMapper.selectList(new LambdaQueryWrapper<Department>()
                .ne(Department::getId, "admin"));
        if (!CollectionUtils.isEmpty(departments)) {
            for (Department department : departments) {
                Map<String, String> param = Maps.newHashMap();
                param.put("departmentId", department.getId());
                param.put("regionId", regionId);
                if (StringUtils.isNotEmpty(metricName)) {
                    param.put("metricName", "ecs_cpu");
                }
                List<QuotaMetric> deptMetricList = quotaMetricMapper.getDepartmentQuotaByParamDisk(param);
                if (!CollectionUtils.isEmpty(deptMetricList)) {
                    param.put("metricName", metricName);
                    List<QuotaMetric> oldList = quotaMetricMapper.getDepartmentQuotaByParamDisk(param);
                    if (CollectionUtils.isEmpty(oldList)) {
                        QuotaMetric quotaMetric = new QuotaMetric();
                        quotaMetric.setDepartmentId(department.getId());
                        quotaMetric.setUsedValue(0L);
                        quotaMetric.setRegionId(regionId);
                        quotaMetric.setName(metricName);
                        quotaMetric.setTotalValue(0L);
                        quotaMetricMapper.insertDepartmentQuota(quotaMetric);
                    }
                }
            }
        }

    }

    private void initDepartQuotaByMetricName(String regionId, String metricName) {
        List<Department> departments = departmentMapper.selectList(new LambdaQueryWrapper<Department>()
                .ne(Department::getId, "admin"));
        if (!CollectionUtils.isEmpty(departments)) {
            for (Department department : departments) {
                Map<String, String> param = Maps.newHashMap();
                param.put("departmentId", department.getId());
                param.put("regionId", regionId);
                param.put("metricName", metricName);
                List<QuotaMetric> oldList = quotaMetricMapper.getDepartmentQuotaByParamDisk(param);
                if (CollectionUtils.isEmpty(oldList)) {
                    QuotaMetric quotaMetric = new QuotaMetric();
                    quotaMetric.setDepartmentId(department.getId());
                    quotaMetric.setUsedValue(0L);
                    quotaMetric.setRegionId(regionId);
                    quotaMetric.setName(metricName);
                    quotaMetric.setTotalValue(0L);
                    quotaMetricMapper.insertDepartmentQuota(quotaMetric);
                }
            }
        }

    }

    private void proessProject(String regionId, String metricName) {
        Map<String, String> param = Maps.newHashMap();
        param.put("regionId", regionId);
        if (StringUtils.isNotEmpty(metricName)) {
            param.put("metricName", "ecs_cpu");
        }
        List<QuotaMetric> projectList = quotaMetricMapper.getProjectQuotaByParamDisk(param);
        if (!CollectionUtils.isEmpty(projectList)) {
            for (QuotaMetric quotaMetric : projectList) {
                String projectId = quotaMetric.getProjectId();
                param.put("projectId", projectId);
                param.put("metricName", metricName);
                List<QuotaMetric> oldList = quotaMetricMapper.getProjectQuotaByParamDisk(param);
                if (CollectionUtils.isEmpty(oldList)) {
                    QuotaMetric quotaMetric2 = new QuotaMetric();
                    quotaMetric2.setProjectId(projectId);
                    quotaMetric2.setUsedValue(0L);
                    quotaMetric2.setRegionId(regionId);
                    quotaMetric2.setName(metricName);
                    quotaMetric2.setTotalValue(0L);
                    quotaMetricMapper.insertProjectQuota(quotaMetric2);
                }
            }
        }

        Map<String, String> param2 = Maps.newHashMap();
        List<Map> projectRootGb = quotaMetricMapper.getProjectRootGbQuotaByParam(param2);
        if (!CollectionUtils.isEmpty(projectRootGb)) {
            for (Map map : projectRootGb) {
                if (Objects.nonNull(map.get("projectId"))) {
                    String project = String.valueOf(map.get("projectId"));
                    QuotaMetric oldMetric = this.getMetricByNameAndProjectIdNoRegion(metricName, project, regionId);
                    if (Objects.isNull(oldMetric)) {
                        QuotaMetric quotaMetric = new QuotaMetric();
                        quotaMetric.setProjectId(project);
                        quotaMetric.setUsedValue(Long.parseLong(String.valueOf(map.get("sum"))));
                        quotaMetric.setRegionId(regionId);
                        quotaMetric.setName(metricName);
                        quotaMetric.setTotalValue(Long.parseLong(String.valueOf(map.get("sum"))));
                        quotaMetricMapper.insertProjectQuota(quotaMetric);
                    } else {
                        QuotaProjectValue proqm = new QuotaProjectValue();
                        proqm.setProjectId(project);
                        proqm.setUsedValue(Long.parseLong(String.valueOf(map.get("sum"))));
                        proqm.setRegionId(regionId);
                        proqm.setName(metricName);
                        proqm.setTotalValue(Long.parseLong(String.valueOf(map.get("sum"))));
                        quotaMetricMapper.updateProjectUsed(proqm);
                    }
                }
            }
        }
    }

    @Override
    public void resetQuota(String regionId) {
        this.resetQuota(regionId, null);
    }

    @Override
    public void resetQuota(String regionId, List<String> metricNames) {
       /* List<Department> departments = departmentMapper.selectList(new LambdaQueryWrapper<Department>().ne(Department::getId,"admin"));
        List<Department> level4Depts = departments.stream().filter(d -> d.getLevel() == 4).collect(Collectors.toList());
        List<Department> level3Depts = departments.stream().filter(d -> d.getLevel() == 3).collect(Collectors.toList());
        List<Department> level2Depts = departments.stream().filter(d -> d.getLevel() == 2).collect(Collectors.toList());
        List<Department> level1Depts = departments.stream().filter(d -> d.getLevel() == 1).collect(Collectors.toList());
        List<Department> level0Depts = departments.stream().filter(d -> d.getLevel() == 0).collect(Collectors.toList());
        this.proessLevelBottom(level4Depts,null,null);
        this.proessLevelOther(level3Depts,null, null);
        this.proessLevelOther(level2Depts,null, null);
        this.proessLevelOther(level1Depts,null,null);
        this.proessLevelOther(level0Depts,null,null);*/
        //查询所有配额指标
        Map<String, Object> mapParam = new HashMap<>();
        if (!CollectionUtils.isEmpty(metricNames)) {
            mapParam.put("metricNames", metricNames);
        }
        List<QuotaMetric> quotaMetricList = quotaMetricMapper.getQuotaMetricList(mapParam);
        List<Department> dept0 = departmentMapper.selectList(new LambdaQueryWrapper<Department>()
                .eq(Department::getLevel, 0)
                .ne(Department::getId, "admin"));//排除admin的默认组织
        for (Department department : dept0) {
            List<DepartmentTreeDetailVO> allDepartmentByRootDeptId = departmentMapper.findAllDepartmentByRootDeptId(department.getId());
            List<String> deptIds = RecursionDeptIdUtils.getDeptIds(allDepartmentByRootDeptId, new ArrayList<>());
            //过滤出带有项目的组织id(根组织没有项目)
            deptIds = deptIds.stream().filter(id -> !id.equals(department.getId())).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(deptIds)) {
                log.info("当前根组织{},{}下没有子组织", department.getName(), department.getId());
                continue;
            }
            //通过组织ID查询项目
            log.info(String.valueOf(deptIds));
            List<Project> projects = projectMapper.selectList(new LambdaQueryWrapper<Project>().in(Project::getDeptId, deptIds));
            List<String> projectIds = projects.stream().map(Project::getId).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(projectIds)) {
                log.info("当前根组织{},{}下没有项目", department.getName(), department.getId());
                continue;
            }
            Map<String, Object> param = Maps.newHashMap();
            param.put("projectIds", projectIds);
            param.put("regionId", regionId);
            if (!CollectionUtils.isEmpty(metricNames)) {
                param.put("metricNames", metricNames);
            }
            //查询项目下面的所有指标配额
            List<QuotaMetric> quotaProjectsByProjectIds = quotaMetricMapper.getQuotaProjectsByProjectIds(param);
            //更新项目的使用量=项目总量
            for (QuotaMetric quotaMetric : quotaProjectsByProjectIds) {
                quotaMetric.setTotalValue(quotaMetric.getUsedValue());
                quotaMetric.setRegionId(regionId);
                quotaMetricMapper.updateProjectQuota(quotaMetric);
            }
            Map<String, List<QuotaMetric>> quotaMetricMap = quotaProjectsByProjectIds.stream()
                    .collect(Collectors.groupingBy(QuotaMetric::getName));
            //根据指标求和
            for (QuotaMetric quotaMetric : quotaMetricList) {
                List<QuotaMetric> quotaMetrics = quotaMetricMap.get(quotaMetric.getName());
                if (CollectionUtil.isEmpty(quotaMetrics)) {
                    continue;
                }
                //所有项目下该指标的分配总量
                long sum = quotaMetrics.stream().mapToLong(QuotaMetric::getTotalValue).sum();
                //设置组织的使用量=总量=组织下所有项目的总量之和
                QuotaMetric updateQuotaMetric = new QuotaMetric();
                updateQuotaMetric.setUsedValue(sum);
                updateQuotaMetric.setName(quotaMetric.getName());
                updateQuotaMetric.setRegionId(regionId);
                updateQuotaMetric.setDepartmentId(department.getId());
                updateQuotaMetric.setTotalValue(sum);
                log.info("logger" + JSONObject.toJSONString(updateQuotaMetric));
                quotaMetricMapper.updateDepartmentQuotaAndUsed(updateQuotaMetric);
            }
        }
    }



    /**
     * 重置根组织的使用量
     *
     * @param regionId
     */
    @Override
    public void resetRootDeptQuota(String regionId) {
        //查询所有配额指标
        List<QuotaMetric> quotaMetricList = quotaMetricMapper.getQuotaMetricList(new HashMap<>());
        List<Department> dept0 = departmentMapper.selectList(new LambdaQueryWrapper<Department>()
                .eq(Department::getLevel, 0)
                .ne(Department::getId, "admin"));//排除admin的默认组织
        for (Department department : dept0) {
            List<DepartmentTreeDetailVO> allDepartmentByRootDeptId = departmentMapper.findAllDepartmentByRootDeptId(department.getId());
            List<String> deptIds = RecursionDeptIdUtils.getDeptIds(allDepartmentByRootDeptId, new ArrayList<>());
            //过滤出带有项目的组织id(根组织没有项目)
            deptIds = deptIds.stream().filter(id -> !id.equals(department.getId())).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(deptIds)) {
                log.info("当前根组织{},{}下没有子组织", department.getName(), department.getId());
                continue;
            }
            //通过组织ID查询组织下所有项目(查询出根组织下的所有项目)
//            log.info(String.valueOf(deptIds));
            List<Project> projects = projectMapper.selectList(new LambdaQueryWrapper<Project>().in(Project::getDeptId, deptIds));
            List<String> projectIds = projects.stream().map(Project::getId).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(projectIds)) {
                log.info("当前根组织组织{},{}下没有项目", department.getName(), department.getId());
                continue;
            }
            Map<String, Object> param = Maps.newHashMap();
            param.put("projectIds", projectIds);
            param.put("regionId", regionId);
            //查询项目下面的所有指标配额
            List<QuotaMetric> quotaProjectsByProjectIds = quotaMetricMapper.getQuotaProjectsByProjectIds(param);
            Map<String, List<QuotaMetric>> quotaMetricMap = quotaProjectsByProjectIds.stream()
                    .collect(Collectors.groupingBy(QuotaMetric::getName));
            //根据指标求和
            for (QuotaMetric quotaMetric : quotaMetricList) {
                List<QuotaMetric> quotaMetrics = quotaMetricMap.get(quotaMetric.getName());
                if (CollectionUtil.isEmpty(quotaMetrics)) {
                    continue;
                }
                //所有项目下该指标的分配总量
                long sum = quotaMetrics.stream().mapToLong(QuotaMetric::getTotalValue).sum();
                QuotaMetric updateQuotaMetric = new QuotaMetric();
                updateQuotaMetric.setUsedValue(sum);
                updateQuotaMetric.setName(quotaMetric.getName());
                updateQuotaMetric.setRegionId(regionId);
                updateQuotaMetric.setDepartmentId(department.getId());
//                log.info(JSONObject.toJSONString(updateQuotaMetric));
                quotaMetricMapper.updateDepartmentQuotaAndUsed(updateQuotaMetric);
            }
        }
    }
}
