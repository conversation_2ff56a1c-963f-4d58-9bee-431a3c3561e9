package com.sugon.cloud.iam.api.task;


import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.entity.UserStrategyEntity;
import com.sugon.cloud.iam.api.service.UserStrategyService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;


@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
@RefreshScope
public class UserStrategySyncHandler {

    private final UserStrategyService service;

    @XxlJob(CommonInstance.USER_STRATEGY_KEY_SYNC_HANDLER)
    public void run() {
        log.info("::::::::::::::user_strategy_sync start");
        List<UserStrategyEntity> list = service.list();
        list.stream().map(UserStrategyEntity::getUserId)
                .distinct()
                .collect(Collectors.toList())
                .forEach(service::syncStrategy2Redis);
        log.info("::::::::::::::user_strategy_sync end");
    }
}
