package com.sugon.cloud.iam.api.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "全局设置-邮件")
public class GlobalEmailEntity {

    @ApiModelProperty(value = "邮件账号")
    private String name;
    @ApiModelProperty(value = "邮件端口")
    private String port;
    @ApiModelProperty(value = "邮件密码")
    private String password;
    @ApiModelProperty(value = "发送邮件地址")
    private String send;
    @ApiModelProperty(value = "邮件服务器地址")
    private String host;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getSend() {
        return send;
    }

    public void setSend(String send) {
        this.send = send;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public GlobalEmailEntity(String name, String port, String password, String send, String host) {
        this.name = name;
        this.port = port;
        this.password = password;
        this.send = send;
        this.host = host;
    }

    public GlobalEmailEntity(){}
}
