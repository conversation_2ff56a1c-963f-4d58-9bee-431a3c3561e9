package com.sugon.cloud.iam.api.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sugon.cloud.iam.common.model.vo.UserViewVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
@ApiModel("管理员用户视图")
public class AdminUserViewVO {
    @ApiModelProperty("用户Id")
    private String id;
    @ApiModelProperty("用户名")
    private String name;
    @ApiModelProperty("邮箱")
    private String email;
    @ApiModelProperty("手机号码")
    private String phone;
    @ApiModelProperty("描述")
    private String extra;
    @ApiModelProperty("用户类型")
    private String type;
    @JsonProperty("created_at")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createdAt;
    @ApiModelProperty("是否启用")
    private boolean enabled;
    @JsonFormat(pattern="yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("过期时间")
    private Date expired;
    @JsonProperty("last_login_ip")
    @ApiModelProperty("最后登录IP")
    private String lastLoginIp;
    @JsonProperty("last_active_at")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("最后登录时间")
    private Date lastActiveAt;
    @JsonIgnore
    @ApiModelProperty("组织ID")
    private String deptId;
    @JsonProperty("sub_users")
    @ApiModelProperty("子用户")
    private List<UserViewVO> subUsers;
    @JsonProperty("mfa_enabled")
    @ApiModelProperty("是否开启MFA")
    private boolean mfaEnabled;
    public boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
}
