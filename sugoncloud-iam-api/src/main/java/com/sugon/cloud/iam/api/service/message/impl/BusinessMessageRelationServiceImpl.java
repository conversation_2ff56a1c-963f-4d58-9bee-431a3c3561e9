package com.sugon.cloud.iam.api.service.message.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sugon.cloud.iam.api.entity.BusinessMessageRelation;
import com.sugon.cloud.iam.api.mapper.BusinessMessageRelationMapper;
import com.sugon.cloud.iam.api.service.message.BusinessMessageRelationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: yangdingshan
 * @Date: 2025/2/10 14:25
 * @Description:
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class BusinessMessageRelationServiceImpl extends ServiceImpl<BusinessMessageRelationMapper, BusinessMessageRelation> implements BusinessMessageRelationService {

    @Override
    public BusinessMessageRelation getRelation(String businessType, String messageType, int templateType) {
        List<BusinessMessageRelation> relations = this.list(new LambdaQueryWrapper<BusinessMessageRelation>()
                .eq(BusinessMessageRelation::getBusinessType, businessType)
                .eq(BusinessMessageRelation::getMessageType, messageType)
                .eq(BusinessMessageRelation::getTemplateType, templateType));
        if (CollUtil.isEmpty(relations)) {
            throw new RuntimeException("未配置业务类型：" + businessType +" 消息模板");
        }
        return relations.get(0);
    }

    @Override
    public boolean isEnableDangerCode() {
        List<BusinessMessageRelation> relations = this.list(new LambdaQueryWrapper<BusinessMessageRelation>()
                .eq(BusinessMessageRelation::getBusinessType, "danger")
                .eq(BusinessMessageRelation::getTemplateType, 3));
        if (CollUtil.isEmpty(relations)) {
            return false;
        }
        return relations.stream().map(BusinessMessageRelation::getEnabled).filter(Boolean.TRUE::equals).findFirst().orElse(false);
    }
}
