package com.sugon.cloud.iam.api.service.ukeyImpl;

import com.sugon.cloud.iam.api.service.UkeyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@ConditionalOnExpression("'${mfa.type}'.contains('ftng')")
@RefreshScope
public class FTNGUkeyCodeVerifyMainServiceImpl implements UkeyService {


}
