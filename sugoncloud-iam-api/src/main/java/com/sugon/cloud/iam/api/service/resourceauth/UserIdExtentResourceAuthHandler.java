package com.sugon.cloud.iam.api.service.resourceauth;

import cn.hutool.core.collection.CollectionUtil;
import com.sugon.cloud.resource.auth.common.service.impl.BaseResourceAuthHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/18 8:49
 */
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class UserIdExtentResourceAuthHandler extends BaseResourceAuthHandler {

    private final SecurityUserTypeAuthHandler securityUserTypeAuthHandler;

    private final UserIdResourceAuthHandler userIdResourceAuthHandler;

    @Override
    public List<String> getUserResource(String userId) {
        List<String> userResource = securityUserTypeAuthHandler.getUserResource(userId);
        if (CollectionUtil.isNotEmpty(userResource)) {
            return userResource;
        }
        return userIdResourceAuthHandler.getUserResource(userId);
    }
}
