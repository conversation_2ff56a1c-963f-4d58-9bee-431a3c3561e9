package com.sugon.cloud.iam.api.service.resourceauth;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.sugon.cloud.common.constants.HeaderParamConstant;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.resource.auth.common.service.impl.BaseResourceAuthHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/24 10:48
 */
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class UserCreateAuthHandler extends BaseResourceAuthHandler {

    private final HttpServletRequest request;


    @Override
    public List<String> getUserResource(String userId) {
        String header = request.getHeader(HeaderParamConstant.USER_TYPE);
        if (StrUtil.containsAny(header, TypeUtil.TYPE_ADMIN, TypeUtil.TYPE_SECURITY, TypeUtil.TYPE_SUB_ADMIN,
                TypeUtil.TYPE_MASTER, TypeUtil.TYPE_DEPT_MASTER, TypeUtil.TYPE_ORG_SYSADMIN, TypeUtil.TYPE_ORG_SECADMIN,
                TypeUtil.TYPE_SYS_ADMIN, TypeUtil.TYPE_SEC_ADMIN)) {
            return CollectionUtil.newArrayList(TypeUtil.TYPE_ADMIN);
        }

        return null;
    }


}
