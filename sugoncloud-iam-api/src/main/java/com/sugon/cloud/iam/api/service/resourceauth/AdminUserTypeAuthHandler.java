package com.sugon.cloud.iam.api.service.resourceauth;

import com.beust.jcommander.internal.Lists;
import com.sugon.cloud.common.constants.HeaderParamConstant;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.mapper.UserMapper;
import com.sugon.cloud.resource.auth.common.service.impl.BaseResourceAuthHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Author: yangdingshan
 * @Date: 2024/1/15 14:11
 * @Description:
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class AdminUserTypeAuthHand<PERSON> extends BaseResourceAuthHandler {

    private final HttpServletRequest request;

    @Override
    public List<String> getUserResource(String userId) {

        String header = request.getHeader(HeaderParamConstant.USER_TYPE);
        if (TypeUtil.TYPE_ADMIN.equals(header)
                || TypeUtil.TYPE_SECURITY.equals(header)
                || TypeUtil.TYPE_SEC_ADMIN.equals(header)
                || TypeUtil.TYPE_SYS_ADMIN.equals(header)) {
            return Lists.newArrayList(TypeUtil.TYPE_ADMIN);
        }
        // 其他无访问权限
        return null;
    }
}
