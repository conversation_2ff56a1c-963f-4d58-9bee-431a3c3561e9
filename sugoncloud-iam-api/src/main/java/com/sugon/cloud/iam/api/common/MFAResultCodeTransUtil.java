package com.sugon.cloud.iam.api.common;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/05/16 15:58
 **/
public class MFAResultCodeTransUtil {

    public final static Map<Integer, String> errCodeMap;

    static {
        errCodeMap = new HashMap<>();
        errCodeMap.put(1, "认证失败需要同步OTP");
        errCodeMap.put(2, "认证失败需要设置静态密码");
        errCodeMap.put(3, "认证失败静态密码错误");
        errCodeMap.put(4, "客户端请求参数缺少或无效");
        errCodeMap.put(5, "没有查找到令牌号，或没有查找到用户绑定的令牌号");
        errCodeMap.put(6, "用户和令牌不存在绑定关系");
        errCodeMap.put(7, "共享密钥格式错误，共享密钥不正确");
        errCodeMap.put(8, "令牌已挂失");
        errCodeMap.put(9, "无效的动态口令");
        errCodeMap.put(11, "令牌号存在，但是令牌类型或属性信息不正确");
        errCodeMap.put(12, "用户不存在，没有查找到用户");
        errCodeMap.put(14, "用户未启用");
        errCodeMap.put(15, "令牌未启用");
        errCodeMap.put(16, "无效的本地密码认证方式");
        errCodeMap.put(18, "获取服务器端挑战码失败");
        errCodeMap.put(19, "令牌已作废");
        errCodeMap.put(20, "令牌已过期");
        errCodeMap.put(22, "用户登录认证临时锁定");
        errCodeMap.put(23, "用户登录认证永久锁定");
        errCodeMap.put(24, "用户没有可以正常使用的令牌");
        errCodeMap.put(25, "用户静态密码已经过期");
        errCodeMap.put(26, "令牌登录认证临时锁定");
        errCodeMap.put(27, "令牌登录认证永久锁定;用户不存在，没有查找到用户(更新用户接口和解除绑定接口返回码)");
        errCodeMap.put(29, "绑定令牌时新增不存在的用户失败");
        errCodeMap.put(30, "获取用户所属的域信息失败");
        errCodeMap.put(32, "用户与令牌不属于同一个域下，不能绑定");
        errCodeMap.put(33, "设置用户静态密码失败");
        errCodeMap.put(34, "设置用户静态密码，旧密码错误");
        errCodeMap.put(35, "设置用户静态密码，新密码与旧密码相同");
        errCodeMap.put(36, "令牌与用户不属于同一个组织机构，不能绑定");
        errCodeMap.put(37, "新令牌与旧令牌一致，不可以更换");
        errCodeMap.put(45, "用户或令牌号未在会话中设置");
        errCodeMap.put(49, "该用户和令牌已经绑定");
        errCodeMap.put(50, "绑定（更换）用户和令牌失败");
        errCodeMap.put(51, "用户绑定令牌数超过上限");
        errCodeMap.put(52, "令牌绑定用户数超过上限");
        errCodeMap.put(54, "可分配的令牌不足,请联系管理员分配更多令牌");
        errCodeMap.put(55, "用户和令牌不存在绑定关系，不能继续操作");
        errCodeMap.put(56, "产生OTP失败");
        errCodeMap.put(57, "短信OTP获取或发送，令牌类型不是短信令牌或用户绑定的令牌不存在短信令牌");
        errCodeMap.put(58, "手机号为空，不能执行短信OTP发送");
        errCodeMap.put(59, "短信发送失败，在调用短信网关发送短信时失败");
        errCodeMap.put(66, "密码长度不正确，请输入正确的密码");
        errCodeMap.put(67, "需要后端认证，没有找到后端认证的配置");
        errCodeMap.put(68, "第三方认证失败");
        errCodeMap.put(69, "不满足后端认证的条件，不支持后端认证的转发");
        errCodeMap.put(70, "后端认证，只支持PAP协议");
        errCodeMap.put(79, "未知错误");
        errCodeMap.put(80, "用户和令牌不存在绑定关系，不能继续操作（解除绑定接口错误码）");
        errCodeMap.put(84, "会话不存在");
        errCodeMap.put(85, "添加会话失败");
        errCodeMap.put(91, "用户绑定了多个口令长度不相等令牌");
        errCodeMap.put(92, "令牌不支持激活（密钥更新）");
        errCodeMap.put(100, "没有获取到代理配置属性值");
        errCodeMap.put(101, "应急口令已经过期");
        errCodeMap.put(102, "应急口令错误");
        errCodeMap.put(103, "手机令牌密码激活错误");
        errCodeMap.put(104, "令牌已经激活");
        errCodeMap.put(105, "手机令牌激活密码已经过期");
        errCodeMap.put(106, "手机令牌标识码,（UDID）不相等");
        errCodeMap.put(107, "手机令牌激活次数达到错误重试上限，已经锁定，需要重新分发");
        errCodeMap.put(108, "手机令牌扩展数据信息不存在");
        errCodeMap.put(109, "令牌不支持一级解锁码");
        errCodeMap.put(110, "令牌不支持二级解锁码");
        errCodeMap.put(111, "获取服务器验证码失败");
        errCodeMap.put(112, "获取令牌激活码失败");
        errCodeMap.put(113, "挑战码长度不正确");
        errCodeMap.put(114, "手机令牌还没有分发，不能激活");
        errCodeMap.put(117, "动态口令长度无效");
        errCodeMap.put(118, "无效挑战值");
        errCodeMap.put(119, "授权不存在或者没有启用授权状态");
        errCodeMap.put(120, "授权类型不正确，与实际服务器节点、令牌数据量不匹配");
        errCodeMap.put(121, "解析并取得授权内容具体的属性数据失败");
        errCodeMap.put(122, "授权已经过期");
        errCodeMap.put(123, "不正确的令牌类型，不允许此操作");
        errCodeMap.put(124, "手机令牌分发站点未启用,不能在线分发");
        errCodeMap.put(125, "OTP重放");
        errCodeMap.put(127, "密码最短长度校验失败");
        errCodeMap.put(128, "密码数字与字母组合校验失败");
        errCodeMap.put(129, "密码不允许重复字符");
        errCodeMap.put(130, "密码不允许使用特殊字符");
        errCodeMap.put(131, "密码数字与字母、特殊字符组合校验失败");
        errCodeMap.put(132, "密码至少包含一个特殊字符");
        errCodeMap.put(133, "密码是数字与字母组合或者包含特殊字符");
        errCodeMap.put(134, "用户名与密码不相同校验失败");
        errCodeMap.put(135, "用户名与密码不相同,不区分大小写校验失败");
        errCodeMap.put(136, "该密码不允许使用");
        errCodeMap.put(137, "与历史密码相同");
        errCodeMap.put(138, "X天之内不允许修改密码");
        errCodeMap.put(128, "组织机构不存在");
        errCodeMap.put(256, "该请求不支持");
        errCodeMap.put(500, "该操作请求处理结果失败");
        errCodeMap.put(512, "业务请求失败，业务处理过程中出现异常");
    }
}
