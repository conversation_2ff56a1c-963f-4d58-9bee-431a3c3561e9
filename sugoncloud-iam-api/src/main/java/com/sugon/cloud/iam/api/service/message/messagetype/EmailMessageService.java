package com.sugon.cloud.iam.api.service.message.messagetype;

import cn.hutool.core.lang.Assert;
import com.sugon.cloud.iam.api.common.AESUtil;
import com.sugon.cloud.iam.api.common.MD5Util;
import com.sugon.cloud.iam.api.entity.EmailTemplates;
import com.sugon.cloud.iam.api.service.GlobalsettingsService;
import com.sugon.cloud.iam.api.service.message.EmailTemplatesService;
import com.sugon.cloud.iam.api.utils.CommonUtils;
import com.sugon.cloud.iam.common.model.dto.SendDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;
import org.thymeleaf.templateresolver.StringTemplateResolver;

import javax.mail.internet.MimeMessage;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author: yangdingshan
 * @Date: 2025/1/25 14:21
 * @Description:
 */
@Slf4j
@Service(value = "message_email")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RefreshScope
public class EmailMessageService implements MessageTypeService {


    private JavaMailSender mailSender;

    private final GlobalsettingsService globalsettingsService;

    private TemplateEngine templateEngine;

    private final EmailTemplatesService emailTemplatesService;

    public void init() {

        JavaMailSenderImpl javaMailSender = new JavaMailSenderImpl();
        String password = globalsettingsService.getGlobalsettingsEntity("email", "password").getPolicyDocument();
        String send = globalsettingsService.getGlobalsettingsEntity("email", "send").getPolicyDocument();
        String host = globalsettingsService.getGlobalsettingsEntity("email", "host").getPolicyDocument();
        String port = globalsettingsService.getGlobalsettingsEntity("email", "port").getPolicyDocument();

        // 动态设置邮件服务器
        javaMailSender.setHost(host);
        javaMailSender.setPort(Integer.parseInt(port));
        javaMailSender.setUsername(send);
        javaMailSender.setPassword(AESUtil.decrypt(password, MD5Util.string2MD5("0nCPbhvq")));
        mailSender = javaMailSender;

        // 直接处理HTML字符串
        if (Objects.isNull(templateEngine)) {
            templateEngine = new TemplateEngine();
            // 创建 StringTemplateResolver 处理动态字符串
            StringTemplateResolver stringTemplateResolver = new StringTemplateResolver();
            stringTemplateResolver.setCacheable(false); // 禁用缓存
            templateEngine.setTemplateResolver(stringTemplateResolver);
        }
    }

    @Override
    public void send(SendDTO dto) {
        init();
        MimeMessage message = mailSender.createMimeMessage();

        try {
            MimeMessageHelper helper = new MimeMessageHelper(message, true, StandardCharsets.UTF_8.name());
            // 获取模板
            EmailTemplates template = emailTemplatesService.getById(dto.getTemplateId());
            Assert.notNull(template, "邮件模板不存在");
            String templateContent = template.getContent();

            // 使用 Thymeleaf 动态渲染模板
            Context context = new Context();
            String regex = "\\[\\[\\$\\{(.*?)}\\]\\]";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(templateContent);
            int index = 0;
            while (matcher.find()) {
                String variable = matcher.group(1);
                context.setVariable(variable, dto.getTemplateParams().get(index));
                index++;
            }
            // 动态渲染 Thymeleaf 模板
            String htmlContent = templateEngine.process(templateContent, context);

            helper.setTo(dto.getContacts().toArray(new String[0]));
            helper.setSubject(template.getSubject());
            helper.setText(htmlContent, true);
            String send = globalsettingsService.getGlobalsettingsEntity("email", "send").getPolicyDocument();
            helper.setFrom(send);
            CompletableFuture.runAsync(() -> mailSender.send(message));
        } catch (Exception e) {
            log.error("邮件发送失败", e);
            String msg = e.getMessage();
            if (!CommonUtils.isContainChinese(msg)) {
                msg = "邮件发送失败";
            }
            throw new RuntimeException(msg);
        }
    }
}
