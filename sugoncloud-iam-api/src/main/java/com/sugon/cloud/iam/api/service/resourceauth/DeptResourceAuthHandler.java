package com.sugon.cloud.iam.api.service.resourceauth;

import com.google.common.collect.Lists;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.iam.api.common.RecursionDeptIdUtils;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.mapper.UserMapper;
import com.sugon.cloud.iam.api.service.DepartmentService;
import com.sugon.cloud.iam.common.model.vo.DepartmentTreeDetailVO;
import com.sugon.cloud.resource.auth.common.service.impl.BaseResourceAuthHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: yangdingshan
 * @Date: 2024/1/10 16:20
 * @Description:
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class DeptResourceAuthHandler extends BaseResourceAuthHandler {

    private final DepartmentService departmentService;

    private final UserMapper userMapper;

    @Override
    public List<String> getUserResource(String userId) {
        User user = userMapper.selectById(userId);
        // 判断是否有权限操作
        if (hasAuth(user)) {
            return Lists.newArrayList(TypeUtil.TYPE_ADMIN);
        }
        List<DepartmentTreeDetailVO> departmentAll = departmentService.findDepartmentAll(userId, null, true);
        List<String> deptIds = Lists.newArrayList();
        RecursionDeptIdUtils.getDeptIds(departmentAll, deptIds);
        return deptIds;
    }

    private boolean hasAuth(User user) {
        List<String> havaAuth = Lists.newArrayList(TypeUtil.TYPE_ADMIN,
                TypeUtil.TYPE_SECURITY,
                TypeUtil.TYPE_SUB_ADMIN,
                TypeUtil.TYPE_SEC_ADMIN,
                TypeUtil.TYPE_SYS_ADMIN,
                TypeUtil.TYPE_SEC_AUDITOR);
        return havaAuth.contains(user.getType());
    }
}
