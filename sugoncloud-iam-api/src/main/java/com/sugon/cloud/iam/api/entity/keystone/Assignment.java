package com.sugon.cloud.iam.api.entity.keystone;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@TableName("assignment")
public class Assignment {

    private String type;
    @JsonProperty("actor_id")
    private String actorId;
    @JsonProperty("target_id")
    private String targetId;
    @JsonProperty("role_id")
    private String roleId;
    private boolean inherited;
}
