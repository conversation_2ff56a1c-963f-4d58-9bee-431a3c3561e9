package com.sugon.cloud.iam.api.entity.keystone;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sugon.cloud.iam.api.entity.keystone.VO.CatalogVO;
import com.sugon.cloud.iam.api.entity.keystone.VO.ProjectNativeVO;
import com.sugon.cloud.iam.api.entity.keystone.VO.RoleVO;
import com.sugon.cloud.iam.api.entity.keystone.VO.UserVO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class Token implements Serializable {
    private static final long serialVersionUID = 1L;
    @JsonProperty(value = "is_domain")
    private boolean domain;
    private List<String> methods;
    private List<RoleVO> roles;
    @JsonProperty(value = "expires_at")
    private Date expiresAt;
    private ProjectNativeVO project;
    private List<CatalogVO> catalog;
    private UserVO user;
//    @JsonProperty(value = "audit_ids")
//    private List<String> auditIds;
    @JsonProperty(value = "issued_at")
    private Date issuedAt;

}
