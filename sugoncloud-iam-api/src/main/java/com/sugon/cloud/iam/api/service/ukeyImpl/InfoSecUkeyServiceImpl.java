package com.sugon.cloud.iam.api.service.ukeyImpl;

import com.sugon.cloud.iam.api.service.UkeyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * 信安世纪动态口令
 */
@Service
@Slf4j
@ConditionalOnExpression("'${mfa.type}'.contains('infoSec')")
@RefreshScope
public class InfoSecUkeyServiceImpl implements UkeyService {
}
