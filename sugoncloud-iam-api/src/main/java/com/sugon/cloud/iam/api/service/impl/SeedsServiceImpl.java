package com.sugon.cloud.iam.api.service.impl;

import static com.sugon.cloud.iam.api.utils.CommonUtils.bytesToHexString;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ft.otp.authng.FTNGErrorCode;
import com.ft.otp.authng.FTNGTokenData;
import com.ft.otp.core.FTNGTokenAPI;
import com.ft.otp.core.ReturnResult;
import com.sugon.cloud.iam.api.entity.SeedEntity;
import com.sugon.cloud.iam.api.mapper.SeedsMapper;
import com.sugon.cloud.iam.api.service.SeedsService;
import com.sugon.cloud.iam.api.utils.FTNGApi;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * (Seeds)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-07 10:45:33
 */
@Service("seedsService")
@Slf4j
@ConditionalOnExpression("${mfa.enabled} &&'${mfa.type}'.contains('ftng')")
public class SeedsServiceImpl extends ServiceImpl<SeedsMapper, SeedEntity> implements SeedsService {

    @Autowired
    private FTNGApi ftngApi;
    @Override
    public ReturnResult upload(MultipartFile file1, MultipartFile file2) {
        ReturnResult returnResult = new ReturnResult();
        FTNGTokenAPI tokenAuth = null;
        log.info("ftng类型");
        try {
            tokenAuth = new FTNGTokenAPI();
            System.err.println("测试接口为:initPskc\\readPskcRec\\uninit_pskc\\decode_pdata");
            returnResult = ftngApi.readSeeds(file1, file2, "");// 令牌文件，密钥文件，密码
            log.info("returnResult2:" + returnResult.getReturnCode());
            if (returnResult.getReturnCode() == 0) {
                int tokenTotal = returnResult.getTokenTotal();
                System.out.println("种子文件中的令牌总数为:" + tokenTotal);
                List<FTNGTokenData> tokenList = returnResult.getTokenList();
                int tokenCount = tokenList.size();
                System.out.println("成功读取的令牌数为:" + tokenCount);
                for (int i = 0; i < tokenCount; i++) {
                    FTNGTokenData tokenData = tokenList.get(i);
                    System.out.println("令牌号为:" + tokenAuth.getString((tokenData.pdata.sn)));
                    System.out.println("令牌私有数据字符串为:" + tokenAuth.getString(tokenData.priv_data));
                    String key = bytesToHexString(tokenData.pdata.key);
                    // if (key.length() > 40) {
                    key = key.substring(0, tokenData.pdata.key_len * 2);
                    // }
                    System.out.println("令牌种子长度为：" + tokenData.pdata.key_len);
                    System.out.println("令牌种子为:" + key);
                    System.out.println("令牌动态口令长度为:" + tokenData.pdata.otp_len);// 口令长度
                    System.out.println("令牌动态口令生成周期为:" + tokenData.pdata.interval);
                    System.out.println("***********************************************************************");
                    SeedEntity seeds = new SeedEntity();
                    seeds.setCode(tokenAuth.getString((tokenData.pdata.sn)));
                    seeds.setPrivateKey(tokenAuth.getString(tokenData.priv_data));
                    seeds.setSeedLen(tokenData.pdata.key_len);
                    seeds.setSeed(key);
                    seeds.setCodeLen(tokenData.pdata.otp_len);
                    seeds.setCodeTime(tokenData.pdata.interval);
                    saveOrUpdate(seeds);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return returnResult;
    }

}

