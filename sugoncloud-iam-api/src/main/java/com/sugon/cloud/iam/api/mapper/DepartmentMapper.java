package com.sugon.cloud.iam.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sugon.cloud.iam.api.entity.Department;
import com.sugon.cloud.iam.common.model.vo.DepartmentTreeDetailVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 组织表(Department)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-04-07 10:51:53
 */
@Repository
public interface DepartmentMapper extends BaseMapper<Department> {
    List<DepartmentTreeDetailVO> findTreeDepartment(@Param("parentId") String parentId);

    List<DepartmentTreeDetailVO> findTreeDepartmentAndOwner(@Param("departmentId") String departmentId);

    List<DepartmentTreeDetailVO> findTreeAllDepartment();

    List<DepartmentTreeDetailVO> findAllDepartment(String userId, String userType, boolean getAdminDept);

    List<DepartmentTreeDetailVO> findAllDepartmentList();

    List<DepartmentTreeDetailVO> findAllDepartmentByRootDeptId(String rootDeptId);
}
