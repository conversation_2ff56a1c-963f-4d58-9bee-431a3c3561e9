package com.sugon.cloud.iam.api.config;

import com.fisherman.api.FmApi;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;

@Slf4j
@RefreshScope
@Data
public class FmDsvsSignConfig {
    @Value("${mfa.fisherman.host}")
    private String dsvsIp;

    @Value("${mfa.fisherman.port}")
    private String dsvsPort;

    FmApi dsvsApi = new FmApi();
    public FmDsvsSignConfig() {
    }


    public void init() {
        log.info("init fisherman sign api config.");
        String ip = dsvsIp;
        String[] ips = new String[1];
        ips[0] = ip;
        dsvsApi.FM_DSVS_Connection(ips, Integer.parseInt(dsvsPort), 5);
        System.out.println("连接成功");
        log.info("connect fisherman sign device success.");
    }

    public void close() {
        dsvsApi.FM_DSVS_CloseConnection();
        log.info("Fisherman vm sgd close complete.");
    }
}
