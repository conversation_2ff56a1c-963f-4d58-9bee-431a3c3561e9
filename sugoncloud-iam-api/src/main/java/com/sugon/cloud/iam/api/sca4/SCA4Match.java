package com.sugon.cloud.iam.api.sca4;

import cn.hutool.core.util.StrUtil;
import com.sugon.cloud.iam.api.common.CommonInstance;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import javax.xml.bind.DatatypeConverter;

import org.springframework.data.redis.core.types.Expiration;

import io.jsonwebtoken.Claims;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @desc:
 * @author: luoy
 */
@Log4j2
public class SCA4Match {

    public static final String signV4Algorithm = "SCA4-HMAC-SHA256";

    private final static Charset UTF8 = StandardCharsets.UTF_8;

    public static String sha256Hex(String s) throws Exception {
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        byte[] d = md.digest(s.getBytes(UTF8));
        return DatatypeConverter.printHexBinary(d).toLowerCase();
    }

    public static byte[] hmac256(byte[] key, String msg) throws Exception {
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKeySpec = new SecretKeySpec(key, mac.getAlgorithm());
        mac.init(secretKeySpec);
        return mac.doFinal(msg.getBytes(UTF8));
    }

    public static String caculateSignature(String stringToSign, String shortDate, String sk, SignValues signValues) throws Exception {

        byte[] secretData = hmac256(("SCA4" + sk).getBytes(UTF8), shortDate);
        byte[] secretService = hmac256(secretData, signValues.credential.scope.getService());
        byte[] secretSigning = hmac256(secretService, "sca4_request");

        log.info("caculateSignature secretData {}", secretData);
        log.info("caculateSignature secretService {}", secretService);
        log.info("caculateSignature secretSigning {}", secretSigning);

        return DatatypeConverter.printHexBinary(hmac256(secretSigning, stringToSign)).toLowerCase();
    }

    public static String getCanonicalRequest(HttpServletRequest req, SCA4Match.SignValues signValues) throws Exception {
        String method = req.getHeader(CommonInstance.X_SCA4_METHOD);

        // Get canonicalHeaders
        List<String> list =  new ArrayList<>();
        for (int i = signValues.signedHeaders.length - 1; i >= 0; i--) {
            list.add(signValues.signedHeaders[i]+":"+req.getHeader(signValues.signedHeaders[i]).toLowerCase());
        }
        String canonicalHeaders = String.join("\n", list) + "\n";
        canonicalHeaders = canonicalHeaders.toLowerCase();

        // Get query
        String queryStr = "";
        StringBuilder queryBuilder = new StringBuilder();
        req.getParameterMap().forEach((key, values) -> {
            try {
                queryBuilder.append(key).append("=").append(URLEncoder.encode(String.join(",", values), "UTF8")).append("&");
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
        });
        if (queryBuilder.length() > 0) {
            queryStr = queryBuilder.substring(0, queryBuilder.length() - 1);
        }
        // Get uri
        String uri = req.getHeader(CommonInstance.X_SCA4_URI);
        // String uri = req.getRequestURI();

        // Get signedHeaders
        String signedHeaders = String.join(",", signValues.signedHeaders);

        // Get request body
        StringBuilder sb = new StringBuilder("");

        if ("GET".equalsIgnoreCase(method)) {
            List<String> toSignList = Arrays.asList(method, uri, queryStr, canonicalHeaders, signedHeaders);
            return String.join( "\n", toSignList);
        } else {
            BufferedReader br = null;

            try {
                br = req.getReader();
                String str;
                while ((str = br.readLine()) != null) {
                    sb.append(str);
                }
                br.close();
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                if (null != br) {
                    try {
                        br.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }

            String payload256Hex = SCA4Match.sha256Hex(sb.toString());
            List<String> toSignList = Arrays.asList(method, uri, queryStr, canonicalHeaders, signedHeaders, payload256Hex);
            return  String.join( "\n", toSignList);
        }
    }

    @Data
    public static  class  Credential {
        String accessKey;
        String secretKey;

        public Credential(String accessKey, String secretKey) {
            this.accessKey = accessKey;
            this.secretKey = secretKey;
        }
    }

    public static SignValues parseSignV4(String v4) throws ParseException {
        // Replace all spaced strings, some clients can send spaced
        // parameters and some won't. So we pro-actively remove any spaces
        // to make parsing easier.

        String v4Auth = v4.replace(" ", "");

        if (StringUtils.isEmpty(v4Auth)){
            throw new RuntimeException("ErrAuthHeaderEmpty");
        }
        // Verify if the header algorithm is supported or not.
        if (!v4.contains(signV4Algorithm)){
            throw new RuntimeException("ErrSignatureVersionNotSupported");

        }
        // Strip off the Algorithm prefix.
        v4Auth = v4Auth.replace(signV4Algorithm, "");

        String[] v4AuthElement = v4Auth.trim().split(",");
        if (v4AuthElement.length!=3){
            throw new RuntimeException("ErrMissingFields");

        }
        // Initialize signature version '4' structured header.

        SignValues singleValue = new SignValues();

        CredentialHeader credentialHeader = parseCredentialHeader(v4AuthElement[0]);

        String[] signedHeaders = parseSignedHeader(v4AuthElement[1]);

        String signature = parseSignature(v4AuthElement[2]);

        singleValue.setCredential(credentialHeader)
                .setSignature(signature)
                .setSignedHeaders(signedHeaders);

        return singleValue;

    }


    // parse credentialHeader string into its structured form.
    public static CredentialHeader parseCredentialHeader(String credElement) throws ParseException {
        String[] creds = credElement.trim().split("=");

        if (creds.length!=2){
            throw new RuntimeException("ErrMissingFields");
        }

        if (!creds[0].equals("Credential")){
            throw new RuntimeException("ErrMissingCredTag");

        }
       //a7030685172f4cd588df5094f4807179/2022-10-24/region/s3/aws4_request
        String[] split = creds[1].split("/");

        if (split.length != 5){
            throw new RuntimeException("ErrCredMalformed");

        }
        CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setAccessKey(split[0]);

        Scope scope = new Scope();

        //2022-10-24 00:00:00 +0000
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        // 注意时区，否则容易出错
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        Date parse = sdf.parse(split[1]);
        scope.setDate(parse)
                .setRegion(split[2])
                .setService(split[3])
                .setRequest(split[4]);
        credentialHeader.setScope(scope);
        return credentialHeader;

    }


    public static String[] parseSignedHeader(String signedHdrElement) {

        String[] split = signedHdrElement.trim().split("=");

        if (split.length!=2){
            throw new RuntimeException("ErrMissingFields");
        }

        if (!"SignedHeaders".equals(split[0])){
            throw new RuntimeException("ErrMissingSignHeadersTag");
        }

        if (StringUtils.isEmpty(split[1])){
            throw new RuntimeException("ErrMissingFields") ;
        }
        String[] signedHeader = split[1].split(";");

        return signedHeader;

    }

    public static String parseSignature(String signElement ) {
        String[] signature = signElement.split("=");
        if (signature.length!=2){
            throw new RuntimeException("ErrMissingFields");
        }

        if (!"Signature".equals(signature[0])){
            throw new RuntimeException("ErrMissingSignature");
        }

        if (StringUtils.isEmpty(signature[1])){
            throw new RuntimeException("ErrMissingFields") ;
        }

        return signature[1];
    }

    @Data
    @Accessors(chain = true)
    public static   class SignValues  {
        CredentialHeader credential ;
        String[] signedHeaders ;
        String signature;
    }

    @Data
    public static class CredentialHeader  {
        String accessKey ;
        Scope scope;
    }

    @Data
    @Accessors(chain = true)
    public static class Scope  {
        Date date;
        String region;
        String service;
        String request;
    }
}
