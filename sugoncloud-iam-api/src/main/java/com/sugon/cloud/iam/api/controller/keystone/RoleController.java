package com.sugon.cloud.iam.api.controller.keystone;

import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.iam.api.entity.keystone.CreateRoleResponse;
import com.sugon.cloud.iam.api.entity.keystone.ListResourceLinkResponse;
import com.sugon.cloud.iam.api.entity.keystone.ListRoleResponse;
import com.sugon.cloud.iam.api.entity.keystone.VO.CreateRoleVO;
import com.sugon.cloud.iam.api.entity.exception.KeyStoneMethodArgumentNotValidException;
import com.sugon.cloud.iam.api.service.RoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;


@RestController
@RequestMapping("/v3/roles")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Api(tags = {"keystone-角色操作"})
@DocumentIgnore
public class RoleController {

    private final RoleService roleService;

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @ApiOperation(value = "创建角色")
    public CreateRoleResponse createRole(@RequestBody CreateRoleVO createRoleVO) {
        if (Objects.isNull(createRoleVO.getRole())) {
            throw new KeyStoneMethodArgumentNotValidException("create_role() param role can not be null");
        }
        if (Objects.isNull(createRoleVO.getRole().getName())) {
            throw new KeyStoneMethodArgumentNotValidException("create_role() param role.name can not be null");
        }
        return roleService.createRole(createRoleVO);
    }

    @GetMapping
    @ApiOperation(value = "查询角色")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "name", value = "角色名称", required = false, dataType = "String"),
            @ApiImplicitParam(name = "domain_id", value = "域ID", required = false, dataType = "String")})
    public ListRoleResponse listRole(@RequestParam(value = "name", required = false) String name,
                                     @RequestParam(value = "domain_id", required = false) String domainId,
                                     HttpServletRequest request) {
        ListRoleResponse listRoleResponse = new ListRoleResponse();
        ListResourceLinkResponse listResourceLinkResponse = new ListResourceLinkResponse();
        String queryString = request.getQueryString();
        StringBuffer requestURL = request.getRequestURL();
        if (!StringUtils.isEmpty(queryString)) {
            requestURL.append("?").append(queryString);
        }
        listResourceLinkResponse.setSelf(requestURL.toString());
        listRoleResponse.setRoles(roleService.listRole(name, domainId));
        listRoleResponse.setLinks(listResourceLinkResponse);
        return listRoleResponse;
    }

    @GetMapping("/{role_id}")
    @ApiOperation(value = "查询角色详情")
    @ApiImplicitParam(name = "role_id", value = "角色ID", required = true, dataType = "String")
    public CreateRoleResponse roleDetail(@PathVariable("role_id") String roleId) {
        return roleService.roleDetail(roleId);
    }
}
