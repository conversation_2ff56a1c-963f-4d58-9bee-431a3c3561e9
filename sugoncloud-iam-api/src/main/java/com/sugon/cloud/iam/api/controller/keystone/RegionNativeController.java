package com.sugon.cloud.iam.api.controller.keystone;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Maps;
import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.mapper.ModelMapper;
import com.sugon.cloud.common.utils.UUIDUtil;
import com.sugon.cloud.iam.api.entity.Region;
import com.sugon.cloud.iam.api.entity.keystone.VO.RegionVO;
import com.sugon.cloud.iam.api.service.RegionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import javax.ws.rs.NotFoundException;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/v3/regions")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Api(tags = {"keystone-域操作"})
@DocumentIgnore
public class RegionNativeController {
    private final RegionService regionService;
    private final ModelMapper mapper;
    @Value("#{'${vip.ip}'.concat(':').concat('${vip.port}')}")
    private String linksPrefix;

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @ApiOperation(value = "创建region")
    public Map<String, Region> create(@RequestBody Map<String, Region> map) {
        Region region = map.get("region");
        if (Objects.isNull(region)) {
            region = new Region();
            region.setId(UUIDUtil.get32UUID());
        }else {
            if (StringUtils.isNotBlank(region.getId())) {
                Region r = regionService.getById(region.getId());
                if (Objects.nonNull(r)) throw new DuplicateKeyException("{\n" +
                        "    \"error\": {\n" +
                        "        \"message\": \"Conflict occurred attempting to store region - Duplicate ID, "+r.getId()+"..\",\n" +
                        "        \"code\": 409,\n" +
                        "        \"title\": \"Conflict\"\n" +
                        "    }\n" +
                        "}");
            }
            if (StringUtils.isNotBlank(region.getParentRegionId())) {
                Region r = regionService.getById(region.getParentRegionId());
                if (Objects.isNull(r))  throw new NotFoundException("{\n" +
                        "    \"error\": {\n" +
                        "        \"message\": \"Could not find region: "+region.getParentRegionId()+".\",\n" +
                        "        \"code\": 404,\n" +
                        "        \"title\": \"Not Found\"\n" +
                        "    }\n" +
                        "}");
            }
            if (StringUtils.isBlank(region.getId())) {
                region.setId(UUIDUtil.get32UUID());
            }
        }
        regionService.save(region);
        RegionVO regionVO = mapper.map(region, RegionVO.class);
        Map links = Maps.newHashMap();
        links.put("self","http://" + linksPrefix+"/v3/regions/"+region.getId());
        regionVO.setLinks(map);
        Map resultMap = new HashMap();
        resultMap.put("region", resultMap);
        return resultMap;
    }

    @GetMapping
    @ApiOperation(value = "查询region")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "parent_region_id", value = "父级regionId", dataType = "String")})
    public Map<String, Object> list (@RequestParam(value = "parent_region_id", required = false) String parentRegionId) {
        List<Region> regions = regionService.list(new QueryWrapper<Region>().lambda()
                .eq(StringUtils.isNotBlank(parentRegionId), Region::getParentRegionId, parentRegionId));
        List<RegionVO> collect = regions.stream().map(region -> region2RegionVO(region)).collect(Collectors.toList());
        Map map = new HashMap();
        map.put("regions", collect);
        Map links = Maps.newHashMap();
        links.put("self", "http://" + linksPrefix+"/v3/regions");
        links.put("previous", null);
        links.put("next", null);
        map.put("links", links);
        return map;
    }
    @GetMapping("{region_id}")
    @ApiOperation(value = "查询region详情")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "region_id", value = "regionId", dataType = "String")})
    public Map<String, Object> detail(@PathVariable("region_id") String regionId) {
        RegionVO regionVO = region2RegionVO(regionService.getById(regionId));
        Map map = Maps.newHashMap();
        map.put("region", regionVO);
        return map;
    }

    private RegionVO region2RegionVO(Region region) {
        RegionVO regionVO = mapper.map(region, RegionVO.class);
        Map links = Maps.newHashMap();
        links.put("self","http://" + linksPrefix+"/v3/regions/"+region.getId());
        regionVO.setLinks(links);
        return regionVO;
    }
}
