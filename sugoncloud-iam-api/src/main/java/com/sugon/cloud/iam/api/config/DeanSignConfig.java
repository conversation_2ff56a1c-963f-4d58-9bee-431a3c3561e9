package com.sugon.cloud.iam.api.config;

import com.cslc.netsignagent.DtsvsHandle;
import com.cslc.netsignagent.NetSignAgent;
import com.cslc.netsignagent.NetSignException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RefreshScope
@Data
public class DeanSignConfig {

    @Value("${mfa.dean.host}")
    private String host;
    @Value("${mfa.dean.port}")
    private Integer port;
    @Value("${mfa.dean.password}")
    private String password;

    NetSignAgent agent = new NetSignAgent();
    DtsvsHandle handle = new DtsvsHandle();

    Map<String,String> errorCodeMap = new HashMap<>();

    public DeanSignConfig() {
    }


    public void init() {
        log.info("init dean sign api config.");
        try{
            handle = agent.initialize(host, port, password, 3000, 10, "", "", handle);
        }catch (NetSignException e) {
            log.error("ErrorCode:0x"+Integer.toHexString(e.getErrorCode()));
            e.printStackTrace();
        } catch (Exception e2) {
            e2.printStackTrace();
        }
        log.info("connect dean sign device success.");
    }

    public void close() {
        try {
            agent.clearEnvironment(handle);
        } catch (NetSignException e) {
            e.printStackTrace();
        }
        log.info("Fisherman vm sgd close complete.");
    }

    public String getErrorDescription(String errorCode) {
        return errorCodeMap.getOrDefault(errorCode, "未知错误");
    }

    public void initErrorCode(){
        errorCodeMap.put("0x16", "解包数据错误，一般是detach或attach验签解包时数据长度不符合规范报错");
        errorCodeMap.put("0xf101", "无效句柄");
        errorCodeMap.put("0xf102", "网络连接错误，请检查签名服务器IP地址是否正确、服务是否启动，以及网络是否正常。");
        errorCodeMap.put("0xf103", "数据发送错误，请检查网络原因");
        errorCodeMap.put("0xf104", "数据接收错误，请检查网络原因，以及是否超时。");
        errorCodeMap.put("0xe001", "权限不满足");
        errorCodeMap.put("0xe705", "数据摘要初始化错误");
        errorCodeMap.put("0xe706", "数据摘要输出结果错误");
        errorCodeMap.put("0xe707", "数据摘要输入数据错误");
        errorCodeMap.put("0xee20", "传入数据参数有误（请检查数据长度、类型以及其他合法性）");
        errorCodeMap.put("0xdf11", "数据解包错");
        errorCodeMap.put("0xdf10", "数据打包错");
        errorCodeMap.put("0xdf14", "");
        errorCodeMap.put("0xdf15", "分配内存错误");
        errorCodeMap.put("0xdf20", "没有空闲密钥");
        errorCodeMap.put("0xdf21", "对公钥做MAC错误");
        errorCodeMap.put("0xdf22", "注册码比对错误");
        errorCodeMap.put("0xdf23", "注册码中的密钥标识错误 密钥号不对，或者共享内存中找不到密钥信息");
        errorCodeMap.put("0xdf24", "密钥没有注册");
        errorCodeMap.put("0xdf25", "没有匹配的证书");
        errorCodeMap.put("0xdf29", "证书解码错误");
        errorCodeMap.put("0xdf31", "验签失败");
        errorCodeMap.put("0xdf32", "数字信封加密错误");
        errorCodeMap.put("0xdf35", "数字证书转码错误");
        errorCodeMap.put("0xdf36", "证书过期");
        errorCodeMap.put("0xdf48", "证书被注销");
        errorCodeMap.put("0xdf56", "证书过期");
        errorCodeMap.put("0xdf57", "证书未生效");
        errorCodeMap.put("0xdf58", "签名数据错误");
        errorCodeMap.put("0xdf93", "DETACH签名失败");
        errorCodeMap.put("0xdf94", "DETACH验证失败");
        errorCodeMap.put("0xdf95", "数字信封解密错误");
        errorCodeMap.put("0xea03", "服务器的时间不对");
        errorCodeMap.put("1001", "构造函数1错误");
        errorCodeMap.put("1002", "构造函数2错误");
        errorCodeMap.put("1003", "证书对象为空");
        errorCodeMap.put("1004", "取颁发者DN为空");
        errorCodeMap.put("1005", "取证书到期时间错误");
        errorCodeMap.put("1006", "取证书生效时间错误");
        errorCodeMap.put("1007", "取证书序列号错误");
        errorCodeMap.put("1008", "取证书签名数据错误");
        errorCodeMap.put("1009", "取签名算法ID错误");
        errorCodeMap.put("1010", "取颁发者DN错误");
        errorCodeMap.put("1011", "取主题DN位空");
        errorCodeMap.put("1012", "取主题DN错误");
        errorCodeMap.put("1013", "取公钥数据错误");
        errorCodeMap.put("1014", "取公钥ID错误");
        errorCodeMap.put("1015", "取证书编码错误");
        errorCodeMap.put("1016", "取CRL发布点错误");
        errorCodeMap.put("1017", "取证书扩展项错误");
        errorCodeMap.put("1018", "证书扩展项X509Extension为空");
        errorCodeMap.put("1019", "证书扩展项X509Extensions为空");
        errorCodeMap.put("0x0", "操作成功(DTCSP_SUCCESS)");
        errorCodeMap.put("0x1", "操作失败");
        errorCodeMap.put("0xe000", "管理权限不足");
        errorCodeMap.put("0xe001", "操作权限不满足");
        errorCodeMap.put("0xe002", "私钥权限不满足");
        errorCodeMap.put("0xe003", "私钥权限不满足");
        errorCodeMap.put("0xe050", "管理员未初始化");
        errorCodeMap.put("0xe051", "不支持的操作（韩总说的）");
        errorCodeMap.put("0xe052", "管理员不存在");
        errorCodeMap.put("0xe054", "管理权限不足");
        errorCodeMap.put("0xe055", "操作员不存在");
        errorCodeMap.put("0xe061", "要读取的 rsa 密钥对不存在");
        errorCodeMap.put("0xe063", "系统主密钥或设备主密钥不存在");
        errorCodeMap.put("0xe100", "密钥号错误");
        errorCodeMap.put("0xe101", "密钥的模长错误");
        errorCodeMap.put("0xe102", "密钥不存在");
        errorCodeMap.put("0xe103", "对称密钥长度出错");
        errorCodeMap.put("0xe104", "密钥类型错误，既不是0签名，也不是1加密");
        errorCodeMap.put("0xe500", "读IC卡失败");
        errorCodeMap.put("0xe501", "读管理员IC卡失败");
        errorCodeMap.put("0xe502", "读操作员IC卡失败");
        errorCodeMap.put("0xe503", "写管理员IC卡失败");
        errorCodeMap.put("0xe504", "写操作员IC卡失败");
        errorCodeMap.put("0xE720", "无效的ECC曲线参数");
        errorCodeMap.put("0xE721", "ECC未进行初始化");
        errorCodeMap.put("0xE722", "错误的ECC密钥对");
        errorCodeMap.put("0xee01", "DTCSP负载均衡错误");
        errorCodeMap.put("0xee02", "DTCSP负载均衡错误");
        errorCodeMap.put("0xee03", "DTCSP负载均衡错误");
        errorCodeMap.put("0xee04", "DTCSP负载均衡错误");
        errorCodeMap.put("0xee05", "DTCSP负载均衡错误");
        errorCodeMap.put("0xee20", "错误的参数");
        errorCodeMap.put("0xee21", "打开文件失败");
        errorCodeMap.put("0xeeee", "错误的命令码");
        errorCodeMap.put("0xF030", "对扩展的RSA运算接口函数，基于PKCS1标准加减数据PAD包时可能返回的错误");
        errorCodeMap.put("0xF031", "对扩展的RSA运算接口函数，基于PKCS1标准加减数据PAD包时可能返回的错误");
        errorCodeMap.put("0xF032", "对扩展的RSA运算接口函数，基于PKCS1标准加减数据PAD包时可能返回的错误");
        errorCodeMap.put("0xF033", "对扩展的RSA运算接口函数，基于PKCS1标准加减数据PAD包时可能返回的错误");
        errorCodeMap.put("0xF034", "对扩展的RSA运算接口函数，基于PKCS1标准加减数据PAD包时可能返回的错误");
        errorCodeMap.put("0xF035", "对扩展的RSA运算接口函数，基于PKCS1标准加减数据PAD包时可能返回的错误.");
        errorCodeMap.put("0xF036", "对扩展的RSA运算接口函数，基于PKCS1标准加减数据PAD包时可能返回的错误");
        errorCodeMap.put("0xF038", "对扩展的RSA运算接口函数，基于PKCS1标准加减数据PAD包时可能返回的错误");
        errorCodeMap.put("0xf100", "初始化Socket失败");
        errorCodeMap.put("0xf101", "关闭Socket失败");
        errorCodeMap.put("0xf102", "网络连接错误");
        errorCodeMap.put("0xf103", "网络发送数据失败");
        errorCodeMap.put("0xf104", "网络接收数据失败");
        errorCodeMap.put("0xf105", "网络选择连接错误");
        errorCodeMap.put("0xf106", "修复网络连接失败");
        errorCodeMap.put("0xf107", "服务器登录信息错误");
        errorCodeMap.put("0xf108", "服务器个数错误");
        errorCodeMap.put("0xf109", "获得服务器IP地址失败");
        errorCodeMap.put("0xF110", "错误的连接密码");
        errorCodeMap.put("0xf201", "数据打包错误");
        errorCodeMap.put("0xf202", "数据解包错误");
        errorCodeMap.put("0xf203", "数据打包错误");
        errorCodeMap.put("0xf204", "数据解包错误");
        errorCodeMap.put("0xf301", "创建信号量失败");
        errorCodeMap.put("0xf302", "创建共享内存错误");
        errorCodeMap.put("0xf303", "信号量P操作错误");
        errorCodeMap.put("0xf304", "信号量V操作错误");
        errorCodeMap.put("0xf305", "共享内存操作失败");
        errorCodeMap.put("0xf306", "共享内存操作失败");
        errorCodeMap.put("0xf400", "读配置文件失败");
        errorCodeMap.put("0xf401", "设备类型错误");
        errorCodeMap.put("0xf402", "口令错误");
        errorCodeMap.put("0xf403", "版本不匹配");
        errorCodeMap.put("0xf500", "负载均衡初始化错误");
        errorCodeMap.put("0xf501", "负载均衡结束错误");
        errorCodeMap.put("0xF505", "不合法的数据长度 // 原因之一：调用初始化接口延时参数值过低。");
        errorCodeMap.put("0xF507", "参数错误");
        errorCodeMap.put("0xf600", "输入数据长度错误");
        errorCodeMap.put("0xf601", "输出数据长度错误");
        errorCodeMap.put("0xf602", "密钥长度错误");
        errorCodeMap.put("0xf603", "RSA模长错误");
        errorCodeMap.put("0xf604", "RSA密钥号错误");
        errorCodeMap.put("0xf605", "RSA密钥名错误");
        errorCodeMap.put("0xf700", "ECC密钥号错误");
        errorCodeMap.put("0xf701", "ECDSA签名数据长度错误");
        errorCodeMap.put("0xf702", "SCE密文长度错误");
        errorCodeMap.put("0xf703", "SCH Hash输出长度错误.");
        errorCodeMap.put("0xf800", "ECC密钥号错误");
        errorCodeMap.put("0xf801", "SCE/SM2密钥协商密钥长度错误");
        errorCodeMap.put("0xf802", "SCE/SM2密钥协商参数错误");
        errorCodeMap.put("0xf803", "SCE/SM2密钥协商ID长度错误");
        errorCodeMap.put("0xf804", "SCH/SM3杂凑结果长度错误");
        errorCodeMap.put("0xf805", "SCH/SM3杂凑参数错误");
        errorCodeMap.put("0xf806", "ECC曲线号错误");
        errorCodeMap.put("0xffff", "错误");
    }

}
