package com.sugon.cloud.iam.api.service;

import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.entity.EndpointForm;
import com.sugon.cloud.iam.api.entity.keystone.CreateEndpointResponse;
import com.sugon.cloud.iam.api.entity.keystone.Endpoint;
import com.sugon.cloud.iam.common.model.vo.CreateEndpointResponseParam;
import com.sugon.cloud.iam.api.entity.keystone.VO.CreateEndpointVO;

import java.util.List;

public interface EndpointService {
    /**
     * 创建keystone的endpoint
     * @return
     */
    CreateEndpointResponse createEndpoint(CreateEndpointVO createEndpointVO);

    /**
     * 查询详情
     * @param endpointId
     * @return
     */
    CreateEndpointResponse endpointDetail(String endpointId);

    /**
     * endpoint列表查询
     * @param interfaceParam 名称
     * @param serviceId 类型
     * @param regionId 类型
     * @return
     */
    List<CreateEndpointResponseParam> listEndpoint(String interfaceParam, String serviceId,String regionId);

    CreateEndpointResponseParam findByServiceNameAndRegionId(String serviceName, String regionId);

    /**
     * 通过IAM 创建 endpoint
     * @return
     */
    int createEndpoint(Endpoint endpoint);

    int updateEndpoint(EndpointForm endpointForm);

    int deleteEndpoint(String id);

    PageCL<Endpoint> list(int pageNum, int pageSize);

}
