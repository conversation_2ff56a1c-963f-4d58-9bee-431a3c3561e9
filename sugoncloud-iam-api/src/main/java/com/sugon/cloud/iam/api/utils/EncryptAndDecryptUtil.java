package com.sugon.cloud.iam.api.utils;

import com.alibaba.fastjson.JSONObject;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.enums.RsaKeySizeEnum;
import com.sugon.cloud.iam.api.exception.BusinessException;
import com.sugon.cloud.tools.common.EncryptEntity;
import com.sugon.cloud.tools.common.EncryptEnvelopeEntity;
import com.sugon.cloud.tools.common.SignEntity;
import com.sugon.cloud.tools.common.VerifyEntity;
import com.sugon.cloud.tools.service.EncAndDecFeignService;
import com.sugon.cloud.tools.service.SignAndVerifyFeignService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.Iterator;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.concurrent.TimeUnit;

@Component
@RefreshScope
public class EncryptAndDecryptUtil {
    private static final Logger logger = LoggerFactory.getLogger(EncryptAndDecryptUtil.class);
    private static final String defaultCharset = "UTF-8";
    private static final String KEY_AES = "AES";
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private SignAndVerifyFeignService signAndVerifyFeignService;

    @Autowired
    private EncAndDecFeignService encAndDecFeignService;
    @Value("${encryption.enabled}")
    private boolean encryption;

    @Value("${encryption.aes.key:sugoncloud-iam-api}")
    private String aesKey;
    /***
     * MD5加码 生成32位md5码
     */
    public String string2MD5(String inStr){
        MessageDigest md5 = null;
        try{
            md5 = MessageDigest.getInstance("MD5");
        }catch (Exception e){
            System.out.println(e.toString());
            e.printStackTrace();
            return "";
        }
        char[] charArray = inStr.toCharArray();
        byte[] byteArray = new byte[charArray.length];

        for (int i = 0; i < charArray.length; i++)
            byteArray[i] = (byte) charArray[i];
        byte[] md5Bytes = md5.digest(byteArray);
        StringBuffer hexValue = new StringBuffer();
        for (int i = 0; i < md5Bytes.length; i++){
            int val = ((int) md5Bytes[i]) & 0xff;
            if (val < 16)
                hexValue.append("0");
            hexValue.append(Integer.toHexString(val));
        }
        return hexValue.toString();

    }

    /**
     * 随机数
     * @param place 定义随机数的位数
     */
    public static String randomGen(int place) {
        String base = "qwertyuioplkjhgfdsazxcvbnmQAZWSXEDCRFVTGBYHNUJMIKLOP0123456789";
        StringBuffer sb = new StringBuffer();
        Random rd = new Random();
        for(int i=0;i<place;i++) {
            sb.append(base.charAt(rd.nextInt(base.length())));
        }
        return sb.toString();
    }

    /**
     * 加密
     *
     * @param data 需要加密的内容
     * @param key 加密密码
     * @return
     */
    public String encrypt(String data, String key) {

        return doAES(data, key, Cipher.ENCRYPT_MODE);
    }

    public String encrypt(String data) {
        return doAES(data, aesKey, Cipher.ENCRYPT_MODE);
    }


    /**
     * 解密
     *
     * @param data 待解密内容
     * @param key 解密密钥
     * @return
     */
    public String decrypt(String data, String key) {
        return doAES(data, key, Cipher.DECRYPT_MODE);
    }

    /**
     * 解密
     *
     * @param data 待解密内容
     * @return
     */
    public String decrypt(String data) {
        return doAES(data, aesKey, Cipher.DECRYPT_MODE);
    }

    /**
     * 加解密
     *
     * @param data 待处理数据
     * @param mode 加解密mode
     * @return
     */
    private String doAES(String data, String key, int mode) {
        try {
            if (StringUtils.isBlank(data) || StringUtils.isBlank(key)) {
                return null;
            }
            //判断是加密还是解密
            boolean encrypt = mode == Cipher.ENCRYPT_MODE;
            byte[] content;
            //true 加密内容 false 解密内容
            if (encrypt) {
                content = data.getBytes(defaultCharset);
            } else {
                content = parseHexStr2Byte(data);
            }
            //1.构造密钥生成器，指定为AES算法,不区分大小写
            KeyGenerator kgen = KeyGenerator.getInstance(KEY_AES);
            //2.根据ecnodeRules规则初始化密钥生成器
            //生成一个128位的随机源,根据传入的字节数组
            SecureRandom random = SecureRandom.getInstance("SHA1PRNG");
            random.setSeed(key.getBytes(defaultCharset));
            kgen.init(128, random);
            //3.产生原始对称密钥
            SecretKey secretKey = kgen.generateKey();
            //4.获得原始对称密钥的字节数组
            byte[] enCodeFormat = secretKey.getEncoded();
            //5.根据字节数组生成AES密钥
            SecretKeySpec keySpec = new SecretKeySpec(enCodeFormat, KEY_AES);
            //6.根据指定算法AES自成密码器
            Cipher cipher = Cipher.getInstance(KEY_AES);// 创建密码器
            //7.初始化密码器，第一个参数为加密(Encrypt_mode)或者解密解密(Decrypt_mode)操作，第二个参数为使用的KEY
            cipher.init(mode, keySpec);// 初始化
            byte[] result = cipher.doFinal(content);
            if (encrypt) {
                //将二进制转换成16进制
                return parseByte2HexStr(result);
            } else {
                return new String(result, defaultCharset);
            }
        } catch (Exception e) {
            logger.error("AES 密文处理异常", e);
        }
        return null;
    }
    /**
     * 将二进制转换成16进制
     *
     * @param buf
     * @return
     */
    public String parseByte2HexStr(byte buf[]) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < buf.length; i++) {
            String hex = Integer.toHexString(buf[i] & 0xFF);
            if (hex.length() == 1) {
                hex = '0' + hex;
            }
            sb.append(hex.toUpperCase());
        }
        return sb.toString();
    }
    /**
     * 将16进制转换为二进制
     *
     * @param hexStr
     * @return
     */
    public byte[] parseHexStr2Byte(String hexStr) {
        if (hexStr.length() < 1) {
            return null;
        }
        byte[] result = new byte[hexStr.length() / 2];
        for (int i = 0; i < hexStr.length() / 2; i++) {
            int high = Integer.parseInt(hexStr.substring(i * 2, i * 2 + 1), 16);
            int low = Integer.parseInt(hexStr.substring(i * 2 + 1, i * 2 + 2), 16);
            result[i] = (byte) (high * 16 + low);
        }
        return result;
    }

    /**
     * 校验redis中的key是否存在，true 存在(并删除该key) | false 不存在
     * @param publicKey
     * @return
     */
    public boolean validatePublicKeyExist(String publicKey) {
        if (StringUtils.isBlank(publicKey)) {
            return false;
        }
        Boolean result = redisTemplate.hasKey(publicKey);
        return !Objects.isNull(result) && result;
    }

    public String decryptByPublickey (String publicKey, String password) throws Exception {
        if (StringUtils.isBlank(publicKey)) {
            return password;
        }
        String privateKey = String.valueOf(redisTemplate.opsForValue().get(publicKey));
        redisTemplate.delete(publicKey);
        String decryptPassword;
        try {
            decryptPassword = new String(RSAUtil.decryptByPrivateKey(org.apache.commons.codec.binary.Base64.decodeBase64(password), privateKey));
        } catch (Exception e) {
            logger.error("解密失败", e);
            throw new Exception("解密失败");
        }
        return decryptPassword;
    }

    public String decryptByPublickeyNotDelete (String publicKey, String password) throws Exception {
        String privateKey = String.valueOf(redisTemplate.opsForValue().get(publicKey));
        String decryptPassword;
        try {
            decryptPassword = new String(RSAUtil.decryptByPrivateKey(org.apache.commons.codec.binary.Base64.decodeBase64(password), privateKey));
        } catch (Exception e) {
            logger.error("解密失败", e);
            throw new Exception("解密失败");
        }
        return decryptPassword;
    }

    public String getPublicKey(String useSecurity, RsaKeySizeEnum keySizeEnum) {
        try {
            String publicKey;
            if (encryption) {
                if("yes".equals(useSecurity)){
                    publicKey = this.getIv();
                    redisTemplate.opsForValue().set(publicKey, publicKey, 12, TimeUnit.HOURS);
                }else {
                    Map<String, Object> keyPairMap = RSAUtil.genKeyPair(keySizeEnum);
                    publicKey = RSAUtil.getPublicKey(keyPairMap);
                    redisTemplate.opsForValue().set(publicKey, RSAUtil.getPrivateKey(keyPairMap), 12, TimeUnit.HOURS);
                }
            } else {
                Map<String, Object> keyPairMap = RSAUtil.genKeyPair(keySizeEnum);
                publicKey = RSAUtil.getPublicKey(keyPairMap);
                redisTemplate.opsForValue().set(publicKey, RSAUtil.getPrivateKey(keyPairMap), 12, TimeUnit.HOURS);
            }
            return publicKey;
        } catch (Exception e) {
            logger.error("init public key error", e);
            throw new BusinessException("获取公钥失败");
        }
    }

    /**
     * 字符串签名 生成hash
     * @param inStr
     * @return
     */
    public String sign (String inStr) {
        SignEntity signRole = new SignEntity();
        signRole.setType(CommonInstance.HMAC);
        signRole.setJson(inStr);
        ResultModel<String> sign = signAndVerifyFeignService.sign(signRole);
        if (sign == null || sign.getStatusCode() == 0) throw new BusinessException("加密机签名失败");
        String hash = sign.getContent();
        return hash;
    }

    /**
     *
     * @param hash 需要校验的hash
     * @param inStr 当前字符串
     * @return
     */
    public boolean verifyHash(String hash, String inStr) {
        VerifyEntity verifyEntity = new VerifyEntity();
        verifyEntity.setType(CommonInstance.HMAC);
        verifyEntity.setSignJson(hash);//hash
        verifyEntity.setInJson(inStr);//当前值
        ResultModel<Boolean> verify = signAndVerifyFeignService.verify(verifyEntity);
        return verify.getContent();
    }

    public String digitalEnvelope(String envelope) {
        if (encryption) {
            ResultModel<String> stringResultModel = signAndVerifyFeignService.decryptEnvelope(envelope);
            return stringResultModel.getContent();
        }
        return envelope;
    }

    public String encryptEnvelope(String cert, String inData) {
        if (encryption) {
        EncryptEnvelopeEntity encryptEnvelope = new EncryptEnvelopeEntity();
        encryptEnvelope.setCert(cert);
        encryptEnvelope.setInData(inData);
        ResultModel<String> stringResultModel = signAndVerifyFeignService.encryptEnvelope(encryptEnvelope);
        return stringResultModel.getContent();
        }
        return inData;
    }

    public String getIv() {
        if (encryption) {
            ResultModel<Map> resultModel = encAndDecFeignService.getIV();
            Map map = resultModel.getContent();
            logger.info("get iv {}: ", resultModel.isSuccess());
            if (!resultModel.isSuccess() || resultModel.getStatusCode() == 0) throw new BusinessException("获取IV失败");
            Iterator<Map.Entry<String, String>> it = map.entrySet().iterator();
            while (it.hasNext()) {
                Map.Entry<String, String> entry = it.next();
                String iv = entry.getValue();
                logger.info("iv = {}", iv);
                return iv;
            }
        }
        return "";
    }
    public String encryptByIv (String singData, String iv) {
        if (StringUtils.isBlank(singData)) {
            logger.warn("加密数据不能为空");
            return singData;
        }
        EncryptEntity encryptEntity = new EncryptEntity();
        encryptEntity.setType(CommonInstance.SM_4);
        encryptEntity.setJson(singData);
        encryptEntity.setModel(CommonInstance.MODEL_CBC);
        encryptEntity.setIv(iv);
        logger.debug("SM4 cbc encrypt param:{}", JSONObject.toJSONString(encryptEntity));
        ResultModel<String> encrypt = encAndDecFeignService.encrypt(encryptEntity);
        logger.info("encrypt result {}", JSONObject.toJSONString(encrypt));
        if (!encrypt.isSuccess() || encrypt.getStatusCode() == 0) throw new BusinessException("加密机加密失败");
        return encrypt.getContent();
    }

    public String decryptByIv(String encryptData, String iv) {
        if (StringUtils.isBlank(encryptData)) {
            logger.warn("解密数据不能为空");
            return encryptData;
        }
        EncryptEntity encryptEntity = new EncryptEntity();
        encryptEntity.setType(CommonInstance.SM_4);
        encryptEntity.setJson(encryptData);
        encryptEntity.setModel(CommonInstance.MODEL_CBC);
        encryptEntity.setIv(iv);
        logger.debug("SM4 cbc decrypt param:{}", JSONObject.toJSONString(encryptEntity));
        ResultModel<String> decrypt = encAndDecFeignService.decrypt(encryptEntity);
        logger.info("decrypt result {}", JSONObject.toJSONString(decrypt));
        if (!decrypt.isSuccess() || decrypt.getStatusCode() == 0) throw new BusinessException("加密机解密失败");
        return decrypt.getContent();
    }
}
