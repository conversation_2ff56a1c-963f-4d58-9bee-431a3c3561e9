package com.sugon.cloud.iam.api.service.resourceauth;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.sugon.cloud.common.constants.HeaderParamConstant;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.resource.auth.common.service.impl.BaseResourceAuthHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/24 10:54
 */
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class UserCreateDeptManagerAuthHandler extends BaseResourceAuthHandler {

    private final HttpServletRequest request;

    @Override
    public List<String> getUserResource(String userId) {
        String header = request.getHeader(HeaderParamConstant.USER_TYPE);
        if (StrUtil.equals(header, TypeUtil.TYPE_ADMIN)
                || StrUtil.equals(header, TypeUtil.TYPE_SECURITY)
                || StrUtil.equals(header, TypeUtil.TYPE_SUB_ADMIN)
                || StrUtil.equals(header, TypeUtil.TYPE_MASTER) ) {
            return CollectionUtil.newArrayList(Boolean.TRUE.toString(), Boolean.FALSE.toString());
        } else if (StrUtil.equals(header, TypeUtil.TYPE_DEPT_MASTER)) {
            return CollectionUtil.newArrayList(Boolean.FALSE.toString());
        }
        return null;
    }
}
