package com.sugon.cloud.iam.api.entity.keystone;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sugon.cloud.iam.common.model.vo.CreateResourceResponseLinksParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "创建角色响应参数")
public class CreateRoleResponseParam {
    @JsonProperty("domain_id")
    @ApiModelProperty("域ID")
    private String domainId;
    @ApiModelProperty("角色名称")
    private String name;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("角色描述")
    private String description;
    @ApiModelProperty("资源link")
    private CreateResourceResponseLinksParam links;
}
