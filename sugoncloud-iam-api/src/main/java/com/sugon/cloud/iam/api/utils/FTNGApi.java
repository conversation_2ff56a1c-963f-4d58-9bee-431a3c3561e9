package com.sugon.cloud.iam.api.utils;


import com.ft.otp.authng.FTNGConstant;
import com.ft.otp.authng.FTNGErrorCode;
import com.ft.otp.authng.FTNGTokenData;
import com.ft.otp.core.FTNGTokenAPI;
import com.ft.otp.core.ReturnResult;
import com.sun.jna.NativeLong;
import com.sun.jna.ptr.IntByReference;
import java.io.IOException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;

/**
 * 坚持诚信工具类
 */
@Component
@ConditionalOnExpression("${mfa.enabled} &&'${mfa.type}'.contains('ftng')")
public class FTNGApi {

    private static final Logger log = LoggerFactory.getLogger(FTNGApi.class);
    FTNGTokenAPI tokenAuth;// 认证引擎对象

    String pin = "";// 默认的pin

    /**
     *
     * 构造java封装接口对象
     */
    public FTNGApi() throws Exception {
        // 初始化接口

        tokenAuth = new FTNGTokenAPI();
    }

    /**
     *
     * 挑战应答型动态口令认证(需要保存更新后的令牌私有数据)
     *
     * @param privateData   令牌私有数据字符串
     * @param otp           挑战应答型动态口令
     * @param challengeCode 挑战码
     * @return 结果对象(返回操作返回码和更新后的私有数据)
     */
    public ReturnResult authChallengeOTP(String privateData, String otp, String challengeCode) throws Exception {
        ReturnResult returnResult = new ReturnResult();
        FTNGTokenData token = new FTNGTokenData();
        returnResult = setTokenData(token, privateData);// 设置令牌数据
        if (isSuccess(returnResult.getReturnCode())) {
            returnResult = tokenAuth.set_user_login(token, getString(token.pdata.sn));// 设置用户(设为令牌号)
        }

        if (isSuccess(returnResult.getReturnCode())) {
            // 验证动态口令
            NativeLong time = tokenAuth.get_passcode_time();
            System.out.println("当前时间:" + time);
            returnResult = tokenAuth.check_chpass(time, token, otp, pin, challengeCode);
        }

        String privateData1 = encode_pdata(token);
        if (privateData1 != null) {
            returnResult.setPrivateData(privateData1);
        }

        return returnResult;
    }

    /**
     * 时间型动态口令认证(需要保存更新后的令牌私有数据)
     *
     * @param privateData 令牌私有数据字符串
     * @param otp         时间型动态口令
     * @return 结果对象(返回操作返回码和更新后的私有数据)
     */
    public ReturnResult authOTP(String privateData, String otp) throws Exception {
        ReturnResult returnResult = new ReturnResult();
        FTNGTokenData token = new FTNGTokenData();
        returnResult = setTokenData(token, privateData);// 设置令牌数据
        log.debug("setTokenData:{}", returnResult.getReturnCode());
        if (isSuccess(returnResult.getReturnCode())) {
            returnResult = tokenAuth.set_user_login(token, getString(token.pdata.sn));// 设置用户(设为令牌号)
        }
        log.debug("set_user_login:{}", returnResult.getReturnCode());
        // if (isSuccess(returnCode)) {
        // returnCode = tokenAuth.set_pin(token, pin);//设定pin码
        // }

        if (isSuccess(returnResult.getReturnCode())) {
            // 验证动态口令
            returnResult = tokenAuth.check_password(token, otp, pin);
        }
        log.debug("check_password:{}", returnResult.getReturnCode());
        ReturnResult returnResult1 = tokenAuth.encode_pdata(token);// 将私有数据结构转为私有数据字符串,保存在FTNGTokenData结构的priv_data成员中
        if (isSuccess(returnResult1.getReturnCode())) {
            String res_privateData = getString(token.priv_data);// 设置私有数据字符串
            returnResult.setPrivateData(res_privateData);
        }
        return returnResult;
    }

    // 业务处理完后加密私有数据
    public String encode_pdata(FTNGTokenData token) throws Exception {
        String privateDate = "";
        ReturnResult result = tokenAuth.encode_pdata(token);// 将私有数据结构转为私有数据字符串,保存在FTNGTokenData结构的priv_data成员中
        if (isSuccess(result.getReturnCode())) {
            privateDate = getString(token.priv_data);// 设置私有数据字符串
        } else {
            return null;
        }
        return privateDate;
    }

    /**
     * 获取挑战值
     *
     * @param privateData 令牌私有数据字符串
     * @return 结果对象(返回操作返回码和挑战码)
     */
    public ReturnResult genChallengeCode(String privateData) throws Exception {
        ReturnResult returnResult = new ReturnResult();
        FTNGTokenData token = new FTNGTokenData();
        returnResult = setTokenData(token, privateData);// 设置令牌数据

        if (isSuccess(returnResult.getReturnCode())) {
            returnResult = tokenAuth.set_user_login(token, getString(token.pdata.sn));// 设置用户(设为令牌号)
        }

        if (isSuccess(returnResult.getReturnCode())) {
            returnResult = tokenAuth.gen_chlg(token);
        }

        String privateData1 = encode_pdata(token);
        if (privateData1 != null) {
            returnResult.setPrivateData(privateData1);
        }

        return returnResult;
    }

    /**
     * 生成当前时间的动态口令（挑战应答型）(认证成功后才能调用此方法)
     *
     * @param privateData   令牌私有数据字符串
     * @param challengeCode 挑战码
     * @return 结果对象(返回操作返回码和动态口令)
     */
    public ReturnResult genChallengeOTP(String privateData, String challengeCode) throws Exception {
        ReturnResult returnResult = new ReturnResult();
        FTNGTokenData token = new FTNGTokenData();
        returnResult = setTokenData(token, privateData);// 设置令牌数据

        if (isSuccess(returnResult.getReturnCode())) {
            returnResult = tokenAuth.set_user_login(token, getString(token.pdata.sn));// 设置用户(设为令牌号)
        }

        if (isSuccess(returnResult.getReturnCode())) {
            returnResult = tokenAuth.set_pin(token, pin);// 设定pin码
        }

        if (isSuccess(returnResult.getReturnCode())) {
            returnResult = tokenAuth.gen_chpass(tokenAuth.get_passcode_time(), token, challengeCode);
        }

        String privateData1 = encode_pdata(token);
        if (privateData1 != null) {
            returnResult.setPrivateData(privateData1);
        }

        return returnResult;
    }

    /**
     * 产生当前时间的动态口令(时间型)(认证成功后才能调用此方法)
     *
     * @param privateData 令牌私有数据字符串
     * @return 结果对象(返回操作返回码和动态口令)
     */
    public ReturnResult genOTP(String privateData) throws Exception {
        ReturnResult returnResult = new ReturnResult();
        FTNGTokenData token = new FTNGTokenData();
        returnResult = setTokenData(token, privateData);// 设置令牌数据

        if (isSuccess(returnResult.getReturnCode())) {
            returnResult = tokenAuth.set_user_login(token, getString(token.pdata.sn));// 设置用户(设为令牌号)
        }

        if (isSuccess(returnResult.getReturnCode())) {
            returnResult = tokenAuth.set_pin(token, pin);// 设定pin码
        }

        if (isSuccess(returnResult.getReturnCode())) {
            returnResult = tokenAuth.genotp(tokenAuth.get_passcode_time(), token);
        }

        String privateData1 = encode_pdata(token);
        if (privateData1 != null) {
            returnResult.setPrivateData(privateData1);
        }

        return returnResult;
    }

    /**
     * 获取令牌激活码(此操作更新了私有数据,应将返回的激活码和私有数据同时保存,如果用更新后的私有数据再次产生的激活码会与之前产生的相同)
     *
     * @param privateData 令牌私有数据字符串
     * @return 结果对象(返回操作返回码，以及操作成功时返回的激活码和更新后的私有密钥)
     */
    public ReturnResult getActiveCode(String privateData) throws Exception {
        ReturnResult returnResult = new ReturnResult();
        FTNGTokenData token = new FTNGTokenData();
        returnResult = setTokenData(token, privateData);// 设置令牌数据
        tokenAuth.enable_token(token);// 默认启用令牌
        if (isSuccess(returnResult.getReturnCode())) {
            byte[] activeCode_bytes = new byte[FTNGConstant.MAX_UP_RESP_SIZE];
            returnResult = tokenAuth.update_key(token, "", null, activeCode_bytes);// 密钥更新
            if (isSuccess(returnResult.getReturnCode())) {
                String activeCode = getString(activeCode_bytes);
                returnResult.setActiveCode(activeCode);// 设置激活码
            }
        }
        ReturnResult result = tokenAuth.encode_pdata(token);// 将私有数据结构转为私有数据字符串,保存在FTNGTokenData结构的priv_data成员中
        if (isSuccess(result.getReturnCode())) {
            String res_privateData = getString(token.priv_data);// 设置私有数据字符串
            returnResult.setPrivateData(res_privateData);

        } else {
            return result;
        }
        return returnResult;
    }

    /**
     * 获取硬件令牌解锁码
     *
     * @param privateData   令牌私有数据字符串
     * @param challengeCode 挑战码(不需要挑战码时传null) @return结果对象(返回操作返回码和解锁码)
     */
    public ReturnResult getPUK(String privateData, String challengeCode) throws Exception {
        ReturnResult returnResult = new ReturnResult();
        FTNGTokenData token = new FTNGTokenData();
        returnResult = setTokenData(token, privateData);// 设置令牌数据
        if (isSuccess(returnResult.getReturnCode())) {
            returnResult = tokenAuth.get_puk(token, challengeCode);
        }

        String privateData1 = encode_pdata(token);
        if (privateData1 != null) {
            returnResult.setPrivateData(privateData1);
        }

        return returnResult;
    }

    /**
     * 获取硬件令牌二级解锁码
     *
     * @param privateData   令牌私有数据字符串
     * @param challengeCode 挑战码(不需要挑战码时传null) @return结果对象(返回操作返回码和解锁码)
     */
    public ReturnResult getPUK2(String privateData, String challengeCode) throws Exception {
        ReturnResult returnResult = new ReturnResult();
        FTNGTokenData token = new FTNGTokenData();
        returnResult = setTokenData(token, privateData);// 设置令牌数据
        if (isSuccess(returnResult.getReturnCode())) {
            returnResult = tokenAuth.get_puk2(token, challengeCode);
        }

        String privateData1 = encode_pdata(token);
        if (privateData1 != null) {
            returnResult.setPrivateData(privateData1);
        }

        return returnResult;
    }

    /**
     * 返回认证引擎返回的字符串
     *
     * @Date in 2013-5-5,下午07:08:24
     * @param bData 认证引擎填充的字节数组
     * @return
     */
    public String getString(byte[] bData) {
        if (bData == null) {
            return "";
        }

        String strData = new String(bData);
        if (strData.indexOf('\0') != -1) {
            strData = strData.substring(0, strData.indexOf('\0'));
        }
        return strData;
    }

    /**
     * 返回是否成功
     *
     * @Date in 2013-5-5,下午07:05:21
     * @param returnCode
     * @return
     */
    private boolean isSuccess(int returnCode) {
        return returnCode == FTNGErrorCode.FT_API_SUCC;
    }

    /**
     * 读种子文件
     *
     * @param xmlFilePath 令牌文件的路径
     * @param keyFilePath 密钥文件的路径
     * @param pass        密码，可以是PEB密码或RSA私钥的保护密码(没有密码时传空字符串)
     * @return 结果对象(返回操作返回码和令牌数据对象列表)
     */
    public ReturnResult readSeeds(MultipartFile xmlFilePath, MultipartFile keyFilePath, String pass) throws Exception {
        // 指定文件保存的位置
        String uploadDir = "/tmp/uploaded-files";
        // 确保目录存在
        File dir = new File(uploadDir);
        if (!dir.exists()) {
            dir.mkdirs(); // 创建目录
        }
        File file1Saved = saveMultipartFile(xmlFilePath, uploadDir);
        File file2Saved = saveMultipartFile(keyFilePath, uploadDir);
        String xmlFilePath1 = file1Saved.getAbsolutePath();
        String keyFilePath1 = file2Saved.getAbsolutePath();
        log.info("xmlFilePath:" + xmlFilePath1);
        log.info("keyFilePath:" + keyFilePath1);
        File xmlFile = new File(xmlFilePath1);
        if (!xmlFile.exists()) {
            throw new Exception("seed file does not exist!");
        }
        pass = null == pass ? "" : pass.trim();
        ReturnResult returnResult = new ReturnResult();
        IntByReference pCount = new IntByReference();
        returnResult = tokenAuth.initPskc(xmlFilePath1, keyFilePath1, pass);
        log.info("returnResult:" + returnResult.getReturnCode());
        if (isSuccess(returnResult.getReturnCode())) {
            pCount.setValue( returnResult.getTokenTotal());
        }
        List<FTNGTokenData> tokenList = new ArrayList<FTNGTokenData>();
        for (int i = 0; i < pCount.getValue(); i++) {
            FTNGTokenData token = new FTNGTokenData();
            returnResult = tokenAuth.readPskcRec(token, i);
            tokenAuth.decode_pdata(token);// 将私有数据字符串设置到私有数据结构转
            tokenAuth.enable_token(token);// 默认启用令牌
            tokenAuth.encode_pdata(token);// 将私有数据结构转为私有数据字符串

            if (isSuccess(returnResult.getReturnCode())) {
                tokenList.add(token);
            }
        }
        int code = tokenAuth.uninit_pskc();
        returnResult.setTokenList(tokenList);// 设置令牌列表
        returnResult.setReturnCode(code);// 设置返回码
        return returnResult;
    }

    /**
     * 通过私有数据字符串设置令牌私有数据,并用私有数据字符串设置令牌结构(设置令牌结构各个属性)
     *
     * @Date in 2013-5-5,下午06:59:01
     * @param token       令牌数据对象
     * @param privateData 令牌私有数据字符串
     * @return 操作结果返回码
     * @throws Exception
     */
    public ReturnResult setTokenData(FTNGTokenData token, String privateData) throws Exception {
        token.write();
        System.arraycopy(privateData.getBytes(), 0, token.priv_data, 0, privateData.length());
        ReturnResult returnResult = tokenAuth.decode_pdata(token);
        return returnResult;
    }

    /**
     * 设置私有数据中的大中小窗口(设置成功后需要保存更新后的令牌私有数据)
     *
     * <pre>
     * 窗口使用情况说明:(1)正常情况使用小窗口(默认为2,表示在前后1分种和当前1分钟共3分钟范围内计算)
     *                  (2)距上次使用时间超过两周时使用中窗口(默认为4,表示在前后2分种和当前1分钟共5分钟范围内计算)
     *                  (3)首次认证时和同步时使用的是大窗口(默认为20,表示在前后10分种和当前1分钟共21分钟范围内计算)
     * </pre>
     *
     * @param auth_wnd    认证小窗口
     * @param med_wnd     认证中窗口
     * @param sync_wnd    认证大窗口(同步窗口)
     * @return 设置操作结果返回码
     */
    public ReturnResult setWnd(FTNGTokenData data, int auth_wnd, int med_wnd, int sync_wnd) throws Exception {
        ReturnResult returnResult = new ReturnResult();

        if (isSuccess(returnResult.getReturnCode())) {
            returnResult = tokenAuth.set_wnd(data, auth_wnd, med_wnd, sync_wnd);
        }

//		int returnCode = returnResult.getReturnCode();
//		System.out.println("返回码:" + returnCode);
//		if (returnCode == 0) {
//			System.out.println("更新后的私有数据:" + returnResult.getPrivateData());
//		}
        // System.out.println("私有数据是否有变化:" +
        // !(returnResult.getPrivateData()).equals(Temp_Data.new_privateData));

        String privateData1 = encode_pdata(data);
        if (privateData1 != null) {
            returnResult.setPrivateData(privateData1);
        }
        FTNGTokenData token2 = new FTNGTokenData();
        setTokenData(token2, privateData1);// 设置令牌数据
        System.out.println("设置后:[小窗口：" + token2.pdata.auth_wnd + "] [中窗口：" + token2.pdata.medium_wnd + "] [大窗口："
                + token2.pdata.sync_wnd + "]");

        return returnResult;
    }

    /**
     * 同步令牌(同步成功后需要保存更新后的令牌私有数据)
     *
     * @param privateData 令牌私有数据字符串
     * @param otp         生成的当前otp
     * @param nextOtp     生成的下一个otp
     * @return 结果对象(返回操作返回码和更新后的令牌私有数据)
     */
    public ReturnResult syncOTP(String privateData, String otp, String nextOtp) throws Exception {
        ReturnResult returnResult = new ReturnResult();
        FTNGTokenData token = new FTNGTokenData();
        returnResult = setTokenData(token, privateData);// 设置令牌数据

        if (isSuccess(returnResult.getReturnCode())) {
            returnResult = tokenAuth.set_user_login(token, getString(token.pdata.sn));// 设置用户(设为令牌号)
        }

        if (isSuccess(returnResult.getReturnCode())) {
            // 同步令牌
            returnResult = tokenAuth.resynch2_token(tokenAuth.get_passcode_time(), token, otp, nextOtp);
        }

        String privateData1 = encode_pdata(token);
        if (privateData1 != null) {
            returnResult.setPrivateData(privateData1);
        }

        return returnResult;
    }

    private File saveMultipartFile(MultipartFile multipartFile, String uploadDir) throws IOException {
        String originalFilename = multipartFile.getOriginalFilename();
        // 校验路径和文件名
        if (!isValidPath(uploadDir) || !isValidFilename(originalFilename)) {
            throw new IllegalArgumentException("Invalid path or filename");
        }
        // 创建文件对象
        File file = new File(uploadDir, multipartFile.getOriginalFilename());

        // 保存文件
        multipartFile.transferTo(file);

        return file;
    }

    private static boolean isValidPath(String path) {
        // 校验路径是否合法
        return path.matches("^[a-zA-Z0-9_./-]+$");
    }

    private static boolean isValidFilename(String filename) {
        // 校验文件名是否合法
        return filename.matches("^[a-zA-Z0-9._-]+$");
    }
}
