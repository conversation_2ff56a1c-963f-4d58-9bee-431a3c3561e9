package com.sugon.cloud.iam.api.service;

import com.sugon.cloud.iam.api.entity.keystone.CreateServiceResponse;
import com.sugon.cloud.iam.api.entity.keystone.CreateServiceResponseParam;
import com.sugon.cloud.iam.api.entity.keystone.VO.CreateServiceVO;

import java.util.List;

public interface ServiceService {
    /**
     * 创建keystone的服务
     * @return
     */
    CreateServiceResponse createService(CreateServiceVO createServiceVO);

    /**
     * 查询详情
     * @param serviceId
     * @return
     */
    CreateServiceResponse serviceDetail(String serviceId);

    /**
     * service列表查询
     * @param name 名称
     * @param type 类型
     * @return
     */
    List<CreateServiceResponseParam> listService(String name, String type);
}
