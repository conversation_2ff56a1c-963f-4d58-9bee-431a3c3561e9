package com.sugon.cloud.iam.api.config;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sugon.cloud.iam.api.entity.MicroService;
import com.sugon.cloud.iam.api.service.MicroServiceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 修改microService.link中的ip地址
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MicroServiceLinkUpdaterConfig implements CommandLineRunner{
    @Value("${iam.ip}")
    private String iamUrl;
    @Value("${update-micro}")
    private Boolean updateMicro;
    @Value("${micro-service.update_link.include_third_part_access_service_id:sugoncloud-meterage-api}")
    private List<String> includeThirdPartAccessServiceIds;

    private final String colon = ":";
    private final MicroServiceService microServiceService;

    @Override
    public void run(String... args) {
        try {
            if (updateMicro) {
                log.info("::::::::::::::update microService link start");
                List<MicroService> msList = microServiceService.list(new QueryWrapper<MicroService>().lambda()
                        .eq(MicroService::getThirdPartAccess, false)
                        .likeRight(MicroService::getLink, "http")
                        .notLike(MicroService::getLink, iamUrl));
                // 更新第三方服务包含的serviceId的link地址
                if (!CollectionUtils.isEmpty(includeThirdPartAccessServiceIds)) {
                    List<MicroService> includeThirdPartAccessServiceList = microServiceService.list(new QueryWrapper<MicroService>().lambda()
                            .eq(MicroService::getThirdPartAccess, true)
                            .in(MicroService::getServiceId, includeThirdPartAccessServiceIds)
                            .likeRight(MicroService::getLink, "http")
                            .notLike(MicroService::getLink, iamUrl));
                    msList.addAll(includeThirdPartAccessServiceList);
                }
                if (!CollectionUtils.isEmpty(msList)) {
                    msList.forEach(e -> {
                        String link = e.getLink();
                        String[] linkSplit = link.split(colon);
                        String port = "";
                        if (linkSplit.length == 3) {
                            port = linkSplit[2];
                        }

                        link = linkSplit[0] + colon + "//" + iamUrl;
                        if (!StringUtils.isEmpty(port)) {
                            link += colon + port;
                        }
                        e.setLink(link);
                    });
                    microServiceService.saveOrUpdateBatch(msList);
                }
                log.info("::::::::::::::update microService link end,size is [{}]", msList.size());
            }
        } catch (Exception e) {
            log.error("::::::::::::::update microService link fail", e);
        }
    }
}
