package com.sugon.cloud.iam.api.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.entity.UserStrategyEntity;
import com.sugon.cloud.iam.api.mapper.UserStrategyMapper;
import com.sugon.cloud.iam.api.service.UserStrategyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class UserStrategyServiceImpl extends ServiceImpl<UserStrategyMapper, UserStrategyEntity> implements UserStrategyService {

    @Resource(name = "defaultRedisTemplate")
    private final RedisTemplate defaultRedisTemplate;

    @Override
    public void syncStrategy2Redis(String userId) {
        List<UserStrategyEntity> userStrategies= this.baseMapper.selectList(
                new LambdaQueryWrapper<UserStrategyEntity>()
                .eq(UserStrategyEntity::getUserId, userId));
        List<String> strategyIds = userStrategies.stream().map(UserStrategyEntity::getStrategyId).collect(Collectors.toList());
        StringBuilder ids = new StringBuilder();
        for (int i = 0; i < strategyIds.size(); i++) {
            ids.append(strategyIds.get(i));
            if (i < strategyIds.size() -1) {
                ids.append(",");
            }
        }
        JSONObject jsonIds = new JSONObject();
        jsonIds.put("policy", ids);
        defaultRedisTemplate.opsForValue().set(CommonInstance.IAM_POLICYDB_USERS+userId, jsonIds.toString());
    }
}
