package com.sugon.cloud.iam.api.vo.message;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: yangdingshan
 * @Date: 2025/1/25 11:28
 * @Description:
 */
@Data
@ApiModel("发送验证码")
public class AuthorizeSendCodeVO {

    @ApiModelProperty("用户名，未登录前必传")
    private String username;

    @ApiModelProperty(value = "业务类型, 登录:login, 敏感操作:danger", required = true)
    @NotBlank(message = "业务类型不能为空")
    @JsonProperty("business_type")
    private String businessType;
}
