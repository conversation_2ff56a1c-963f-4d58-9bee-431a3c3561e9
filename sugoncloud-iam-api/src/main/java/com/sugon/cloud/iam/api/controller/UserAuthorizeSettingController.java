package com.sugon.cloud.iam.api.controller;

import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.iam.api.service.MFAAuthorizeService;
import com.sugon.cloud.iam.api.service.Oauth2AuthorizeService;
import com.sugon.cloud.iam.api.service.resourceauth.UserResourceAuthHandler;
import com.sugon.cloud.iam.api.vo.MFADetailVO;
import com.sugon.cloud.iam.api.vo.MfaResultVO;
import com.sugon.cloud.iam.api.vo.OAuth2CallbackResponseVO;
import com.sugon.cloud.iam.api.vo.OAuth2DetailVO;
import com.sugon.cloud.iam.api.vo.OAuth2ServerItem;
import com.sugon.cloud.log.client.starter.annotation.LogRecordAnnotation;
import com.sugon.cloud.resource.auth.common.aop.ResourceAuth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/05/16 11:49
 **/
@Api(tags = "用户认证设置")
@RequestMapping(value = "/api/users")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RestController
@RefreshScope
@DocumentIgnore
public class UserAuthorizeSettingController {

    private final MFAAuthorizeService mfaAuthorizeService;
    private final Oauth2AuthorizeService oauth2AuthorizeService;
    @Value("${mfa.type}")
    private String mfaType;

    //////////////////////////////////////////////////////MFA///////////////////////////////////////////////////////////
    @GetMapping("/{user_id}/authorize/mfa")
    @ApiImplicitParams({@ApiImplicitParam(name = "user_id", value = "用户ID", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "query_qr", value = "项目ID", defaultValue = "false", dataType = "String", paramType = "query")})
    @ApiOperation("查询用户开通MFA认证详情")
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    public ResultModel<MFADetailVO> get(@PathVariable("user_id") String userId,
                                        @RequestParam(value = "query_qr",defaultValue = "false", required = false) boolean queryQr) {
        return ResultModel.success(mfaAuthorizeService.get(userId, queryQr));
    }

    /**
     * 开通mfa\绑定令牌
     * @param userId
     * @param certificate
     * @return
     */
    @PutMapping("/{user_id}/authorize/mfa/open")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "user_id", value = "用户ID", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "login", value = "是否启动登录认证", dataType = "boolean", paramType = "query"),
            @ApiImplicitParam(name = "danger", value = "是否启动操作保护", dataType = "boolean", paramType = "query"),
    })
    @ApiOperation("开通MFA")
    @LogRecordAnnotation(value = "用户开通MFA", detail = "用户开通MFA:{{#_ret.resource}}", resourceId = "{{#userId}}", resource = "{USER{#userId}}")
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    public ResultModel<MfaResultVO> open(@PathVariable("user_id") String userId,
                                         @RequestParam(value ="certificate",required = false) String certificate,
                                         @RequestParam(value ="operateType",required = false) Integer operateType,
                                         @RequestParam(value ="login",required = false) boolean login,
                                         @RequestParam(value ="danger",required = false) boolean danger) {
        return ResultModel.success(mfaAuthorizeService.open(userId, certificate, operateType, login, danger));
    }

    @PutMapping("/{user_id}/authorize/mfa/close")
    @ApiImplicitParams({@ApiImplicitParam(name = "user_id", value = "用户ID", required = true, dataType = "String", paramType = "path")})
    @ApiOperation("关闭MFA")
    @LogRecordAnnotation(value = "用户关闭MFA", detail = "用户关闭MFA:{{#_ret.resource}}", resourceId = "{{#userId}}", resource = "{USER{#userId}}")
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    public ResultModel<String> close(@PathVariable("user_id") String userId) {
        return ResultModel.success(mfaAuthorizeService.close(userId, null));
    }
    /////////////////////////////////////////////////// OAUTH2.0 ///////////////////////////////////////////////////////
    @GetMapping("/authorize/oauth2/callback")
    @ApiImplicitParams({@ApiImplicitParam(name = "code", value = "code", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "user_id", value = "配置Oauth2的用户ID", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "redirect_uri", value = "重定向地址", required = true, dataType = "String", paramType = "query")})
    @ApiOperation("oauth2的callback")
    public ResultModel<OAuth2CallbackResponseVO> oauth2Callback(@RequestParam("code") String code
            , @RequestParam("user_id") String userId
            , @RequestParam("redirect_uri") String redirectUri) {
        // 生成一个token
        return ResultModel.success(oauth2AuthorizeService.callback(code, userId, redirectUri));
    }

    @PutMapping("/authorize/oauth2")
    @ApiOperation("oauth2修改")
    @LogRecordAnnotation(value = "oauth2认证修改", detail = "用户{USER{#userId}}修改oauth2", resourceId = "{{#userId}}", resource = "{USER{#userId}}")
    @DocumentIgnore
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    public ResultModel<String> oauth2Update(@RequestParam("user_id") String userId, @Valid @RequestBody OAuth2DetailVO oauth2DetailVO) {
        return ResultModel.success(oauth2AuthorizeService.oauth2Update(userId, oauth2DetailVO));
    }

    @GetMapping("/authorize/{user_id}/oauth2")
    @ApiOperation("oauth2查询")
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    public ResultModel<OAuth2DetailVO> oauth2Detail(@PathVariable("user_id") String userId) {
        return ResultModel.success(oauth2AuthorizeService.oauth2Detail(userId));
    }

    @GetMapping("/authorize/oauth2")
    @ApiOperation("oauth2查询列表")
    @DocumentIgnore
    public ResultModel<List<OAuth2ServerItem>> oauth2List() {
        return ResultModel.success(oauth2AuthorizeService.oauth2List());
    }

    @GetMapping("/authorize/mfa/type")
    @ApiOperation("查询开通MFA认证类型")
    @LogRecordAnnotation(value = "查询开通的认证管理类型", detail = "查询开通的认证管理类型")
    public ResultModel<String> getMfaType() {
        return ResultModel.success("执行成功",mfaType);
    }

    @PostMapping("bind/{user_id}/{serial_number}")
    @ApiOperation("绑定序列号")
    public ResultModel bindSerialNumber(@PathVariable("user_id") String userId, @PathVariable("serial_number") String serialNumber) {

        return ResultModel.success("");
    }


}
