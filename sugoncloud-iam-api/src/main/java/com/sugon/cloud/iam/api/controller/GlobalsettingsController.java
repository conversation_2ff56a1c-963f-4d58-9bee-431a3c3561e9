package com.sugon.cloud.iam.api.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.common.mapper.ModelMapper;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.common.utils.I18nUtil;
import com.sugon.cloud.iam.api.common.AESUtil;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.common.MD5Util;
import com.sugon.cloud.iam.api.common.MailValidUtil;
import com.sugon.cloud.iam.api.entity.GlobalEmailEntity;
import com.sugon.cloud.iam.api.entity.GlobalLockEntity;
import com.sugon.cloud.iam.api.entity.GlobalsettingsEntity;
import com.sugon.cloud.iam.api.enums.RsaKeySizeEnum;
import com.sugon.cloud.iam.api.service.GlobalsettingsService;
import com.sugon.cloud.iam.api.service.resourceauth.UserTypeExtentAuthHandler;
import com.sugon.cloud.iam.api.utils.CommonUtils;
import com.sugon.cloud.iam.api.utils.EncryptAndDecryptUtil;
import com.sugon.cloud.iam.api.vo.GlobalSettingsSmsVO;
import com.sugon.cloud.iam.common.model.vo.GlobalsettingsEntityVo;
import com.sugon.cloud.log.client.context.LogRecordContext;
import com.sugon.cloud.log.client.starter.annotation.LogRecordAnnotation;
import com.sugon.cloud.resource.auth.common.aop.ResourceAuth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/globalsettings")
@Api(tags = "Iam全局设置管理", description = "  ")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RefreshScope
public class GlobalsettingsController{
    Logger logger = LoggerFactory.getLogger(GlobalsettingsController.class);

    private final GlobalsettingsService globalsettingsService;
    private final ModelMapper mapper;
    private final EncryptAndDecryptUtil encryptAndDecryptUtil;
    @Autowired
    private RedisTemplate redisTemplate;

    @Value("${iam.phonePattern:^[1](([3][0-9])|([9][2])|([4][5-9])|([5][0-3,5-9])|([6][5,6])|([7][0-8])|([8][0-9])|([9][1,8,9]))[0-9]{8}$}")
    private String phonePattern;

    @GetMapping
    @ApiOperation("获取全局设置分页列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "page_num", value = "页码", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "page_size", value = "每页显示条数", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "policy_name", value = "全局设置名", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "policy_type", value = "全局设置类型", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "policy_display_name", value = "全局设置配置项", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "platform", value = "平台", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "type", value = "类型 env:环境参数, sys:系统参数, sec:安全参数", paramType = "query", dataType = "string")})
    @ResourceAuth(handler = UserTypeExtentAuthHandler.class, resources = TypeUtil.TYPE_SECURITY)
    @LogRecordAnnotation(value = "{{#value}}", detail = "{{#value}}")
    @DocumentIgnore
    public ResultModel<PageCL<GlobalsettingsEntityVo>> getAllGlobalsettingsByParam(@RequestParam(value = "page_num") Integer pageNum,
                                                                                   @RequestParam(value = "page_size") Integer pageSize,
                                                                                   @RequestParam(value = "policy_name", required = false) String policyName,
                                                                                   @RequestParam(value = "policy_type", required = false) String policyType,
                                                                                   @RequestParam(value = "policy_display_name", required = false) String policyDisplayName,
                                                                                   @RequestParam(value = "platform", required = false) String platform,
                                                                                   @RequestParam(value = "type", required = false) String type) {
        try {
            LambdaQueryWrapper<GlobalsettingsEntity> queryWrapper = new LambdaQueryWrapper<>();
            if (StrUtil.isNotBlank(type)) {
                queryWrapper.eq(GlobalsettingsEntity::getType, type);
            }
            if (!StringUtils.isEmpty(policyName)) {
                queryWrapper.like(GlobalsettingsEntity::getPolicyName, policyName);
            }
            if (!StringUtils.isEmpty(policyType)) {
                queryWrapper.like(GlobalsettingsEntity::getPolicyType, policyType);
            }
            if (!StringUtils.isEmpty(platform) && "cloudview".equals(platform)){
                queryWrapper.groupBy(GlobalsettingsEntity::getPolicyDisplayName);
            }
            List<GlobalsettingsEntity> globalsettingsEntityIPage = globalsettingsService.list(queryWrapper);
            if (!StringUtils.isEmpty(platform) && "cloudview".equals(platform)){
                for (GlobalsettingsEntity globalsettingsEntity : globalsettingsEntityIPage){
                    if (globalsettingsEntity.getPolicyDisplayName().equals("系统发件人邮箱服务器")){
                        String password = globalsettingsService.getGlobalsettingsEntity("email","password").getPolicyDocument();
                        String send = globalsettingsService.getGlobalsettingsEntity("email","send").getPolicyDocument();
                        String host = globalsettingsService.getGlobalsettingsEntity("email","host").getPolicyDocument();
                        String port = globalsettingsService.getGlobalsettingsEntity("email","port").getPolicyDocument();
                        globalsettingsEntity.setPolicyDocument(send+","+AESUtil.decrypt(password, MD5Util.string2MD5("0nCPbhvq"))+","+port+","+host);
                    }
                    if (globalsettingsEntity.getPolicyDisplayName().equals("用户登录密码错误配置")){
                        String errorMinute = globalsettingsService.getGlobalsettingsEntity("password","error_time_interval").getPolicyDocument();
                        String errorTime = globalsettingsService.getGlobalsettingsEntity("password","error_times").getPolicyDocument();
                        String lockMinute = globalsettingsService.getGlobalsettingsEntity("password","lock_minute").getPolicyDocument();
                        globalsettingsEntity.setPolicyDocument(errorMinute+","+errorTime+","+lockMinute);
                    }
                    if (globalsettingsEntity.getPolicyDisplayName().equals("系统短信发送服务器")){
                        String endpoint = globalsettingsService.getGlobalsettingsEntity("sms","endpoint").getPolicyDocument();
                        String projectId = globalsettingsService.getGlobalsettingsEntity("sms","project_id").getPolicyDocument();
                        globalsettingsEntity.setPolicyDocument(endpoint+","+projectId);
                        globalsettingsEntity.setRetainedField("短信发送服务器");
                    }
                }
            }
            List<GlobalsettingsEntityVo> list = mapper.mapList(globalsettingsEntityIPage,GlobalsettingsEntityVo.class);
            list.forEach(item -> {
                item.setRetainedField(I18nUtil.getMessage(item.getRetainedField()));
                item.setPolicyDisplayName(I18nUtil.getMessage(item.getPolicyDisplayName()));
            });
            // 做了国际化后，用代码进行过滤
            if (CollUtil.isNotEmpty(list) && StrUtil.isNotBlank(policyDisplayName)) {
                list = list.stream().filter(item -> item.getPolicyDisplayName().contains(policyDisplayName)).collect(Collectors.toList());
            }
            PageCL<GlobalsettingsEntityVo> pageCL = new PageCL();
            pageCL = pageCL.getPageInfo(pageNum, pageSize, list);
            if("env".equals(type)) {
                LogRecordContext.putVariable("value", "查询环境参数列表");
            } else if ("sys".equals(type)) {
                LogRecordContext.putVariable("value", "查询系统参数列表");
            } else if ("sec".equals(type)) {
                LogRecordContext.putVariable("value", "查询安全参数列表");
            } else {

            }

            return ResultModel.success("获取全局设置分页列表成功",pageCL);
        } catch (Exception e) {
            logger.error("getAllGlobalsettingsByParam error", e);
            if (CommonUtils.isContainChinese(e.getMessage())) {
                return ResultModel.error(e.getMessage());
            } else {
                return ResultModel.error("获取全局设置分页列表失败");
            }
        }
    }
    @GetMapping("/list")
    @ApiOperation("获取全局设置列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "policy_name", value = "全局设置名", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "policy_type", value = "全局设置类型", paramType = "query", dataType = "string")})
    @ResourceAuth(handler = UserTypeExtentAuthHandler.class, resources = TypeUtil.TYPE_SECURITY)
    @DocumentIgnore
    public ResultModel<List<GlobalsettingsEntity>> getAllGlobalsettingsListByParam(@RequestParam(value = "policy_name", required = false) String policyName,
                                                       @RequestParam(value = "policy_type", required = false) String policyType) {
        ResultModel resultModel = new ResultModel();
        try {
            LambdaQueryWrapper<GlobalsettingsEntity> queryWrapper = new LambdaQueryWrapper<GlobalsettingsEntity>();
            if (!StringUtils.isEmpty(policyName)) {
                queryWrapper.like(GlobalsettingsEntity::getPolicyName, policyName);
            }
            if (!StringUtils.isEmpty(policyType)) {
                queryWrapper.like(GlobalsettingsEntity::getPolicyType, policyType);
            }
            List<GlobalsettingsEntity> globalsettingsEntityList = globalsettingsService.list( queryWrapper);
            globalsettingsEntityList.forEach(item -> {
                item.setRetainedField(I18nUtil.getMessage(item.getRetainedField()));
                item.setPolicyDisplayName(I18nUtil.getMessage(item.getPolicyDisplayName()));
            });
            resultModel = ResultModel.success("获取全局设置列表成功",globalsettingsEntityList);
        } catch (Exception e) {
            logger.error("getAllGlobalsettingsListByParam error", e);
            if (CommonUtils.isContainChinese(e.getMessage())) {
                resultModel = ResultModel.error(e.getMessage());
            } else {
                resultModel = ResultModel.error("获取全局设置列表失败");
            }
        }
        return resultModel;
    }

    @PostMapping
    @ApiOperation("创建全局设置")
    @LogRecordAnnotation(value = "创建全局设置", detail = "创建全局设置{{#globalsettingsEntity.policyDisplayName}}", resourceId = "{{#globalsettingsEntity.uuid}}", resource = "{{#globalsettingsEntity.policyDisplayName}}")
    @ResourceAuth(handler = UserTypeExtentAuthHandler.class, resources = TypeUtil.TYPE_SECURITY)
    @DocumentIgnore
    public ResultModel<GlobalsettingsEntity> createGlobalsettings(@RequestBody GlobalsettingsEntity globalsettingsEntity) {
        ResultModel resultModel = new ResultModel();
        try {
            LambdaQueryWrapper<GlobalsettingsEntity> queryWrapper = new LambdaQueryWrapper<GlobalsettingsEntity>();
            queryWrapper.eq(GlobalsettingsEntity::getPolicyName, globalsettingsEntity.getPolicyName()).eq(GlobalsettingsEntity::getPolicyType, globalsettingsEntity.getPolicyType());
            GlobalsettingsEntity ramPolicyEntity1 = globalsettingsService.getOne(queryWrapper);
            if (null != ramPolicyEntity1) {
                resultModel = ResultModel.error("全局设置当前类型全局名称名已存在，创建全局设置失败");
                return resultModel;
            }
            globalsettingsService.save(globalsettingsEntity);
            resultModel = ResultModel.success("创建全局设置成功",globalsettingsEntity);
        } catch (Exception e) {
            logger.error("createGlobalsettings error", e);
            if (CommonUtils.isContainChinese(e.getMessage())) {
                resultModel = ResultModel.error(e.getMessage());
            } else {
                resultModel = ResultModel.error("创建全局设置失败");
            }
        }
        return resultModel;
    }


    @GetMapping("/info")
    @ApiOperation("查询全局设置")
    public ResultModel<GlobalsettingsEntity> getGlobalsettingsByname(@RequestParam(value = "policy_type") String type,
                                               @RequestParam(value = "policy_key") String policyKey,
                                                                     HttpServletRequest request) {
        ResultModel resultModel;
        try {
            if("user".equals(type) && "phone".equals(policyKey)){
                GlobalsettingsEntity globalsettingsEntity = new GlobalsettingsEntity();
                globalsettingsEntity.setPolicyDocument(phonePattern);
                globalsettingsEntity.setPolicyType(type);
                globalsettingsEntity.setPolicyName(policyKey);
                return ResultModel.success("查询全局设置成功",globalsettingsEntity);
            }
            String internetAccess = request.getHeader("InternetAccess");
            boolean isInternet = false;
            if (Objects.nonNull(internetAccess)) {
                isInternet = Boolean.parseBoolean(internetAccess);
            }
            if (CommonInstance.REAL_IP.equals(type) && CommonInstance.REAL_IP.equals(policyKey)) {
                if (isInternet) {
                    policyKey += CommonInstance.REAL_IP_SUFFIX;
                }
            }
            if (CommonInstance.VNC_IP.equals(policyKey) && isInternet && CommonInstance.VNC_IP.equals(type)) {
                type = CommonInstance.VNC_PUBLIC_IP;
            }
            if (CommonInstance.CCE_WS.equals(policyKey) && isInternet && CommonInstance.CCE_WS.equals(type)) {
                type = CommonInstance.CCE_PUBLIC_WS;
            }
            QueryWrapper<GlobalsettingsEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("policy_name", policyKey)
                    .eq("policy_type", type);

            GlobalsettingsEntity globalsettingsEntity = globalsettingsService.getOne(queryWrapper);
            if (Objects.nonNull(globalsettingsEntity)) {
                globalsettingsEntity.setRetainedField(I18nUtil.getMessage(globalsettingsEntity.getRetainedField()));
                globalsettingsEntity.setPolicyDisplayName(I18nUtil.getMessage(globalsettingsEntity.getPolicyDisplayName()));
            }
            resultModel = ResultModel.success("查询全局设置成功",globalsettingsEntity);
        } catch (Exception e) {
            logger.error("getGlobalsettingsByname error", e);
            if (CommonUtils.isContainChinese(e.getMessage())) {
                resultModel = ResultModel.error(e.getMessage());
            } else {
                resultModel = ResultModel.error("查询全局设置失败");
            }
        }
        return resultModel;
    }

    @GetMapping("/{id}")
    @ApiOperation("查询全局设置")
    @DocumentIgnore
    public ResultModel<GlobalsettingsEntity> getGlobalsettingsById(@PathVariable("id") String id) {
        ResultModel resultModel = new ResultModel();
        try {
            GlobalsettingsEntity globalsettingsEntity = globalsettingsService.getById(id);
            globalsettingsEntity.setRetainedField(I18nUtil.getMessage(globalsettingsEntity.getRetainedField()));
            globalsettingsEntity.setPolicyDisplayName(I18nUtil.getMessage(globalsettingsEntity.getPolicyDisplayName()));
            resultModel = ResultModel.success("查询全局设置成功",globalsettingsEntity);
        } catch (Exception e) {
            logger.error("getGlobalsettingsById error", e);
            if (CommonUtils.isContainChinese(e.getMessage())) {
                resultModel = ResultModel.error(e.getMessage());
            } else {
                resultModel = ResultModel.error("查询全局设置失败");
            }
        }
        return resultModel;
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除全局设置")
    @LogRecordAnnotation(value = "删除全局设置", detail = "删除全局设置{{#name}}", resourceId = "{{#id}}", resource = "{{#name}}")
    @ResourceAuth(handler = UserTypeExtentAuthHandler.class, resources = TypeUtil.TYPE_SECURITY)
    @DocumentIgnore
    public ResultModel<GlobalsettingsEntity> deleteGlobalsettings(@PathVariable("id") String id) {
        ResultModel resultModel = new ResultModel();
        try {
            GlobalsettingsEntity globalsettingsEntity = globalsettingsService.getById(id);
            if (Objects.nonNull(globalsettingsEntity)){
                if (CommonInstance.ONE_PLACE_LOGIN.equals(globalsettingsEntity.getPolicyName()) &&
                        CommonInstance.POLICY_TYPE.equals(globalsettingsEntity.getPolicyType())) {
                    redisTemplate.delete(CommonInstance.ONE_PLACE_LOGIN);
                }
                resultModel.setResource(globalsettingsEntity.getPolicyDisplayName());
                LogRecordContext.putVariable("name", globalsettingsEntity.getPolicyDisplayName());
            }
            globalsettingsService.removeById(id);
            resultModel = ResultModel.success("删除全局设置成功",globalsettingsEntity);
        } catch (Exception e) {
            logger.error("deleteGlobalsettings error", e);
            if (CommonUtils.isContainChinese(e.getMessage())) {
                resultModel = ResultModel.error(e.getMessage());
            } else {
                resultModel = ResultModel.error("删除全局设置失败");
            }
        }
        return resultModel;
    }

    @PutMapping("/email")
    @ApiOperation("修改全局设置-邮件")
    @ResourceAuth(handler = UserTypeExtentAuthHandler.class, resources = TypeUtil.TYPE_SECURITY)
    @DocumentIgnore
    public ResultModel<String> editEmailSettings(@RequestBody GlobalEmailEntity globalEmailEntity){
        ResultModel resultModel = new ResultModel();
        try{
            if (!StringUtils.isEmpty(globalEmailEntity.getPassword())){
                GlobalsettingsEntity passwordSetting = globalsettingsService.getGlobalsettingsEntity("email","password");
                if (!globalEmailEntity.getPassword().equals(passwordSetting.getPolicyDocument())){
                    passwordSetting.setPolicyDocument(AESUtil.encrypt(globalEmailEntity.getPassword(), MD5Util.string2MD5("0nCPbhvq")));
                }else {
                    globalEmailEntity.setPassword(AESUtil.decrypt(globalEmailEntity.getPassword(), MD5Util.string2MD5("0nCPbhvq")));
                }
                globalsettingsService.updateById(passwordSetting);
            }
            if (!StringUtils.isEmpty(globalEmailEntity.getHost())){
                GlobalsettingsEntity hostSetting = globalsettingsService.getGlobalsettingsEntity("email","host");
                hostSetting.setPolicyDocument(globalEmailEntity.getHost());
                globalsettingsService.updateById(hostSetting);
            }
            if (!StringUtils.isEmpty(globalEmailEntity.getSend())){
                GlobalsettingsEntity sendSetting = globalsettingsService.getGlobalsettingsEntity("email","send");
                sendSetting.setPolicyDocument(globalEmailEntity.getSend());
                globalsettingsService.updateById(sendSetting);
            }
            if (!StringUtils.isEmpty(globalEmailEntity.getPort())){
                GlobalsettingsEntity portSetting = globalsettingsService.getGlobalsettingsEntity("email","port");
                portSetting.setPolicyDocument(globalEmailEntity.getPort());
                globalsettingsService.updateById(portSetting);
            }
            List<GlobalsettingsEntity> globalsettings = globalsettingsService.list();
            JSONObject globalSettings = new JSONObject();
            for (GlobalsettingsEntity setting:globalsettings) {
                globalSettings.put(setting.getPolicyType()+setting.getPolicyName(), setting.getPolicyDocument());
            }
            resultModel = ResultModel.success("修改邮件全局设置成功");
        }catch (Exception e){
            logger.error("editEmailSettings error", e);
            if (CommonUtils.isContainChinese(e.getMessage())) {
                resultModel = ResultModel.error(e.getMessage());
            } else {
                resultModel = ResultModel.error("修改邮件全局设置失败");
            }
        }
        return resultModel;
    }

    @PostMapping("/check")
    @ApiOperation("email连接测试")
    @ResourceAuth(handler = UserTypeExtentAuthHandler.class, resources = TypeUtil.TYPE_SECURITY)
    @DocumentIgnore
    public ResultModel<String> checkEmail(@RequestBody GlobalEmailEntity globalEmailEntity){
        ResultModel resultModel = new ResultModel();
        try{
            GlobalsettingsEntity passwordSetting = globalsettingsService.getGlobalsettingsEntity("email","password");
            if (!globalEmailEntity.getPassword().equals(passwordSetting.getPolicyDocument())){
                passwordSetting.setPolicyDocument(AESUtil.encrypt(globalEmailEntity.getPassword(), MD5Util.string2MD5("0nCPbhvq")));
            }else {
                globalEmailEntity.setPassword(AESUtil.decrypt(globalEmailEntity.getPassword(), MD5Util.string2MD5("0nCPbhvq")));
            }
            String mailInfo= MailValidUtil.validSendMail(globalEmailEntity.getHost(),globalEmailEntity.getSend(),globalEmailEntity.getPassword(),globalEmailEntity.getPort());
            if (!StringUtils.isEmpty(mailInfo)){
                resultModel = ResultModel.error("连接失败，原因："+mailInfo);
            }else {
                resultModel = ResultModel.success("连接成功");
            }
        }catch (Exception e){
            logger.error("checkEmail error", e);
            if (CommonUtils.isContainChinese(e.getMessage())) {
                resultModel = ResultModel.error(e.getMessage());
            } else {
                resultModel = ResultModel.error("email连接测试失败");
            }
        }
        return resultModel;
    }

    @PutMapping("/lock")
    @ApiOperation("修改lock的全局设置")
    @ResourceAuth(handler = UserTypeExtentAuthHandler.class, resources = TypeUtil.TYPE_SECURITY)
    @DocumentIgnore
    public ResultModel<String> editLockSettings(@RequestBody GlobalLockEntity globalLockEntity){
        ResultModel resultModel = new ResultModel();
        try{
            if (!StringUtils.isEmpty(globalLockEntity.getErrorMinutes())){
                GlobalsettingsEntity hostSetting = globalsettingsService.getGlobalsettingsEntity("password","error_time_interval");
                hostSetting.setPolicyDocument(globalLockEntity.getErrorMinutes());
                globalsettingsService.updateById(hostSetting);
            }
            if (!StringUtils.isEmpty(globalLockEntity.getErrorTimes())){
                GlobalsettingsEntity sendSetting = globalsettingsService.getGlobalsettingsEntity("password","error_times");
                sendSetting.setPolicyDocument(globalLockEntity.getErrorTimes());
                globalsettingsService.updateById(sendSetting);
            }
            if (!StringUtils.isEmpty(globalLockEntity.getLockMinutes())){
                GlobalsettingsEntity portSetting = globalsettingsService.getGlobalsettingsEntity("password","lock_minute");
                portSetting.setPolicyDocument(globalLockEntity.getLockMinutes());
                globalsettingsService.updateById(portSetting);
            }
            List<GlobalsettingsEntity> globalsettings = globalsettingsService.list();
            JSONObject globalSettings = new JSONObject();
            for (GlobalsettingsEntity setting:globalsettings) {
                globalSettings.put(setting.getPolicyType()+setting.getPolicyName(), setting.getPolicyDocument());
            }
            resultModel = ResultModel.success("修改lock的全局设置成功");
        }catch (Exception e){
            logger.error("editLockSettings error", e);
            if (CommonUtils.isContainChinese(e.getMessage())) {
                resultModel = ResultModel.error(e.getMessage());
            } else {
                resultModel = ResultModel.error("修改lock的全局设置失败");
            }
        }
        return resultModel;
    }

    @PutMapping("/{id}")
    @ApiOperation("修改全局设置")
    @LogRecordAnnotation(value = "修改全局设置", detail = "修改全局设置{{#ramPolicyEntity.policyDisplayName}}", resourceId = "{{#id}}", resource = "{{#ramPolicyEntity.policyDisplayName}}")
    @ResourceAuth(handler = UserTypeExtentAuthHandler.class, resources = TypeUtil.TYPE_SECURITY)
    @DocumentIgnore
    public ResultModel<String> editGlobalsettings(@PathVariable("id") String id, @RequestBody GlobalsettingsEntity ramPolicyEntity) {
        ResultModel resultModel = new ResultModel();
        try {
            GlobalsettingsEntity ramPolicy = globalsettingsService.getById(id);
            if (Objects.nonNull(ramPolicy)) {
                if (CommonInstance.ONE_PLACE_LOGIN.equals(ramPolicy.getPolicyName()) &&
                        CommonInstance.POLICY_TYPE.equals(ramPolicy.getPolicyType())) {
                    redisTemplate.delete(CommonInstance.ONE_PLACE_LOGIN);
                } else if (CommonInstance.TOKEN_EXPIRED_KEY.equals(ramPolicy.getPolicyName()) &&
                        CommonInstance.TOKEN.equals(ramPolicy.getPolicyType())) {
                    redisTemplate.delete(CommonInstance.TOKEN_EXPIRED_KEY);
                }
            }
            //bug 3655 start
            if (CommonInstance.CLOBAL_SETTING_PASSWORD_ERROR_TIME_INTERVAL.equals(ramPolicyEntity.getPolicyName())
                && CommonInstance.CLOBAL_SETTING_PASSWORD.equals(ramPolicyEntity.getPolicyType())) {
                String policyDocument = ramPolicyEntity.getPolicyDocument();
                ramPolicyEntity.setPolicyDocument(policyDocument.split(",")[0]);
                int errorTimes = Integer.parseInt(policyDocument.split(",")[1]);
                int lockMinute = Integer.parseInt(policyDocument.split(",")[2]);
                globalsettingsService.update(new LambdaUpdateWrapper<GlobalsettingsEntity>()
                        .set(GlobalsettingsEntity::getPolicyDocument, errorTimes)
                        .eq(GlobalsettingsEntity::getPolicyName,CommonInstance.CLOBAL_SETTING_PASSWORD_ERROR_TIMES)
                        .eq(GlobalsettingsEntity::getPolicyType,CommonInstance.CLOBAL_SETTING_PASSWORD));
                globalsettingsService.update(new LambdaUpdateWrapper<GlobalsettingsEntity>()
                        .set(GlobalsettingsEntity::getPolicyDocument, lockMinute)
                        .eq(GlobalsettingsEntity::getPolicyName,CommonInstance.CLOBAL_SETTING_PASSWORD_LOCK_MINUTE)
                        .eq(GlobalsettingsEntity::getPolicyType,CommonInstance.CLOBAL_SETTING_PASSWORD));
            }
            //bug 3655 end
            ramPolicyEntity.setPolicyDisplayName(ramPolicy.getPolicyDisplayName());
            ramPolicyEntity.setRetainedField(ramPolicy.getRetainedField());
            ramPolicyEntity.setUuid(id);
            resultModel.setResource(ramPolicyEntity.getPolicyDisplayName());
            if("email".equals(ramPolicyEntity.getPolicyType())&&"password".equals(ramPolicyEntity.getPolicyName())){  //授权码加密
                ramPolicyEntity.setPolicyDocument(AESUtil.encrypt(ramPolicyEntity.getPolicyDocument(), MD5Util.string2MD5("0nCPbhvq")));
            }
            globalsettingsService.updateById(ramPolicyEntity);
            resultModel.setContent(id);
            resultModel.setStatusMes("修改全局设置成功");
            List<GlobalsettingsEntity> globalsettings = globalsettingsService.list();
            JSONObject globalSettings = new JSONObject();
            for (GlobalsettingsEntity setting:globalsettings) {
                globalSettings.put(setting.getPolicyType()+setting.getPolicyName(), setting.getPolicyDocument());
            }
            ResultModel.success("修改全局设置",id);
        } catch (Exception e) {
            logger.error("editGlobalsettings error", e);
            if (CommonUtils.isContainChinese(e.getMessage())) {
                resultModel = ResultModel.error(e.getMessage());
            } else {
                resultModel = ResultModel.error("修改全局设置失败");
            }
        }
        return resultModel;
    }

    @GetMapping("/publickey")
    @ApiOperation("查询密码公钥")
    public ResultModel<String> getPublicKey(@RequestParam(value = "use_security", required = false) String useSecurity) {
        ResultModel resultModel = new ResultModel();
        try {
            String publicKey = encryptAndDecryptUtil.getPublicKey(useSecurity, RsaKeySizeEnum.RSA_1024);
            resultModel = ResultModel.success("查询密码公钥成功",publicKey);
        } catch (Exception e) {
            logger.error("getPublicKey error", e);
            if (CommonUtils.isContainChinese(e.getMessage())) {
                resultModel = ResultModel.error(e.getMessage());
            } else {
                resultModel = ResultModel.error("查询密码公钥成功失败");
            }
        }
        return resultModel;
    }

    @GetMapping("/publickey-iam")
    @ApiOperation("查询密码公钥-多区域环境使用")
    public ResultModel<String> getPublicKeyIam(@RequestParam(value = "use_security", required = false) String useSecurity) {
        ResultModel resultModel = new ResultModel();
        try {
            String publicKey = encryptAndDecryptUtil.getPublicKey(useSecurity, RsaKeySizeEnum.RSA_2048);
            resultModel = ResultModel.success("查询密码公钥成功",publicKey);
        } catch (Exception e) {
            logger.error("getPublicKey error", e);
            if (CommonUtils.isContainChinese(e.getMessage())) {
                resultModel = ResultModel.error(e.getMessage());
            } else {
                resultModel = ResultModel.error("查询密码公钥成功失败");
            }
        }
        return resultModel;
    }

    @PutMapping("/sms")
    @ApiOperation("修改全局设置-短信")
    @ResourceAuth(handler = UserTypeExtentAuthHandler.class, resources = TypeUtil.TYPE_SECURITY)
    @DocumentIgnore
    public ResultModel<String> editSmsSettings(@RequestBody GlobalSettingsSmsVO globalSettingsSmsVO){
        ResultModel<String> resultModel;
        try{
            List<GlobalsettingsEntity> smsSettings = globalsettingsService.list(new LambdaQueryWrapper<GlobalsettingsEntity>()
                    .eq(GlobalsettingsEntity::getPolicyType, "sms"));
            for (GlobalsettingsEntity smsSetting : smsSettings) {
                if ("endpoint".equals(smsSetting.getPolicyName())) {
                    smsSetting.setPolicyDocument(globalSettingsSmsVO.getEndpoint());
                } else if ("project_id".equals(smsSetting.getPolicyName())) {
                    smsSetting.setPolicyDocument(globalSettingsSmsVO.getProjectId());
                }
            }
            globalsettingsService.updateBatchById(smsSettings);
            resultModel = ResultModel.success("修改短信全局设置成功");
        }catch (Exception e){
            logger.error("editSmsSettings error", e);
            if (CommonUtils.isContainChinese(e.getMessage())) {
                resultModel = ResultModel.error(e.getMessage());
            } else {
                resultModel = ResultModel.error("修改短信全局设置失败");
            }
        }
        return resultModel;
    }

}
