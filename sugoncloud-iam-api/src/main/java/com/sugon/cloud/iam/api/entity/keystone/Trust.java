package com.sugon.cloud.iam.api.entity.keystone;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "trust")
public class Trust {
    @TableId(type = IdType.UUID)
    private String id;
    @JsonProperty(value = "trustor_user_id")
    private String trustorUserId;
    @JsonProperty(value = "trustee_user_id")
    private String trusteeUserId;
    @JsonProperty(value = "project_id")
    private String projectId;
    private boolean impersonation;
    @JsonProperty(value = "deleted_at")
    private Date deletedAt;
    @JsonProperty(value = "expires_at")
    private Date expiresAt;
    @JsonProperty(value = "remaining_uses")
    private int remainingUses;
    private String extra;
    @JsonProperty(value = "expires_at_int")
    private long expiresAtInt;
}
