package com.sugon.cloud.iam.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sugon.cloud.iam.api.entity.UserAccessEntity;

public interface UserAccessService extends IService<UserAccessEntity> {
    /**
     * 根据用户id获取用户相关访问权限
     * @param userId
     * @return
     */
    UserAccessEntity getAccessByUserId(String userId);

    void updateAccessById(UserAccessEntity userAccessEntity);
}
