package com.sugon.cloud.iam.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.service.UserService;
import com.sugon.cloud.log.client.service.MessageFeignService;
import com.sugon.cloud.log.common.model.vo.MessageVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @Author: yangdingshan
 * @Date: 2024/10/31 16:28
 * @Description:
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Log4j2
@RefreshScope
public class MessageSendService {

    @Value("${iam.securityAdminUserId:003f67adc2d4465da6e2f716a621307a}")
    private String securityAdminUserId;

    @Value("${iam.inner.userId:0d2bbb018e8b44b985a169647379f413}")
    private String adminUserId;

    private final MessageFeignService messageFeignService;

    private final UserService userService;

    @Value("#{'${send.message.userType:security,admin,audit}'.split(',')}")
    private List<String> needSendMessageUserType;

    /**
     * 同时给超级管理员和安全管理员发送消息
     *
     * @param content
     */
    public void sendAdminAndSecurityMessage(String content) {
        try {
            MessageVo messageVo = new MessageVo();
            messageVo.setUser(securityAdminUserId);
            messageVo.setSvc("sugoncloud-iam-api");
            messageVo.setContent(content);
            messageVo.setEmailEnable(true);
            messageFeignService.createMessage(messageVo);
            messageVo.setUser(adminUserId);
            messageFeignService.createMessage(messageVo);
        } catch (Exception e) {
            log.error("发送消息失败", e);
        }
    }

    public void sendMessageByUserName(String userName, String content) {
        User user = userService.getOne(new LambdaQueryWrapper<User>().eq(User::getName, userName));
        if (Objects.nonNull(user) && needSendMessageUserType.contains(user.getType())) {
            this.sendAdminAndSecurityMessage(content);
        }
    }

}
