package com.sugon.cloud.iam.api.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sugon.cloud.common.annotations.DocumentIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author: yangdingshan
 * @Date: 2024/6/19 17:23
 * @Description:
 */
@Data
@ApiModel("合同列表项")
public class ProjectContractVO {

    @JsonProperty("id")
    @ApiModelProperty("主键")
    private String id;
    @JsonProperty("contract_code")
    @ApiModelProperty("合同编号")
    private String contractCode;
    @JsonProperty("name")
    @ApiModelProperty("云服务合同名称")
    private String name;
    @JsonProperty("start_time")
    @ApiModelProperty("合同生效时间")
    private Date startTime;
    @JsonProperty("end_time")
    @ApiModelProperty("合同失效时间")
    private Date endTime;
    @JsonProperty("contract_time")
    @ApiModelProperty("合同的签订时间")
    private Date contractTime;
    @JsonProperty("create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;
    @JsonProperty("create_user")
    @ApiModelProperty("创建人")
    private String createUser;
    @JsonProperty("create_user_name")
    @ApiModelProperty("创建人名称")
    private String createUserName;
    @JsonProperty("file")
    @ApiModelProperty("附件")
    @DocumentIgnore
    private String file;
    @JsonProperty("description")
    @ApiModelProperty("简介")
    private String description;
    @JsonProperty("status")
    @ApiModelProperty("1 有效  0 删除")
    private Integer status;
    @JsonProperty("contract_type")
    @ApiModelProperty("合同类型")
    private String contractType;
    @JsonProperty("contract_type_name")
    @ApiModelProperty("合同类型名称")
    private String contractTypeName;
    @JsonIgnore
    private String pid;
    @JsonProperty("org_id")
    @ApiModelProperty("组织id")
    private String orgId;
    @JsonProperty("org_name")
    @ApiModelProperty("组织名称")
    private String orgName;
    @JsonProperty("payment_type")
    @ApiModelProperty("回款类型 （按年：contract_payment_year,按季度：contract_payment_quarter,按月：contract_payment_month）")
    private String paymentType;
    @JsonProperty("payment_type_name")
    @ApiModelProperty("回款类型名称 （按年：contract_payment_year,按季度：contract_payment_quarter,按月：contract_payment_month）")
    private String paymentTypeName;
    @JsonProperty("update_time")
    @ApiModelProperty("更新时间")
    private Date updateTime;
    @JsonProperty("update_user")
    @ApiModelProperty("更新用户")
    private String updateUser;
    @JsonProperty("update_user_name")
    @ApiModelProperty("更新用户名称")
    private String updateUserName;
    @JsonProperty("sale_user")
    @ApiModelProperty(
            value = "销售人员",
            hidden = true
    )
    private String saleUser;
    @JsonProperty("sale_name")
    @ApiModelProperty(
            value = "销售人员名称",
            hidden = true
    )
    private String saleName;
    @JsonProperty("amount")
    @ApiModelProperty("合同金额")
    private BigDecimal amount;
    @JsonProperty("meter_type")
    @ApiModelProperty("计费类型（统一计费：contract_meter_unification , 合同计费：contract_meter_contract）")
    @DocumentIgnore
    private String meterType;
    @JsonProperty("meter_type_name")
    @ApiModelProperty("计费类型名称（统一计费：contract_meter_unification , 合同计费：contract_meter_contract）")
    private String meterTypeName;
    @ApiModelProperty("父合同")
    private ProjectContractVO parent;
    @ApiModelProperty("合同协议集合")
    private List<ProjectContractAgreementVO> agreements;
    @ApiModelProperty("附件集合")
    private List<AttachmentInfoVO> attachments;

    @Data
    @ApiModel("合同协议项")
    public static class ProjectContractAgreementVO {
        @JsonProperty("id")
        @ApiModelProperty("主键")
        private String id;
        @JsonProperty("contract_id")
        @ApiModelProperty("合同ID")
        private String contractId;
        @JsonProperty("name")
        @ApiModelProperty("名称")
        private String name;
        @JsonProperty("agreement_type")
        @ApiModelProperty("协议类型")
        private String agreementType;
        @JsonProperty("agreement_type_name")
        private String agreementTypeName;
        @JsonProperty("amount")
        @ApiModelProperty("合同金额")
        private BigDecimal amount;
        @JsonProperty("start_time")
        @ApiModelProperty("合同生效时间")
        @JsonFormat(
                pattern = "yyyy-MM-dd"
        )
        @JSONField(
                format = "yyyy-MM-dd"
        )
        private Date startTime;
        @JsonProperty("end_time")
        @ApiModelProperty("合同失效时间")
        @JsonFormat(
                pattern = "yyyy-MM-dd"
        )
        @JSONField(
                format = "yyyy-MM-dd"
        )
        private Date endTime;
        @ApiModelProperty("客户单位")
        private String customer;
        @JsonProperty("contract_time")
        @JsonFormat(
                pattern = "yyyy-MM-dd"
        )
        @JSONField(
                format = "yyyy-MM-dd"
        )
        @ApiModelProperty("合同签订日期")
        private Date contractTime;
        @JsonProperty("payment_type")
        @ApiModelProperty("回款类型 （按年：contract_payment_year,按季度：contract_payment_quarter,按月：contract_payment_month）")
        private String paymentType;
        @JsonProperty("payment_type_name")
        @ApiModelProperty("回款类型名称 （按年：contract_payment_year,按季度：contract_payment_quarter,按月：contract_payment_month）")
        private String paymentTypeName;
        @JsonProperty("file")
        @ApiModelProperty("附件")
        @DocumentIgnore
        private String file;
        @JsonProperty("create_time")
        @ApiModelProperty("创建时间")
        private Date createTime;
        @JsonProperty("create_user")
        @ApiModelProperty("创建人")
        private String createUser;
        @JsonProperty("create_user_name")
        @ApiModelProperty("创建人名称")
        private String createUserName;
        @JsonProperty("sale_user")
        @ApiModelProperty(
                value = "销售人员",
                hidden = true
        )
        private String saleUser;
        @JsonProperty("sale_name")
        @ApiModelProperty(
                value = "销售人员名称",
                hidden = true
        )
        private String saleName;
        @JsonProperty("update_time")
        @ApiModelProperty("更新时间")
        private Date updateTime;
        @JsonProperty("update_user")
        @ApiModelProperty("更新用户")
        private String updateUser;
        @JsonProperty("update_user_name")
        @ApiModelProperty("更新用户名称")
        private String updateUserName;
        @JsonProperty("org_id")
        @ApiModelProperty("组织id")
        private String orgId;
        @JsonProperty("org_name")
        @ApiModelProperty("组织名称")
        private String orgName;
        @ApiModelProperty("附件集合")
        private List<AttachmentInfoVO> attachments;
    }

    @Data
    @ApiModel("附件信息")
    public static class AttachmentInfoVO {
        @ApiModelProperty("附件ID")
        private String id;
        @ApiModelProperty("附件名称")
        private String name;
        @ApiModelProperty("附件大小")
        private Long size;
        @ApiModelProperty("创建时间")
        private Date createDate;
    }
}
