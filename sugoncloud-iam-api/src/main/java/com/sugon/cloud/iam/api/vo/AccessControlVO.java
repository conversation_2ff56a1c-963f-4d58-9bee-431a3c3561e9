package com.sugon.cloud.iam.api.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-04-19 14:10
 */
@Data
public class AccessControlVO implements Serializable {
    /**
     * ip白名单
     */
    @ApiModelProperty("ip白名单")
    private String ip;

    /**
     * 开始日期
     */
    @ApiModelProperty("开始日期")
    private String startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty("结束日期")
    private String endDate;

    /**
     * 是否开启时间限制
     */
    @ApiModelProperty("是否开启时间限制")
    private Boolean limitFlag;

    /**
     * 限制时间
     */
    @ApiModelProperty("限制时间")
    private String limitTime;
}
