package com.sugon.cloud.iam.api.config;

import cn.com.infosec.netpass.managementapi.ManagementAPI;
import cn.com.infosec.netpass.managementapi.ManagementAPIException;
import cn.com.infosec.netpass.authapi.CardAuthManager;
import cn.com.infosec.netpass.managementapi.SysProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;

@Slf4j
@RefreshScope
@Data
public class InfoSecSignConfig {

    @Value("${mfa.infoSec.host:127.0.0.1}")
    private String host;
    @Value("${mfa.infoSec.port:80}")
    private Integer port;
    @Value("${mfa.infoSec.minConnection:10}")
    private Integer minConnection;
    @Value("${mfa.infoSec.maxConnection:20}")
    private Integer maxConnection;
    @Value("${mfa.infoSec.timeout:5}")
    private Integer timeout;
    @Value("${mfa.infoSec.user:admin}")
    private String user;
    @Value("${mfa.infoSec.password:admin}")
    private String password;


    public void init() {
        log.info("init InfoSec sign api config.");
        try{
            SysProperty sysProperty = new SysProperty();
            sysProperty.setServerIP(host);
            sysProperty.setPort(port);
            sysProperty.setMinConn(minConnection);
            sysProperty.setMaxConn(maxConnection);
            sysProperty.setTimeout(timeout);
            sysProperty.setUser(user);
            sysProperty.setPassword(password);
            ManagementAPI.setAPIProperty(sysProperty);

            cn.com.infosec.netpass.authapi.SysProperty cardProperty = new cn.com.infosec.netpass.authapi.SysProperty();
            BeanUtils.copyProperties(sysProperty, cardProperty);
            CardAuthManager.setAPIProperty(cardProperty);
        }catch (ManagementAPIException e) {
            log.error("初始化信安世纪连接业务异常", e);
        } catch (Exception e2) {
            log.error("初始化信安世纪连接异常", e2);
        }
        log.info("connect InfoSec sign device success.");
    }

    public void close() {
        log.info("InfoSec sign close complete.");
    }

}
