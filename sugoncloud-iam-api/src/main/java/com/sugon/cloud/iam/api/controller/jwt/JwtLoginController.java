package com.sugon.cloud.iam.api.controller.jwt;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.common.utils.IPUtil;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.common.SecretCertificate;
import com.sugon.cloud.iam.api.entity.GlobalsettingsEntity;
import com.sugon.cloud.iam.api.entity.exception.IamLoginException;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.exception.BusinessException;
import com.sugon.cloud.iam.api.service.GlobalsettingsService;
import com.sugon.cloud.iam.api.service.JwtService;
import com.sugon.cloud.iam.api.service.MFAAuthorizeService;
import com.sugon.cloud.iam.api.service.UkeyService;
import com.sugon.cloud.iam.api.service.UserService;
import com.sugon.cloud.iam.api.service.strategy.StrategyFactory;
import com.sugon.cloud.iam.api.utils.CommonUtils;
import com.sugon.cloud.iam.api.utils.EncryptAndDecryptUtil;
import com.sugon.cloud.iam.api.utils.JacksonUtils;
import com.sugon.cloud.iam.api.vo.MFADetailVO;
import com.sugon.cloud.iam.common.model.vo.LoginUserVO;
import com.sugon.cloud.iam.common.model.vo.MfaVo;
import com.sugon.cloud.iam.common.model.vo.ValidateTokenUserResponseVO;
import com.sugon.cloud.log.client.context.LogRecordContext;
import com.sugon.cloud.log.client.service.MessageFeignService;
import com.sugon.cloud.log.client.starter.annotation.LogRecordAnnotation;
import com.sugon.cloud.log.common.model.vo.MessageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.event.AuthenticationFailureBadCredentialsEvent;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;


@RestController
@Log4j2
@RequestMapping("/api/oauth")
@RefreshScope
@Api(value = "认证控制器", tags = "认证控制器")
public class JwtLoginController {
    @Autowired
    private JwtService jwtService;
    @Autowired
    private EncryptAndDecryptUtil encryptAndDecryptUtil;

    @Autowired
    private UserService userService;

    @Autowired
    private HttpServletRequest request;

    @Value("${encryption.enabled}")
    private boolean encryptionEnabled;

    @Value("${mfa.type}")
    private String mfaType;

    @Value("${mfa.enabled}")
    private boolean mfaEnabled;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private GlobalsettingsService globalsettingsService;

    @Autowired
    private MFAAuthorizeService mfaAuthorizeService;

    @Autowired
    private UkeyService ukeyService;

    @Autowired
    private StrategyFactory strategyFactory;

    @Autowired
    private AnnotationConfigServletWebServerApplicationContext applicationContext;

    @Autowired
    private MessageFeignService messageFeignService;

    @Value("${internetAccess.enabled:false}")
    private boolean InternetAccessEnabled;

    @PostMapping("/token")
    @LogRecordAnnotation(value = "用户登录", operatorId = "{{#user?.id}}", operatorName = "{{#loginUserVO.username}}", operatorType = "{{#userType}}", detail = "{{#loginUserVO.username}}用户登录",
            resourceId = "{{#userId}}", resource = "{{#loginUserVO.username}}")
    @ApiOperation(value = "认证")
    public ResultModel<String> login(@RequestBody LoginUserVO loginUserVO){
        // 判断平台用户是否可以登录
        userService.checkPlatformUser(loginUserVO.getUsername());
        String internetAccess = request.getHeader("InternetAccess");
        log.debug("internetAccess {} ", internetAccess);
        // 开启外网访问则admin不能登录
        if (InternetAccessEnabled && "true".equals(internetAccess)) {
            if ("admin".equals(loginUserVO.getUsername())) {
                return ResultModel.error("admin用户禁止登录");
            }
        }
        // inner用户登录验证
        userService.checkInnerUserLogin(loginUserVO.getUsername());
        ResultModel resultModel = new ResultModel();
        try {
            User user = userService.getOne(new LambdaQueryWrapper<User>().eq(User::getName, loginUserVO.getUsername()));

            if (Objects.isNull(user)) {

                // 向安全管理员发送消息
                List<User> list = userService.list(new LambdaQueryWrapper<User>().eq(User::getType, TypeUtil.TYPE_SECURITY));
                MessageVo messageVo = new MessageVo();
                messageVo.setSvc("sugoncloud-iam-api");
                messageVo.setContent(loginUserVO.getUsername() + "用户不存在，登录失败！");
                for (User user1 : list) {
                    messageVo.setUser(user1.getId());
                    messageFeignService.createMessage(messageVo);
                }

                UsernamePasswordAuthenticationToken token = new UsernamePasswordAuthenticationToken(loginUserVO.getUsername(), loginUserVO.getPassword());
                IamLoginException iamLoginException = new IamLoginException("");
                AuthenticationFailureBadCredentialsEvent event = new AuthenticationFailureBadCredentialsEvent(token, iamLoginException);
                applicationContext.publishEvent(event);
                throw iamLoginException;
            }
            LogRecordContext.putVariable("user", user);
            LogRecordContext.putVariable("userId", user.getId());
            LogRecordContext.putVariable("userType", user.getType());
            //bug 3419 start 421711 锁定的用户需要记录审计日志, 在记录用户信息之后再抛出异常
            String lockUser = CommonInstance.UNAME_ERROR_LOCK+loginUserVO.getUsername();
            Object lockedTime = redisTemplate.opsForValue().get(lockUser);
            if (ObjectUtil.isNotNull(lockedTime)) {
                GlobalsettingsEntity entity = globalsettingsService.getGlobalsettingsEntity(CommonInstance.CLOBAL_SETTING_PASSWORD, CommonInstance.CLOBAL_SETTING_PASSWORD_LOCK_MINUTE);
                throw new IamLoginException("用户名/密码错误次数达到上限，锁定"+entity.getPolicyDocument()+"分钟");
            }
            //bug 3419 end 421711
            String password;
            if (loginUserVO.isOriginLogin()) {
                password = loginUserVO.getPassword();
            } else if (encryptionEnabled) {
                String publicKey = loginUserVO.getPublickey();
                if (!encryptAndDecryptUtil.validatePublicKeyExist(publicKey)) {
                    log.info("publicKey=[{}] 失效", loginUserVO.getPublickey());
                    throw new BusinessException("登录信息失效");
                }
                boolean verify = encryptAndDecryptUtil.verifyHash(user.getHash(), user.toEncryptAndDecryptJSONStr());
                if (!verify) {
                    throw new BusinessException("用户完整性被破坏无法登录");
                }
                password = encryptAndDecryptUtil.decryptByPublickey(publicKey, loginUserVO.getPassword());
            } else {
                password = encryptAndDecryptUtil.decryptByPublickey(loginUserVO.getPublickey(), loginUserVO.getPassword());
            }
            MFADetailVO mfaDetailVO = mfaAuthorizeService.get(user.getId(), false);
            //双因子验证
            if (mfaEnabled && Objects.nonNull(mfaDetailVO) ){
                //渔翁
                if ( SecretCertificate.FISHERMAN.equals(mfaType) ||
                        SecretCertificate.DEAN.equals(mfaType) || SecretCertificate.AOLIAN.equals(mfaType)) {
                    MfaVo mfaVo = new MfaVo();
                    mfaVo.setCert(mfaDetailVO.getTokenSN());
                    mfaVo.setSignNature(loginUserVO.getLoginInfoSign());
                    mfaVo.setInData(loginUserVO.getUsername() + ":" + password + ":" + loginUserVO.getIv());
                    if ( !ukeyService.verifyData(mfaVo)) {
                        throw new BusinessException("MFA Ukey验签不正确");
                    }
                } else if (SecretCertificate.OPT_SERVER.equals(mfaType)) {
                    if (Objects.nonNull(mfaDetailVO) && !mfaAuthorizeService.auth(user.getName(), mfaDetailVO.getTokenSN(), loginUserVO.getMfaCode())) {
                        throw new BusinessException("MFA动态口令输入不正确");
                    }
                } else if (SecretCertificate.SMS.equals(mfaType) || SecretCertificate.EMAIL.equals(mfaType)) {
                    // 验证码验证
                    if (mfaAuthorizeService.checkValidateCode("login", mfaDetailVO)) {
                        String validateCode = request.getHeader("Validate-Code");
                        if (Objects.isNull(validateCode)) {
                            throw new BusinessException("请输入验证码");
                        }
                        if (Boolean.FALSE.equals(redisTemplate.hasKey(validateCode))) {
                            throw new BusinessException("验证码错误,请重新输入");
                        }
                    }
                } else {
                    boolean result = strategyFactory.verifyService(mfaType)
                            .verifyMfaToken(mfaDetailVO.getTokenSN(), loginUserVO.getMfaCode(), loginUserVO.getChallenge());
                    if (!result) {
                        throw new BusinessException("动态口令输入不正确");
                    }
                }
            }

            String token = jwtService.login(loginUserVO.getUsername(), password, false);
            String ipAddr = IPUtil.getIpAddr(request);
            Object o = redisTemplate.opsForValue().get(CommonInstance.UNAME_ERROR_COUNT + loginUserVO.getUsername());
            //不为0时再设置
            if (o != null && !"0".equals(o.toString())) {
                long expiredTime = jwtService.getExpiredTime();
                redisTemplate.opsForValue().set(CommonInstance.UNAME_ERROR_COUNT + token, o, expiredTime, TimeUnit.SECONDS);
            }
            redisTemplate.opsForValue().set(CommonInstance.UNAME_ERROR_COUNT + loginUserVO.getUsername(), 0);
            userService.update(null, new LambdaUpdateWrapper<User>()
                    .eq(User::getId, user.getId())
                    .set(User::getLastActiveAt, new Date())
                    .set(User::getLastLoginIp, ipAddr));
            resultModel.setContent(token);
        } catch (Exception e) {
            String msg = e.getMessage();
            if ("Cannot pass null or empty values to constructor".equals(msg)) {
                msg = "用户未初始化密码";
            }
            log.error("用户登录异常", e);
            if (!CommonUtils.isContainChinese(msg)) {
                msg = "用户登录异常";
            }
            throw new IamLoginException(msg);
        }
        return resultModel;
    }

    @GetMapping("/validate-token")
    @ApiOperation(value = "验证token")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "token", value = "token", required = true, dataType = "String")})
    public ResultModel<ValidateTokenUserResponseVO> validateToken(@RequestParam("token") String token,HttpServletRequest httpRequest) throws Exception {
        ValidateTokenUserResponseVO validateTokenUserResponseVO = jwtService.validateToken(token,httpRequest);
        if (Objects.isNull(validateTokenUserResponseVO)) {
            return ResultModel.error("token validate error!");
        }
        ResultModel resultModel = new ResultModel();
        resultModel.setContent(validateTokenUserResponseVO);
        return resultModel;
    }

    @DeleteMapping
    @ApiOperation(value = "退出登录")
    @LogRecordAnnotation(value = "退出登录", detail = "用户{{#username}}退出登录", resource = "{{#username}}", resourceId = "{{#userid}}")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = CommonInstance.AUTHORIZATION, value = "token", required = true, paramType = "header", dataType = "String")})
    public ResultModel logout(@RequestHeader(CommonInstance.AUTHORIZATION) String token) {
        String username = "";
        String userid = "";
        try {
            ValidateTokenUserResponseVO validateTokenUserResponseVO = jwtService.validateToken(token,null);
            if (!Objects.isNull(validateTokenUserResponseVO)) {
                username = validateTokenUserResponseVO.getUsername();
                userid = validateTokenUserResponseVO.getUserId();
            }
        } catch (Exception e) {
            log.error("检验token失败", e);
        }
        LogRecordContext.putVariable("username", username);
        LogRecordContext.putVariable("userid", userid);
        jwtService.setBlack(token);
        return ResultModel.success("退出登录成功","");
    }

    @GetMapping
    @ApiOperation(value = "获取token过期时间")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = CommonInstance.AUTHORIZATION, value = "token", required = true, paramType = "header", dataType = "String")})
    public ResultModel<Long> expiredDate(@RequestHeader(CommonInstance.AUTHORIZATION) String token) {
        return ResultModel.success("",jwtService.currentUserExpiredDate(token));
    }

    @GetMapping("/need-mfa-code")
    @ApiOperation(value = "是否绑定了证书")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "username", value = "用户名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "business_type", value = "业务类型, 登录:login, 敏感操作:danger", dataType = "String")
    })
    @DocumentIgnore
    public ResultModel<Boolean> checkUserNeedmfaCode(@RequestParam("username") String username,
                                                     @RequestParam(value = "business_type", required = false) String businessType) {
        User user = userService.getOne(new LambdaQueryWrapper<User>().eq(User::getName, username));
        if (Objects.isNull(user)) {
            return ResultModel.success(false);
        }
        /*if ("fisherman".equals(mfaType)) {
            return ResultModel.success(false);
        }else if ("opt-server".equals(mfaType)){
            return ResultModel.success(Objects.nonNull(mfaAuthorizeService.get(user.getId(), false)));
        }*/
        MFADetailVO mfaDetailVO = mfaAuthorizeService.get(user.getId(), false);
        // 短信验证开关
        if (SecretCertificate.SMS.equals(mfaType) || SecretCertificate.EMAIL.equals(mfaType)) {
            return ResultModel.success(mfaAuthorizeService.checkValidateCode(businessType, mfaDetailVO));
        }
        return ResultModel.success(Objects.nonNull(mfaDetailVO));
    }



}
