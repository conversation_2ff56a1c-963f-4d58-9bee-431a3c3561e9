package com.sugon.cloud.iam.api.controller.keystone;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.iam.api.entity.keystone.Assignment;
import com.sugon.cloud.iam.api.service.AssignmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping
@Api(tags = {"keystone-用户项目角色关系"})
@DocumentIgnore
public class AssignmentController {
    @Value("#{'${vip.ip}'.concat(':').concat('${vip.port}')}")
    private String url;
    @Autowired
    private AssignmentService assignmentService;
    @GetMapping("/v3/role_assignments")
    @ApiOperation(value = "根据用户和项目获取角色关系")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "user.id", value = "用户id", required = true, dataType = "String")
            , @ApiImplicitParam(name = "scope.project.id", value = "项目id", required = true, dataType = "String")})
    public Map<String, Object> roleAssignments(@RequestParam(value = "user.id", required = false) String userId,
                                      @RequestParam(value = "scope.project.id", required = false)String projectId){
        List<Assignment> list = assignmentService.list(new QueryWrapper<Assignment>().lambda()
                .eq(StringUtils.isNotBlank(userId), Assignment::getActorId, userId)
                .eq(StringUtils.isNotBlank(projectId), Assignment::getTargetId, projectId));
        List result = Lists.newArrayList();
        list.stream().forEach(assignment -> {
            //TODO 构造对象麻烦 直接map
            Map scopeMap = new HashMap();
            Map projectMap = new HashMap();
            projectMap.put("id", assignment.getTargetId());
            scopeMap.put("project", projectMap);
            Map roleMap = new HashMap();
            roleMap.put("id", assignment.getRoleId());
            Map userMap = new HashMap();
            userMap.put("id", assignment.getActorId());
            Map linksMap = new HashMap();
            String href = "http://"+url+"/v3/projects/"+assignment.getTargetId()+"/users/"+assignment.getActorId()+"/roles/"+assignment.getRoleId();
            linksMap.put("assignment", href);
            Map map = new HashMap();
            map.put("scope", scopeMap);
            map.put("role", roleMap);
            map.put("user", userMap);
            map.put("links", linksMap);
            result.add(map);
        });
        Map linksMap = new HashMap();
        String href = "http://"+url+"/v3/role_assignments";
        if (StringUtils.isNotBlank(userId) && StringUtils.isNotBlank(projectId)) {
            href = href+"?user.id="+userId+"&scope.project.id="+projectId;
        }
        if (StringUtils.isBlank(userId) && StringUtils.isNotBlank(projectId)) {
            href = href+"?scope.project.id="+projectId;
        }
        if (StringUtils.isNotBlank(userId) && StringUtils.isBlank(projectId)) {
            href = href+"?user.id="+userId;
        }
        linksMap.put("self", href);
        linksMap.put("previous",null);
        linksMap.put("next",null);

        Map map = new HashMap();
        map.put("role_assignments", result);
        map.put("links", linksMap);
        return map;
    }
}
