package com.sugon.cloud.iam.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sugon.cloud.common.constants.HeaderParamConstant;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.common.mapper.ModelMapper;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.common.utils.UUIDUtil;
import com.sugon.cloud.iam.api.common.AESUtil;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.common.MD5Util;
import com.sugon.cloud.iam.api.common.RecursionDeptIdUtils;
import com.sugon.cloud.iam.api.entity.CloudViewGatewayUser;
import com.sugon.cloud.iam.api.entity.Department;
import com.sugon.cloud.iam.api.entity.IamRole;
import com.sugon.cloud.iam.api.entity.UserAccessEntity;
import com.sugon.cloud.iam.api.entity.UserAuthorizeSettings;
import com.sugon.cloud.iam.api.entity.UserRoleMapping;
import com.sugon.cloud.iam.api.entity.keystone.Assignment;
import com.sugon.cloud.iam.api.entity.keystone.Project;
import com.sugon.cloud.iam.api.entity.keystone.Role;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.exception.BusinessException;
import com.sugon.cloud.iam.api.mapper.AssignmentMapper;
import com.sugon.cloud.iam.api.mapper.DepartmentMapper;
import com.sugon.cloud.iam.api.mapper.IamRoleMapper;
import com.sugon.cloud.iam.api.mapper.ProjectMapper;
import com.sugon.cloud.iam.api.mapper.RoleMapper;
import com.sugon.cloud.iam.api.mapper.UserAuthorizeSettingsMapper;
import com.sugon.cloud.iam.api.mapper.UserMapper;
import com.sugon.cloud.iam.api.mapper.UserRoleMappingMapper;
import com.sugon.cloud.iam.api.service.DepartmentService;
import com.sugon.cloud.iam.api.service.GlobalsettingsService;
import com.sugon.cloud.iam.api.service.MFAAuthorizeService;
import com.sugon.cloud.iam.api.service.UserAccessService;
import com.sugon.cloud.iam.api.service.UserAuthorizeService;
import com.sugon.cloud.iam.api.service.UserRoleMappingService;
import com.sugon.cloud.iam.api.service.UserService;
import com.sugon.cloud.iam.api.utils.BeanConvertUtils;
import com.sugon.cloud.iam.api.utils.CommonUtils;
import com.sugon.cloud.iam.api.utils.DateUtils;
import com.sugon.cloud.iam.api.utils.DepartmentPathUtils;
import com.sugon.cloud.iam.api.utils.EncryptAndDecryptUtil;
import com.sugon.cloud.iam.api.vo.AccessControlVO;
import com.sugon.cloud.iam.api.vo.UserBindProjectsVO;
import com.sugon.cloud.iam.api.vo.UserBindRolesVO;
import com.sugon.cloud.iam.api.vo.UserCreateOrUpdateVO;
import com.sugon.cloud.iam.api.vo.UserProjectVO;
import com.sugon.cloud.iam.api.vo.UserRoleVO;
import com.sugon.cloud.iam.common.model.dto.UserDeptAndUserIdsDTO;
import com.sugon.cloud.iam.common.model.dto.UserOwnDeptAndProjectDTO;
import com.sugon.cloud.iam.common.model.vo.DepartmentTreeDetailVO;
import com.sugon.cloud.iam.common.model.vo.ProjectDetailVO;
import com.sugon.cloud.iam.common.model.vo.UserViewVO;
import com.sugon.cloud.iam.common.model.vo.UserVo;
import com.sugon.cloud.log.client.context.LogRecordContext;
import com.sugon.cloud.log.client.service.MessageFeignService;
import com.sugon.cloud.log.common.model.vo.MessageVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RefreshScope
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Value("${encryption.enabled}")
    private boolean encryptionEnabled;
    @Value("${iam.securityAdminUserId}")
    private String securityAdminUserId;
    @Value("${iam.inner.userId:0d2bbb018e8b44b985a169647379f413}")
    private String adminUserId;
    @Value("${iam.userCertOne2One:true}")
    private boolean userCertOne2One;

    @Value("${iam.phonePattern:}")
    private String phonePattern;

    private final UserMapper userMapper;

    private final DepartmentMapper departmentMapper;

    private final DepartmentService departmentService;

    private final ProjectMapper projectMapper;

    private final RoleMapper roleMapper;

    private final AssignmentMapper assignmentMapper;

    private final UserRoleMappingMapper userRoleMappingMapper;

    private final UserRoleMappingService userRoleMappingService;

    private final IamRoleMapper iamRoleMapper;

    private final ModelMapper mapper;

    private final EncryptAndDecryptUtil encryptAndDecryptUtil;

    private final MessageFeignService messageFeignService;

    private final UserAuthorizeSettingsMapper userAuthorizeSettingsMapper;
    private final MFAAuthorizeService mfaAuthorizeService;
    private final Map<String, UserAuthorizeService> userAuthorizeServiceMap;

    private final UserAccessService userAccessService;

    private final GlobalsettingsService globalsettingsService;

    @Resource(name = "defaultRedisTemplate")
    private final RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private HttpServletRequest request;

    @Override
    public String getProjectIdByUserName (String name) {
        return userMapper.getProjectIdByUserName(name);
    }

    @Override
    public User getByName(String name) {
        return userMapper.getByName(name);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public String createUser(UserCreateOrUpdateVO userVO) {
        User user = initUser(userVO, true);
        String projectId = UUIDUtil.get32UUID();
        Department department = initDepartment(user, projectId, userVO.getDepartmentName());
        this.departmentMapper.insert(department);
        user.setDeptId(department.getId());
        Project project = initProject(user, projectId);
        this.projectMapper.insert(project);
        Role role = getAdminRole();
        user.setDefaultProjectId(projectId);
        user.setDomainId(projectId);
        user.setType(TypeUtil.TYPE_MASTER);
        assignmentMapper.insert(initAssignment(user, project, role));
        // 绑定内置角色
        innerRoleBind(user.getId(), TypeUtil.TYPE_MASTER);
        if (encryptionEnabled) {
            IamRole iamRole = iamRoleMapper.selectOne(new QueryWrapper<IamRole>().lambda().eq(IamRole::getType, TypeUtil.TYPE_MASTER));
            List<String> roleIds = new ArrayList<>();
            roleIds.add(iamRole.getId());
            user.setHashRole(encryptAndDecryptUtil.sign(JSONObject.toJSONString(roleIds)));
        }
        userMapper.insert(user);
        if (encryptionEnabled) {
           this.updateUserHash(user.getId());
        }
        MessageVo messageVo = new MessageVo();
        messageVo.setUser(user.getId());
        messageVo.setSvc("sugoncloud-iam-api");
        messageVo.setContent("用户注册成功");
        messageVo.setEmailEnable(true);
        messageFeignService.createMessage(messageVo);
        messageVo.setUser(securityAdminUserId);
        messageVo.setContent("[" + user.getName() + "]用户注册成功");
        messageFeignService.createMessage(messageVo);
        return user.getId();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public String createAdmin(UserCreateOrUpdateVO userVO, boolean checkUser) {
        User user = initUser(userVO, checkUser);
        String projectId = UUIDUtil.get32UUID();
        Project project = initProjectNoDepartment(user, projectId);
        this.projectMapper.insert(project);
        Role role = getAdminRole();
        user.setDefaultProjectId(projectId);
        user.setDomainId(projectId);
        if (globalsettingsService.getBmStatus()) {
            user.setType(TypeUtil.TYPE_SYS_ADMIN);
        } else {
            user.setType(TypeUtil.TYPE_ADMIN);
        }
        user.setDeptId(null);
        assignmentMapper.insert(initAssignment(user, project, role));
        userMapper.insert(user);
        if (encryptionEnabled) {
            this.updateUserHash(user.getId());
        }
        MessageVo messageVo = new MessageVo();
        messageVo.setUser(user.getId());
        messageVo.setSvc("sugoncloud-iam-api");
        messageVo.setContent("创建管理员成功");
        messageVo.setEmailEnable(true);
        messageFeignService.createMessage(messageVo);
        messageVo.setUser(securityAdminUserId);
        messageVo.setContent("[" + user.getName() + "]管理员创建成功");
        messageFeignService.createMessage(messageVo);
        return user.getId();
    }

    @Override
    public Department createMasterUserFromGateway(CloudViewGatewayUser cloudViewGatewayUser) {
        User user = mapper.map(cloudViewGatewayUser, User.class);
        String pwd = AESUtil.decrypt(cloudViewGatewayUser.getPassword()
                , MD5Util.string2MD5(cloudViewGatewayUser.getSalt()));
        user.setPassword(new BCryptPasswordEncoder().encode(pwd));
        user.setType(TypeUtil.TYPE_MASTER);
        user.setExtra(wrapperUserExtra(""));

        String projectId = UUIDUtil.get32UUID();
        // 顶级组织
        Department department = initDepartment(user, projectId, null);
        department.setLevel(CommonInstance.MASTER_DEPARTMENT_LEVEL);
        this.departmentMapper.insert(department);
        user.setDeptId(department.getId());
        // 顶级项目
        Project project = initProject(user, projectId);
        this.projectMapper.insert(project);
        Role role = getAdminRole();
        assignmentMapper.insert(initAssignment(user, project, role));
        // 一级组织
        Department subDepartment = initDepartment(user, projectId, null);
        subDepartment.setName(user.getName() + "_" + "一级部门");
        subDepartment.setParentId(department.getId());
        subDepartment.setLevel(department.getLevel() + 1);
        this.departmentMapper.insert(subDepartment);
        Project gatewayDefaultProject = cloudViewGatewayUser.getGatewayDefaultProject();
        if (Objects.nonNull(gatewayDefaultProject)) {
            // 一级组织的项目
            gatewayDefaultProject.setDomain(false);
            gatewayDefaultProject.setEnabled(true);
            gatewayDefaultProject.setDomainId(projectId);
            gatewayDefaultProject.setParentId(projectId);
            gatewayDefaultProject.setAlias(gatewayDefaultProject.getName());
            gatewayDefaultProject.setDeptId(subDepartment.getId());
            this.projectMapper.insert(gatewayDefaultProject);
        } else {
            log.info("createMasterUserFromGateway user:[{}] have not gatewayDefaultProject."
                    , JSONObject.toJSONString(cloudViewGatewayUser));
        }

        // 绑定内置角色
        innerRoleBind(user.getId(), TypeUtil.TYPE_MASTER);

        user.setDefaultProjectId(projectId);
        user.setDomainId(projectId);
        user.setCreatedAt(cloudViewGatewayUser.getCreateAt());
        userMapper.insert(user);
        return department;
    }

    /**
     * 将界面传过来的description字段转为JSON字符串，适配底层OpenStack
     * @param extraOrigin
     * @return
     */
    public static String wrapperUserExtra(String extraOrigin) {
        JSONObject extra = new JSONObject();
        extra.put(CommonInstance.USER_EXTRA_DESCRIPTION_KEY, extraOrigin);
        extra.put(CommonInstance.PASSWORD_ENCODE, false);
        return extra.toString();
    }

    /**
     * 将界面传过来的description字段转为JSON字符串，适配底层OpenStack
     * @param extra
     * @return
     */
    public static String parseUserExtra(String extra,String key) {
        try {
            JSONObject result = JSONObject.parseObject(extra);
            return result.getString(key);
        } catch (Exception e) {
            log.error("parseUserExtra error:[{}]", extra);
        }
        return null;
    }

    /**
     * 绑定内置角色
     *
     * @param userId
     * @param userType
     */
    private void innerRoleBind(String userId, String userType) {
        IamRole iamRole = iamRoleMapper.selectOne(new QueryWrapper<IamRole>().lambda().eq(IamRole::getType, userType));
        if (Objects.isNull(iamRole)) {
            throw new BusinessException("还未内置" + userType + "角色");
        }
        this.assignmentRole(userId, iamRole.getId(), null, false);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public String createSubUser(UserCreateOrUpdateVO userVO, boolean checkUser) {
        User user = initUser(userVO, checkUser);
        Department department = departmentMapper.selectById(user.getDeptId());
        if (department == null) throw new BusinessException("不存在组织信息id:"+user.getDeptId());
        Project defaultProject = projectMapper.findById(department.getDomainId());
        if (defaultProject == null) throw new BusinessException("不存在domain信息id:"+department.getDomainId());
        user.setDefaultProjectId(defaultProject.getId());
        user.setDomainId(defaultProject.getId());
        if (userVO.isDepartmentManager()) {
            user.setType(TypeUtil.TYPE_DEPT_MASTER);
            // 绑定内置角色
            innerRoleBind(user.getId(),TypeUtil.TYPE_DEPT_MASTER);
        }
        //bm环境设置用户类型
        if (globalsettingsService.getBmStatus() && CommonInstance.MASTER_DEPARTMENT_LEVEL.equals(department.getLevel())) {
            Assert.isTrue(StrUtil.isNotBlank(userVO.getUserType()), "一级组织不能创建普通用户");
            // 组织内三元各只能创建一人
            int userCount = this.count(new LambdaQueryWrapper<User>().eq(User::getDeptId, department.getId()).eq(User::getType, userVO.getUserType()));
            Assert.isTrue(userCount == 0, "组织下已经存在" + userVO.getUserType() + "角色用户");
            // 设置类型
            user.setType(userVO.getUserType());
        }
        if (encryptionEnabled) {
            List<String> roleIds = new ArrayList<>();
            if (userVO.isDepartmentManager()) {
                IamRole iamRole = iamRoleMapper.selectOne(new QueryWrapper<IamRole>().lambda().eq(IamRole::getType, TypeUtil.TYPE_DEPT_MASTER));
                roleIds.add(iamRole.getId());
            }
            user.setHashRole(encryptAndDecryptUtil.sign(JSONObject.toJSONString(roleIds)));
        }
        initUserRole(userVO.getRoleIds(), user.getId());
        initUserProjectRole(userVO.getProjectRoleIds(), user.getId());
        //OpenStack的admin 角色
        Role role = getAdminRole();
        assignmentMapper.insert(initAssignment(user, defaultProject, role));
        userMapper.insert(user);
        if (encryptionEnabled) {
            this.updateUserHash(user.getId());
        }
        return user.getId();
    }

    private User initUser(UserCreateOrUpdateVO userVO, boolean checkUser){
        //将前端传过来的密码进行解密
        String password = userVO.getPassword();
        String publicKey = userVO.getPublickey();
        try {
            password = encryptAndDecryptUtil.decryptByPublickey(publicKey, password);
        } catch (Exception e) {
           log.error("create user error", e);
            throw new BusinessException("创建用户失败");
        }
        User user = new User();
        BeanUtils.copyProperties(userVO, user);
        if (checkUser) {
            // 校验用户名、邮箱和电话
            this.checkUserName(userVO);
            user.setPassword(new BCryptPasswordEncoder().encode(password));
        }
        Date date = new Date();
        user.setId(UUIDUtil.get32UUID());
        user.setCreatedAt(date);
        user.setExtra(wrapperUserExtra(userVO.getExtra()));
        //bug 3425
        user.setLastPasswordTime(date);
        if (encryptionEnabled) {
            String iv = encryptAndDecryptUtil.getIv();
            user.setIv(iv);
            user.setPhone(encryptAndDecryptUtil.encryptByIv(user.getPhone(), iv));
            user.setEmail(encryptAndDecryptUtil.encryptByIv(user.getEmail(), iv));
            user.setPassword(encryptAndDecryptUtil.encryptByIv(user.getPassword(), iv));
            JSONObject extraObj = JSONObject.parseObject(user.getExtra());
            //密码加密之后设置密码已加密标识
            extraObj.put(CommonInstance.PASSWORD_ENCODE, true);
            user.setExtra(extraObj.toString());
        }
        return user;
    }

    @Override
    public void createSubUserFromGateway(CloudViewGatewayUser cloudViewGatewayUser, Department department) {
        User user = mapper.map(cloudViewGatewayUser, User.class);
        String pwd = AESUtil.decrypt(cloudViewGatewayUser.getPassword()
                , MD5Util.string2MD5(cloudViewGatewayUser.getSalt()));
        user.setPassword(new BCryptPasswordEncoder().encode(pwd));
        // 用户都设置为组织管理员
        user.setType(TypeUtil.TYPE_DEPT_MASTER);
        user.setExtra(wrapperUserExtra(""));
        user.setDeptId(department.getId());

        String defaultProjectId = department.getDomainId();
        user.setDefaultProjectId(defaultProjectId);
        user.setDomainId(defaultProjectId);

        assignmentMapper.insert(initAssignment(user, initProject(user, defaultProjectId), getAdminRole()));
        // 绑定内置角色
        innerRoleBind(user.getId(), TypeUtil.TYPE_DEPT_MASTER);
        user.setCreatedAt(cloudViewGatewayUser.getCreateAt());
        userMapper.insert(user);
    }

    @Override
    public PageCL<UserViewVO> getUserPage(String userId, Integer pageNum, Integer pageSize, String currentUserId) {
        User user = userMapper.selectById(userId);
        if (Objects.isNull(user)) throw new BusinessException("用户不存在ID"+userId);
        String currentDeptId = user.getDeptId();
        List<String> deptIds = Lists.newArrayList();
        Department department = departmentMapper.selectById(user.getDeptId());
        if (Objects.isNull(department)) throw new BusinessException("用户所在组织不存在ID"+userId);
        if (TypeUtil.TYPE_DEPT_MASTER.equals(user.getType()) ||
                TypeUtil.TYPE_MASTER.equals(user.getType())) {
            List<DepartmentTreeDetailVO> treeDepartment = departmentMapper.findTreeDepartment(currentDeptId);
            RecursionDeptIdUtils.getDeptIds(treeDepartment, deptIds);
        }
        deptIds.add(department.getId());
        IPage<User> page = new Page<>(pageNum, pageSize);
        IPage<User> userIPage = userMapper.selectPage(page, new QueryWrapper<User>().lambda().in(User::getDeptId, deptIds));
        PageCL<UserViewVO> pageCL = new PageCL<>();
        pageCL.setPageSize(pageSize);
        pageCL.setPageNum(pageNum);
        List<UserViewVO> viewVOS = Lists.newArrayList();
        userIPage.getRecords().forEach(u -> {
            UserViewVO userViewVO = new UserViewVO();
            BeanUtils.copyProperties(u, userViewVO);
            if (encryptionEnabled) {
                User currentUser = userMapper.selectById(currentUserId);
                String cert = JSONObject.parseObject(currentUser.getExtra()).getString(CommonInstance.ENCRYPTION_CONTENT_KEY);
                String email = encryptAndDecryptUtil.decryptByIv(userViewVO.getEmail(), userViewVO.getIv());
                userViewVO.setEmail(encryptAndDecryptUtil.encryptEnvelope(cert, email));
                String phone = encryptAndDecryptUtil.decryptByIv(userViewVO.getPhone(), userViewVO.getIv());
                userViewVO.setPhone(encryptAndDecryptUtil.encryptEnvelope(cert, phone));
            }
            userViewVO.setExtra(parseUserExtra(userViewVO.getExtra(), CommonInstance.USER_EXTRA_DESCRIPTION_KEY));
            viewVOS.add(userViewVO);
        });
        pageCL.setList(viewVOS);
        pageCL.setTotal((int)userIPage.getTotal());
        pageCL.setSize(pageSize);
        pageCL.setPageCount((int) ((userIPage.getTotal()+pageSize-1) / pageSize));
        return pageCL;
    }

    @Override
    public String updatePassword(String userId, String password, String oldPassword) {
        User user = userMapper.selectById(userId);
        if (Objects.isNull(user)) throw new BusinessException("用户不存在ID:"+userId);
        if ("inner".equals(user.getName())) {
            throw new BusinessException("内置用户禁止修改密码");
        }
        BCryptPasswordEncoder bcryptPasswordEncoder = new BCryptPasswordEncoder();
        String newPassword = bcryptPasswordEncoder.encode(password);
        String presentedPassword = user.getPassword();
        if (encryptionEnabled) {
            newPassword = encryptAndDecryptUtil.encryptByIv(newPassword, user.getIv());
            presentedPassword = encryptAndDecryptUtil.decryptByIv(presentedPassword, user.getIv());
        }
        boolean matches =  bcryptPasswordEncoder.matches(oldPassword, presentedPassword);
        if (!matches) throw new BusinessException("原密码不正确");
        userMapper.update(null, new LambdaUpdateWrapper<User>()
                .eq(User::getId, userId)
                .set(User::getPassword, newPassword)
                //bug 3425
                .set(User::getLastPasswordTime, new Date()));
        MessageVo messageVo = new MessageVo();
        messageVo.setUser(user.getId());
        messageVo.setSvc("sugoncloud-iam-api");
        messageVo.setContent("您的用户密码已被修改");
        messageVo.setEmailEnable(true);
        messageFeignService.createMessage(messageVo);
        // 如果修改密码用户是security，则需要再给admin用户发送一条消息
        if (TypeUtil.TYPE_SECURITY.equals(user.getType())) {
            messageVo.setUser(adminUserId);
            messageVo.setContent("[" + user.getName() + "]用户密码已修改");
            messageFeignService.createMessage(messageVo);
        } else if (TypeUtil.TYPE_ADMIN.equals(user.getType())) {
            // 如果是admin用户修改，则需要同时给securityadmin发送一条消息
            messageVo.setUser(securityAdminUserId);
            messageVo.setContent("[" + user.getName() + "]用户密码已修改");
            messageFeignService.createMessage(messageVo);
        } else if (TypeUtil.TYPE_AUDIT.equals(user.getType())) {
            // 如果是日志管理员修改密码，则需要同时给admin/securityadmin发送消息
            messageVo.setContent("[" + user.getName() + "]用户密码已修改");
            messageVo.setUser(adminUserId);
            messageFeignService.createMessage(messageVo);
            messageVo.setUser(securityAdminUserId);
            messageFeignService.createMessage(messageVo);
        }
        return user.getName();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public String delete(String userId) {
        User user = userMapper.selectById(userId);
        if (Objects.isNull(user)) throw new BusinessException("用户不存在ID:"+userId);
        // 日志：区分是删除用户还是删除组织
        String value = "删除用户";
        String name = user.getName();
        String detail = "删除用户：" + name;
        LogRecordContext.putVariable("value", value);
        LogRecordContext.putVariable("name", name);
        LogRecordContext.putVariable("detail", detail);
        if("admin".equals(user.getName())){
            throw new BusinessException("不能删除超级管理员");
        }
        if("inner".equals(user.getName())){
            throw new BusinessException("不能删除内置账号");
        }
        if(TypeUtil.TYPE_SECURITY.equals(user.getType())){
            throw new BusinessException("不能删除安全管理员");
        }
        if(TypeUtil.TYPE_AUDIT.equals(user.getType())){
            throw new BusinessException("不能删除日志管理员");
        }
        if(TypeUtil.TYPE_MASTER.equals(user.getType())){
            //421803
            List<User> users = userMapper.selectList(new QueryWrapper<User>().lambda().eq(User::getDeptId, user.getDeptId())
                    .in(User::getType, Lists.newArrayList(TypeUtil.TYPE_ORG_SECADMIN, TypeUtil.TYPE_ORG_SYSADMIN, TypeUtil.TYPE_ORG_SECAUDITOR)));
            //校验是否有子组织存在
            Integer total = departmentMapper.selectCount(new QueryWrapper<Department>().lambda().eq(Department::getParentId, user.getDeptId()));
            Department department = departmentMapper.selectById(user.getDeptId());
            value = "删除组织";
            name = Objects.isNull(department) ? "" : department.getName();
            detail = "删除组织：" + name;
            //421803
            if ((Objects.nonNull(total) && total > 0) || Objects.nonNull(users) && users.size() > 0) {
                LogRecordContext.putVariable("value", value);
                LogRecordContext.putVariable("name", name);
                LogRecordContext.putVariable("detail", detail);
                throw new BusinessException("组织有子组织或有组织内三员");
            }
            assignmentMapper.delete(new QueryWrapper<Assignment>().lambda().eq(Assignment::getActorId, userId));
            userRoleMappingMapper.delete(new QueryWrapper<UserRoleMapping>().lambda().eq(UserRoleMapping::getUserId, userId));
            userMapper.deleteById(userId);
            projectMapper.delete(new QueryWrapper<Project>().lambda().eq(Project::getDeptId, user.getDeptId()));
            departmentMapper.deleteById(user.getDeptId());
        }else {
            assignmentMapper.delete(new QueryWrapper<Assignment>().lambda().eq(Assignment::getActorId, userId));
            userRoleMappingMapper.delete(new QueryWrapper<UserRoleMapping>().lambda().eq(UserRoleMapping::getUserId, userId));
            userMapper.deleteById(userId);
        }
        LogRecordContext.putVariable("value", value);
        LogRecordContext.putVariable("name", name);
        LogRecordContext.putVariable("detail", detail);
        MessageVo messageVo = new MessageVo();
        messageVo.setSvc("sugoncloud-iam-api");
        messageVo.setEmailEnable(true);
        messageVo.setUser(securityAdminUserId);
        messageVo.setContent("[" + user.getName() +"]用户已被删除");
        messageFeignService.createMessage(messageVo);
        // 关闭相关认证设置
        deleteUserAuthorizeSettings(userId, user);
        return user.getName();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public String deleteAdmin(String userId) {
        User user = userMapper.selectById(userId);
        if (Objects.isNull(user)) throw new BusinessException("管理员不存在ID:"+userId);
        //sys_admin也能删除
        if(!TypeUtil.TYPE_ADMIN.equals(user.getType()) && !TypeUtil.TYPE_SYS_ADMIN.equals(user.getType())){
            throw new BusinessException("目标用户类型不正确");
        }
        if("admin".equals(user.getName())){
            throw new BusinessException("不能删除超级管理员");
        }
        if("inner".equals(user.getName())){
            throw new BusinessException("不能删除内置账号");
        }
        if(TypeUtil.TYPE_SECURITY.equals(user.getType())){
            throw new BusinessException("不能删除安全管理员");
        }
        if(TypeUtil.TYPE_AUDIT.equals(user.getType())){
            throw new BusinessException("不能删除日志管理员");
        }
        assignmentMapper.delete(new QueryWrapper<Assignment>().lambda().eq(Assignment::getActorId, userId));
        userRoleMappingMapper.delete(new QueryWrapper<UserRoleMapping>().lambda().eq(UserRoleMapping::getUserId, userId));
        userMapper.deleteById(userId);
        projectMapper.delete(new QueryWrapper<Project>().lambda().eq(Project::getId, user.getDefaultProjectId()));
        MessageVo messageVo = new MessageVo();
        messageVo.setSvc("sugoncloud-iam-api");
        messageVo.setEmailEnable(true);
        messageVo.setUser(securityAdminUserId);
        messageVo.setContent("[" + user.getName() +"]管理员已被删除");
        messageFeignService.createMessage(messageVo);
        // 关闭相关认证设置
        deleteUserAuthorizeSettings(userId, user);
        return user.getName();
    }

    private void deleteUserAuthorizeSettings(String userId, User deleteUser) {
        userAuthorizeServiceMap.forEach((k, userAuthorizeService) -> {
            try {
                userAuthorizeService.preClose(userId, deleteUser);
            } catch (Exception e) {
                log.error("call {}.close error:", k, e);
            }
        });
        userAuthorizeSettingsMapper.delete(new LambdaUpdateWrapper<UserAuthorizeSettings>()
                .eq(UserAuthorizeSettings::getUserId, userId));
    }

    @Override
    public int assignmentRole(String userId, String roleId, String projectId,boolean userExist) {
        UserRoleMapping userRoleMapping = new UserRoleMapping();
        if (userExist && Objects.isNull(userMapper.selectById(userId))) {
            throw new BusinessException("用户不存在ID:"+userId);
        }
        if(StringUtils.isNotBlank(projectId)) {
            if (Objects.isNull(projectMapper.selectById(projectId))) {
                throw new BusinessException("组织不存在ID:"+projectId);
            }
            userRoleMapping.setProjectId(projectId);
        }
        userRoleMapping.setRoleId(roleId);
        userRoleMapping.setUserId(userId);
        //先删除在插入，防止数据重复
        userRoleMappingMapper.delete(new QueryWrapper<UserRoleMapping>().lambda()
                .eq(UserRoleMapping::getUserId, userId)
                .eq(UserRoleMapping::getRoleId, roleId));
        int flag = userRoleMappingMapper.insert(userRoleMapping);
        MessageVo messageVo = new MessageVo();
        messageVo.setSvc("sugoncloud-iam-api");
        messageVo.setEmailEnable(true);
        messageVo.setUser(userId);
        messageVo.setContent("您的用户角色权限已被变更");
        messageFeignService.createMessage(messageVo);
        //future 绑定角色 同步更新到Redis
        this.syncUserRole2Redis(userId);
        return flag;
    }

    @Override
    public int assignmentProject(String userId, String projectId) {
        UserRoleMapping userRoleMapping = new UserRoleMapping();
        if (Objects.isNull(userMapper.selectById(userId))) {
            throw new BusinessException("用户不存在ID:"+userId);
        }

        Project project = projectMapper.selectById(projectId);
        if (Objects.isNull(project)) {
            throw new BusinessException("项目不存在ID:"+projectId);
        }
        userRoleMapping.setProjectId(projectId);
        userRoleMapping.setUserId(userId);
        // 先删除，再插入
        userRoleMappingMapper.delete(new QueryWrapper<UserRoleMapping>().lambda()
                .eq(UserRoleMapping::getUserId, userId)
                .eq(UserRoleMapping::getProjectId, projectId));
        userRoleMappingMapper.insert(userRoleMapping);
        return 0;
    }

    @Override
    public int removeRole(String userId, String roleId) {
        int delete = userRoleMappingMapper.delete(new QueryWrapper<UserRoleMapping>().lambda().eq(UserRoleMapping::getUserId, userId)
                .eq(UserRoleMapping::getRoleId, roleId));
        //future 绑定角色 同步更新到Redis
        this.syncUserRole2Redis(userId);
        return delete;
    }

    @Override
    public PageCL<UserViewVO> getUserPageByDeptId(String deptId, Integer pageNum, Integer pageSize, String name,
                                                  String userId, String userType) {
        //获取当前登录用户
        User user = userMapper.selectById(userId);
        if (Objects.isNull(user)) {
            throw new BusinessException("用户不存在ID:"+userId);
        }
        String userDepartmentId = user.getDeptId();
       /* if (StringUtils.isNotBlank(userId)) {
            User user = userMapper.selectById(userId);
            if (Objects.nonNull(user)) {
                userDepartmentId = user.getDeptId();
            }
        }*/

        IPage<User> page = new Page<>(pageNum, pageSize);
        if (globalsettingsService.getBmStatus()) {
            // 保密环境，不能查询master/department_master类型用户
            page = userMapper.selectPage(page, new QueryWrapper<User>().lambda().in(User::getDeptId, deptId)
                    .and(wrapper -> wrapper.isNull(User::getType).or()
                            .in(User::getType, Lists.newArrayList(TypeUtil.TYPE_ORG_SECADMIN, TypeUtil.TYPE_ORG_SYSADMIN, TypeUtil.TYPE_ORG_SECAUDITOR)))
                    .and(StringUtils.isNotEmpty(name), q -> q.like(User::getName, name).or().like(User::getAlias, name))
                    .orderByDesc(User::getCreatedAt));
        } else {
            page = userMapper.selectPage(page, new QueryWrapper<User>().lambda().in(User::getDeptId, deptId)
                    // 组织管理员不查询同级组织的组织管理员
                    .isNull(StringUtils.equals(userType, TypeUtil.TYPE_DEPT_MASTER) && StringUtils.equals(deptId, userDepartmentId), User::getType)
                    .and(StringUtils.isNotEmpty(name), q -> q.like(User::getName, name).or().like(User::getAlias, name))
                    .orderByDesc(User::getCreatedAt));
        }

        PageCL<UserViewVO> pageCL = new PageCL<>();
        pageCL.setPageSize(pageSize);
        pageCL.setPageNum(pageNum);
        Map<String, Boolean> userMFAMap =  mfaAuthorizeService.getUserMFAMap();;
        List<UserViewVO> viewVOS = Lists.newArrayList();
        page.getRecords().forEach(u ->{
            UserViewVO userViewVO = new UserViewVO();
            BeanUtils.copyProperties(u, userViewVO);
            if (StringUtils.isBlank(userViewVO.getAlias())) {
                userViewVO.setAlias(userViewVO.getName());
            }
            userViewVO.setExtra(parseUserExtra(userViewVO.getExtra(), CommonInstance.USER_EXTRA_DESCRIPTION_KEY));
            if (encryptionEnabled) {
                userViewVO.setPhone(null);
                userViewVO.setEmail(null);
            }
            userViewVO.setMfaEnabled(userMFAMap.containsKey(u.getId()));
            viewVOS.add(userViewVO);
        });
        pageCL.setList(viewVOS);
        pageCL.setTotal((int)page.getTotal());
        pageCL.setSize(pageSize);
        pageCL.setPageCount((int) ((page.getTotal()+pageSize-1) / pageSize));
        return pageCL;
    }

    private void initUserProjectRole(List<Map<String, String>> projectRoleIds, String userId) {
        if (!CollectionUtils.isEmpty(projectRoleIds)) {
            List<UserRoleMapping> assignments = Lists.newArrayList();
            projectRoleIds.forEach(map -> map.forEach((key, value) -> {
                UserRoleMapping userRoleMapping = new UserRoleMapping();
                userRoleMapping.setUserId(userId);
                userRoleMapping.setRoleId(value);
                userRoleMapping.setProjectId(key);
                assignments.add(userRoleMapping);

            }));
            if (assignments.size() > 0) {
                userRoleMappingService.saveBatch(assignments);
            }
        }
    }
    private void initUserRole(List<String> roleIds, String userId) {
        if (!CollectionUtils.isEmpty(roleIds)) {
            List<UserRoleMapping> assignments = Lists.newArrayList();
            roleIds.forEach(roleId ->{
                UserRoleMapping userRoleMapping = new UserRoleMapping();
                userRoleMapping.setUserId(userId);
                userRoleMapping.setRoleId(roleId);
                assignments.add(userRoleMapping);
            });
            if (assignments.size() > 0) {
                userRoleMappingService.saveBatch(assignments);
            }
        }
    }
    private void checkUserName (UserCreateOrUpdateVO user) {
        checkEmailAndPhone(user);

        User result = userMapper.getByName(user.getName());
        if (result != null) {
            throw new BusinessException("账号已存在");
        }
        boolean existed = userMapper.selectCount(new QueryWrapper<User>().lambda()
                .eq(User::getEmail, user.getEmail())
                .or()
                .eq(User::getPhone, user.getPhone())) > 0;
        if (existed) throw new BusinessException("电话或者邮箱已被占用");
    }

    /**
     * 修改用户判断
     *
     * @param user
     */
    public void updateUserCheck(UserCreateOrUpdateVO user) {
        checkEmailAndPhone(user);
        boolean existed = userMapper.selectCount(new QueryWrapper<User>().lambda()
                .ne(User::getId, user.getId())
                .and(wrapper -> wrapper
                        .eq(User::getEmail, user.getEmail())
                        .or()
                        .eq(User::getPhone, user.getPhone()))
                ) > 0;
        if (existed) throw new BusinessException("电话或者邮箱已被占用");
    }

    @Override
    public void checkInnerUserLogin(String username) {
        if (!"inner".equals(username)) {
            return;
        }
        if (HeaderParamConstant.FEIGN_ENABLE_VALUE.equals(request.getHeader(HeaderParamConstant.FEIGN_ENABLE_KEY))) {
            return;
        }

        String internetAccess = request.getHeader("InternetAccess");
        if ("true".equals(internetAccess)) {
            throw new BusinessException("inner用户禁止公网登录");
        }
        // 前面都满足的情况下，禁止web登录
        if (CommonUtils.isFromWeb(request)) {
            throw new BusinessException("inner用户禁止登录");
        }
    }

    @Override
    public UserViewVO getContactInfo(String username) {
        User user = this.getOne(new LambdaQueryWrapper<User>().eq(User::getName, username));
        if (Objects.isNull(user)) {
            throw new BusinessException("用户不存在");
        }
        if (encryptionEnabled) {
            user.setEmail(encryptAndDecryptUtil.decryptByIv(user.getEmail(), user.getIv()));
            user.setPhone(encryptAndDecryptUtil.decryptByIv(user.getPhone(), user.getIv()));
        }

        UserViewVO userViewVO = new UserViewVO();
        userViewVO.setPhone(user.getPhone());
        userViewVO.setEmail(user.getEmail());
        return userViewVO;
    }

    @Override
    public void checkPlatformUser(String username) {
        Assert.isTrue(StrUtil.isNotBlank(username), "用户名不能为空");
        // inner用户都可登录
        if ("inner".equals(username)) {
            return;
        }
        User user = this.getOne(new LambdaQueryWrapper<User>().eq(User::getName, username));
        Assert.isTrue(Objects.nonNull(user), "用户名/密码错误");
        if (globalsettingsService.getBmStatus()) {
            // 保密环境，禁止云平台用户登录
            if (CommonInstance.PLATFORM_ROLES.contains(user.getType())) {
                throw new BusinessException("用户名/密码错误");
            }
            // 如果是安全管理员，根据开关判断是否能登录
            if (TypeUtil.TYPE_SECURITY.equals(user.getType()) && !globalsettingsService.getSecurityAdminLoginStatus()) {
                throw new BusinessException("用户名/密码错误");
            }
        } else {
            // 云平台环境，禁止保密用户登录
            if (CommonInstance.BM_ROLES.contains(user.getType())) {
                throw new BusinessException("用户名/密码错误");
            }
        }
    }

    @Override
    public PageCL<UserViewVO> listOrgUser(String currentUserId, Integer pageNum, Integer pageSize, String name) {
        IPage<User> page = new Page<>(pageNum, pageSize);
        IPage<User> userIPage = new Page<>(pageNum, pageSize);
        User user = userMapper.selectById(currentUserId);

        // 保密->组织系统管理员/组织安全管理员/阻住审计管理员
        if (TypeUtil.TYPE_ORG_SYSADMIN.equals(user.getType())
                || TypeUtil.TYPE_ORG_SECADMIN.equals(user.getType())
                || TypeUtil.TYPE_ORG_SECAUDITOR.equals(user.getType())) {
            // 查询根部门
            Department rootDep = departmentService.getRootDepartmentByDeptId(user.getDeptId());
            // 查询根部门下的子部门
            List<DepartmentTreeDetailVO> subDeps = departmentService.getDeptsByDeptId(rootDep.getId());
            List<String> deptIds = new ArrayList<>();
            //添加根部门id
            deptIds.add(rootDep.getId());
            RecursionDeptIdUtils.getDeptIds(subDeps, deptIds);
            if (CollUtil.isNotEmpty(deptIds)) {
                userIPage = userMapper.selectPage(page, new QueryWrapper<User>().lambda()
                        .and(StringUtils.isNotEmpty(name), q -> q.like(User::getName, name).or().like(User::getAlias, name))
                        .in(User::getDeptId, deptIds)
                        .and(q->q.isNull(User::getType).or().notIn(User::getType, Lists.newArrayList(TypeUtil.TYPE_MASTER, TypeUtil.TYPE_DEPT_MASTER)))
                        .orderByDesc(User::getCreatedAt));
            }
        }
        PageCL<UserViewVO> pageCL = new PageCL<>();
        pageCL.setPageSize(pageSize);
        pageCL.setPageNum(pageNum);
        List<UserViewVO> viewVOS = Lists.newArrayList();
        userIPage.getRecords().forEach(u -> {
            UserViewVO userViewVO = new UserViewVO();
            BeanUtils.copyProperties(u, userViewVO);
            viewVOS.add(userViewVO);
        });
        pageCL.setList(viewVOS);
        pageCL.setTotal((int)userIPage.getTotal());
        pageCL.setSize(pageSize);
        pageCL.setPageCount((int) ((userIPage.getTotal()+pageSize-1) / pageSize));
        return pageCL;
    }

    /**
     * 批量绑定用户角色
     * @param userRoleVOS
     */
    @Override
    public void bindUserRole(List<UserRoleVO> userRoleVOS) {
        //节约性能，不校验Role 、 user 是否存在
        List<UserRoleMapping> usersRoles = Lists.newArrayList();
        userRoleVOS.forEach(userRoleVO -> {
            if (Objects.isNull(userRoleVO.getUserId()) || Objects.isNull(userRoleVO.getRoleId()))
                throw new BusinessException("用户ID或角色ID不能为空");
            UserRoleMapping userRoleMapping = mapper.map(userRoleVO, UserRoleMapping.class);
            //查询用户-角色是否存在
            List<UserRoleMapping> userRoleMappings = userRoleMappingMapper.selectList(new LambdaQueryWrapper<UserRoleMapping>()
                    .eq(UserRoleMapping::getUserId, userRoleVO.getUserId())
                    .eq(UserRoleMapping::getRoleId, userRoleVO.getRoleId())
                    .isNull(UserRoleMapping::getProjectId));
            //用户-角色不存在才保存
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(userRoleMappings)) {
                usersRoles.add(userRoleMapping);
            }
        });
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(usersRoles)) {
            userRoleMappingService.saveBatch(usersRoles);
        }

    }

    /**
     * 批量解绑用户角色
     * @param userRoleVOS
     */
    @Override
    public void unbindUserRole(List<UserRoleVO> userRoleVOS) {
        //单表操作不需要开启事务
        userRoleVOS.forEach(userRoleVO -> {
            if (Objects.isNull(userRoleVO.getUserId()) || Objects.isNull(userRoleVO.getRoleId()))
                throw new BusinessException("用户ID或角色ID不能为空");
            //数据不多直接一条一条删除
            userRoleMappingMapper.delete(new QueryWrapper<UserRoleMapping>().lambda()
                    .eq(UserRoleMapping::getUserId, userRoleVO.getUserId())
                    .eq(UserRoleMapping::getRoleId, userRoleVO.getRoleId()));
            //产品调研说绑定角色时不用和关联项目
        });
    }

    @Override
    public void bindUserProject(List<UserProjectVO> userProjectVOS) {
        //节约性能，不校验project 、 user 是否存在
        List<UserRoleMapping> usersProjects = Lists.newArrayList();
        userProjectVOS.forEach(userProjectVO -> {
            if (Objects.isNull(userProjectVO.getUserId()) || Objects.isNull(userProjectVO.getProjectId()))
                throw new BusinessException("用户ID或项目ID不能为空");
            UserRoleMapping userRoleMapping = mapper.map(userProjectVO, UserRoleMapping.class);
            //查询用户-项目是否存在
            List<UserRoleMapping> userRoleMappings = userRoleMappingMapper.selectList(new LambdaQueryWrapper<UserRoleMapping>()
                    .eq(UserRoleMapping::getUserId, userProjectVO.getUserId())
                    .eq(UserRoleMapping::getProjectId, userProjectVO.getProjectId())
                    .isNull(UserRoleMapping::getRoleId));
            //用户-项目不存在才保存
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(userRoleMappings)) {
                usersProjects.add(userRoleMapping);
            }
        });
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(usersProjects)) {
            userRoleMappingService.saveBatch(usersProjects);
        }
    }

    @Override
    public void unbindUserProject(List<UserProjectVO> userProjectVOS) {
        //单表操作不需要开启事务
        userProjectVOS.forEach(userProjectVO -> {
            if (Objects.isNull(userProjectVO.getUserId()) || Objects.isNull(userProjectVO.getProjectId()))
                throw new BusinessException("用户ID或项目ID不能为空");
            //数据不多直接一条一条删除
            userRoleMappingMapper.delete(new QueryWrapper<UserRoleMapping>().lambda()
                    .eq(UserRoleMapping::getUserId, userProjectVO.getUserId())
                    .eq(UserRoleMapping::getProjectId, userProjectVO.getProjectId()));
        });
    }

    private void checkEmailAndPhone(UserCreateOrUpdateVO user) {
        String emailPattern = "^[A-Za-z\\d]+([-_.][A-Za-z\\d]+)*@([A-Za-z\\d]+[-.])+[A-Za-z\\d]{2,4}$";
        boolean emailIsMatch = Pattern.matches(emailPattern, user.getEmail());
        if (!emailIsMatch) throw new BusinessException("邮箱格式有误");

        if (StringUtils.isNotBlank(phonePattern)) {
            boolean phoneIsMatch = Pattern.matches(phonePattern, user.getPhone());
            if (!phoneIsMatch) throw new BusinessException("手机号码格式不正确");
        }
    }

    private Role getAdminRole() {
        Role role = roleMapper.selectOne(new QueryWrapper<Role>().lambda().eq(Role::getName,"admin"));
        if (role == null) {
            throw new BusinessException("admin role not exist");
        }
        return role;
    }
    private Assignment initAssignment(User user, Project project, Role role) {
        Assignment assignment = new Assignment();
        assignment.setActorId(user.getId());
        assignment.setRoleId(role.getId());
        assignment.setTargetId(project.getId());
        assignment.setType("UserProject");
        assignment.setInherited(false);
        return assignment;
    }

    private Project initProject(User user, String id) {
        Project project = new Project();
        project.setDomain(true);
        project.setDomainId("<<keystone.domain.root>>");
        project.setId(id);
        project.setName(user.getName()+"_"+"project");
        project.setAlias(project.getName());
        project.setDeptId(user.getDeptId());
        project.setEnabled(true);
        return project;
    }

    private Project initProjectNoDepartment(User user, String id) {
        Project project = new Project();
        project.setDomain(true);
        project.setDomainId("<<keystone.domain.root>>");
        project.setId(id);
        project.setName(user.getName()+"_"+"project");
        project.setAlias(project.getName());
        project.setEnabled(true);
        return project;
    }

    private Department initDepartment(User user, String projectId,String departmentName) {
        Department department = new Department();
        department.setId(UUIDUtil.get32UUID());
        if (StringUtils.isNotEmpty(departmentName)) {
            if (departmentService.topParentDepartmentNameDuplication(departmentName, null)) {
                throw new BusinessException("组织名称已存在");
            }
            department.setName(departmentName);
        } else {
            department.setName(user.getName() + "_" + "dept");
        }
        department.setDomainId(projectId);
        if(StringUtils.isNoneBlank(user.getExtra())){
            JSONObject jsonObject = JSONObject.parseObject(user.getExtra());
            department.setDescription(jsonObject.getString(CommonInstance.USER_EXTRA_DESCRIPTION_KEY));
        }
        return department;
    }

    @Override
    public int update(UserCreateOrUpdateVO user) {
        User u = new User();
        BeanUtils.copyProperties(user, u);
        u.setPassword(null);
        if (user.isDepartmentManager()) {
            u.setType(TypeUtil.TYPE_DEPT_MASTER);

            innerRoleBind(user.getId(),TypeUtil.TYPE_DEPT_MASTER);
        } else {
            u.setType(" ");
            IamRole iamRole = iamRoleMapper.selectOne(new QueryWrapper<IamRole>().lambda().eq(IamRole::getType, TypeUtil.TYPE_DEPT_MASTER));
            removeRole(user.getId(), iamRole.getId());
        }
        return 0;
    }

    @Override
    public void batchAssignmentRoles(String userId, UserBindRolesVO userBindRolesVO) {
        if (Objects.isNull(userMapper.selectById(userId))) {
            throw new BusinessException("用户不存在ID:" + userId);
        }
        // 先删除用户已绑定的角色，再绑定（用户项目角色）
        //产品最新需求，用户绑定角色时不再关联项目
        /*if (StringUtils.isNotBlank(userBindRolesVO.getProjectId())) {
            userRoleMappingMapper.delete(new QueryWrapper<UserRoleMapping>().lambda()
                    .eq(UserRoleMapping::getUserId, userId)
                    .eq(UserRoleMapping::getProjectId, userBindRolesVO.getProjectId())
                    .isNotNull(UserRoleMapping::getRoleId));
        } else {
            //先删除用户已绑定的角色，再绑定（用户角色）
            userRoleMappingMapper.delete(new QueryWrapper<UserRoleMapping>().lambda()
                .eq(UserRoleMapping::getUserId, userId)
                .isNull(UserRoleMapping::getProjectId)
                .isNotNull(UserRoleMapping::getRoleId));
        }*/
        userRoleMappingMapper.delete(new QueryWrapper<UserRoleMapping>().lambda()
                .eq(UserRoleMapping::getUserId, userId)
                .isNotNull(UserRoleMapping::getRoleId));
        List<String> roleIds = userBindRolesVO.getRoleIds();
        if (CollectionUtils.isEmpty(roleIds)) {
            return;
        }
//        String projectId = userBindRolesVO.getProjectId();
        List<UserRoleMapping> userRoleMappings = roleIds.stream().map(roleId -> {
            UserRoleMapping userRoleMapping = new UserRoleMapping();
            /*if (StringUtils.isNotBlank(projectId)) {
                userRoleMapping.setProjectId(projectId);
            }*/
            userRoleMapping.setUserId(userId);
            userRoleMapping.setRoleId(roleId);
            return userRoleMapping;
        }).collect(Collectors.toList());
        userRoleMappingService.saveBatch(userRoleMappings);
        MessageVo messageVo = new MessageVo();
        messageVo.setSvc("sugoncloud-iam-api");
        messageVo.setEmailEnable(true);
        messageVo.setUser(userId);
        messageVo.setContent("您的用户角色权限已被变更");
        messageFeignService.createMessage(messageVo);
        //future 绑定角色 同步更新到Redis
        this.syncUserRole2Redis(userId);
    }

    @Override
    public void batchAssignmentProjects(String userId, UserBindProjectsVO userBindProjectsVO) {
        if (Objects.isNull(userMapper.selectById(userId))) {
            throw new BusinessException("用户不存在ID:" + userId);
        }
        // 查询用户已绑定的项目
        Set<String> originUserProjectSet = userRoleMappingMapper.selectList(new QueryWrapper<UserRoleMapping>().lambda()
                .eq(UserRoleMapping::getUserId, userId)
                .isNotNull(UserRoleMapping::getProjectId))
                .stream().map(UserRoleMapping::getProjectId).collect(Collectors.toSet());
        Set<String> projectIds = userBindProjectsVO.getProjectIds();

        // 需要删除的项目
        Sets.SetView<String> deleteItems = Sets.difference(originUserProjectSet, projectIds);
        if (!CollectionUtils.isEmpty(deleteItems)) {
            // 先删除用户已绑定的项目，再绑定
            userRoleMappingMapper.delete(new QueryWrapper<UserRoleMapping>().lambda()
                    .eq(UserRoleMapping::getUserId, userId)
                    .isNotNull(UserRoleMapping::getProjectId)
                    .in(UserRoleMapping::getProjectId, deleteItems));
        }
        // 需要新增的项目
        Sets.SetView<String> addItems = Sets.difference(projectIds, originUserProjectSet);
        if (CollectionUtils.isEmpty(addItems)) {
            return;
        }
        List<UserRoleMapping> userRoleMappings = addItems.stream().map(projectId -> {
            UserRoleMapping userRoleMapping = new UserRoleMapping();
            userRoleMapping.setProjectId(projectId);
            userRoleMapping.setUserId(userId);
            return userRoleMapping;
        }).collect(Collectors.toList());
        userRoleMappingService.saveBatch(userRoleMappings);
    }

    @Override
    public List<UserVo> getUsers(List<String> ids) {
        return BeanConvertUtils.convertListTo(userMapper.selectList(new QueryWrapper<User>().lambda().in(User::getId,ids)), UserVo::new);
    }

    @Override
    public PageCL<UserViewVO>  getUsersByProjectId(int pageNum, int pageSize, String projectId, String userId) throws Exception {
        Project project = projectMapper.selectById(projectId);
        if (Objects.isNull(project)){
            throw new BusinessException("项目ID"+projectId+"不存在");
        }
        List<User> users = userMapper.getUsersByProjectId(projectId);
        List<UserViewVO> viewVOS = Lists.newArrayList();
        users.stream().forEach(u -> {
            UserViewVO userViewVO = new UserViewVO();
            BeanUtils.copyProperties(u, userViewVO);
            if(StringUtils.isBlank(userViewVO.getAlias())){
                userViewVO.setAlias(userViewVO.getName());
            }
            userViewVO.setExtra(parseUserExtra(userViewVO.getExtra(), CommonInstance.USER_EXTRA_DESCRIPTION_KEY));
            if (encryptionEnabled) {
//                String cert = JSONObject.parseObject(user.getExtra()).getString(CommonInstance.ENCRYPTION_CONTENT_KEY);
//                String email = encryptAndDecryptUtil.decryptByIv(userViewVO.getEmail(), userViewVO.getIv());
                userViewVO.setEmail(null);
//                String phone = encryptAndDecryptUtil.decryptByIv(userViewVO.getPhone(), userViewVO.getIv());
                userViewVO.setPhone(null);
            }
            viewVOS.add(userViewVO);
        });
        PageCL<UserViewVO> pageInfo = new PageCL<>();
        pageInfo = pageInfo.getPageInfo(pageNum, pageSize, viewVOS);
        return pageInfo;
    }

    @Override
    public PageCL<UserViewVO> listAllUser(String currentUserId, Integer pageNum, Integer pageSize, String name) {
        IPage<User> page = new Page<>(pageNum, pageSize);
        IPage<User> userIPage = new Page<>(pageNum, pageSize);
        User user = userMapper.selectById(currentUserId);
        // 根账号
        if (TypeUtil.TYPE_MASTER.equals(user.getType())) {
            List<String> deptIds = Lists.newArrayList();
            List<DepartmentTreeDetailVO> treeDepartments = departmentMapper.findTreeDepartment(user.getDeptId());
            RecursionDeptIdUtils.getDeptIds(treeDepartments, deptIds);
            if(!CollectionUtils.isEmpty(deptIds)){
                userIPage = userMapper.selectPage(page, new QueryWrapper<User>().lambda()
                        .and(StringUtils.isNotEmpty(name), q -> q.like(User::getName, name).or().like(User::getAlias, name))
                        .in(User::getDeptId, deptIds)
                        .orderByDesc(User::getCreatedAt));
            }
        }
        // 组织管理员
        if (TypeUtil.TYPE_DEPT_MASTER.equals(user.getType())) {
            List<String> deptIds = Lists.newArrayList();
            deptIds.add(user.getDeptId());
            List<DepartmentTreeDetailVO> treeDepartments = departmentMapper.findTreeDepartment(user.getDeptId());
            RecursionDeptIdUtils.getDeptIds(treeDepartments, deptIds);
            //查询同组织的管理员
            List<User> departmentUsers = userMapper.selectList(new QueryWrapper<User>().lambda()
                    .eq(User::getDeptId, user.getDeptId())
                    .eq(User::getType, TypeUtil.TYPE_DEPT_MASTER));
            List<String> deptUserIds = departmentUsers.stream().map(User::getId).collect(Collectors.toList());
            deptUserIds.add(user.getId());
            userIPage = userMapper.selectPage(page, new QueryWrapper<User>().lambda()
                    .and(StringUtils.isNotEmpty(name), q -> q.like(User::getName, name).or().like(User::getAlias, name))
                    .in(User::getDeptId, deptIds)
                    // 组织管理员不查询同级组织的组织管理员
                    .notIn(User::getId, deptUserIds)
                    .orderByDesc(User::getCreatedAt));
        }
        // 保密->组织系统管理员/组织安全管理员
        if (TypeUtil.TYPE_ORG_SYSADMIN.equals(user.getType())
                || TypeUtil.TYPE_ORG_SECADMIN.equals(user.getType())) {
            List<String> deptIds = Lists.newArrayList();
            // 查询所有子组织
            List<DepartmentTreeDetailVO> treeDepartments = departmentMapper.findTreeDepartment(user.getDeptId());
            RecursionDeptIdUtils.getDeptIds(treeDepartments, deptIds);
            if (CollUtil.isNotEmpty(deptIds)) {
                userIPage = userMapper.selectPage(page, new QueryWrapper<User>().lambda()
                        .and(StringUtils.isNotEmpty(name), q -> q.like(User::getName, name).or().like(User::getAlias, name))
                        .in(User::getDeptId, deptIds)
                        .and(q->q.isNull(User::getType).or().notIn(User::getType, Lists.newArrayList(TypeUtil.TYPE_MASTER, TypeUtil.TYPE_DEPT_MASTER)))
                        .orderByDesc(User::getCreatedAt));
            }
        }
        // 管理员
        if (TypeUtil.TYPE_ADMIN.equals(user.getType())) {
            userIPage = userMapper.selectPage(page, new QueryWrapper<User>().lambda()
                    .and(StringUtils.isNotEmpty(name), q -> q.like(User::getName, name).or().like(User::getAlias, name))
                    .isNotNull(User::getDeptId)
                    .and(q->q.isNull(User::getType).or().in(User::getType, Lists.newArrayList(TypeUtil.TYPE_MASTER, TypeUtil.TYPE_DEPT_MASTER)))
                    .orderByDesc(User::getCreatedAt));
        }
        // 保密->管理员/安全管理员
        if (TypeUtil.TYPE_SYS_ADMIN.equals(user.getType()) || TypeUtil.TYPE_SEC_ADMIN.equals(user.getType())) {
            userIPage = userMapper.selectPage(page, new QueryWrapper<User>().lambda()
                    .and(StringUtils.isNotEmpty(name), q -> q.like(User::getName, name).or().like(User::getAlias, name))
                    .isNotNull(User::getDeptId)
                    .and(q->q.isNull(User::getType).or().in(User::getType, Lists.newArrayList(TypeUtil.TYPE_ORG_SYSADMIN, TypeUtil.TYPE_ORG_SECADMIN, TypeUtil.TYPE_ORG_SECAUDITOR)))
                    .orderByDesc(User::getCreatedAt));
        }
        // 安全管理员
        if (TypeUtil.TYPE_SECURITY.equals(user.getType())) {
            userIPage = userMapper.selectPage(page, new QueryWrapper<User>().lambda()
                    .and(StringUtils.isNotEmpty(name), q -> q.like(User::getName, name).or().like(User::getAlias, name))
                    .and(q -> q.ne(User::getName, "inner"))
                    .and(q -> q.isNotNull(User::getDeptId)
                            .or()
                            .eq(User::getType, TypeUtil.TYPE_ADMIN)
                            .or()
                            .eq(User::getType, TypeUtil.TYPE_AUDIT))
                    .and(q -> q.isNull(User::getType)
                            .or()
                            .notIn(User::getType, Lists.newArrayList(TypeUtil.TYPE_ORG_SYSADMIN, TypeUtil.TYPE_ORG_SECADMIN, TypeUtil.TYPE_ORG_SECAUDITOR)))
                    .orderByDesc(User::getCreatedAt));
        }

        PageCL<UserViewVO> pageCL = new PageCL<>();
        pageCL.setPageSize(pageSize);
        pageCL.setPageNum(pageNum);
        List<UserViewVO> viewVOS = Lists.newArrayList();
        List<DepartmentTreeDetailVO> departments = departmentMapper.findAllDepartmentList();
        Map<String, Boolean> userMFAMap = mfaAuthorizeService.getUserMFAMap();
        userIPage.getRecords().forEach(u -> {
            UserViewVO userViewVO = new UserViewVO();
            BeanUtils.copyProperties(u, userViewVO);
            if (StringUtils.isBlank(userViewVO.getAlias())) {
                userViewVO.setAlias(userViewVO.getName());
            }
            userViewVO.setExtra(parseUserExtra(userViewVO.getExtra(), CommonInstance.USER_EXTRA_DESCRIPTION_KEY));
            userViewVO.setDeptPath(DepartmentPathUtils.buildDeptPath(userViewVO.getDeptId(),departments));
            if (encryptionEnabled) {
                userViewVO.setEmail(null);
                userViewVO.setPhone(null);
            }
            userViewVO.setMfaEnabled(userMFAMap.containsKey(u.getId()));
            viewVOS.add(userViewVO);
        });
        pageCL.setList(viewVOS);
        pageCL.setTotal((int)userIPage.getTotal());
        pageCL.setSize(pageSize);
        pageCL.setPageCount((int) ((userIPage.getTotal()+pageSize-1) / pageSize));
        return pageCL;
    }

    @Override
    public List<User> listInnerUser() {
        List<User> innerUsers = userMapper.selectList(new QueryWrapper<User>().lambda()
                .eq(User::getType, TypeUtil.TYPE_ADMIN)
                .or()
                .eq(User::getType, TypeUtil.TYPE_AUDIT)
                .or()
                .eq(User::getType, TypeUtil.TYPE_SECURITY));
        return innerUsers;
    }

    public void updateUserHash(String userId) {
        if (encryptionEnabled) {
            User user = userMapper.selectById(userId);
            user.setHash(encryptAndDecryptUtil.sign(user.toEncryptAndDecryptJSONStr()));
            userMapper.update(null, new LambdaUpdateWrapper<User>()
                    .eq(User::getId, user.getId())
                    .set(User::getHash, user.getHash()));
        }
    }

    private void syncUserRole2Redis(String userId) {
        List<UserRoleMapping> userRoleMappingList = userRoleMappingService.lambdaQuery().eq(UserRoleMapping::getUserId, userId).list();
        StringBuilder roleIds = new StringBuilder();
        for (int i = 0; i < userRoleMappingList.size(); i++) {
            if (Objects.nonNull(userRoleMappingList.get(i).getRoleId())) {
                roleIds.append(userRoleMappingList.get(i).getRoleId());
                if (i < userRoleMappingList.size() -1) {
                    roleIds.append(",");
                }
            }
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("policy", roleIds.toString());
        redisTemplate.opsForValue().set(CommonInstance.IAM_ROLEDB_USERS+userId, jsonObject.toString());
    }

    @Override
    public void setUserDefaultResourceProject(String userId, String projectId, boolean cancel){
        Map<String, String> param = Maps.newHashMap();
        param.put("userId", userId);
        userMapper.deleteUserDefaultResurceProject(param);
        // 取消的选择时，直接返回
        if (cancel) {
            return;
        }
        param.put("projectId", projectId);
        userMapper.insertUserDefaultResurceProject(param);
    }

    @Override
    public UserOwnDeptAndProjectDTO getUserOwnDepartmentsAndProjects(String userId, String projectType) {
        User user = this.getById(userId);
        if (Objects.isNull(user)) {
            log.info("can't find user with userId=[{}].", userId);
            return new UserOwnDeptAndProjectDTO();
        }
        String userType = user.getType();
        List<String> deptIds = Lists.newArrayList();
        List<String> projects = null;
        String deptId = user.getDeptId();
        deptIds.add(deptId);
        // 普通用户只查询自己所属的项目集合
        if (StringUtils.isBlank(userType)) {
            projects = userRoleMappingMapper.selectList(new QueryWrapper<UserRoleMapping>().lambda()
                            .eq(UserRoleMapping::getUserId, userId)
                            .isNotNull(UserRoleMapping::getProjectId))
                    .stream().map(UserRoleMapping::getProjectId)
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(projects) && StringUtils.isNotBlank(projectType)) {
                projects = projectMapper.selectList(new LambdaQueryWrapper<Project>()
                                .select(Project::getId)
                                .eq(Project::getType, projectType)
                                .in(Project::getId, projects)
                        ).stream()
                        .map(Project::getId)
                        .collect(Collectors.toList());
            }
        } else if (TypeUtil.TYPE_DEPT_MASTER.equals(userType)
                || TypeUtil.TYPE_MASTER.equals(userType)
                || TypeUtil.TYPE_ORG_SYSADMIN.equals(userType)) {
            String departmentId = user.getDeptId();
            // 组织管理员/根账号查询自己所属组织及子级组织的项目集合
            List<DepartmentTreeDetailVO> treeDepartments = departmentMapper.findTreeDepartment(departmentId);
            RecursionDeptIdUtils.getDeptIds(treeDepartments, deptIds);
            projects = projectMapper.selectList(new QueryWrapper<Project>().lambda()
                            .eq(StringUtils.isNotBlank(projectType), Project::getType, projectType)
                            .in(Project::getDeptId, deptIds)
                            // 排除内置的project
                            .ne(Project::getId, user.getDefaultProjectId()))
                    .stream().map(Project::getId).collect(Collectors.toList());
        } else if (TypeUtil.TYPE_ADMIN.equals(userType)
                || TypeUtil.TYPE_SYS_ADMIN.equals(userType)) {
            deptIds = null;
            projects = projectMapper.selectList(new QueryWrapper<Project>().lambda()
                            .eq(StringUtils.isNotBlank(projectType), Project::getType, projectType)
                            .isNotNull(Project::getDeptId)
                            // 排除内置的project
                            .isNotNull(Project::getParentId))
                    .stream().map(Project::getId).collect(Collectors.toList());
        } else {
            deptIds = null;
            log.info("current user with userId=[{}],type=[{}],not belonging to any department.", userId, userType);
        }
        UserOwnDeptAndProjectDTO result = new UserOwnDeptAndProjectDTO();
        result.setProjects(projects);
        result.setDepartments(deptIds);
        // 填充projectInfos
        if (!CollectionUtils.isEmpty(projects)) {
            List<Project> projects1 = projectMapper
                    .selectList(new LambdaQueryWrapper<Project>()
                            .in(Project::getId, projects));
            List<ProjectDetailVO> projectDetailVOS = mapper.mapList(projects1
                    , ProjectDetailVO.class);
            for (ProjectDetailVO projectDetailVO : projectDetailVOS) {
                projects1.stream().filter(p -> p.getId().equals(projectDetailVO.getId())).findFirst()
                        .ifPresent(p -> {
                            if (StrUtil.isNotBlank(p.getAlias())) {
                                projectDetailVO.setName(p.getAlias());
                            }
                        });
            }
            result.setProjectInfos(projectDetailVOS);
        }
        return result;
    }

    @Override
    public PageCL<UserViewVO> getUsersByDeptIdAndUserIds(UserDeptAndUserIdsDTO userDeptAndUserIdsDTO) {
        if (CollectionUtil.isEmpty(userDeptAndUserIdsDTO.getUserIds())) {
            return new PageCL<>();
        }
        IPage<User> page = new Page(userDeptAndUserIdsDTO.getPageNum(), userDeptAndUserIdsDTO.getPageSize());
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(User::getId, userDeptAndUserIdsDTO.getUserIds())
                .eq(StrUtil.isNotBlank(userDeptAndUserIdsDTO.getDeptId()), User::getDeptId, userDeptAndUserIdsDTO.getDeptId())
                .like(StrUtil.isNotBlank(userDeptAndUserIdsDTO.getName()), User::getName, StrUtil.format("%{}%", userDeptAndUserIdsDTO.getName()));
        // notIn可以保证普通用户都能查询出来
        if (globalsettingsService.getBmStatus()) {
            queryWrapper.notIn(User::getType, TypeUtil.getInnerRoleTypes());
        } else {
            queryWrapper.notIn(User::getType, TypeUtil.getBmInnerRoleTypes());
        }
        IPage<User> userIPage = userMapper.selectPage(page, queryWrapper);


        PageCL<UserViewVO> pageCL = new PageCL<>();
        pageCL.setPageSize(userDeptAndUserIdsDTO.getPageSize());
        pageCL.setPageNum(userDeptAndUserIdsDTO.getPageNum());
        List<UserViewVO> viewVOS = Lists.newArrayList();
        List<DepartmentTreeDetailVO> departments = departmentMapper.findAllDepartmentList();
        page.getRecords().forEach(u -> {
            UserViewVO userViewVO = new UserViewVO();
            BeanUtils.copyProperties(u, userViewVO);
            if (StringUtils.isBlank(userViewVO.getAlias())) {
                userViewVO.setAlias(userViewVO.getName());
            }
            userViewVO.setExtra(parseUserExtra(userViewVO.getExtra(), CommonInstance.USER_EXTRA_DESCRIPTION_KEY));
            userViewVO.setDeptPath(DepartmentPathUtils.buildDeptPath(userViewVO.getDeptId(),departments));
            userViewVO.setEmail(null);
            userViewVO.setPhone(null);
            viewVOS.add(userViewVO);
        });
        pageCL.setList(viewVOS);
        pageCL.setPageNum((int)userIPage.getCurrent());
        pageCL.setPageSize((int)userIPage.getSize());
        pageCL.setTotal((int)userIPage.getTotal());
        pageCL.setPageCount((int)((userIPage.getTotal() + userIPage.getSize() - 1L) / userIPage.getSize()));
        pageCL.setSize((int)userIPage.getSize());
        return pageCL;
    }

    @Override
    public PageCL<UserViewVO> getUsersByCurrentUserIdAndDeptIdAndNotUserIds(UserDeptAndUserIdsDTO userDeptAndUserIdsDTO) {
        int pageNum = userDeptAndUserIdsDTO.getPageNum();
        int pageSize = userDeptAndUserIdsDTO.getPageSize();
        String name = userDeptAndUserIdsDTO.getName();
        boolean admin = userDeptAndUserIdsDTO.isAdmin();
        String deptId = userDeptAndUserIdsDTO.getDeptId();
        List<String> userIds = userDeptAndUserIdsDTO.getUserIds();
        IPage<User> page = new Page<>(pageNum, pageSize);
        IPage<User> userIPage = new Page<>(pageNum, pageSize);
        User user = userMapper.selectById(userDeptAndUserIdsDTO.getCurrentUserId());
        // 表示该节点只能绑定admin类型用户
        if (admin) {
            userIPage = userMapper.selectPage(page, new QueryWrapper<User>().lambda()
                    .eq(StrUtil.isNotBlank(deptId), User::getDeptId, deptId)
                    .like(StrUtil.isNotBlank(name), User::getName, name)
                    .eq(User::getType, globalsettingsService.getBmStatus() ? TypeUtil.TYPE_SYS_ADMIN : TypeUtil.TYPE_ADMIN)
                    .ne(User::getName, "inner")
                    .notIn(CollUtil.isNotEmpty(userIds), User::getId, userIds)
                    .orderByDesc(User::getCreatedAt));
        } else {
            // 根账号
            if (TypeUtil.TYPE_MASTER.equals(user.getType())) {
                List<String> deptIds = Lists.newArrayList();
                List<DepartmentTreeDetailVO> treeDepartments = departmentMapper.findTreeDepartment(user.getDeptId());
                RecursionDeptIdUtils.getDeptIds(treeDepartments, deptIds);
                if (StrUtil.isNotBlank(deptId)) {
                    deptIds.stream().filter(p -> p.equals(deptId)).collect(Collectors.toList());
                }
                if(!CollectionUtils.isEmpty(deptIds)){
                    userIPage = userMapper.selectPage(page, new QueryWrapper<User>().lambda()
                            .and(StringUtils.isNotEmpty(name), q -> q.like(User::getName, name).or().like(User::getAlias, name))
                            .in(User::getDeptId, deptIds)
                            .notIn(CollectionUtil.isNotEmpty(userIds), User::getId, userIds)
                            .orderByDesc(User::getCreatedAt));
                }
            }
            // 组织管理员
            if (TypeUtil.TYPE_DEPT_MASTER.equals(user.getType())) {
                List<String> deptIds = Lists.newArrayList();
                deptIds.add(user.getDeptId());
                List<DepartmentTreeDetailVO> treeDepartments = departmentMapper.findTreeDepartment(user.getDeptId());
                RecursionDeptIdUtils.getDeptIds(treeDepartments, deptIds);
                //查询同组织的管理员
                List<User> departmentUsers = userMapper.selectList(new QueryWrapper<User>().lambda()
                        .eq(User::getDeptId, user.getDeptId())
                        .eq(User::getType, TypeUtil.TYPE_DEPT_MASTER));
                List<String> deptUserIds = departmentUsers.stream().map(u->u.getId()).collect(Collectors.toList());
                deptUserIds.add(user.getId());
                if (userIds != null) {
                    deptUserIds.addAll(userIds);
                }
                if (StrUtil.isNotBlank(deptId)) {
                    deptIds.stream().filter(p -> p.equals(deptId)).collect(Collectors.toList());
                }
                if(!CollectionUtils.isEmpty(deptIds)) {
                    userIPage = userMapper.selectPage(page, new QueryWrapper<User>().lambda()
                            .and(StringUtils.isNotEmpty(name), q -> q.like(User::getName, name).or().like(User::getAlias, name))
                            .in(User::getDeptId, deptIds)
                            // 组织管理员不查询同级组织的组织管理员
                            .notIn(User::getId, deptUserIds)
                            .orderByDesc(User::getCreatedAt));
                }
            }

            // 管理员
            if (TypeUtil.TYPE_ADMIN.equals(user.getType())) {
                userIPage = userMapper.selectPage(page, new QueryWrapper<User>().lambda()
                        .and(StringUtils.isNotEmpty(name), q -> q.like(User::getName, name).or().like(User::getAlias, name))
                        .isNotNull(User::getDeptId)
                        .eq(StrUtil.isNotBlank(deptId), User::getDeptId, deptId)
                        .notIn(CollectionUtil.isNotEmpty(userIds), User::getId, userIds)
                        .and(q->q.isNull(User::getType).or().ne(User::getType, TypeUtil.TYPE_ADMIN))
                        .orderByDesc(User::getCreatedAt));
            }
            // 安全管理员
            if (TypeUtil.TYPE_SECURITY.equals(user.getType())) {
                userIPage = userMapper.selectPage(page, new QueryWrapper<User>().lambda()
                        .and(StringUtils.isNotEmpty(name), q -> q.like(User::getName, name).or().like(User::getAlias, name))
                        .notIn(CollectionUtil.isNotEmpty(userIds), User::getId, userIds)
                        .and(q -> q.isNotNull(User::getDeptId)
                                .or()
                                .eq(User::getType, TypeUtil.TYPE_ADMIN)
                                .or()
                                .eq(User::getType, TypeUtil.TYPE_AUDIT))
                        .orderByDesc(User::getCreatedAt));
            }
        }

        PageCL<UserViewVO> pageCL = new PageCL<>();
        pageCL.setPageSize(pageSize);
        pageCL.setPageNum(pageNum);
        List<UserViewVO> viewVOS = Lists.newArrayList();
        List<DepartmentTreeDetailVO> departments = departmentMapper.findAllDepartmentList();
        userIPage.getRecords().forEach(u -> {
            UserViewVO userViewVO = new UserViewVO();
            BeanUtils.copyProperties(u, userViewVO);
            if (StringUtils.isBlank(userViewVO.getAlias())) {
                userViewVO.setAlias(userViewVO.getName());
            }
            userViewVO.setExtra(parseUserExtra(userViewVO.getExtra(), CommonInstance.USER_EXTRA_DESCRIPTION_KEY));
            userViewVO.setDeptPath(DepartmentPathUtils.buildDeptPath(userViewVO.getDeptId(),departments));
            userViewVO.setEmail(null);
            userViewVO.setPhone(null);
            viewVOS.add(userViewVO);
        });
        pageCL.setList(viewVOS);
        pageCL.setTotal((int)userIPage.getTotal());
        pageCL.setSize(pageSize);
        pageCL.setPageCount((int) ((userIPage.getTotal()+pageSize-1) / pageSize));
        return pageCL;
    }

    @Override
    public String unlockUser(String userId) {
        User user = userMapper.selectById(userId);
        if (Objects.isNull(user)) {
            log.info("当前用户:{}不存在，直接返回成功", userId);
            return userId;
        }

        // 删除redis中的锁
        String lockUser = CommonInstance.UNAME_ERROR_LOCK + user.getName();
        redisTemplate.delete(lockUser);
        // 删除redis中用于判断错误次数的key
        redisTemplate.delete(CommonInstance.UNAME_ERROR_TIMES+user.getName());

        return user.getName();
    }

    @Override
    public String setAccessControl(AccessControlVO accessControlVO, String userId) {

        // 如果设置了开始和结束时间，需要进行比对，结束时间必须大于等于开始时间
        if (StringUtils.isNoneBlank(accessControlVO.getStartDate(), accessControlVO.getEndDate())) {
            if (DateUtils.parseDate(accessControlVO.getEndDate()).compareTo(DateUtils.parseDate(accessControlVO.getStartDate())) < 0) {
                throw new BusinessException("入参有误，开始时间小于结束时间！");
            }
        }

        // 校验用户是否存在
        User user = userMapper.selectById(userId);
        if (Objects.isNull(user)) {
            throw new BusinessException("用户ID不存在:" + userId);
        }

        // 设置ip白名单
        LambdaUpdateWrapper<User> updateWrapper = new LambdaUpdateWrapper<User>()
                .set(User::getAllowIp, accessControlVO.getIp())
                .eq(User::getId, userId);
        userMapper.update(null, updateWrapper);
        if (encryptionEnabled) {
            this.updateUserHash(userId);
        }

        // 设置访问时间策略
        // 如果时间限制标识为空，则默认设置为false
        if (Objects.isNull(accessControlVO.getLimitFlag())) {
            accessControlVO.setLimitFlag(Boolean.FALSE);
        }
        UserAccessEntity userAccess = userAccessService.getAccessByUserId(userId);
        if (Objects.nonNull(userAccess)) {
            UserAccessEntity updateAccess = new UserAccessEntity();
            BeanUtils.copyProperties(accessControlVO, updateAccess);
            updateAccess.setId(userAccess.getId());
            userAccessService.updateAccessById(updateAccess);
        } else {
            if (StringUtils.isNotBlank(accessControlVO.getStartDate()) || StringUtils.isNotBlank(accessControlVO.getEndDate()) || Boolean.TRUE.equals(accessControlVO.getLimitFlag())) {
                UserAccessEntity insertAccess = new UserAccessEntity();
                BeanUtils.copyProperties(accessControlVO, insertAccess);
                insertAccess.setUserId(userId);
                insertAccess.setIsDelete(Boolean.FALSE);
                userAccessService.save(insertAccess);
            }
        }

        // 进行消息发送
        MessageVo messageVo = new MessageVo();
        messageVo.setSvc("sugoncloud-iam-api");
        messageVo.setEmailEnable(true);
        messageVo.setUser(userId);
        messageVo.setContent("您的用户已设置访问权限");
        messageFeignService.createMessage(messageVo);
        messageVo.setUser(securityAdminUserId);
        messageVo.setContent("[" + user.getName() + "]用户已设置访问权限");
        messageFeignService.createMessage(messageVo);

        return user.getName();
    }
}
