package com.sugon.cloud.iam.api.mapper;

import com.sugon.cloud.iam.api.entity.CloudViewGatewayUser;
import com.sugon.cloud.iam.api.entity.EcsUsedQuota;
import com.sugon.cloud.iam.api.entity.EvsUsedQuota;
import com.sugon.cloud.iam.api.entity.OriginKeystoneUser;
import com.sugon.cloud.iam.api.entity.keystone.Project;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface LocalUserMapper {
    List<CloudViewGatewayUser> queryGatewayUsers();

    Project getDefaultProjectByUserName(String userName);

    List<OriginKeystoneUser> queryKeystoneUsers();

    String getKeystoneAdminProjectId();

    void updateIamProjectIdById(String keystoneAdminProjectId, String iamAdminProjectId);

    EcsUsedQuota getEcsUsedQuotaByPurpose(String purpose, String projectId);

    String getTopDeptId(String masterUserName);

    List<EvsUsedQuota> getEvsVolumeUsedQuota(String projectId);

    long getEvsVolumeSnapshotsUsedQuota(String projectId);
}
