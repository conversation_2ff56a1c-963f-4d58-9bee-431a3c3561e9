package com.sugon.cloud.iam.api.config;

import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/05/16 16:55
 **/
public class MFAMappingJackson2HttpMessageConverter extends MappingJackson2HttpMessageConverter {
    public MFAMappingJackson2HttpMessageConverter(){
        List<MediaType> mediaTypes = new ArrayList<>();
        mediaTypes.add(MediaType.APPLICATION_OCTET_STREAM);
        setSupportedMediaTypes(mediaTypes);
    }
}
