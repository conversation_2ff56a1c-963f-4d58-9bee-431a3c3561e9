package com.sugon.cloud.iam.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.constants.HeaderParamConstant;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.iam.api.entity.UserMicroEntity;
import com.sugon.cloud.iam.api.entity.UserMicroViewer;
import com.sugon.cloud.iam.api.service.UserMicroService;
import com.sugon.cloud.iam.api.service.resourceauth.UserIdResourceAuthHandler;
import com.sugon.cloud.iam.api.service.resourceauth.UserMicroAuthHandler;
import com.sugon.cloud.resource.auth.common.aop.ResourceAuth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

@Api(tags = "用户收藏API")
@RequestMapping(value = "/api/users-micro")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RestController
@DocumentIgnore
public class UserMicroController {

    /**
     * 服务对象
     */
    private final UserMicroService userMicroService;

    private final HttpServletRequest request;

    @GetMapping("/user")
    @ApiOperation("查询用户收藏表")
    public ResultModel<List<UserMicroViewer>> getByUser(HttpServletRequest request) {
        LambdaQueryWrapper<UserMicroEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserMicroEntity::getUserId, request.getHeader(HeaderParamConstant.USER_ID));
        List<UserMicroViewer> userMicroViewers = this.userMicroService.queryList(request.getHeader(HeaderParamConstant.USER_ID));
        if (CollectionUtils.isEmpty(userMicroViewers)) {
            return ResultModel.success(userMicroViewers);
        }
        String internetAccess = request.getHeader("InternetAccess");
        boolean isInternet = false;
        if (Objects.nonNull(internetAccess)) {
            isInternet = Boolean.parseBoolean(internetAccess);
        }
        // 如果是外网的话，将连接设置public_link
        if (isInternet) {
            userMicroViewers.forEach(userMicroViewer -> userMicroViewer.setLink(userMicroViewer.getPublicLink()));
        }
        return ResultModel.success(userMicroViewers);
    }


    @PostMapping
    @ApiOperation("创建收藏")
    public ResultModel<Boolean> create(@Validated @RequestBody UserMicroEntity userMicroEntity) {
        LambdaQueryWrapper<UserMicroEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserMicroEntity::getUserId, request.getHeader(HeaderParamConstant.USER_ID));
        int num = userMicroService.count(wrapper);
        wrapper.eq(UserMicroEntity::getMicroId,userMicroEntity.getMicroId());
        if(userMicroService.count(wrapper)>0){
            return ResultModel.error("已存在收藏");
        }
        userMicroEntity.setUserId(request.getHeader(HeaderParamConstant.USER_ID));
        userMicroEntity.setSort(num+1);
        return ResultModel.success("创建收藏成功",  userMicroService.save(userMicroEntity));
    }

    @PutMapping("/user")
    @ApiOperation("修改收藏")
    @ResourceAuth(handler = UserIdResourceAuthHandler.class, resources = "#userMicroEntities[0].userId")
    public ResultModel<Boolean> update(@Validated @RequestBody List<UserMicroEntity> userMicroEntities) {
        LambdaQueryWrapper<UserMicroEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserMicroEntity::getUserId, request.getHeader(HeaderParamConstant.USER_ID));
        userMicroService.remove(wrapper);
        return ResultModel.success("修改收藏成功", userMicroService.saveOrUpdateBatch(userMicroEntities));
    }

    /**
     * 删除单条收藏
     *
     * @param id 主键
     * @return 单条数据
     */
    @DeleteMapping("/{id}")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "Id", required = true, dataType = "String", paramType = "path")})
    @ApiOperation("删除单条收藏")
    @ResourceAuth(handler = UserMicroAuthHandler.class, resources = "#id")
    public ResultModel<Boolean> delete(@PathVariable("id") String id) {
        return ResultModel.success("删除收藏成功", this.userMicroService.removeById(id));
    }


    @DeleteMapping("/user")
    @ApiOperation("删除用户所有收藏")
    public ResultModel<Boolean> deleteByUser() {
        LambdaQueryWrapper<UserMicroEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserMicroEntity::getUserId, request.getHeader(HeaderParamConstant.USER_ID));
        return ResultModel.success("删除用户收藏成功", this.userMicroService.remove(wrapper));
    }
}
