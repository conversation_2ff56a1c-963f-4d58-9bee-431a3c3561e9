package com.sugon.cloud.iam.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sugon.cloud.iam.api.entity.keystone.User;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface UserMapper extends BaseMapper<User> {

    String getProjectIdByUserName(String name);

    User getByName(String name);

    List<User> getUsersByProjectId(String projectId);

    void insertUserDefaultResurceProject(Map map);

    void deleteUserDefaultResurceProject(Map map);

    String getUserDefaultProjectId(String userId);
}
