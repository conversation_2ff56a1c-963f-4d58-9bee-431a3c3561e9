package com.sugon.cloud.iam.api.config;


import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@RefreshScope
public class BeanInitConfig {

    /**
     * 渔翁签名验签服务器
     * @return
     */
    @ConditionalOnExpression("${mfa.enabled} && '${mfa.type}'.equals('fisherman')")
    @Bean(initMethod = "init", destroyMethod = "close")
    public FmDsvsSignConfig createDsvs() {
        return new FmDsvsSignConfig();
    }

    /**
     * 得安签名验签服务器
     * @return
     */
    @ConditionalOnExpression("${mfa.enabled} && '${mfa.type}'.equals('dean')")
    @Bean(initMethod = "init", destroyMethod = "close")
    public DeanSignConfig createDeanConfig() {
        return new DeanSignConfig();
    }


    /**
     * 信安世纪签名验签服务器
     * @return
     */
    @ConditionalOnExpression("${mfa.enabled} && '${mfa.type}'.equals('infoSec')")
    @Bean(initMethod = "init", destroyMethod = "close")
    public InfoSecSignConfig createInfoSecSignConfig() {
        return new InfoSecSignConfig();
    }


}
