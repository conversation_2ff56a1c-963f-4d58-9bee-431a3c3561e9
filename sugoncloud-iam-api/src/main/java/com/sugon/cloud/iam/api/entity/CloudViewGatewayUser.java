package com.sugon.cloud.iam.api.entity;

import com.sugon.cloud.iam.api.entity.keystone.Project;
import lombok.Data;

import java.util.Date;

/**
 * (LocalUser)实体类
 *
 * <AUTHOR>
 * @since 2021-03-26 11:35:36
 */
@Data
public class CloudViewGatewayUser {
    private String id;
    private String name;
    private String ownerId;
    private String email;
    private String mobile;
    private String password;
    private String salt;
    private Date createAt;
    private Project gatewayDefaultProject;
}
