package com.sugon.cloud.iam.api.service.resourceauth;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.sugon.cloud.resource.auth.common.service.impl.BaseResourceAuthHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: yangdingshan
 * @Date: 2024/1/10 16:16
 * @Description:
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class UserIdResourceAuthHandler extends BaseResourceAuthHandler {


    @Override
    public List<String> getUserResource(String userId) {
        return Lists.newArrayList(userId);
    }
}
