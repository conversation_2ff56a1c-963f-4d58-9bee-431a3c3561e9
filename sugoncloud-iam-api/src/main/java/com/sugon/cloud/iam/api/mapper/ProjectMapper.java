package com.sugon.cloud.iam.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sugon.cloud.iam.api.entity.keystone.Project;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface ProjectMapper extends BaseMapper<Project> {

    Project findById(String id);

    Project findByUserIdAndRoleId(String userId, String roleId);

    List<Project> findByUserId(String userId);

    List<Project> findByMap(Map map);

}
