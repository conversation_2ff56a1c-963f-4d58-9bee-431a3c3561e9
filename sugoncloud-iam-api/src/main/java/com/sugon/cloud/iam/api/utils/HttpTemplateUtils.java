package com.sugon.cloud.iam.api.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.poi.ss.formula.functions.T;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: yangdingshan
 * @Date: 2024/6/14 11:58
 * @Description:
 */
public class HttpTemplateUtils {

    private static final Logger logger = LoggerFactory.getLogger (HttpTemplateUtils.class);
    private static final RestTemplate restTemplate = new RestTemplate();

    public static  <T> ResponseEntity<T> post(String url, Object request, HttpHeaders headers, Class<T> responseType, Object... uriVariables) {
        headers = setHeader(headers);
        return restTemplate.exchange(url, HttpMethod.POST, new HttpEntity<>(request, headers), responseType, uriVariables);
    }

    public static  <T> T post(String url, Object request, HttpHeaders headers, TypeReference<T> type, Object... uriVariables) {
        if (!url.startsWith("http")) {
            url = "http://" + url;
        }
        headers = setHeader(headers);
        ResponseEntity<String> exchange = restTemplate.exchange(url, HttpMethod.POST, new HttpEntity<>(request, headers), String.class, uriVariables);
        if (exchange.getStatusCode() != HttpStatus.OK) {
            logger.error("请求接口【" + url + "】失败:{}", JacksonUtils.writeAsJsonString(exchange));
            throw new RuntimeException("请求接口【" + url + "】失败");
        }
        return JacksonUtils.parseObject(exchange.getBody(), type);
    }

    public static <T> ResponseEntity<T> post(String url, String jsonParam, HttpHeaders headers, Class<T> responseType) {
        headers = setHeader(headers);
        return restTemplate.exchange(url, HttpMethod.POST, new HttpEntity<>(jsonParam, headers), responseType);
    }

    public static <T> ResponseEntity<T> put(String url, String jsonParam, HttpHeaders headers, Class<T> responseType) {
        headers = setHeader(headers);
        return restTemplate.exchange(url, HttpMethod.PUT, new HttpEntity<>(jsonParam, headers), responseType);
    }

    public static <T> ResponseEntity<T> get(String url, HttpHeaders headers, Class<T> responseType) {
        headers = setHeader(headers);
        return restTemplate.exchange(url, HttpMethod.GET, new HttpEntity<>(headers), responseType);
    }
    public static <T> ResponseEntity<T> head(String url, HttpHeaders headers, Class<T> responseType) {
        headers = setHeader(headers);
        return restTemplate.exchange(url, HttpMethod.HEAD, new HttpEntity<>(headers), responseType);
    }
    public static <T> ResponseEntity<T> get(String url, Map<String, Object> paramMap, HttpHeaders headers, Class<T> responseType) {
        headers = setHeader(headers);
        return restTemplate.exchange(url, HttpMethod.GET, new HttpEntity<>(headers), responseType, paramMap);
    }

    public static <T> T get(String url, Map<String, Object> paramMap, HttpHeaders headers, TypeReference<T> type) {
        if (!url.startsWith("http")) {
            url = "http://" + url;
        }
        headers = setHeader(headers);
        if (paramMap == null) {
            paramMap = new HashMap<>();
        }
        ResponseEntity<String> exchange = restTemplate.exchange(url, HttpMethod.GET, new HttpEntity<>(headers), String.class, paramMap);
        if (exchange.getStatusCode() != HttpStatus.OK) {
            logger.error("请求接口【" + url + "】失败:{}", JacksonUtils.writeAsJsonString(exchange));
            throw new RuntimeException("请求接口【" + url + "】失败");
        }
        return JacksonUtils.parseObject(exchange.getBody(), type);
    }


    public static <T> ResponseEntity<T> delete(String url, String jsonParam, HttpHeaders headers, Class<T> responseType) {
        headers = setHeader(headers);
        return restTemplate.exchange(url, HttpMethod.DELETE, new HttpEntity<>(jsonParam, headers), responseType);
    }
    public static <T> ResponseEntity<T> delete(String url, HttpHeaders headers, Class<T> responseType) {
        headers = setHeader(headers);
        return restTemplate.exchange(url, HttpMethod.DELETE, new HttpEntity<>(headers), responseType);
    }

    public static HttpHeaders setHeader(HttpHeaders headers){
        if (Objects.isNull(headers)) {
            headers = new HttpHeaders();
        }
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String key = headerNames.nextElement();
                String value = request.getHeader(key);
                if(!headers.containsKey(key)){
                    headers.add(key, value);
                }
            }
        }
        return headers;
    }
}
