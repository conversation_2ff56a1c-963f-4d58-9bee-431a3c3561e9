package com.sugon.cloud.iam.api.service.ukeyImpl;

import com.fisherman.api.FmApi;
import com.sugon.cloud.iam.api.config.FmDsvsSignConfig;
import com.sugon.cloud.iam.api.service.UkeyService;
import com.sugon.cloud.iam.common.model.vo.MfaVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Base64;

/**
 * 渔翁签名验签服务器 渔翁的key
 */
@Service
@Slf4j
@ConditionalOnExpression("'${mfa.type}'.contains('fisherman')")
@RefreshScope
public class FishermanUkeyServiceImpl implements UkeyService {

    @Autowired(required = false)
    private FmDsvsSignConfig fmDsvsSignConfig;

    public static void main(String[] args) {
        FmApi api = new FmApi();
        String ip = "**************";
        String[] ips = new String[1];
        ips[0] = ip;
        api.FM_DSVS_Connection(ips, 2020, 5);
        System.out.println("连接成功");

//        // 6签名
//        String s1 = "12345678";
//        byte[] data = s1.getBytes();
//        // 131585为SM2 65538为RSA 也可传入16进制
//        byte[] sign = api.FM_DSVS_SignData(131585, 6, "12345678", data);
//        System.out.println("签名成功");
//        System.out.println(Arrays.toString(sign));

        // 7.2直接将证书放入验签
        String cert = "MIIDrjCCA1KgAwIBAgIIdB8ADwEHcFAwDAYIKoEcz1UBg3UFADBmMQswCQYDVQQGEwJDTjEOMAwGA1UECAwFaGViZWkxFTATBgNVBAcMDHNoaWppYXpodWFuZzEOMAwGA1UECgwFaGViY2ExDjAMBgNVBAsMBWhlYmNhMRAwDgYDVQQDDAdIQlNNMkNBMB4XDTIzMTExNzAzMTM1NVoXDTI0MTExNzE1NTk1OVowgcExCzAJBgNVBAYTAkNOMRIwEAYDVQQIDAnmsrPljJfnnIExDjAMBgNVBAoMBWhlYmNhMQ4wDAYDVQQLDAVoZWJjYTEKMAgGA1UECwwBMDEbMBkGA1UEAQwSMTMwMTExMjAyMzAxMDEwMDE4MQ8wDQYIKoEchu9KAQMMATAxJDAiBgNVBCoMG+a4lOe/geS4quS6uua1i+ivleS4gDY2NTU1NjEeMBwGA1UEAwwV5riU57+B5Liq5Lq65rWL6K+V5LiAMFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAENZy89QUpHAejvqViIrZ5mpRYABP5HDYxFmYgvXa9phD4gsVGu4QKPNb6YWQOoHUfEaQGYkGknWsNH8OVWyrOdaOCAYowggGGMAwGA1UdEwQFMAMBAQAwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMEMA4GA1UdDwEB/wQEAwIAwDARBglghkgBhvhCAQEEBAMCAIAwQgYDVR0gBDswOTA3BggqgRyG70oBBzArMCkGCCsGAQUFBwIBFh1odHRwczovL3d3dy5oZWJjYS5jb20vY3BzLnBkZjAnBggqgRyG70oBBAQbMUA2MDIxU0YwMTMwMTExMjAyMzAxMDEwMDE4MB8GA1UdIwQYMBaAFHowvaXh9jXCrV0PZtV1Wbo+hZUdMD0GA1UdHwQ2MDQwMqAwoC6GLGh0dHA6Ly9jcmwuaGViY2EuY29tL2NybGRvd25sb2FkL0hCU00yQ0EuY3JsMEgGCCsGAQUFBwEBBDwwOjA4BggrBgEFBQcwAoYsaHR0cDovL2NybC5oZWJjYS5jb20vY3JsZG93bmxvYWQvSEJTTTJDQS5jZXIwHQYDVR0OBBYEFIlqVTw7NRSO2Wn+b5/Wz4gucDkyMAwGCCqBHM9VAYN1BQADSAAwRQIgP/QySk0YZGW4FNpbm99p4KgXGdQdtGaUioZRlotMiwkCIQCK6+/afyDgld6zOYPOonCUQ7b4kjMeEm42tAPHbhztWw==";
        //原文 要先base64 12345678
        String s1 = "YWRtaW46a2V5c3RvbmVfc3Vnb246NEQ4RkIzNEExNUYxQkIzNUIxMTAzQkI4RkRGNDg4N0I=";
        byte[] data = s1.getBytes();
        //签名
        String signString = "MEUCIQDbpXOgEVUlhU2avykIEFS+FT+JxqHTHkbmTbx8yu57QgIgPuaM+47wTwL6dPoisrVYbJelDo4URABzJ99tINB4Uew=";
        byte[] sign = signString.getBytes();

        int state = api.FM_DSVS_VerifySignedData(1, cert.getBytes(), null,
                Base64.getDecoder().decode(data), Base64.getDecoder().decode(sign), 0);
        System.out.println(state);
        if (state == 0) {
            System.out.println("直接将证书放入验签成功");
        } else {
            System.out.println("直接将证书放入验签失败");
        }


//        byte[] random = api.FM_DSVS_GenRandomData(32);
//        System.out.println("生成随机数");
//        System.out.println(DatatypeConverter.printHexBinary(random));

        api.FM_DSVS_CloseConnection();
        System.out.println("关闭连接");
    }

    /**
     * 双因子验签
     * @param mfaVo
     * @return
     */
    @Override
    public boolean verifyData(MfaVo mfaVo){
        byte[] data = mfaVo.getInData().getBytes();
        byte[] sign = mfaVo.getSignNature().getBytes();
        String cert = mfaVo.getCert();
        mfaVo.setInData(Base64.getEncoder().encodeToString(mfaVo.getInData().getBytes()));
        FmApi api = fmDsvsSignConfig.getDsvsApi();
        int state = api.FM_DSVS_VerifySignedData(1, cert.getBytes(), null,
                data, Base64.getDecoder().decode(sign), 0);
        boolean result = false;
        if (state == 0) {
            result = true;
        }
        return result;
    }


}
