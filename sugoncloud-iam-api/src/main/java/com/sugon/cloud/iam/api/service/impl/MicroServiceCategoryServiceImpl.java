package com.sugon.cloud.iam.api.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.sugon.cloud.common.mapper.ModelMapper;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.common.utils.I18nUtil;
import com.sugon.cloud.common.utils.UUIDUtil;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.entity.BlacklistMicroService;
import com.sugon.cloud.iam.api.entity.MicroService;
import com.sugon.cloud.iam.api.entity.MicroServiceCategory;
import com.sugon.cloud.iam.api.exception.BusinessException;
import com.sugon.cloud.iam.api.mapper.MicroServiceCategoryMapper;
import com.sugon.cloud.iam.api.mapper.MicroServiceMapper;
import com.sugon.cloud.iam.api.mapper.QuotaTypeMapper;
import com.sugon.cloud.iam.api.service.BlacklistMicroServiceService;
import com.sugon.cloud.iam.api.service.MicroServiceCategoryService;
import com.sugon.cloud.iam.api.vo.CreateOrUpdateMicroServiceCategoryVO;
import com.sugon.cloud.iam.api.vo.MicroServiceCategoryDetailVO;
import com.sugon.cloud.iam.common.enums.BlacklistTypeEnum;
import com.sugon.cloud.iam.common.model.vo.MicroServiceDetailVO;
import com.sugon.cloud.iam.common.model.vo.QuotaType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * (MicroServiceCategory)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-04-23 09:45:37
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class MicroServiceCategoryServiceImpl implements MicroServiceCategoryService {
    private final MicroServiceCategoryMapper microServiceCategoryMapper;
    private final MicroServiceMapper microServiceMapper;
    private final ModelMapper mapper;
//    private final LicenseVerify licenseVerify;
    private final QuotaTypeMapper quotaTypeMapper;

    private final BlacklistMicroServiceService blacklistMicroServiceService;

    @Value("${black.enable:false}")
    private boolean blackEnable;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public MicroServiceCategory queryById(String id) {
        return this.microServiceCategoryMapper.selectById(id);
    }

    /**
     * 查询多条数据
     *
     * @param name 查询名称
     * @param pageNum 查询起始位置
     * @param pageSize 查询条数
     * @return 对象列表
     */
    @Override
    public PageCL<MicroServiceCategoryDetailVO> pageList(String name, int pageNum, int pageSize, boolean isInternet, boolean checkChild) {
        List<BlacklistMicroService> blacklistCategory = Collections.emptyList();
        List<BlacklistMicroService> blacklistMicros = Collections.emptyList();
        if (blackEnable) {
            List<BlacklistMicroService> blacklistMicroServices = blacklistMicroServiceService.listByTypes(Arrays.asList(BlacklistTypeEnum.CATEGORY, BlacklistTypeEnum.MICRO));
            blacklistCategory = blacklistMicroServices.stream().filter(p -> BlacklistTypeEnum.CATEGORY.equals(p.getType())).collect(Collectors.toList());
            blacklistMicros = blacklistMicroServices.stream().filter(p -> BlacklistTypeEnum.MICRO.equals(p.getType())).collect(Collectors.toList());
        }
        // 查询分类
        List<MicroServiceCategory> microServiceCategories = microServiceCategoryMapper.selectList(new QueryWrapper<MicroServiceCategory>().lambda()
                .like(StringUtils.isNotBlank(name), MicroServiceCategory::getName, name)
                .notIn(blackEnable && CollectionUtil.isNotEmpty(blacklistCategory), MicroServiceCategory::getId, blacklistCategory.stream().map(BlacklistMicroService::getId).collect(Collectors.toList()))
                .orderByAsc(MicroServiceCategory::getOrder));
        List<MicroServiceCategoryDetailVO> result = mapper.mapList(microServiceCategories, MicroServiceCategoryDetailVO.class);
        if (!CollectionUtils.isEmpty(result)) {
            // 获取授权的serviceIds
//            List<String> authorizedLicenseKeys = getAuthorizedLicenseKeys();
            List<String> microServiceCategoryIds = result.stream().map(MicroServiceCategoryDetailVO::getId).collect(Collectors.toList());
            // 查询分类下的所有微服务
            List<MicroService> microServices = microServiceMapper.selectList(new QueryWrapper<MicroService>().lambda()
                    .in(MicroService::getCategoryId, microServiceCategoryIds)
                    .notIn(blackEnable && CollectionUtil.isNotEmpty(blacklistMicros), MicroService::getId, blacklistMicros.stream().map(BlacklistMicroService::getId).collect(Collectors.toList()))
                    .notIn(blackEnable && CollectionUtil.isNotEmpty(blacklistCategory), MicroService::getCategoryId, blacklistCategory.stream().map(BlacklistMicroService::getId).collect(Collectors.toList()))

//                    .in(MicroService::getLicenseKey, authorizedLicenseKeys)
                    .orderByAsc(MicroService::getOrder));
            List<MicroServiceDetailVO> microServiceDetailVOList = new ArrayList<>();
            microServices.forEach(e->{
                if(isInternet) {
                    e.setLink(e.getPublicLink());
                }
                // 国际化处理
                e.setName(I18nUtil.getMessageByKey(e.getName(), e.getName()));
                e.setDescription(I18nUtil.getMessageByKey(e.getDescription(), e.getDescription()));
                microServiceDetailVOList.add(mapper.map(e, MicroServiceDetailVO.class));
            });
            // 以分类id来分组微服务
            Map<String, List<MicroServiceDetailVO>> categoryMicroServiceMap = microServiceDetailVOList.stream()
                    .collect(Collectors.groupingBy(MicroServiceDetailVO::getCategoryId));
            // 将微服务集合回填到分类中
            result = result
                    .stream()
                    .peek(e -> e.setChildren(categoryMicroServiceMap.get(e.getId())))
                    .filter(e -> !checkChild || !CollectionUtils.isEmpty(e.getChildren()))
                    .collect(Collectors.toList());
        }
        try {
            return new PageCL<MicroServiceCategoryDetailVO>().getPageInfo(pageNum, pageSize, result);
        } catch (Exception e) {
            log.error("com.sugon.cloud.iam.api.service.impl.MicroServiceCategoryServiceImpl.pageList error:", e);
            throw new BusinessException("查询列表报错");
        }
    }

    /**
     * 获取授权的serviceIds
     * @return 已授权的serviceIds
     */
//    @Override
//    public List<String> getAuthorizedLicenseKeys() {
//        // init "-1" to avoid result is empty.
//        List<String> authorizedLicenseKeys = Lists.newArrayList("-1");
//        ClusterInfo clusterInfo = licenseVerify.getExtData();
//        if (Objects.isNull(clusterInfo)) {
//            return authorizedLicenseKeys;
//
//        }
//
//        List<String> authorizedLicenseKeysSub = clusterInfo.getProductAuthorizationInfos()
//                .stream()
//                .filter(ProductAuthorizationInfo::isAuthorized)
//                .map(ProductAuthorizationInfo::getLicenseKey)
//                .collect(Collectors.toList());
//        if (!CollectionUtils.isEmpty(authorizedLicenseKeysSub)) {
//            authorizedLicenseKeys.addAll(authorizedLicenseKeysSub);
//        }
//        return authorizedLicenseKeys;
//    }


    /**
     * 查询多条数据
     *
     * @param name 查询名称
     * @param pageNum 查询起始位置
     * @param pageSize 查询条数
     * @return 对象列表
     */
    @Override
    public PageCL<MicroServiceCategoryDetailVO> pageListAll(String name, int pageNum, int pageSize, boolean isInternet) {
        IPage<MicroServiceCategory> page = new Page(pageNum, pageSize);
        page.orders();
        // 查询分类
        List<BlacklistMicroService> blacklistCategory = Collections.emptyList();
        List<BlacklistMicroService> blacklistMicros = Collections.emptyList();
        if (blackEnable) {
            List<BlacklistMicroService> blacklistMicroServices = blacklistMicroServiceService.listByTypes(Arrays.asList(BlacklistTypeEnum.CATEGORY, BlacklistTypeEnum.MICRO));
            blacklistCategory = blacklistMicroServices.stream().filter(p -> BlacklistTypeEnum.CATEGORY.equals(p.getType())).collect(Collectors.toList());
            blacklistMicros = blacklistMicroServices.stream().filter(p -> BlacklistTypeEnum.MICRO.equals(p.getType())).collect(Collectors.toList());
        }
        IPage<MicroServiceCategory> projectIPage = microServiceCategoryMapper.selectPage(page, new QueryWrapper<MicroServiceCategory>().lambda()
                .like(StringUtils.isNotBlank(name), MicroServiceCategory::getName, name)
                .notIn(blackEnable && CollectionUtil.isNotEmpty(blacklistCategory), MicroServiceCategory::getId, blacklistCategory.stream().map(BlacklistMicroService::getId).collect(Collectors.toList()))
                .orderByAsc(MicroServiceCategory::getOrder));
        List<MicroServiceCategoryDetailVO> result = mapper.mapList(projectIPage.getRecords(), MicroServiceCategoryDetailVO.class);
        if (!CollectionUtils.isEmpty(result)) {
            // 查询分类下的所有微服务
            List<MicroService> microServices = microServiceMapper.selectList(new QueryWrapper<MicroService>().lambda()
                    .notIn(blackEnable && CollectionUtil.isNotEmpty(blacklistMicros), MicroService::getId, blacklistMicros.stream().map(BlacklistMicroService::getId).collect(Collectors.toList()))
                    .notIn(blackEnable && CollectionUtil.isNotEmpty(blacklistCategory), MicroService::getCategoryId, blacklistCategory.stream().map(BlacklistMicroService::getId).collect(Collectors.toList()))
                    .orderByAsc(MicroService::getOrder));
            microServices.forEach(e->{
                if(isInternet) {
                    e.setLink(e.getPublicLink());
                }
                // 国际化处理
                e.setName(I18nUtil.getMessageByKey(e.getName(), e.getName()));
                e.setDescription(I18nUtil.getMessageByKey(e.getDescription(), e.getDescription()));
            });
            // 以分类id来分组微服务
            Map<String, List<MicroServiceDetailVO>> categoryMicroServiceMap = microServices.stream().map(e -> mapper.map(e, MicroServiceDetailVO.class))
                    .collect(Collectors.groupingBy(MicroServiceDetailVO::getCategoryId));
            // 将微服务集合回填到分类中
            result = result.stream().map(e -> {
                e.setChildren(categoryMicroServiceMap.get(e.getId()));
                return e;
            }).collect(Collectors.toList());
        }
        try {
            return new PageCL<MicroServiceCategoryDetailVO>().
                    getPageByPageHelper(page, result);
        } catch (Exception e) {
            log.error("com.sugon.cloud.iam.api.service.impl.MicroServiceCategoryServiceImpl.pageListAll error:", e);
            throw new BusinessException("查询所有微服务菜单出错");
        }
    }

    /**
     * 查询所有配额的分类
     * @return
     */
    @Override
    public List<MicroServiceCategoryDetailVO> ListAllCategoryByQuota() {
        try {
            Map<String, String> typeParam = Maps.newHashMap();
            List<QuotaType> quotaTypeList = quotaTypeMapper.getQuotaTypes(typeParam);
            List<String> categoryIds;
            if (blackEnable) {
                List<BlacklistMicroService> blacklistMicroServices = blacklistMicroServiceService.listByTypes(Arrays.asList(BlacklistTypeEnum.CATEGORY, BlacklistTypeEnum.MICRO));
                List<String> blackCategoryIds = blacklistMicroServices.stream().filter(p -> BlacklistTypeEnum.CATEGORY.equals(p.getType())).map(BlacklistMicroService::getId).collect(Collectors.toList());
                List<String> blackMicroIds = blacklistMicroServices.stream().filter(p -> BlacklistTypeEnum.MICRO.equals(p.getType())).map(BlacklistMicroService::getId).collect(Collectors.toList());
                categoryIds = quotaTypeList.stream()
                        .filter(q -> !blackCategoryIds.contains(q.getCategoryId()))
                        .filter(q -> !blackMicroIds.contains(q.getMicroId()))
                        .map(QuotaType::getCategoryId)
                        .collect(Collectors.toList());
            } else {
                categoryIds = quotaTypeList.stream().map(QuotaType::getCategoryId).collect(Collectors.toList());
            }
            // 查询配额分类
            List<MicroServiceCategory> microServiceCategoryList = microServiceCategoryMapper.selectList(new QueryWrapper<MicroServiceCategory>().lambda()
                    .in(MicroServiceCategory::getId, categoryIds)
                    .orderByAsc(MicroServiceCategory::getOrder));
            return mapper.mapList(microServiceCategoryList, MicroServiceCategoryDetailVO.class);
            // return filtration(result);
        } catch (Exception e) {
            log.error("ListAllCategoryByQuota error:", e);
            throw new BusinessException("查询所有配额的分类出错");
        }
    }

    private List<MicroServiceCategoryDetailVO> filtration(List<MicroServiceCategoryDetailVO> result) {
        if (!blackEnable) {
            return result;
        }
        List<BlacklistMicroService> blacklistMicroServices = blacklistMicroServiceService.listByTypes(Arrays.asList(BlacklistTypeEnum.CATEGORY, BlacklistTypeEnum.MICRO));
        List<String> categoryIds = blacklistMicroServices.stream().filter(p -> BlacklistTypeEnum.CATEGORY.equals(p.getType())).map(BlacklistMicroService::getId).collect(Collectors.toList());
        List<String> microIds = blacklistMicroServices.stream().filter(p -> BlacklistTypeEnum.MICRO.equals(p.getType())).map(BlacklistMicroService::getId).collect(Collectors.toList());
        List<MicroServiceCategoryDetailVO> list = result.stream().filter(p -> !categoryIds.contains(p.getId())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(list)) {
            for (MicroServiceCategoryDetailVO microServiceCategoryDetailVO : list) {
                List<MicroServiceDetailVO> children = microServiceCategoryDetailVO.getChildren();
                if (CollectionUtil.isNotEmpty(children)) {
                    microServiceCategoryDetailVO.setChildren(children.stream().filter(p -> !microIds.contains(p.getId())).collect(Collectors.toList()));
                }

            }
        }
        return list;
    }


    /**
     * 新增数据
     *
     * @param createOrUpdateMicroServiceCategoryVO 实例对象
     * @return 实例对象
     */
    @Override
    public MicroServiceCategory insert(CreateOrUpdateMicroServiceCategoryVO createOrUpdateMicroServiceCategoryVO) {
        // 判断名称是否存在
        Integer count = microServiceCategoryMapper.selectCount(new LambdaQueryWrapper<MicroServiceCategory>().eq(MicroServiceCategory::getName, createOrUpdateMicroServiceCategoryVO.getName()));
        if (count > 0) {
            throw new BusinessException("微服务类别名称已存在");
        }
        createOrUpdateMicroServiceCategoryVO.setId(UUIDUtil.get32UUID());
        MicroServiceCategory microServiceCategory = mapper.map(createOrUpdateMicroServiceCategoryVO, MicroServiceCategory.class);
        // 设置排序
        List<Map<String, Object>> maps = microServiceCategoryMapper.selectMaps(new QueryWrapper<MicroServiceCategory>().select("MAX(`order`) as maxOrder"));
        Double maxOrder = (Double) maps.get(0).get("maxOrder");
        microServiceCategory.setOrder(maxOrder + 10);
        // 设置类型为自定义类型
        microServiceCategory.setType(CommonInstance.MICRO_CUSTOM);
        // 描述
        if (StrUtil.isBlank(microServiceCategory.getDescription())) {
            microServiceCategory.setDescription(microServiceCategory.getName());
        }
        this.microServiceCategoryMapper.insert(microServiceCategory);
        return microServiceCategory;
    }

    /**
     * 修改数据
     *
     * @param vo 实例对象
     * @return 实例对象
     */
    @Override
    public MicroServiceCategory update(MicroServiceCategoryDetailVO vo) {
        MicroServiceCategory microServiceCategory = microServiceCategoryMapper.selectById(vo.getId());
        if (Objects.isNull(microServiceCategory)) {
            throw new BusinessException("微服务类别不存在");
        }
        // 判断名称是否存在
        Integer count = microServiceCategoryMapper.selectCount(new LambdaQueryWrapper<MicroServiceCategory>()
                .eq(MicroServiceCategory::getName, vo.getName())
                .ne(MicroServiceCategory::getId, vo.getId()));
        if (count > 0) {
            throw new BusinessException("微服务类别名称已存在");
        }

        if (CommonInstance.MICRO_CUSTOM.equals(microServiceCategory.getType())) {
            // 自定义菜单才能修改名称
            microServiceCategory.setName(vo.getName());
        }
        if (StrUtil.isNotBlank(vo.getDescription())) {
            microServiceCategory.setDescription(vo.getName());
        }
        if (Objects.nonNull(vo.getNavHidden())) {
            microServiceCategory.setNavHidden(vo.getNavHidden());
        }
        if (Objects.nonNull(vo.getOrder())) {
            microServiceCategory.setOrder(vo.getOrder());
        }
        microServiceCategoryMapper.updateById(microServiceCategory);
        return this.queryById(vo.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public void deleteById(String id) {
        MicroServiceCategory microServiceCategory = microServiceCategoryMapper.selectById(id);
        if (Objects.isNull(microServiceCategory)) {
            throw new BusinessException("微服务类别不存在");
        }
        if (!CommonInstance.MICRO_CUSTOM.equals(microServiceCategory.getType())) {
            throw new BusinessException("非自定义微服务类别不能删除");
        }
        microServiceMapper.delete(new LambdaQueryWrapper<MicroService>().eq(MicroService::getCategoryId, id));
        microServiceCategoryMapper.deleteById(id);
    }
}
