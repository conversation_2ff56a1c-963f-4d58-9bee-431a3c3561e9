package com.sugon.cloud.iam.api.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.common.mapper.ModelMapper;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.common.utils.UUIDUtil;
import com.sugon.cloud.iam.api.entity.Region;
import com.sugon.cloud.iam.api.entity.RegionForm;
import com.sugon.cloud.iam.api.service.EndpointService;
import com.sugon.cloud.iam.api.service.RegionService;
import com.sugon.cloud.iam.api.service.resourceauth.RegionResourceAuthHandler;
import com.sugon.cloud.iam.api.utils.EncryptAndDecryptUtil;
import com.sugon.cloud.iam.api.vo.UpdateRegionVO;
import com.sugon.cloud.iam.common.model.dto.RegionInfoDTO;
import com.sugon.cloud.iam.common.model.dto.VmwareParamDTO;
import com.sugon.cloud.iam.common.model.vo.CreateEndpointResponseParam;
import com.sugon.cloud.iam.common.model.vo.RegionVo;
import com.sugon.cloud.log.client.starter.annotation.LogRecordAnnotation;
import com.sugon.cloud.resource.auth.common.aop.ResourceAuth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

@Api(tags = "区域管理")
@RequestMapping(value = "/api/region")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RestController
@Slf4j
public class RegionController {

    private final RegionService regionService ;
    private final EndpointService endpointService;
    private final ModelMapper mapper;
    private final EncryptAndDecryptUtil encryptAndDecryptUtil;
    @GetMapping
    @ApiOperation("查询所有区域")
    public ResultModel<List<Region>> regionList () {
        List<Region> list = regionService.list();
        list.forEach(r -> {
            if (RegionForm.VMWARE_TYPE.equalsIgnoreCase(r.getType())) {
                JSONObject extraObj = JSONObject.parseObject(r.getExtra());
                extraObj.remove("vmware_password");
                r.setExtra(extraObj.toString());
            }
        });
        return ResultModel.success(list);
    }

    @GetMapping("/for-feign")
    @ApiOperation("查询所有区域")
    @DocumentIgnore
    public ResultModel<List<RegionVo>> regionListForFeign () {
        List<RegionVo> regionVos = Lists.newArrayList();
        List<Region> regions = regionService.list();
        if(!CollectionUtils.isEmpty(regions)){
            regionVos = mapper.mapList(regions,RegionVo.class);
        }
        return ResultModel.success(regionVos);
    }

    @PutMapping("/{region_id}")
    @LogRecordAnnotation(value = "修改区域",detail = "修改区域{{#regionId}}描述:{{#updateRegionVO.description}}")
    @ApiOperation("修改区域描述")
    @ResourceAuth(handler = RegionResourceAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    @ApiImplicitParams({@ApiImplicitParam(name = "region_id", value = "区域ID", required = true, dataType = "String", paramType = "path")})
    @DocumentIgnore
    public ResultModel<String> updateRegion(@PathVariable("region_id") String regionId,
                                        @Valid @RequestBody UpdateRegionVO updateRegionVO) {
        regionService.updateRegion(regionId, updateRegionVO);
        return ResultModel.success(regionId);
    }

    @PostMapping
    @LogRecordAnnotation(value = "创建域",detail = "创建域:{{#regionForm.description}}")
    @ApiOperation("创建域")
    @ResourceAuth(handler = RegionResourceAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    @DocumentIgnore
    public ResultModel<Boolean> createRegion(@Valid @RequestBody RegionForm regionForm) {
        return ResultModel.success("创建域成功",regionService.createRegion(regionForm));
    }

    @DeleteMapping("{id}")
    @LogRecordAnnotation(value = "删除域",detail = "删除域:{{#_ret.resource}}")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "域ID", required = true, dataType = "String", paramType = "path")})
    @ApiOperation("删除域")
    @ResourceAuth(handler = RegionResourceAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    @DocumentIgnore
    public ResultModel<String> deleteRegion(@PathVariable("id") String id) {
        List<CreateEndpointResponseParam> list = endpointService.listEndpoint(null, null, id);
        if (list.size() > 0) {
            return ResultModel.error("该region存在endpoint不能删除");
        }
        Region region = regionService.getById(id);
        regionService.removeById(id);
        return ResultModel.success("删除成功",region.getDescription(),"");
    }

    @GetMapping("{id}/full-infos")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "域ID", required = true, dataType = "String", paramType = "path")})
    @ApiOperation(value = "获取域详情", hidden = true)
    public ResultModel<RegionInfoDTO> fullDetail(@PathVariable("id") String id) {
        Region region = regionService.getById(id);
        if (Objects.isNull(region)) {
            return ResultModel.success("获取域详情成功", null);
        }
        RegionInfoDTO result = mapper.map(region, RegionInfoDTO.class);
        if (RegionForm.VMWARE_TYPE.equalsIgnoreCase(region.getType())) {
            VmwareParamDTO vmwareParamDTO = JSONObject.parseObject(region.getExtra(), VmwareParamDTO.class);
            // 密码解密
            String decryptPwd = encryptAndDecryptUtil.decrypt(vmwareParamDTO.getVmwarePassword());
            vmwareParamDTO.setVmwarePassword(decryptPwd);
            result.setVmwareParam(vmwareParamDTO);
        }
        return ResultModel.success("获取域详情成功",result);
    }
}
