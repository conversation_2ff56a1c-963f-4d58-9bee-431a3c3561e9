package com.sugon.cloud.iam.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.entity.SecretKeyEntity;
import com.sugon.cloud.iam.api.entity.exception.SubjectTokenAccessDeniedException;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.exception.SCA4SignatureException;
import com.sugon.cloud.iam.api.sca4.SCA4Match;
import com.sugon.cloud.iam.api.service.SecretKeyService;
import com.sugon.cloud.iam.api.service.UserService;
import com.sugon.cloud.iam.common.model.vo.ValidateTokenUserResponseVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.CharUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.nio.file.Files;
import java.security.SignatureException;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;
import java.util.Objects;
import java.util.TimeZone;

import static com.sugon.cloud.iam.api.sca4.SCA4Match.sha256Hex;

@Api(tags = "校验SCA4")
@RequestMapping(value = "/api/sca4")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RestController
@Log4j2
@DocumentIgnore
public class SCA4Controller {

    private final UserService userService;
    private final SecretKeyService secretKeyService;

    @RequestMapping("/validate-sca4")
    @ApiOperation("校验SCA4")
    public ResultModel<ValidateTokenUserResponseVO> validateSCA4(HttpServletRequest httpRequest) {
        log.debug("req.getQueryString() {}", httpRequest.getQueryString());
        String sca4 = httpRequest.getHeader(CommonInstance.AUTHORIZATION);
        if (Objects.isNull(sca4))
            throw new SCA4SignatureException("SCA4-HMAC-SHA256不合法");
        String region = httpRequest.getHeader(CommonInstance.X_SCA4_REGION);
        if (Objects.isNull(region)) {
            throw new SCA4SignatureException("X-SCA4-Region不能为空");
        }
       /* String action = httpRequest.getHeader(CommonInstance.X_SCA4_ACTION);
        log.info("action is {}", action);
        if (Objects.isNull(action)) {
            throw new SCA4SignatureException("X-SCA4-Action不能为空");
        }*/
        try {
            SCA4Match.SignValues signValues = SCA4Match.parseSignV4(httpRequest.getHeader(CommonInstance.AUTHORIZATION));
            String accessKey = signValues.getCredential().getAccessKey();
            SecretKeyEntity secretKeyEntity = secretKeyService.getOne(new LambdaQueryWrapper<SecretKeyEntity>().eq(SecretKeyEntity::getAccessKey, accessKey));
            String secretKey = secretKeyEntity.getSecretKey();
            String userId = secretKeyEntity.getUserId();
            User user = userService.getById(userId);
            String timestamp = httpRequest.getHeader("X-SCA4-Date");

            log.debug("accessKey: {}", accessKey);
            log.debug("secretKey: {}", secretKey);
            log.debug("userId: {}", userId);
            log.debug("timestamp: {}", timestamp);

            if (StringUtils.isEmpty(timestamp)){
                throw new RuntimeException("ErrMissingDateHeader");
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            // 注意时区，否则容易出错
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            String date = sdf.format(new Date(Long.valueOf(timestamp + "000")));
            // sign string
            String canonicalRequest = SCA4Match.getCanonicalRequest(httpRequest, signValues);
            String credentialScope = date +"/" +
                    signValues.getCredential().getScope().getRegion() + "/" +
                    signValues.getCredential().getScope().getService() + "/" +
                    "sca4_request";
            String hashedCanonicalRequest = sha256Hex(canonicalRequest);
            String stringToSign = SCA4Match.signV4Algorithm + "\n" + timestamp + "\n" + credentialScope + "\n" + hashedCanonicalRequest;

            log.debug("canonicalRequest: {}", canonicalRequest);
            log.debug("credentialScope: {}", credentialScope);
            log.debug("hashedCanonicalRequest: {}", hashedCanonicalRequest);
            log.debug("stringToSign: {}", stringToSign);

            // compute sign
            String caculateSignature = SCA4Match.caculateSignature(stringToSign, date, secretKey, signValues);

            log.debug("caculateSignature: {}", caculateSignature);

            if (!caculateSignature.equals(signValues.getSignature())) {
                throw new SignatureException("签名不一致");
            }
            ValidateTokenUserResponseVO.ValidateTokenUserResponseVOBuilder builder = ValidateTokenUserResponseVO.builder();
            // 允许多人登录
            builder.loginElsewhere(false);
            return ResultModel.success("", builder.userId(userId)
                    .username(user.getName())
                    .userType(user.getType())
                    .build());
        } catch (Exception e) {
            log.error("sca4 error:", e);
        }
        return ResultModel.error("sca4 validate error!");
    }

    @GetMapping("inner-validate")
    @ApiOperation("内部校验SCA4")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = CommonInstance.AUTHORIZATION, value = "TOKEN", required = true, dataType = "String", paramType = "header")})
    public ResultModel<ValidateTokenUserResponseVO> validateSca4(@RequestHeader(CommonInstance.AUTHORIZATION) String token) {
        try {
            SCA4Match.SignValues signValues = SCA4Match.parseSignV4(token);
            String accessKey = signValues.getCredential().getAccessKey();
            SecretKeyEntity secretKeyEntity = secretKeyService.getOne(new LambdaQueryWrapper<SecretKeyEntity>()
                    .eq(SecretKeyEntity::getAccessKey, accessKey));
            String userId = secretKeyEntity.getUserId();
            User user = userService.getById(userId);
            ValidateTokenUserResponseVO.ValidateTokenUserResponseVOBuilder builder = ValidateTokenUserResponseVO.builder();
            // 允许多人登录
            builder.loginElsewhere(false);
            return ResultModel.success("", builder.userId(userId)
                    .username(user.getName())
                    .userType(user.getType())
                    .build());
        } catch (Exception e) {
            log.error("validate sca4 error ",e);
        }
        return ResultModel.error("validate sca4 error");
    }

    @GetMapping("/validate/ak-sk")
    @ApiOperation("校验ak、sk(只给cce csi接口使用)")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = CommonInstance.AUTHORIZATION, value = "TOKEN", required = true, dataType = "String", paramType = "header")})
    @DocumentIgnore
    public ResultModel<ValidateTokenUserResponseVO> validateAkSk(@RequestParam("token") String token) {
        return secretKeyService.validateBasicToken(token);
    }
}
