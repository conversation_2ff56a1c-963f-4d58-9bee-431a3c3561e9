package com.sugon.cloud.iam.api.entity;

import lombok.Data;
import java.io.Serializable;
import java.util.List;

@Data
public class StatementEntity implements Serializable {

    private static final long serialVersionUID = -2420969111719187691L;

    private boolean effect;

    private List<String> action;

    private List<String> resource;

    private String condition;

    public boolean getEffect() {
        return effect;
    }

}
