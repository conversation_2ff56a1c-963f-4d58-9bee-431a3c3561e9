package com.sugon.cloud.iam.api.entity;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * Licens实体 20160530
 *
 * <AUTHOR>
 *
 */
@ApiModel("许可信息")
public class LicenseInfo implements Serializable {

	/**
	 * 主键
	 */
	@ApiModelProperty(value = "主键")
	private String licenseId;

	/**
	 * License的版本
	 */
	@ApiModelProperty(value = "License的版本")
	private String licenseVersion;

	/**
	 * 客户名称
	 */
	@ApiModelProperty(value = "客户名称")
	private String endUserName;

	/**
	 * 系统类型, gbk, utf-8
	 */
	@ApiModelProperty(value = "系统类型, gbk, utf-8")
	private String systemType;

	@ApiModelProperty(value = "License服务信息")
	private List<LicenseServiceInfo> licenseServiceInfoList;

	public String getLicenseId() {
		return licenseId;
	}

	public void setLicenseId(String licenseId) {
		this.licenseId = licenseId;
	}

	public String getLicenseVersion() {
		return licenseVersion;
	}

	public void setLicenseVersion(String licenseVersion) {
		this.licenseVersion = licenseVersion;
	}

	public String getEndUserName() {
		return endUserName;
	}

	public void setEndUserName(String endUserName) {
		this.endUserName = endUserName;
	}

	public String getSystemType() {
		return systemType;
	}

	public void setSystemType(String systemType) {
		this.systemType = systemType;
	}

	public List<LicenseServiceInfo> getLicenseServiceInfoList() {
		return licenseServiceInfoList;
	}

	public void setLicenseServiceInfoList(List<LicenseServiceInfo> licenseServiceInfoList) {
		this.licenseServiceInfoList = licenseServiceInfoList;
	}

	@Override
	public String toString() {
		return JSONObject.toJSONString(this);
	}
}
