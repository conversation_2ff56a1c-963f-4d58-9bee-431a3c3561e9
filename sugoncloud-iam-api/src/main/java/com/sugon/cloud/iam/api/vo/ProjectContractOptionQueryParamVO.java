package com.sugon.cloud.iam.api.vo;

import com.sugon.cloud.common.annotations.DocumentIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: yangdingshan
 * @Date: 2024/6/19 17:26
 * @Description:
 */
@Data
@ApiModel("项目合同选项查询参数")
public class ProjectContractOptionQueryParamVO {

    @ApiModelProperty("合同名称")
    private String name;
    @ApiModelProperty("合同编号")
    private String code;
    @ApiModelProperty("页码")
    private int pageNum;
    @ApiModelProperty("每页显示条数")
    private int pageSize;
    @ApiModelProperty("项目ID")
    private String projectId;
    @ApiModelProperty("计费类型(统一计费：contract_meter_unification | 合同计费：contract_meter_contract)")
    @DocumentIgnore
    private List<String> meterTypes;
    @DocumentIgnore
    private List<String> unificationOrgIds;
    @DocumentIgnore
    private String contractOrgId;
}
