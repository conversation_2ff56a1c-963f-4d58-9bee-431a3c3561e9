package com.sugon.cloud.iam.api.entity.keystone;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sugon.cloud.iam.common.model.vo.CreateResourceResponseLinksParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "创建服务响应参数")
public class CreateServiceResponseParam {
    @JsonProperty("region_id")
    @ApiModelProperty("区域ID")
    private String regionId;
    @ApiModelProperty("服务名称")
    private String name;
    @ApiModelProperty("服务类型")
    private String type;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("服务描述")
    private String description;
    @ApiModelProperty("是否启用")
    private Boolean enabled;
    @ApiModelProperty("资源链接")
    private CreateResourceResponseLinksParam links;
}
