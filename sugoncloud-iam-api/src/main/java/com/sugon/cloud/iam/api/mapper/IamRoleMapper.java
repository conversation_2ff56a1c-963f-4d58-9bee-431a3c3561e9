package com.sugon.cloud.iam.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sugon.cloud.iam.api.entity.IamRole;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IamRoleMapper extends BaseMapper<IamRole> {

    List<IamRole> findByUserIdAndProjectId (String userId, String projectId);

    List<IamRole> findByUserId(String userId, String projectId);

    IPage<IamRole> findByUserIdPage(IPage<IamRole> page, String userId, boolean queryInnerRole, String projectId);
}
