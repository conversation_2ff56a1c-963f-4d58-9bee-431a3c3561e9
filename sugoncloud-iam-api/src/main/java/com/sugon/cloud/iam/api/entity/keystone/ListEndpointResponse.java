package com.sugon.cloud.iam.api.entity.keystone;

import com.sugon.cloud.iam.common.model.vo.CreateEndpointResponseParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "endpoint列表")
public class ListEndpointResponse {
    @ApiModelProperty(value = "endpoint列表")
    private List<CreateEndpointResponseParam> endpoints;
    @ApiModelProperty(value = "links")
    private ListResourceLinkResponse links;
}
