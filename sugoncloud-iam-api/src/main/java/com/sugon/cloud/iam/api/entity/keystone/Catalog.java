package com.sugon.cloud.iam.api.entity.keystone;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.List;

@Data
@TableName("service")
public class Catalog {
    private static final long serialVersionUID = 123456L;
    @TableField(exist = false)
    private List<Endpoint> endpoints;
    private String type;
    private String id;
    private String name;
    private String extra;
    private String description;
    private boolean enabled;
}
