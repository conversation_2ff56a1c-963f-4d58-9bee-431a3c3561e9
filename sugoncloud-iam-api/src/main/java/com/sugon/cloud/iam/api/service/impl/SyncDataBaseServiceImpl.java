package com.sugon.cloud.iam.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.sugon.cloud.iam.api.common.AESUtil;
import com.sugon.cloud.iam.api.common.MD5Util;
import com.sugon.cloud.iam.api.entity.*;
import com.sugon.cloud.iam.api.entity.keystone.Assignment;
import com.sugon.cloud.iam.api.entity.keystone.Project;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.exception.BusinessException;
import com.sugon.cloud.iam.api.mapper.LocalUserMapper;
import com.sugon.cloud.iam.api.mapper.ProjectMapper;
import com.sugon.cloud.iam.api.service.AssignmentService;
import com.sugon.cloud.iam.api.service.QuotaService;
import com.sugon.cloud.iam.api.service.SyncDataBaseService;
import com.sugon.cloud.iam.api.service.UserService;
import com.sugon.cloud.iam.common.model.vo.QuotaMetric;
import com.sugon.cloud.iam.common.model.vo.QuotaType;
import io.jsonwebtoken.lang.Collections;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
@RefreshScope
public class SyncDataBaseServiceImpl implements SyncDataBaseService {

    private final LocalUserMapper localUserMapper;
    private final UserService userService;
    private final AssignmentService assignmentService;
    private final ProjectMapper projectMapper;

    private final QuotaService quotaService;

    private final String PURPOSE_TYPE_ECS = "ECS";

    private final String PURPOSE_TYPE_MYSQL = "MYSQL";

    // 内置三元管理用户名称
    @Value("#{'${inner.username:admin,auditadmin,securityadmin}'.split(',')}")
    private List<String> innerUserNames;

    @Override
    public void syncUserExtra() {
        log.info("开始从gateway.ram_user表中同步密码、mobile、email至user表...");
        int syncUserExtraCount = 0;
        List<CloudViewGatewayUser> cloudViewGatewayUsers = localUserMapper.queryGatewayUsers();
        if (!Collections.isEmpty(cloudViewGatewayUsers)) {
            Map<String, CloudViewGatewayUser> userNameObjectMap = cloudViewGatewayUsers.stream()
                    .collect(Collectors.toMap(CloudViewGatewayUser::getName, o -> o));
            List<User> users = userService.list();
            users = users.stream().peek(user -> {
                if (userNameObjectMap.containsKey(user.getName())) {
                    CloudViewGatewayUser cloudViewGatewayUser = userNameObjectMap.get(user.getName());
                    user.setPhone(cloudViewGatewayUser.getMobile());
                    user.setEmail(cloudViewGatewayUser.getEmail());
                    String pwd = AESUtil.decrypt(cloudViewGatewayUser.getPassword()
                            , MD5Util.string2MD5(cloudViewGatewayUser.getSalt()));
                    user.setPassword(new BCryptPasswordEncoder().encode(pwd));
                }
            }).collect(Collectors.toList());
            userService.saveOrUpdateBatch(users);
            syncUserExtraCount = cloudViewGatewayUsers.size();

        }
        log.info("从gateway.ram_user表中同步密码、mobile、email至user表完成,共完成[" + syncUserExtraCount + "]条...");
    }

    @Override
    public void syncUserName() {
        log.info("开始从local_user表中同步用户名称至user表...");
        int syncUserNameCount = 0;
        List<OriginKeystoneUser> originKeystoneUsers = localUserMapper.queryKeystoneUsers();
        if (!Collections.isEmpty(originKeystoneUsers)) {
            Map<String, String> userIdNameMap = originKeystoneUsers.stream().collect(Collectors.toMap(OriginKeystoneUser::getId, OriginKeystoneUser::getName));
            List<User> users = userService.list();
            users = users.stream().peek(user -> user.setName(userIdNameMap.get(user.getId())))
                    .collect(Collectors.toList());
            userService.saveOrUpdateBatch(users);
            syncUserNameCount = users.size();
        }
        log.info("从local_user表中同步用户名称至user表完成,共完成[" + syncUserNameCount + "]条...");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncGatewayUser() {
        log.info("===========>>>>>>开始从gateway.ram_user表中同步用户至user表...");
        int syncGatewayUserCount = 0;
        List<CloudViewGatewayUser> cloudViewGatewayUsers = localUserMapper.queryGatewayUsers();
        if (!Collections.isEmpty(cloudViewGatewayUsers)) {
            // 计数
            syncGatewayUserCount = cloudViewGatewayUsers.size();

            // 1.处理名称为admin的project
            log.info("===========>>>>>>1.开始处理名称为admin的project");
            String keystoneAdminProjectId = localUserMapper.getKeystoneAdminProjectId();
            if (StringUtils.isEmpty(keystoneAdminProjectId)) {
                throw new BusinessException("keystone 中无 admin project");
            }
            Project iamAdminProject = projectMapper.selectOne(new LambdaQueryWrapper<Project>().eq(Project::getName, "admin"));
            if (Objects.isNull(iamAdminProject)) {
                throw new BusinessException("iam 中无 admin project");
            }
            String iamAdminProjectId = iamAdminProject.getId();
            // 1.1 修改user.default_project_id
            User user = new User();
            user.setDefaultProjectId(keystoneAdminProjectId);
            userService.update(user, new LambdaUpdateWrapper<User>().eq(User::getDefaultProjectId, iamAdminProjectId));
            // 1.2 修改assignment.target_id
            Assignment assignment = new Assignment();
            assignment.setTargetId(keystoneAdminProjectId);
            assignmentService.update(assignment, new LambdaUpdateWrapper<Assignment>().eq(Assignment::getTargetId, iamAdminProjectId));
            // 1.3 修改project.id
            localUserMapper.updateIamProjectIdById(keystoneAdminProjectId, iamAdminProjectId);
            log.info("===========>>>>>>1.名称为admin的project处理完毕");

            List<CloudViewGatewayUser> innerUsers = cloudViewGatewayUsers.stream()
                    .filter(gatewayUser -> innerUserNames.contains(gatewayUser.getName()))
                    .collect(Collectors.toList());
            syncInnerUser(innerUsers);

            List<CloudViewGatewayUser> masterUsers = cloudViewGatewayUsers.stream()
                    .filter(gatewayUser -> !innerUserNames.contains(gatewayUser.getName())
                            && StringUtils.isEmpty(gatewayUser.getOwnerId()))
                    .collect(Collectors.toList());

            Map<String, List<CloudViewGatewayUser>> owerIdSubUsersMap = cloudViewGatewayUsers.stream()
                    .filter(gatewayUser -> StringUtils.isNotEmpty((gatewayUser.getOwnerId())))
                    .collect(Collectors.groupingBy(CloudViewGatewayUser::getOwnerId));
            masterUsers.forEach(masterUser -> {
                String masterUserName = masterUser.getName();
                log.info("===========>>>>>>2.开始处理masterUser[{}]", masterUserName);
                Project gatewayDefaultProject = localUserMapper.getDefaultProjectByUserName(masterUserName);
                masterUser.setGatewayDefaultProject(gatewayDefaultProject);
                Department firstDepartment = userService.createMasterUserFromGateway(masterUser);
                List<CloudViewGatewayUser> subUsers = owerIdSubUsersMap.get(masterUser.getId());
                if (!CollectionUtils.isEmpty(subUsers)) {
                    log.info("===========>>>>>>2.1处理名称为masterUser[{}]的subUser.size=[{}]", masterUserName, subUsers.size());
                    subUsers.forEach(subUser -> userService.createSubUserFromGateway(subUser, firstDepartment));
                }
                log.info("===========>>>>>>2.处理masterUser[{}]完成", masterUserName);
            });
        }
        log.info("===========>>>>>>从gateway.ram_user表中同步用户至user表完成,共完成[" + syncGatewayUserCount + "]条...");
    }

    private void syncInnerUser(List<CloudViewGatewayUser> innerUsers) {
        if (CollectionUtils.isEmpty(innerUsers)) {
            return;
        }
        Map<String, CloudViewGatewayUser> userNameObjectMap = innerUsers.stream()
                .collect(Collectors.toMap(CloudViewGatewayUser::getName, o -> o));
        List<User> users = userService.list();
        users = users.stream().peek(user -> {
            if (userNameObjectMap.containsKey(user.getName())) {
                CloudViewGatewayUser cloudViewGatewayUser = userNameObjectMap.get(user.getName());
                user.setPhone(cloudViewGatewayUser.getMobile());
                user.setEmail(cloudViewGatewayUser.getEmail());
                String pwd = AESUtil.decrypt(cloudViewGatewayUser.getPassword()
                        , MD5Util.string2MD5(cloudViewGatewayUser.getSalt()));
                user.setPassword(new BCryptPasswordEncoder().encode(pwd));
            }
        }).collect(Collectors.toList());
        userService.saveOrUpdateBatch(users);
    }

    @Override
    public void syncGatewayQuota() {
        List<CloudViewGatewayUser> cloudViewGatewayUsers = localUserMapper.queryGatewayUsers();
        List<CloudViewGatewayUser> masterUsers = cloudViewGatewayUsers.stream()
                .filter(gatewayUser -> !innerUserNames.contains(gatewayUser.getName())
                        && StringUtils.isEmpty(gatewayUser.getOwnerId()))
                .collect(Collectors.toList());
        masterUsers.forEach(masterUser -> {
            String masterUserName = masterUser.getName();
            log.info("===========>>>>>>1.开始处理masterUser[{}]的配额", masterUserName);
            String topDeptId = localUserMapper.getTopDeptId(masterUserName);
            Project project = projectMapper.selectOne(new LambdaQueryWrapper<Project>()
                    .select(Project::getId)
                    .eq(Project::getDeptId, topDeptId));

            Project subProject = projectMapper.selectOne(new LambdaQueryWrapper<Project>()
                    .select(Project::getDeptId, Project::getId)
                    .eq(Project::getParentId, project.getId()));

            if (Objects.nonNull(subProject)) {
                String projectId = subProject.getId();
                String subDeptId = subProject.getDeptId();
                // ecs的cpu、内存配额处理
                EcsUsedQuota ecsUsedQuota = localUserMapper.getEcsUsedQuotaByPurpose("kvm", projectId);
                quotaCpuRamHandler(ecsUsedQuota, PURPOSE_TYPE_ECS, topDeptId, subDeptId, projectId);
                List<EvsUsedQuota> evsUsedQuotas = localUserMapper.getEvsVolumeUsedQuota(projectId);
                long volumeUsed = evsUsedQuotas.stream()
                        .filter(evsUsedQuota -> filterView(evsUsedQuota.getDisplayName()))
                        .mapToLong(EvsUsedQuota::getUsedTotal)
                        .sum();
                long volumeSnapshotsUsed = localUserMapper.getEvsVolumeSnapshotsUsedQuota(projectId);

                EvsUsedQuota evsUsedQuota = new EvsUsedQuota();
                evsUsedQuota.setVolumeUsed(volumeUsed);
                evsUsedQuota.setVolumeSnapshotsUsed(volumeSnapshotsUsed);
                quotaEvsHandler(evsUsedQuota, topDeptId, subDeptId, projectId);
                // 云数据库MySQL的cpu、内存配额处理
                ecsUsedQuota = localUserMapper.getEcsUsedQuotaByPurpose("db_mysql", projectId);
                quotaCpuRamHandler(ecsUsedQuota, PURPOSE_TYPE_MYSQL, topDeptId, subDeptId, projectId);
            }
        });
    }

    private void quotaEvsHandler(EvsUsedQuota evsUsedQuota, String deptId, String subDeptId, String projectId) {
        long volumeUsed = evsUsedQuota.getVolumeUsed();
        long volumeSnapshotsUsed = evsUsedQuota.getVolumeSnapshotsUsed();
        QuotaType quotaType = new QuotaType();
        quotaType.setName("evs");
        quotaType.setDescription("elastic_volume_service_1");

        QuotaMetric evsQuotaMetric = new QuotaMetric();
        evsQuotaMetric.setDepartmentId(deptId);
        evsQuotaMetric.setTotalValue(volumeUsed * 5);
        evsQuotaMetric.setUsedValue(volumeUsed * 5);
        evsQuotaMetric.setName("evs_capacity");

        QuotaMetric evsSnapshotQuotaMetric = new QuotaMetric();
        evsSnapshotQuotaMetric.setDepartmentId(deptId);
        evsSnapshotQuotaMetric.setTotalValue(volumeSnapshotsUsed * 5);
        evsSnapshotQuotaMetric.setUsedValue(volumeSnapshotsUsed * 5);
        evsSnapshotQuotaMetric.setName("evs_snapshot");

        quotaType.setQuotaMetricList(Lists.newArrayList(evsQuotaMetric, evsSnapshotQuotaMetric));
        // 更新顶级组织配额
        quotaService.updateDepartmentQuota(deptId, quotaType);

        // 更新一级组织配额
        evsQuotaMetric.setDepartmentId(subDeptId);
        evsSnapshotQuotaMetric.setDepartmentId(subDeptId);
        quotaType.setQuotaMetricList(Lists.newArrayList(evsQuotaMetric, evsSnapshotQuotaMetric));
        quotaService.updateDepartmentQuota(subDeptId, quotaType);

        // 更新项目配额
        evsQuotaMetric.setProjectId(projectId);
        evsQuotaMetric.setUsedValue(volumeUsed);
        evsSnapshotQuotaMetric.setProjectId(projectId);
        evsSnapshotQuotaMetric.setUsedValue(volumeSnapshotsUsed);
        quotaType.setQuotaMetricList(Lists.newArrayList(evsQuotaMetric, evsSnapshotQuotaMetric));
        quotaService.updateProjectQuota(projectId, quotaType);
    }

    private void quotaCpuRamHandler(EcsUsedQuota ecsUsedQuota, String purposeType, String deptId, String subDeptId, String projectId) {
        if (Objects.isNull(ecsUsedQuota) || ecsUsedQuota.getCpuUsedTotal() == 0) {
            return;
        }
        long cpuUsedTotal = ecsUsedQuota.getCpuUsedTotal();
        long ramUsedTotal = ecsUsedQuota.getRamUsedTotal();
        QuotaType quotaType = new QuotaType();
        QuotaMetric cpuQuotaMetric = new QuotaMetric();
        cpuQuotaMetric.setDepartmentId(deptId);
        cpuQuotaMetric.setTotalValue(cpuUsedTotal * 5);
        cpuQuotaMetric.setUsedValue(0L);

        QuotaMetric ramQuotaMetric = new QuotaMetric();
        ramQuotaMetric.setDepartmentId(deptId);
        ramQuotaMetric.setTotalValue(ramUsedTotal * 5);
        ramQuotaMetric.setUsedValue(0L);
        if (PURPOSE_TYPE_ECS.equals(purposeType)) {
            quotaType.setName("ecs");
            quotaType.setDescription("elastic_compute_service");

            cpuQuotaMetric.setName("ecs_cpu");
            cpuQuotaMetric.setTotalName("cpu总量（个）");
            cpuQuotaMetric.setTypeName("ecs");
            cpuQuotaMetric.setUsedName("cpu使用量（个）");

            ramQuotaMetric.setName("ecs_ram");
            ramQuotaMetric.setTotalName("内存总量（MB）");
            ramQuotaMetric.setTypeName("ecs");
            ramQuotaMetric.setUsedName("内存使用量（MB）");

            quotaType.setQuotaMetricList(Lists.newArrayList(cpuQuotaMetric,ramQuotaMetric));
        } else if (PURPOSE_TYPE_MYSQL.equals(purposeType)) {
            quotaType.setName("mysql");
            quotaType.setDescription("云数据库MySQL配额");

            cpuQuotaMetric.setName("mysql_cpu");
            cpuQuotaMetric.setTotalName("cpu总量（个）");
            cpuQuotaMetric.setTypeName("mysql");
            cpuQuotaMetric.setUsedName("cpu使用量（个）");

            ramQuotaMetric.setName("mysql_ram");
            ramQuotaMetric.setTotalName("内存总量（MB）");
            ramQuotaMetric.setTypeName("mysql");
            ramQuotaMetric.setUsedName("内存使用量（MB）");

            quotaType.setQuotaMetricList(Lists.newArrayList(cpuQuotaMetric, ramQuotaMetric));
        }

        if (PURPOSE_TYPE_ECS.equals(purposeType) || PURPOSE_TYPE_MYSQL.equals(purposeType)) {
            // 更新顶级组织配额
            quotaService.updateDepartmentQuota(deptId, quotaType);
            // 更新一级组织配额
            cpuQuotaMetric.setDepartmentId(subDeptId);
            ramQuotaMetric.setDepartmentId(subDeptId);
            cpuQuotaMetric.setTotalValue(cpuUsedTotal);
            ramQuotaMetric.setTotalValue(ramUsedTotal);
            quotaType.setQuotaMetricList(Lists.newArrayList(cpuQuotaMetric, ramQuotaMetric));
            quotaService.updateDepartmentQuota(subDeptId, quotaType);

            // 更新项目配额
            cpuQuotaMetric.setProjectId(projectId);
            cpuQuotaMetric.setUsedValue(cpuUsedTotal);
            ramQuotaMetric.setProjectId(projectId);
            ramQuotaMetric.setUsedValue(ramUsedTotal);
            quotaType.setQuotaMetricList(Lists.newArrayList(cpuQuotaMetric, ramQuotaMetric));
            quotaService.updateProjectQuota(projectId, quotaType);
        }
    }

    private boolean filterView(String volumeDisplayName) {
        //过滤条件
        String p1 = "\\w{8}(-\\w{4}){3}-\\w{12}",
                p2 = ".*-kube_masters-.*",
                p3 = "datastore-.*",
                p4 = ".*-kube_minions-.*",
                p5 = ".*-ng-master-.*",
                p6 = ".*-ng-core-.*",
                p7 = "ng-master-es-.*",
                p8 = "image-.*",
                p9 = "ng-.*";
        if (StringUtils.isNotBlank(volumeDisplayName)) {
            return !Pattern.matches(p2, volumeDisplayName)
                    && !(Pattern.matches(p3, volumeDisplayName)
                    && volumeDisplayName.substring(p3.length() - 2).matches(p1))
                    && !Pattern.matches(p4, volumeDisplayName)
                    && !Pattern.matches(p5, volumeDisplayName)
                    && !Pattern.matches(p6, volumeDisplayName)
                    && !Pattern.matches(p7, volumeDisplayName)
                    && !(Pattern.matches(p8, volumeDisplayName)
                    && volumeDisplayName.substring(p8.length() - 2).matches(p1))
                    && !Pattern.matches(p9, volumeDisplayName);
        }
        return false;
    }
}
