package com.sugon.cloud.iam.api.controller;

import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.constants.HeaderParamConstant;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.entity.IamRole;
import com.sugon.cloud.iam.api.service.SecurityService;
import com.sugon.cloud.iam.api.service.resourceauth.SecurityUserTypeAuthHandler;
import com.sugon.cloud.iam.common.model.vo.UserViewVO;
import com.sugon.cloud.resource.auth.common.aop.ResourceAuth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@Api(tags = "安全管理员API")
@RequestMapping(value = "/api/securities")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RestController
@DocumentIgnore
public class SecurityUserController {
    private final SecurityService securityService;

    @ApiOperation("根账号列表")
    @GetMapping("/master-list")
    @ApiImplicitParams({@ApiImplicitParam(name = "name", value = "名称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "page_num", value = "页码", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "page_size", value = "每页显示条数", paramType = "query", dataType = "int")})
    @ResourceAuth(handler = SecurityUserTypeAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    public ResultModel<PageCL<UserViewVO>> masterUserList(HttpServletRequest req,@RequestParam(value = "name", required = false) String name,
                                                          @RequestParam(value = "page_num", defaultValue = "1", required = false) int pageNum,
                                                          @RequestParam(value = "page_size", defaultValue = "15", required = false) int pageSize) {
        return ResultModel.success(securityService.masterUserList(name, pageNum, pageSize,req.getHeader(HeaderParamConstant.USER_ID)));
    }

    @ApiOperation("内置角色列表")
    @GetMapping("/inner-role-list")
    @ApiImplicitParams({@ApiImplicitParam(name = "name", value = "名称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "page_num", value = "页码", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "page_size", value = "每页显示条数", paramType = "query", dataType = "int")})
    @ResourceAuth(handler = SecurityUserTypeAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    public ResultModel<PageCL<IamRole>> innerRoleList(@RequestParam(value = "name", required = false) String name,
                                                      @RequestParam(value = "page_num", defaultValue = "1", required = false) int pageNum,
                                                      @RequestParam(value = "page_size", defaultValue = "15", required = false) int pageSize) {
        return ResultModel.success(securityService.innerRoleList(name, pageNum, pageSize));
    }
}
