package com.sugon.cloud.iam.api.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: yangdingshan
 * @Date: 2024/3/12 16:22
 * @Description:
 */
@Data
@ApiModel("修改项目审批类型")
public class UpdateProjectOperationTypeVO {

    @ApiModelProperty(value = "id", hidden = true)
    private String id;

    @NotNull(message = "审批类型不能为空")
    @ApiModelProperty(value = "审批类型")
    @JsonProperty("operation_approve_type")
    private String operationApproveType;
}
