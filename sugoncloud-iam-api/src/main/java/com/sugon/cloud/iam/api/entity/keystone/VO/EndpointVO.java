package com.sugon.cloud.iam.api.entity.keystone.VO;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sugon.cloud.iam.api.entity.keystone.Endpoint;
import lombok.Data;

import java.util.List;

@Data
public class EndpointVO {
    private String regionId;
    private String url;
    @JsonProperty(value = "interface")
    @TableField("interface")
    private String interfaceParam;
    private String id;
    @TableField(exist = false)
    private String region;
}
