package com.sugon.cloud.iam.api.service.message;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sugon.cloud.iam.api.entity.BusinessMessageRelation;

/**
 * @Author: yangdingshan
 * @Date: 2025/2/10 14:25
 * @Description:
 */
public interface BusinessMessageRelationService extends IService<BusinessMessageRelation> {

    /**
     * 获取模版
     * @param businessType
     * @param messageType
     * @param templateType
     * @return
     */
    BusinessMessageRelation getRelation(String businessType, String messageType, int templateType);

    boolean isEnableDangerCode();
}
