package com.sugon.cloud.iam.api.controller.keystone;

import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.iam.api.entity.keystone.CreateServiceResponse;
import com.sugon.cloud.iam.api.entity.keystone.ListResourceLinkResponse;
import com.sugon.cloud.iam.api.entity.keystone.ListServiceResponse;
import com.sugon.cloud.iam.api.entity.keystone.VO.CreateServiceVO;
import com.sugon.cloud.iam.api.entity.exception.KeyStoneMethodArgumentNotValidException;
import com.sugon.cloud.iam.api.service.ServiceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;


@RestController
@RequestMapping("/v3/services")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Api(tags = {"keystone-服务操作"})
@DocumentIgnore
public class ServiceController {

    private final ServiceService serviceService;

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @ApiOperation(value = "创建服务")
    public CreateServiceResponse createService(@RequestBody CreateServiceVO createServiceVO) {
        if (Objects.isNull(createServiceVO.getService())) {
            throw new KeyStoneMethodArgumentNotValidException("create_service() param service can not be null");
        }
        if (Objects.isNull(createServiceVO.getService().getType())) {
            throw new KeyStoneMethodArgumentNotValidException("create_service() param service.type can not be null");
        }
        return serviceService.createService(createServiceVO);
    }

    @GetMapping
    @ApiOperation(value = "查询服务")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "name", value = "服务名称", required = false, dataType = "String")
            , @ApiImplicitParam(name = "type", value = "服务类型", required = false, dataType = "String")})
    public ListServiceResponse listService(@RequestParam(value = "name", required = false) String name,
                                           @RequestParam(value = "type", required = false) String type,
                                           HttpServletRequest request) {
        ListServiceResponse listServiceResponse = new ListServiceResponse();
        ListResourceLinkResponse listResourceLinkResponse = new ListResourceLinkResponse();
        String queryString = request.getQueryString();
        StringBuffer requestURL = request.getRequestURL();
        if (!StringUtils.isEmpty(queryString)) {
            requestURL.append("?").append(queryString);
        }
        listResourceLinkResponse.setSelf(requestURL.toString());
        listServiceResponse.setServices(serviceService.listService(name, type));
        listServiceResponse.setLinks(listResourceLinkResponse);
        return listServiceResponse;
    }

    @GetMapping("/{service_id}")
    @ApiOperation(value = "查询服务详情")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "service_id", value = "服务ID", required = true, dataType = "String")})
    public CreateServiceResponse serviceDetail(@PathVariable("service_id") String serviceId) {
        return serviceService.serviceDetail(serviceId);
    }
}
