package com.sugon.cloud.iam.api.entity.keystone;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "查询服务响应")
public class ListServiceResponse {
    @ApiModelProperty("服务列表")
    private List<CreateServiceResponseParam> services;
    @ApiModelProperty("资源链接")
    private ListResourceLinkResponse links;
}
