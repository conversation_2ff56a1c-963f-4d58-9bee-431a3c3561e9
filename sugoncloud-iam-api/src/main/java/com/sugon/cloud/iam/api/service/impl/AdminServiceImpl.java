package com.sugon.cloud.iam.api.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.common.mapper.ModelMapper;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.common.utils.UUIDUtil;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.common.RecursionDeptIdUtils;
import com.sugon.cloud.iam.api.entity.IamRole;
import com.sugon.cloud.iam.api.entity.UserRoleMapping;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.exception.BusinessException;
import com.sugon.cloud.iam.api.mapper.DepartmentMapper;
import com.sugon.cloud.iam.api.mapper.IamRoleMapper;
import com.sugon.cloud.iam.api.mapper.UserMapper;
import com.sugon.cloud.iam.api.mapper.UserRoleMappingMapper;
import com.sugon.cloud.iam.api.service.AdminService;
import com.sugon.cloud.iam.api.service.GlobalsettingsService;
import com.sugon.cloud.iam.api.service.MFAAuthorizeService;
import com.sugon.cloud.iam.api.vo.AdminUserViewVO;
import com.sugon.cloud.iam.api.vo.RoleCreateOrUpdate;
import com.sugon.cloud.iam.common.model.vo.RoleResponseVO;
import com.sugon.cloud.iam.common.model.vo.UserViewVO;
import com.sugon.cloud.log.client.context.LogRecordContext;
import com.sugon.cloud.log.client.model.CommonBusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class AdminServiceImpl implements AdminService {

    private final ModelMapper mapper;
    private final DepartmentMapper departmentMapper;
    private final UserRoleMappingMapper userRoleMappingMapper;
    private final UserMapper userMapper;
    private final IamRoleMapper roleMapper;
    private final MFAAuthorizeService mfaAuthorizeService;
    private final GlobalsettingsService globalsettingsService;

    @Override
    public PageCL<AdminUserViewVO> masterUserAndSubUserList(String name, int pageNum, int pageSize) {
        IPage<User> page = new Page<>(pageNum, pageSize);
        userMapper.selectPage(page, new QueryWrapper<User>().lambda()
                .like(StringUtils.isNotBlank(name), User::getName, name)
                .eq(User::getType, TypeUtil.TYPE_MASTER)
                .orderByDesc(User::getCreatedAt));
        List<AdminUserViewVO> result = mapper.mapList(page.getRecords(), AdminUserViewVO.class);
        Map<String, Boolean> userMFAMap = mfaAuthorizeService.getUserMFAMap();
        result.forEach(u -> {
            u.setExtra(UserServiceImpl.parseUserExtra(u.getExtra(), CommonInstance.USER_EXTRA_DESCRIPTION_KEY));
            List<String> deptIds = Lists.newArrayList();
            RecursionDeptIdUtils.getDeptIds(departmentMapper.findTreeDepartment(u.getDeptId()), deptIds);
            if (!CollectionUtils.isEmpty(deptIds)) {
                List<User> subUsers = userMapper.selectList(new QueryWrapper<User>().lambda()
                        .in(User::getDeptId, deptIds)
                        .orderByDesc(User::getCreatedAt));
                subUsers.forEach(i -> i.setExtra(UserServiceImpl.parseUserExtra(i.getExtra(),
                        CommonInstance.USER_EXTRA_DESCRIPTION_KEY)));
                u.setSubUsers(mapper.mapList(subUsers, UserViewVO.class));
                u.setMfaEnabled(userMFAMap.containsKey(u.getId()));
            }
        });

        try {
            return new PageCL<AdminUserViewVO>().
                    getPageByPageHelper(page, result);
        } catch (Exception e) {
            log.error("com.sugon.cloud.iam.api.service.impl.AdminServiceImpl.masterUserAndSubUserList error:", e);
            throw new BusinessException("查询根账户列表失败");
        }
    }

    @Override
    public PageCL<UserViewVO> adminUserList(String currentUserId, String name, int pageNum, int pageSize) {
        IPage<User> page = new Page<>(pageNum, pageSize);
        List<String> userTypes;
        if (globalsettingsService.getBmStatus()) {
            userTypes = Lists.newArrayList(TypeUtil.TYPE_SYS_ADMIN, TypeUtil.TYPE_SEC_AUDITOR, TypeUtil.TYPE_SEC_ADMIN);
        } else {
            userTypes = Lists.newArrayList(TypeUtil.TYPE_ADMIN);
        }
        userMapper.selectPage(page, new QueryWrapper<User>().lambda()
                .like(StringUtils.isNotBlank(name), User::getName, name)
                .in(User::getType, userTypes)
                .ne(User::getId, currentUserId)
                .ne(User::getName, "admin")
                .ne(User::getName, "inner")
                .orderByDesc(User::getCreatedAt));
        List<UserViewVO> viewVOS = Lists.newArrayList();
        List<User> result = page.getRecords();
        Map<String, Boolean> userMFAMap =  mfaAuthorizeService.getUserMFAMap();
        for (User u : result) {
            UserViewVO userViewVO = new UserViewVO();
            BeanUtils.copyProperties(u, userViewVO);
            if (StringUtils.isBlank(userViewVO.getAlias())) {
                userViewVO.setAlias(userViewVO.getName());
            }
            if (!CollectionUtils.isEmpty(userMFAMap)) {
                userViewVO.setMfaEnabled(userMFAMap.containsKey(u.getId()));
            }
            userViewVO.setExtra(parseUserExtra(userViewVO.getExtra(), CommonInstance.USER_EXTRA_DESCRIPTION_KEY));
            viewVOS.add(userViewVO);
        }
        try {
            return new PageCL<UserViewVO>().
                    getPageByPageHelper(page, viewVOS);
        } catch (Exception e) {
            log.error("adminUserList error:", e);
            throw new BusinessException("管理员账户列表失败");
        }
    }

    /**
     * 将界面传过来的description字段转为JSON字符串，适配底层OpenStack
     *
     * @param extra
     * @return
     */
    private String parseUserExtra(String extra, String key) {
        try {
            JSONObject result = JSONObject.parseObject(extra);
            return result.getString(key);
        } catch (Exception e) {
            log.error("parseUserExtra error:[{}]", extra);
        }
        return null;
    }

    @Override
    public PageCL<RoleResponseVO> listAdminRole(String name, int pageNum, int pageSize, String userType) {
        PageCL<RoleResponseVO> pageCL = new PageCL();
        IPage<IamRole> page = new Page<>(pageNum, pageSize);
        try {
            if (globalsettingsService.getBmStatus()) {
                page = roleMapper.selectPage(page, new QueryWrapper<IamRole>().lambda()
                        .eq(IamRole::getType, StrUtil.isNotBlank(userType) ? userType : TypeUtil.TYPE_COMMON));
            } else {
                page = roleMapper.selectPage(page, new QueryWrapper<IamRole>().lambda()
                        .like(StringUtils.isNotEmpty(name), IamRole::getName, name)
                        .eq(IamRole::getType, TypeUtil.TYPE_SUB_ADMIN)
                        .orderByDesc(IamRole::getCreateTime));
            }
            pageCL = new PageCL<RoleResponseVO>().
                    getPageByPageHelper(page, mapper.mapList(page.getRecords(), RoleResponseVO.class));
        } catch (Exception e) {
            log.error("listAdminRole error:", e);
            throw new BusinessException("查询所有管理员角色列表报错");
        }
        return pageCL;
    }

    @Override
    public int createIamAdminRole(RoleCreateOrUpdate role) {
        IamRole r = mapper.map(role, IamRole.class);
        String uuid = UUIDUtil.get32UUID();
        r.setId(uuid);
        r.setType(TypeUtil.TYPE_SUB_ADMIN);
        r.setDeptId(uuid);
        boolean existed = roleMapper.selectCount(new QueryWrapper<IamRole>()
                .lambda()
                .eq(IamRole::getName, r.getName())
                .isNotNull(IamRole::getType)) > 0;
        if (existed) throw new BusinessException("角色名已存在: " + r.getName());
        LogRecordContext.putVariable("uuid", uuid);
        role.setId(uuid);
        return roleMapper.insert(r);
    }

    @Override
    public String deleteIamAdminRole(String roleId) {
        IamRole iamRole = roleMapper.selectById(roleId);
        if (Objects.isNull(iamRole)) throw new BusinessException("角色不存在: " + roleId);
        if (!TypeUtil.TYPE_SUB_ADMIN.equals(iamRole.getType())) {
            throw new CommonBusinessException("目标角色类型不正确", iamRole.getName());
        }
        userRoleMappingMapper.delete(new QueryWrapper<UserRoleMapping>().lambda().eq(UserRoleMapping::getRoleId, roleId));
        roleMapper.deleteById(roleId);
        return iamRole.getName();
    }
}
