package com.sugon.cloud.iam.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.entity.IamRole;
import com.sugon.cloud.iam.api.entity.UserRoleMapping;
import com.sugon.cloud.iam.api.vo.RoleCreateOrUpdate;
import com.sugon.cloud.iam.common.model.vo.RoleResponseVO;

import java.util.List;

public interface IamRoleService extends IService<IamRole> {

    List<IamRole> findByUserIdAndProjectId(String userId, String projectId);

    int createIamRole (RoleCreateOrUpdate role);

    String deleteRole (String roleId);

    List<RoleResponseVO> list (String userId, String projectId, boolean forOperations);

    List<String> listUseForHash(String userId);

    PageCL<RoleResponseVO> listByDept(String deptId, String name, int pageNum, int pageSize, String userType);

    PageCL<RoleResponseVO> userRoles(String userId, String name, int pageNum, int pageSize, boolean queryInnerRole, String projectId);

    PageCL<RoleResponseVO> listAll(String currentUserId, String name, int pageNum, int pageSize);

    List<UserRoleMapping> listUserRoleMappingByRoleId (String roleId);
}
