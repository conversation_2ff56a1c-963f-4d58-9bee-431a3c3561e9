package com.sugon.cloud.iam.api.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: yangdingshan
 * @Date: 2025/7/29 11:15
 * @Description:
 */
@Data
public class PolicyFilterVO {

    /**
     * 黑名单id集合
     */
    @JsonProperty("blacklist_ids")
    private List<String> blacklistIds = new ArrayList<>();

    /**
     * 微服务id集合
     */
    @JsonProperty("hidden_micro_service_ids")
    private List<String> hiddenMicroServiceIds = new ArrayList<>();
}
