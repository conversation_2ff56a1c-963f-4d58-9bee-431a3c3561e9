package com.sugon.cloud.iam.api.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sugon.cloud.common.utils.UUIDUtil;
import com.sugon.cloud.iam.api.entity.Region;
import com.sugon.cloud.iam.api.entity.RegionForm;
import com.sugon.cloud.iam.api.entity.VmwareParam;
import com.sugon.cloud.iam.api.exception.BusinessException;
import com.sugon.cloud.iam.api.mapper.RegionMapper;
import com.sugon.cloud.iam.api.service.RegionService;
import com.sugon.cloud.iam.api.utils.EncryptAndDecryptUtil;
import com.sugon.cloud.iam.api.vo.UpdateRegionVO;
import com.sugon.cloud.log.client.model.CommonBusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;


@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class RegionServiceImpl extends ServiceImpl<RegionMapper, Region> implements RegionService {

    private final EncryptAndDecryptUtil encryptAndDecryptUtil;

    @Override
    public String createRegion(RegionForm regionForm) {
        Region region = new Region();
        region.setDescription(regionForm.getDescription());
        region.setType(regionForm.getType());
        String formRegionId = regionForm.getId();
        if (StringUtils.isNotBlank(formRegionId)) {
            Region originRegion = this.getById(formRegionId);
            if (Objects.nonNull(originRegion)) {
                throw new BusinessException("域ID(" + formRegionId + ")已经存在");
            }
            region.setId(formRegionId);
        } else {
            region.setId(UUIDUtil.get32UUID());
        }
        region.setExtra("{}");
        String type = regionForm.getType();
        if (RegionForm.VMWARE_TYPE.equalsIgnoreCase(type)) {
            VmwareParam vmwareParam = regionForm.getVmwareParam();
            if (StringUtils.isBlank(vmwareParam.getVmwareName()) || StringUtils.isBlank(vmwareParam.getUrl())
                    || StringUtils.isBlank(vmwareParam.getVmwarePassword())) {
                throw new BusinessException("用户名、密码和地址都不能为空");
            }
            String vmwarePwd;
            try {
                vmwarePwd = encryptAndDecryptUtil.decryptByPublickey(vmwareParam.getPublicKey(), vmwareParam.getVmwarePassword());
            } catch (Exception e) {
                log.error("createRegion decryptByPublickey error:", e);
                throw new BusinessException("创建Vmware域失败");
            }
            String decryptVmwarePwd = encryptAndDecryptUtil.encrypt(vmwarePwd);
            vmwareParam.setVmwarePassword(decryptVmwarePwd);
            // 将Vmware相关信息存入extra字段中,设置为null来剔除publicKey
            vmwareParam.setPublicKey(null);
            region.setExtra(JSONObject.toJSONString(vmwareParam));
        }
        this.save(region);

        return region.getId();
    }

    @Override
    public void updateRegion(String regionId, UpdateRegionVO updateRegionVO) {
        Region region = this.getById(regionId);
        if (Objects.isNull(region)) {
            log.error("regionId=[{}], updateRegionDesc region not exist", regionId);
            throw new CommonBusinessException("不存在此Region");
        }
        region.setDescription(updateRegionVO.getDescription());
        if (RegionForm.VMWARE_TYPE.equalsIgnoreCase(region.getType())) {
            VmwareParam vmwareParam = updateRegionVO.getVmwareParam();
            String extra = region.getExtra();
            VmwareParam originVmwareParam = JSONObject.parseObject(extra, VmwareParam.class);
            if (StringUtils.isNotBlank(vmwareParam.getVmwareName())) {
                originVmwareParam.setVmwareName(vmwareParam.getVmwareName());
            }
            if (StringUtils.isNotBlank(vmwareParam.getUrl())) {
                originVmwareParam.setUrl(vmwareParam.getUrl());
            }
            if (StringUtils.isNotBlank(vmwareParam.getVmwarePassword())) {
                String newVmwarePwd;
                try {
                    newVmwarePwd = encryptAndDecryptUtil.decryptByPublickey(vmwareParam.getPublicKey(), vmwareParam.getVmwarePassword());
                } catch (Exception e) {
                    log.error("updateRegion decryptByPublickey error:", e);
                    throw new BusinessException("修改Vmware域失败");
                }
                String newDecryptVmwarePwd = encryptAndDecryptUtil.encrypt(newVmwarePwd);
                originVmwareParam.setVmwarePassword(newDecryptVmwarePwd);
            }
            region.setExtra(JSONObject.toJSONString(originVmwareParam));
        }
        this.updateById(region);
    }
}
