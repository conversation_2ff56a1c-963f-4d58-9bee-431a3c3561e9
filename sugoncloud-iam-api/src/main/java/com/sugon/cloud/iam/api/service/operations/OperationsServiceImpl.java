package com.sugon.cloud.iam.api.service.operations;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.service.OperationsService;
import com.sugon.cloud.iam.api.utils.HttpTemplateUtils;
import com.sugon.cloud.iam.api.vo.ProjectContractOptionQueryParamVO;
import com.sugon.cloud.iam.api.vo.ProjectContractVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: yangdingshan
 * @Date: 2024/6/19 17:19
 * @Description:
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class OperationsServiceImpl implements OperationsService {

    @Value("${sugoncloudOperationsApiUrl:}")
    private String sugoncloudOperationsApiUrl;

    @Override
    public ResultModel<PageCL<ProjectContractVO>> projectContractOptions(ProjectContractOptionQueryParamVO projectContractOptionQueryParamVO) {
        String url = sugoncloudOperationsApiUrl + "/api/contracts/project-options";
        return HttpTemplateUtils.post(url, projectContractOptionQueryParamVO, null, new TypeReference<ResultModel<PageCL<ProjectContractVO>>>() {});
    }

    @Override
    public ResultModel<PageCL<ProjectContractVO>> list(int pageNum, int pageSize, String orgId, String contractType, String meterType, String paymentType, String saleUser, String startTime, String endTime, String projectId, String resourceId, String name, String contractCode) {
        StringBuffer url = new StringBuffer(sugoncloudOperationsApiUrl)
                .append("/api/contracts?1=1")
                .append("&page_num={page_num}")
                .append("&page_size={page_size}")
                .append("&org_id={org_id}")
                .append("&contract_type={contract_type}")
                .append("&meter_type={meter_type}")
                .append("&payment_type={payment_type}")
                .append("&sale_user={sale_user}")
                .append("&start_time={start_time}")
                .append("&end_time={end_time}")
                .append("&project_id={project_id}")
                .append("&resource_id={resource_id}")
                .append("&name={name}")
                .append("&contract_code={contract_code}");

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("page_num", pageNum);
        paramMap.put("page_size", pageSize);
        paramMap.put("org_id", orgId);
        paramMap.put("contract_type", contractType);
        paramMap.put("meter_type", meterType);
        paramMap.put("payment_type", paymentType);
        paramMap.put("sale_user", saleUser);
        paramMap.put("start_time", startTime);
        paramMap.put("end_time", endTime);
        paramMap.put("project_id", projectId);
        paramMap.put("resource_id", resourceId);
        paramMap.put("name", name);
        paramMap.put("contract_code", contractCode);
        return HttpTemplateUtils.get(url.toString(), paramMap, null, new TypeReference<ResultModel<PageCL<ProjectContractVO>>>() {});
    }

    @Override
    public ResultModel getAllApplyListByProjectId(String projectId, String approvalStatus) {
        String url = sugoncloudOperationsApiUrl + "/api/resourceApply/by-project-id?project_id={project_id}&approval_status={approval_status}";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("project_id", projectId);
        paramMap.put("approval_status", approvalStatus);
        return HttpTemplateUtils.get(url, paramMap, null, new TypeReference<ResultModel>() {
        });
    }
}
