package com.sugon.cloud.iam.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Data;


public enum ActionTypeEnum {

    READ("read","读"),
    WRITE("write","写"),
    LIST("list","列表"),
    AUTH("auth","权限");
    @EnumValue
    private String name;
    private String describe;
    ActionTypeEnum(String name, String describe) {
        this.name = name;
        this.describe = describe;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }
}
