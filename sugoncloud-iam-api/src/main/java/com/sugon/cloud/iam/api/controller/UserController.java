package com.sugon.cloud.iam.api.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.constants.HeaderParamConstant;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.common.mapper.ModelMapper;
import com.sugon.cloud.common.model.IdNameInfo;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.ann.CreateGroup;
import com.sugon.cloud.iam.api.ann.SubCreateGroup;
import com.sugon.cloud.iam.api.ann.UpdateGroup;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.entity.GlobalsettingsEntity;
import com.sugon.cloud.iam.api.entity.UserAccessEntity;
import com.sugon.cloud.iam.api.entity.UserAuthorizeSettings;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.enums.UserAuthorizeType;
import com.sugon.cloud.iam.api.exception.BusinessException;
import com.sugon.cloud.iam.api.jwt.JwtTokenFilter;
import com.sugon.cloud.iam.api.jwt.JwtTokenUtils;
import com.sugon.cloud.iam.api.mapper.UserAuthorizeSettingsMapper;
import com.sugon.cloud.iam.api.mapper.UserMapper;
import com.sugon.cloud.iam.api.service.GlobalsettingsService;
import com.sugon.cloud.iam.api.service.IamRoleService;
import com.sugon.cloud.iam.api.service.ProjectService;
import com.sugon.cloud.iam.api.service.UserAccessService;
import com.sugon.cloud.iam.api.service.UserService;
import com.sugon.cloud.iam.api.service.impl.UserServiceImpl;
import com.sugon.cloud.iam.api.service.resourceauth.DeptResourceAuthHandler;
import com.sugon.cloud.iam.api.service.resourceauth.DeptResourceNoNormalAuthHandler;
import com.sugon.cloud.iam.api.service.resourceauth.ProjectResourceAuthHandler;
import com.sugon.cloud.iam.api.service.resourceauth.ProjectResourceExtentAuthHandler;
import com.sugon.cloud.iam.api.service.resourceauth.RoleAuthExtentHandler;
import com.sugon.cloud.iam.api.service.resourceauth.RoleAuthHandler;
import com.sugon.cloud.iam.api.service.resourceauth.SecurityUserTypeAuthHandler;
import com.sugon.cloud.iam.api.service.resourceauth.UserCreateAuthHandler;
import com.sugon.cloud.iam.api.service.resourceauth.UserIdResourceAuthHandler;
import com.sugon.cloud.iam.api.service.resourceauth.UserResourceAuthHandler;
import com.sugon.cloud.iam.api.service.resourceauth.UserResourceNoNormalAuthHandler;
import com.sugon.cloud.iam.api.utils.CommonUtils;
import com.sugon.cloud.iam.api.utils.EncryptAndDecryptUtil;
import com.sugon.cloud.iam.api.vo.*;
import com.sugon.cloud.iam.common.model.dto.UserDeptAndUserIdsDTO;
import com.sugon.cloud.iam.common.model.dto.UserOwnDeptAndProjectDTO;
import com.sugon.cloud.iam.common.model.vo.ProjectDetailVO;
import com.sugon.cloud.iam.common.model.vo.RoleResponseVO;
import com.sugon.cloud.iam.common.model.vo.UserViewVO;
import com.sugon.cloud.iam.common.model.vo.UserVo;
import com.sugon.cloud.log.client.context.LogRecordContext;
import com.sugon.cloud.log.client.service.MessageFeignService;
import com.sugon.cloud.log.client.starter.annotation.LogRecordAnnotation;
import com.sugon.cloud.log.common.model.vo.MessageVo;
import com.sugon.cloud.resource.auth.common.aop.ResourceAuth;
import com.sugon.cloud.resource.auth.common.aop.ResourceAuths;
import com.sugon.cloud.resource.auth.common.service.ResourceAuthHandler;
import com.sugon.cloud.resource.auth.common.util.ResourceAuthUtils;
import io.jsonwebtoken.lang.Collections;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jasypt.encryption.StringEncryptor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Api(tags = "用户API")
@RequestMapping(value = "/api/users")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RestController
@Slf4j
@RefreshScope
public class UserController {
    private final UserService userService;
    private final ProjectService projectService;
    private final IamRoleService iamRoleService;
    private final ModelMapper mapper;
    private final UserMapper userMapper;
    private final EncryptAndDecryptUtil encryptAndDecryptUtil;
    private final MessageFeignService messageFeignService;
    private final RedisTemplate redisTemplate;
    @Value("${encryption.enabled}")
    private boolean encryptionEnabled;
    @Value("${mfa.enabled}")
    private boolean mfaEnabled;
    @Value("${iam.securityAdminUserId}")
    private String securityAdminUserId;
    @Value("${iam.inner.userId:0d2bbb018e8b44b985a169647379f413}")
    private String adminUserId;
    private final GlobalsettingsService globalsettingsService;
    private final JwtTokenUtils jwtTokenUtils;
    private final HttpServletRequest request;
    private final UserAuthorizeSettingsMapper userAuthorizeSettingsMapper;
    private final UserAccessService userAccessService;
    private final StringEncryptor encryptor;
    @Value("${user.contact-info.mask.enabled:true}")
    private boolean maskEnabled;

    @ApiOperation("注册用户")
    @PostMapping
    @LogRecordAnnotation(value = "{{#value}}", operatorId = "{{#_ret.resource}}", operatorName = "{{#user.name}}",
            detail = "{{#detail}}", resourceId = "{{#userId}}", resource = "{{#name}}")
    public ResultModel<String> register(@Validated({CreateGroup.class}) @RequestBody UserCreateOrUpdateVO user,HttpServletRequest request) {
        User loginUser = userMapper.selectById(request.getHeader(HeaderParamConstant.USER_ID));
        QueryWrapper<GlobalsettingsEntity> queryWrapper = new QueryWrapper<GlobalsettingsEntity>();
        queryWrapper.eq("policy_name", "register_enable")
                .eq("policy_type", "user");
        GlobalsettingsEntity globalsettingsEntity = globalsettingsService.getOne(queryWrapper);
        //没查询到globalsettingsEntity ， 给一个默认值
        if (Objects.isNull(globalsettingsEntity)) {
            globalsettingsEntity = new GlobalsettingsEntity();
            globalsettingsEntity.setPolicyDocument("true");
        }
        if(Objects.isNull(loginUser)){
            if("false".equals(globalsettingsEntity.getPolicyDocument())){
                return ResultModel.error("不支持自主注册账号");
            }
        }else {
            if(!TypeUtil.TYPE_SECURITY.equals(loginUser.getType())
                    && !TypeUtil.TYPE_ADMIN.equals(loginUser.getType())
                    && !TypeUtil.TYPE_SYS_ADMIN.equals(loginUser.getType())){
                if("false".equals(globalsettingsEntity.getPolicyDocument())){
                    return ResultModel.error("不支持自主注册账号");
                }
            }
        }
        String userId = userService.createUser(user);
        // 日志：区分是创建用户还是创建组织
        String message = "注册用户成功";
        String value = "注册用户";
        String name = user.getName();
        String detail = "注册用户：" + name;
        if (StringUtils.isNotBlank(user.getDepartmentName())) {
            message = "创建组织成功";
            value = "创建组织";
            name = user.getDepartmentName();
            detail = "创建组织：" + name;
        }
        LogRecordContext.putVariable("value", value);
        LogRecordContext.putVariable("name", name);
        LogRecordContext.putVariable("detail", detail);
        LogRecordContext.putVariable("userId", userId);
        ResultModel resultModel = ResultModel.success(message, userId);
        return resultModel;
    }

    @ApiOperation("创建用户")
    @PostMapping("sub")
    @LogRecordAnnotation(value = "创建用户", detail = "创建组织{DEPARTMENT{#user.deptId}}下的用户:{{#user.name}}", resourceId = "{{#id}}", resource = "{{#user.name}}")
    @ResourceAuths(value = {
            @ResourceAuth(handler = UserCreateAuthHandler.class, resources = TypeUtil.TYPE_ADMIN),
            @ResourceAuth(handler = DeptResourceAuthHandler.class, resources = "#user.deptId"),
//            @ResourceAuth(handler = UserCreateDeptManagerAuthHandler.class, resources = "#user.departmentManager"),
    })
    public ResultModel<String> createSub(@RequestBody @Validated({SubCreateGroup.class, CreateGroup.class}) UserCreateOrUpdateVO user) {
        String id = userService.createSubUser(user, true);
        LogRecordContext.putVariable("id", id);
        return ResultModel.success("创建用户成功", id);
    }

    @ApiOperation("通过用户ID查询用户列表")
    @GetMapping("{user_id}/list")
    @ApiImplicitParams({@ApiImplicitParam(name = "user_id", value = "用户ID", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "page_num", value = "页码", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "page_size", value = "每页显示条数", paramType = "query", dataType = "int")})
    @ResourceAuth(handler = UserResourceNoNormalAuthHandler.class, resources = "#userId")
    public ResultModel<PageCL<UserViewVO>> userList(@PathVariable("user_id") String userId,
                                                    @RequestParam(value = "page_num", defaultValue = "0", required = false) int pageNum,
                                                    @RequestParam(value = "page_size", defaultValue = "15", required = false) int pageSize,
                                                    HttpServletRequest request) {
        PageCL<UserViewVO> userPage = userService.getUserPage(userId, pageNum, pageSize, request.getHeader(HeaderParamConstant.USER_ID));
        return ResultModel.success(userPage);
    }

    @ApiOperation("通过用户ID数组查询用户")
    @GetMapping("/by-ids")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "用户ID数组", required = true, allowMultiple = true, dataType = "String", paramType = "query"),
    })
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#ids")
    public ResultModel<List<UserVo>> users(@RequestParam("ids") List<String> ids) {
        if (Collections.isEmpty(ids)) return ResultModel.error("查询的用户ID为空");
        List<UserVo> users = userService.getUsers(ids);
        users.forEach(user -> {
            user.setPassword(null);
        });
        return ResultModel.success(users);
    }

    @ApiOperation("通过组织ID查询用户列表")
    @GetMapping("dept/{dept_id}/list")
    @ApiImplicitParams({@ApiImplicitParam(name = "dept_id", value = "组织ID", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "page_num", value = "页码", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "page_size", value = "每页显示条数", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "name", value = "用户名", paramType = "query", dataType = "String")})
    @ResourceAuth(handler = DeptResourceNoNormalAuthHandler.class, resources = "#deptId")
    public ResultModel<PageCL<UserViewVO>> userListByDeptId(@PathVariable("dept_id") String deptId,
                                                            @RequestParam(value = "page_num", defaultValue = "0", required = false) int pageNum,
                                                            @RequestParam(value = "page_size", defaultValue = "15", required = false) int pageSize,
                                                            @RequestParam(value = "name", required = false) String name,
                                                            HttpServletRequest request) {
        PageCL<UserViewVO> userPage = userService.getUserPageByDeptId(deptId, pageNum, pageSize, name,
                request.getHeader(HeaderParamConstant.USER_ID),
                request.getHeader(HeaderParamConstant.USER_TYPE));
        /*if (encryptionEnabled) {
            User user = userService.getById(request.getHeader(HeaderParamConstant.USER_ID));
            String cert = JSONObject.parseObject(user.getExtra()).getString(CommonInstance.ENCRYPTION_CONTENT_KEY);
            String s = encryptAndDecryptUtil.encryptEnvelope(cert, JSONObject.toJSONString(userPage, SerializerFeature.WriteMapNullValue));
            return ResultModel.success("获取列表成功",s);
        }*/
        return ResultModel.success(userPage);
    }

    @ApiOperation("用户详情")
    @GetMapping("{user_id}")
//    @LogRecordAnnotation(value = "查看用户详情", detail = "查看用户{USER{#userId}}详情{{#_ret.resource}}", resourceId = "{{#userId}}", resource = "{USER{#userId}}")
    @ApiImplicitParams({@ApiImplicitParam(name = "user_id", value = "用户id", required = true, dataType = "String", paramType = "path")})
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    public ResultModel<UserViewVO> userDetail(@PathVariable("user_id") String userId, HttpServletRequest request) {

        User user = userService.getById(userId);
        if (Objects.isNull(user)) throw new BusinessException("不存在该ID[" + userId + "]的用户");
        if (encryptionEnabled) {
            user.setEmail(encryptAndDecryptUtil.decryptByIv(user.getEmail(), user.getIv()));
            user.setPhone(encryptAndDecryptUtil.decryptByIv(user.getPhone(), user.getIv()));
        }
        user.setExtra(UserServiceImpl.parseUserExtra(user.getExtra(), CommonInstance.USER_EXTRA_DESCRIPTION_KEY));
        UserViewVO result = mapper.map(user, UserViewVO.class);
        if( mfaEnabled ){
            UserAuthorizeSettings userAuthorizeSettingsCheck = userAuthorizeSettingsMapper.selectOne(new LambdaQueryWrapper<UserAuthorizeSettings>()
                    .eq(UserAuthorizeSettings::getUserId, userId)
                    .eq(UserAuthorizeSettings::getType, UserAuthorizeType.MFA));
            if( !Objects.isNull(userAuthorizeSettingsCheck) ){
                result.setMfaEnabled(true);
            }
        }
        String token = JwtTokenFilter.resolveTokenPublic(request);
        String lockCount = CommonInstance.UNAME_ERROR_COUNT + token;
        Object o = redisTemplate.opsForValue().get(lockCount);
        if (o != null) {
            result.setLastLoginErrorCount(Integer.parseInt(String.valueOf(o)));
        }
        if (StringUtils.isBlank(result.getAlias())) {
            result.setAlias(result.getName());
        }
        return ResultModel.success(result);

    }

    @ApiOperation("用户详情-不加数字信封")
    @GetMapping("/{user_id}/not-envelope")
    @ApiImplicitParams({@ApiImplicitParam(name = "user_id", value = "用户id", required = true, dataType = "String", paramType = "path")})
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    @DocumentIgnore
    public ResultModel<UserViewVO> userDetailNotEnvelope(@PathVariable("user_id") String userId) {
        User user = userService.getById(userId);
        if (Objects.isNull(user)) throw new BusinessException("不存在该ID[" + userId + "]的用户");
        if (encryptionEnabled) {
            String email = encryptAndDecryptUtil.decryptByIv(user.getEmail(), user.getIv());
            user.setEmail(email);
            String phone = encryptAndDecryptUtil.decryptByIv(user.getPhone(), user.getIv());
            user.setPhone(phone);
        }
        user.setExtra(UserServiceImpl.parseUserExtra(user.getExtra(), CommonInstance.USER_EXTRA_DESCRIPTION_KEY));
        UserViewVO result = mapper.map(user, UserViewVO.class);
        if (StringUtils.isBlank(result.getAlias())) {
            result.setAlias(result.getName());
        }
        return ResultModel.success(result);
    }

    @Deprecated
    @ApiOperation("修改密码(deprecated)")
    @PutMapping("{user_id}/password/{password}/{old_password}")
    @LogRecordAnnotation(value = "修改密码", detail = "修改用户[{{#_ret.resource}}]的密码", resourceId = "{{#userId}}", resource = "{USER{#userId}}")
    @ApiImplicitParams({@ApiImplicitParam(name = "user_id", value = "用户id", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "password", value = "用户新密码", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "old_password", value = "用户旧密码", required = true, dataType = "String", paramType = "path")})
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    @DocumentIgnore
    public ResultModel<String> updatePassword(@PathVariable("user_id") String userId,
                                      @PathVariable("password") String password,
                                      @PathVariable("old_password") String oldPassword) {

        return ResultModel.success("修改密码成功", userService.updatePassword(userId, password, oldPassword));
    }



    @ApiOperation("修改密码")
    @PutMapping("{user_id}/update-password")
    @LogRecordAnnotation(value = "修改密码", detail = "修改用户[{{#name}}]的密码", resourceId = "{{#userId}}", resource = "{USER{#userId}}")
    @ApiImplicitParams({@ApiImplicitParam(name = "user_id", value = "用户id", required = true, dataType = "String", paramType = "path")})
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    public ResultModel<String> updatePasswordV2(@PathVariable("user_id") String userId,
                                        @Validated @RequestBody UpdateUserPwdVO updateUserPwdVO) {
        try {

            String oldPassword = updateUserPwdVO.getOldPassword();
            String newPassword = updateUserPwdVO.getPassword();
            oldPassword = encryptAndDecryptUtil.decryptByPublickeyNotDelete(updateUserPwdVO.getPublickey(), oldPassword);
            newPassword = encryptAndDecryptUtil.decryptByPublickey(updateUserPwdVO.getPublickey(), newPassword);
            String s = userService.updatePassword(userId, newPassword, oldPassword);
            if (encryptionEnabled) {
                userService.updateUserHash(userId);
            }
            return ResultModel.success("修改密码成功", s, s);
        } catch (Exception e) {
            if(CommonUtils.isContainChinese(e.getMessage())){
                throw new BusinessException(e.getMessage());
            }else {
                throw new BusinessException("修改密码失败");
            }
        }

    }

    @ApiOperation("重置密码")
    @PutMapping("{user_id}/password")
    @LogRecordAnnotation(value = "重置密码", detail = "重置用户[{{#_ret.resource}}]的密码", resourceId = "{{#userId}}", resource = "{USER{#userId}}")
    @ApiImplicitParams({@ApiImplicitParam(name = "user_id", value = "用户id", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "password", value = "密码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "publickey", value = "密码公钥匙", dataType = "String", paramType = "query")})
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    @DocumentIgnore
    public ResultModel<String> resetPassword(@PathVariable("user_id") String userId,
                                             @RequestParam("password") String password,
                                             @RequestParam(value = "publickey", required = false) String publickey,
                                             HttpServletRequest request) {
        User user = userService.getById(userId);
        if (Objects.isNull(user)) throw new BusinessException("用户不存在ID:" + userId);
        if ("inner".equals(user.getName())) {
            throw new BusinessException("内置用户禁止修改密码");
        }
        String token = request.getHeader(CommonInstance.AUTHORIZATION).substring(7);
        String currentUsername = jwtTokenUtils.getUsernameFromToken(token);
        User currentUser= userService.getOne(new LambdaQueryWrapper<User>().eq(User::getName, currentUsername));
        //future 安全管理员和日志管理员只能securityamin能重置 start
        if (CommonInstance.USER_TYPE_SECURITY.equals(user.getType())
                || CommonInstance.USER_TYPE_AUDIT.equals(user.getType())) {
            if (!"security".equals(currentUser.getType())) {
                throw new BusinessException("当前用户不能重置该用户密码");
            }
        }
        // 保密
        if (CommonInstance.SEC_ADMIN.equals(user.getType())
                || CommonInstance.SEC_AUDITOR.equals(user.getType())) {
            if (!CommonInstance.SEC_ADMIN.equals(currentUser.getType())) {
                throw new BusinessException("当前用户不能重置该用户密码");
            }
        }
        //future 只能security admin重置内置用户密码 start
        // userSource为空表示为本平台注册的用户
        String userSource = UserServiceImpl.parseUserExtra(user.getExtra(), CommonInstance.USER_EXTRA_SOURCE_KEY);
        if (CommonInstance.USER_TYPE_ADMIN.equals(user.getType()) && StringUtils.isBlank(userSource)) {
            if (!"security".equals(currentUser.getType()) && !"admin".equals(currentUser.getName())) {
                throw new BusinessException("当前用户不能重置该用户密码");
            }
        }
        if (CommonInstance.SYS_ADMIN.equals(user.getType()) && StringUtils.isBlank(userSource)) {
            if (!CommonInstance.SEC_ADMIN.equals(currentUser.getType()) && !CommonInstance.SYS_ADMIN.equals(currentUser.getName())) {
                throw new BusinessException("当前用户不能重置该用户密码");
            }
        }
        //future 只能security admin重置内置用户密码 end
        if (StringUtils.isBlank(password)) throw new BusinessException("密码不能为空");
        try {
            password = encryptAndDecryptUtil.decryptByPublickey(publickey, password);
        } catch (Exception e) {
            log.error("error", e);
            throw new BusinessException("重置密码失败");
        }
        String pwd = new BCryptPasswordEncoder().encode(password);
        if (encryptionEnabled) {
            pwd = encryptAndDecryptUtil.encryptByIv(pwd, user.getIv());
        }
        userService.lambdaUpdate()
                .eq(User::getId, userId)
                .set(User::getPassword, pwd)
                .update();
        MessageVo messageVo = new MessageVo();
        messageVo.setUser(user.getId());
        messageVo.setSvc("sugoncloud-iam-api");
        messageVo.setContent("您的用户密码已被修改");
        messageVo.setEmailEnable(true);
        messageFeignService.createMessage(messageVo);
        messageVo.setUser(securityAdminUserId);
        messageVo.setContent("[" + user.getName() + "]用户密码已被修改");
        messageFeignService.createMessage(messageVo);
        // 重置日志管理员的密码，同时也需要给admin发送信息
        if (TypeUtil.TYPE_AUDIT.equals(user.getType())) {
            messageVo.setUser(adminUserId);
            messageFeignService.createMessage(messageVo);
        }
        if (encryptionEnabled) {
            userService.updateUserHash(userId);
        }
        return ResultModel.success("重置密码成功", user.getName(), user.getName());
    }

    @ApiOperation("修改用户")
    @LogRecordAnnotation(value = "修改用户", detail = "修改用户:{{#user.name}}", resourceId = "{{#user.id}}", resource = "{{#user.name}}")
    @PutMapping
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#user.id")
    public ResultModel<String> update(@RequestBody @Validated(UpdateGroup.class) UserCreateOrUpdateVO user,HttpServletRequest request) {

        userService.updateUserCheck(user);
        User userOrigin = userService.getById(user.getId());
        if (Objects.isNull(userOrigin)) throw new BusinessException("用户不存在ID:" + user.getId());
        if (!userOrigin.getName().equals(user.getName())) {
            throw new BusinessException("用户账号不能修改");
        }
        User u = new User();
        BeanUtils.copyProperties(user, u);
        u.setName(userOrigin.getName());
        u.setPassword(null);
        JSONObject extraObj;
        if (StringUtils.isNotBlank(userOrigin.getExtra())) {
            extraObj = JSONObject.parseObject(userOrigin.getExtra());
            extraObj.put(CommonInstance.USER_EXTRA_DESCRIPTION_KEY, u.getExtra());
        } else {
            extraObj = JSONObject.parseObject(UserServiceImpl.wrapperUserExtra(u.getExtra()));
        }
        u.setExtra(extraObj.toString());
        if (encryptionEnabled) {
            User ivUser = userService.getById(u.getId());
            String iv = ivUser.getIv();
            // 电话号码
            if (StringUtils.isNotBlank(user.getPhone())) {
                //加密存储
                u.setPhone(encryptAndDecryptUtil.encryptByIv(user.getPhone(), iv));
            }
            // 邮箱
            if (StringUtils.isNotBlank(user.getEmail())) {
                //加密存储
                u.setEmail(encryptAndDecryptUtil.encryptByIv(user.getEmail(), iv));
            }
        }
        userService.saveOrUpdate(u);
        if (encryptionEnabled) {
            userService.updateUserHash(user.getId());
        }
        MessageVo messageVo = new MessageVo();
        messageVo.setUser(user.getId());
        messageVo.setSvc("sugoncloud-iam-api");
        messageVo.setContent("您的用户信息已被修改");
        messageVo.setEmailEnable(true);
        messageFeignService.createMessage(messageVo);
        messageVo.setUser(securityAdminUserId);
        messageVo.setContent("[" + user.getName() + "]用户信息已被修改");
        messageFeignService.createMessage(messageVo);
        return ResultModel.success("修改用户成功", null);
    }

    @ApiOperation("修改用户状态")
    @PutMapping("/user_id/{user_id}/status/{status}")
    @LogRecordAnnotation(value = "修改用户状态", detail = "修改用户[{{#_ret.resource}}]的状态", resourceId = "{{#userId}}", resource = "{USER{#userId}}", auditFlag = "true")
    @ApiImplicitParams({@ApiImplicitParam(name = "user_id", value = "用户id", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "status", value = "用户状态", required = true, dataType = "Boolean", paramType = "path")})
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    public ResultModel<String> updateUserType(@PathVariable("user_id") String userId,
                                      @PathVariable("status") Boolean status) {

        User user = userService.getById(userId);
        if (Objects.isNull(user)) throw new BusinessException("用户不存在ID:" + userId);
        //bug 3724 start 主要是为了解决长时间未登录 账户被冻结之后 重新激活 更新登录时间
        userService.lambdaUpdate().eq(User::getId, userId)
                .set(User::getEnabled, status)
                .set(User::getLastActiveAt, new Date())
                .update();
        //bug 3724 end
        MessageVo messageVo = new MessageVo();
        messageVo.setUser(userId);
        messageVo.setSvc("sugoncloud-iam-api");
        if (status) {
            messageVo.setContent("您的用户被设置为激活状态");
        } else {
            messageVo.setContent("您的用户被设置为禁用状态");
        }
        messageVo.setEmailEnable(true);
        //发给安全管理员
        messageVo.setUser(securityAdminUserId);
        if (status) {
            messageVo.setContent("[" + user.getName() + "]用户被设置为激活状态");
        } else {
            //fixed bug 3194
            try {
                Set<String> keys = redisTemplate.keys(user.getName() + ":*");
                if (ObjectUtil.isNotNull(keys)) {
                    redisTemplate.delete(keys);
                }
            } catch (Exception e) {
                log.error("update User Type and remove token error", e);
            }
            messageVo.setContent("[" + user.getName() + "]用户被设置为禁用状态");
        }
        messageFeignService.createMessage(messageVo);
        if (encryptionEnabled) {
            userService.updateUserHash(userId);
        }
        return ResultModel.success("修改用户状态成功", user.getName(), null);
    }

    @ApiOperation("删除用户")
    @DeleteMapping("{user_id}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "user_id", value = "用户ID", required = true, dataType = "String", paramType = "path"),
    })
    @LogRecordAnnotation(value = "{{#value}}", detail = "{{#detail}}", resourceId = "{{#userId}}", resource = "{{#name}}")
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    public ResultModel<String> delete(@PathVariable("user_id") String userId) {

        return ResultModel.success("删除成功", userService.delete(userId), null);
    }

    @ApiOperation("用户绑定组织角色、项目")
    @PostMapping("{user_id}/roles/{role_id}")
    @LogRecordAnnotation(value = "用户绑定组织角色、项目", detail = "用户[{USER{#userId}}]绑定组织角色[{ROLE{#roleId}}],项目[{PROJECT{#projectId}}]", resourceId = "{{#userId}}", resource = "{USER{#userId}}")
    @ApiImplicitParams({@ApiImplicitParam(name = "user_id", value = "用户id", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "role_id", value = "角色Id", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "project_id", value = "项目Id", required = false, dataType = "String", paramType = "query")})
    @ResourceAuths(value = {@ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
            , @ResourceAuth(handler = RoleAuthHandler.class, resources = "#roleId")})
    public ResultModel<String> assignmentRole(@PathVariable("user_id") String userId,
                                      @PathVariable("role_id") String roleId,
                                      @RequestParam(value = "project_id", required = false) String projectId) {

        userService.assignmentRole(userId, roleId, projectId, true);
        //TODO 更新角色完整性
        this.updateUserRoleHash(userId);
        return ResultModel.success("用户绑定/解绑组织角色、项目成功", null);
    }

    @ApiOperation("用户绑定项目")
    @PostMapping("{user_id}/project/{project_id}")
    @LogRecordAnnotation(value = "用户绑定项目", detail = "用户[{USER{#userId}}]绑定项目[{PROJECT{#projectId}}]", resourceId = "{{#userId}}", resource = "{USER{#userId}}")
    @ApiImplicitParams({@ApiImplicitParam(name = "user_id", value = "用户id", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "project_id", value = "项目Id", required = true, dataType = "String", paramType = "path")})
    @ResourceAuths({@ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId"),
            @ResourceAuth(handler = ProjectResourceAuthHandler.class, resources = "#projectId")})
    public ResultModel<String> assignmentProject(@PathVariable("user_id") String userId,
                                         @PathVariable("project_id") String projectId) {

        userService.assignmentProject(userId, projectId);
        return ResultModel.success("用户绑定/解绑项目成功", null);
    }

    @ApiOperation("用户移除角色")
    @DeleteMapping("{user_id}/roles/{role_id}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "user_id", value = "用户ID", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "role_id", value = "角色ID", required = true, dataType = "String", paramType = "path"),
    })
    @LogRecordAnnotation(value = "用户移除角色", detail = "用户[{USER{#userId}}]移除角色[{ROLE{#roleId}}]", resourceId = "{{#userId}}", resource = "{USER{#userId}}")
    @ResourceAuths({@ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId"),
            @ResourceAuth(handler = RoleAuthHandler.class, resources = "#roleId")})
    public ResultModel<String> removeRole(@PathVariable("user_id") String userId,
                                          @PathVariable("role_id") String roleId) {

        userService.removeRole(userId, roleId);
        //TODO 更新角色完整性
        this.updateUserRoleHash(userId);
        MessageVo messageVo = new MessageVo();
        messageVo.setSvc("sugoncloud-iam-api");
        messageVo.setEmailEnable(true);
        messageVo.setUser(userId);
        messageVo.setContent("您的用户角色权限已变更");
        messageFeignService.createMessage(messageVo);
        return ResultModel.success("用户移除角色成功", null);
    }

    @ApiOperation("用户的project列表")
    @GetMapping("{user_id}/projects")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "user_id", value = "用户ID", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "name", value = "用户名", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "page_num", value = "页码", defaultValue = "1", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "page_size", value = "每页显示条数", defaultValue = "15", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "project_type", value = "项目类型", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "meter_type", value = "计费方式计费类型", dataType = "String", paramType = "query"),
    })
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    public ResultModel<PageCL<ProjectDetailVO>> userProjects(@PathVariable("user_id") String userId,
                                                             @RequestParam(value = "name", required = false) String name,
                                                             @RequestParam(value = "page_num", defaultValue = "1", required = false) int pageNum,
                                                             @RequestParam(value = "page_size", defaultValue = "15", required = false) int pageSize,
                                                             @RequestParam(value = "project_type", required = false) String projectType,
                                                             @RequestParam(value = "meter_type", required = false) String meterType) {

        PageCL<ProjectDetailVO> pageResult = projectService.pageList(userId, null, name, pageNum, pageSize, false, projectType, meterType);
        return ResultModel.success(pageResult);
    }

    @ApiOperation("用户的role列表")
    @GetMapping("/{user_id}/roles")
    @ApiImplicitParams({@ApiImplicitParam(name = "user_id", value = "用户ID", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "name", value = "模糊查询名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "query_inner_role", value = "是否查询内置角色", paramType = "query", dataType = "boolean"),
            @ApiImplicitParam(name = "page_num", value = "页码", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "page_size", value = "每页显示条数", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "project_id", value = "项目ID", paramType = "query", dataType = "String")})
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    public ResultModel<PageCL<RoleResponseVO>> userRoles(@PathVariable("user_id") String userId,
                                                         @RequestParam(value = "query_inner_role", defaultValue = "false", required = false) boolean queryInnerRole,
                                                         @RequestParam(value = "name", required = false) String name,
                                                         @RequestParam(value = "page_num", defaultValue = "1", required = false) int pageNum,
                                                         @RequestParam(value = "page_size", defaultValue = "15", required = false) int pageSize,
                                                         @RequestParam(value = "project_id", required = false) String projectId) {


        PageCL<RoleResponseVO> pageResult = iamRoleService.userRoles(userId, name, pageNum, pageSize, queryInnerRole, projectId);
        return ResultModel.success(pageResult);
    }

    @ApiOperation("用户批量绑定/解绑组织角色、项目")
    @PostMapping("{user_id}/bind-roles")
    @LogRecordAnnotation(value = "用户绑定/解绑部门角色、项目",
            detail = "用户[{USER{#userId}}]绑定/解绑部门角色{{#projectIdIsNull ? '':','}}{PROJECT_PREFIX{#userBindRolesVO.projectId}}",
            resourceId = "{{#userId}}", resource = "{USER{#userId}}", auditFlag = "true")
    @ApiImplicitParams({@ApiImplicitParam(name = "user_id", value = "用户id", required = true, dataType = "String", paramType = "path")})
    @ResourceAuths(value = {@ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
            , @ResourceAuth(handler = RoleAuthExtentHandler.class, resources = "#userBindRolesVO.roleIds")})
    public ResultModel<String> batchAssignmentRoles(@PathVariable("user_id") String userId,
                                            @RequestBody UserBindRolesVO userBindRolesVO) {


        boolean projectIdIsNull = StringUtils.isBlank(userBindRolesVO.getProjectId());
        LogRecordContext.putVariable("projectIdIsNull", projectIdIsNull);
        userService.batchAssignmentRoles(userId, userBindRolesVO);
        this.updateUserRoleHash(userId);
        return ResultModel.success(projectIdIsNull ? "用户绑定/解绑组织角色成功" : "用户绑定/解绑组织角色、项目成功", null);
    }

    @ApiOperation("用户批量绑定项目")
    @PostMapping("{user_id}/bind-projects")
    @LogRecordAnnotation(value = "用户绑定/解绑项目",
            detail = "用户[{USER{#userId}}]绑定/解绑项目", resourceId = "{{#userId}}", resource = "{USER{#userId}}")
    @ApiImplicitParams({@ApiImplicitParam(name = "user_id", value = "用户id", required = true, dataType = "String", paramType = "path")})
    @ResourceAuths(value = {@ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
            , @ResourceAuth(handler = ProjectResourceExtentAuthHandler.class, resources = "#userBindProjectsVO.projectIds")})
    public ResultModel<String> batchAssignmentProjects(@PathVariable("user_id") String userId,
                                               @RequestBody UserBindProjectsVO userBindProjectsVO) {

        userService.batchAssignmentProjects(userId, userBindProjectsVO);
        return ResultModel.success("用户绑定/解绑组织项目成功", null);
    }

    @ApiOperation("设置用户过期时间")
    @PostMapping("{user_id}/expired")
    @LogRecordAnnotation(value = "设置用户过期时间", detail = "用户[{{#_ret.resource}}]设置过期时间", resourceId = "{{#userId}}", resource = "{USER{#userId}}", auditFlag = "true")
    @ApiImplicitParams({@ApiImplicitParam(name = "user_id", value = "用户ID", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "expired", value = "过期时间(yyyy-MM-dd)", paramType = "query", dataType = "date")})
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    @DocumentIgnore
    public ResultModel<String> userExpired(@PathVariable("user_id") String userId,
                                   @RequestParam(value = "expired", defaultValue = "1970-01-01", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date expired) {


        User user = userService.getById(userId);
        if (Objects.isNull(user)) throw new BusinessException("用户不存在ID:"+userId);
        //fixed bug 3214
        Calendar instance = Calendar.getInstance();
        instance.set(1970,1,1,11,11,11);
        if (expired.before(instance.getTime()))
            expired = null;
        userService.lambdaUpdate()
                .eq(User::getId, userId)
                .set(User::getExpired, expired)
                .update();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String dateString = null;
        if (expired != null) {
            dateString = formatter.format(expired);
        }
        //fixed bug 4314 start
        try {
            Set<String> keys = redisTemplate.keys(user.getName() + ":*");
            if (ObjectUtil.isNotNull(keys)) {
                redisTemplate.delete(keys);
            }
        } catch (Exception e) {
            log.error("update User Type and remove token error", e);
        }
        //fixed bug 4314 end
        MessageVo messageVo = new MessageVo();
        messageVo.setUser(userId);
        messageVo.setSvc("sugoncloud-iam-api");
        messageVo.setContent("您的用户过期时间已被设置为：[" + dateString + "]");
        messageVo.setEmailEnable(true);
        messageFeignService.createMessage(messageVo);
        messageVo.setUser(securityAdminUserId);
        messageVo.setContent("[" + user.getName() + "]用户过期时间已被设置为：[" + dateString + "]");
        messageFeignService.createMessage(messageVo);
        if (encryptionEnabled) {
            userService.updateUserHash(userId);
        }
        return ResultModel.success("设置用户过期时间成功", user.getName(), user.getName());
    }

    @ApiOperation("设置IP白名单")
    @PostMapping("{user_id}/set-ip")
    @LogRecordAnnotation(value = "设置用户IP白名单", detail = "用户[{{#_ret.resource}}]设置IP白名单")
    @ApiImplicitParams({@ApiImplicitParam(name = "user_id", value = "用户ID", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "ip", value = "设置IP白名单", paramType = "query", dataType = "String")})
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    @DocumentIgnore
    public ResultModel<String> userIP(@PathVariable("user_id") String userId,
                              @RequestParam("ip") String ip) {

        User user = userService.getById(userId);
        if (Objects.isNull(user)) throw new BusinessException("用户不存在ID:" + userId);
        userService.lambdaUpdate()
                .eq(User::getId, userId)
                .set(User::getAllowIp, ip)
                .update();
        if (encryptionEnabled) {
            userService.updateUserHash(userId);
        }
        MessageVo messageVo = new MessageVo();
        messageVo.setSvc("sugoncloud-iam-api");
        messageVo.setEmailEnable(true);
        messageVo.setUser(userId);
        messageVo.setContent("您的用户已关联IP白名单:[" + ip + "]");
        messageFeignService.createMessage(messageVo);
        messageVo.setUser(securityAdminUserId);
        messageVo.setContent("[" + user.getName() + "]用户已关联IP白名单:[" + ip + "]");
        messageFeignService.createMessage(messageVo);
        ResultModel resultModel = ResultModel.success("设置IP白名单成功", user.getName());
        resultModel.setResource(user.getName());
        return resultModel;
    }

    @ApiOperation("设置用户访问权限")
    @PostMapping("{user_id}/set-access")
    @LogRecordAnnotation(value = "设置用户访问权限", detail = "用户[{{#_ret.resource}}]设置访问权限", resourceId = "{{#userId}}", resource = "{USER{#userId}}")
    @ApiImplicitParams({@ApiImplicitParam(name = "user_id", value = "用户ID", required = true, dataType = "String", paramType = "path")})
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    @DocumentIgnore
    public ResultModel<String> userAccessControl(@PathVariable("user_id") String userId,
                                      @RequestBody AccessControlVO accessControlVO) {
        String userName = userService.setAccessControl(accessControlVO, userId);
        ResultModel resultModel = ResultModel.success("设置用户访问权限成功", userName);
        resultModel.setResource(userName);
        return resultModel;
    }

    @ApiOperation("获取用户访问权限")
    @GetMapping("{user_id}/access")
    @ApiImplicitParams({@ApiImplicitParam(name = "user_id", value = "用户ID", required = true, dataType = "String", paramType = "path")})
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    @DocumentIgnore
    public ResultModel<AccessControlVO> getAccess(@PathVariable("user_id") String userId) {

        User user = userService.getById(userId);
        if (Objects.isNull(user)) {
            return ResultModel.error("当前用户不存在！");
        }
        AccessControlVO accessControlVO = new AccessControlVO();
        if (StringUtils.isNotBlank(user.getAllowIp())) {
            accessControlVO.setIp(user.getAllowIp());
        }

        UserAccessEntity userAccess = userAccessService.getAccessByUserId(userId);
        if (Objects.nonNull(userAccess)) {
            BeanUtils.copyProperties(userAccess, accessControlVO);
        }

        return ResultModel.success(accessControlVO);
    }

    @ApiOperation("校验密码")
    @PostMapping("{user_id}/verification/password")
    @ApiImplicitParams({@ApiImplicitParam(name = "user_id", value = "用户id", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "password", value = "密码", required = true, dataType = "String", paramType = "query")})
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    public ResultModel<String> verificationPassword(@PathVariable("user_id") String userId,
                                           @RequestBody CheckPasswordVo checkPasswordVo) {


        String publicKey = checkPasswordVo.getPublicKey();
        String password = checkPasswordVo.getPassword();
        if (!org.springframework.util.StringUtils.isEmpty(publicKey)) {
            try {
                if (!org.springframework.util.StringUtils.isEmpty(password)) {
                    password = encryptAndDecryptUtil.decryptByPublickey(publicKey, password);
                }
            } catch (Exception e) {
                return ResultModel.error("解密失败");
            }
        }
        User user = userService.getById(userId);
        if (Objects.isNull(user)) throw new BusinessException("用户不存在ID:" + userId);
        BCryptPasswordEncoder bcryptPasswordEncoder = new BCryptPasswordEncoder();
        String pwd = user.getPassword();
        if (encryptionEnabled) {
            pwd = encryptAndDecryptUtil.decryptByIv(pwd, user.getIv());
        }
        boolean matches = bcryptPasswordEncoder.matches(password, pwd);
        if (matches) {
            return ResultModel.success("校验密码成功", null);
        } else {
            return ResultModel.error("密码错误，解锁失败");
        }
    }

    @ApiOperation("查询一个项目里的所有成员")
    @GetMapping("/by-project")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page_num", value = "页码", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "page_size", value = "每页显示条数", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "project_id", value = "项目ID", paramType = "query", dataType = "String")})
    @ResourceAuth(handler = ProjectResourceAuthHandler.class, resources = "#projectId")
    public ResultModel<PageCL<UserViewVO>> userRoles(@RequestParam(value = "page_num", defaultValue = "1", required = false) int pageNum,
                                                     @RequestParam(value = "page_size", defaultValue = "15", required = false) int pageSize,
                                                     @RequestParam(value = "project_id") String projectId,
                                                     HttpServletRequest request) {
        try {
            PageCL<UserViewVO> pageResult = userService.getUsersByProjectId(pageNum, pageSize, projectId, request.getHeader(HeaderParamConstant.USER_ID));
            /*if (encryptionEnabled) {
                User user = userService.getById(request.getHeader(HeaderParamConstant.USER_ID));
                String cert = JSONObject.parseObject(user.getExtra()).getString(CommonInstance.ENCRYPTION_CONTENT_KEY);
                String s = encryptAndDecryptUtil.encryptEnvelope(cert, JSONObject.toJSONString(pageResult, SerializerFeature.WriteMapNullValue));
                return ResultModel.success("获取列表成功",s);
            }*/
            return ResultModel.success(pageResult);
        } catch (Exception e) {
            if (CommonUtils.isContainChinese(e.getMessage())) {
                return ResultModel.error(e.getMessage());
            }
            return ResultModel.error("查询项目的成员失败");
        }
    }

    @ApiOperation("查询所有用户列表")
    @GetMapping("/all")
    @ApiImplicitParams({@ApiImplicitParam(name = "page_num", value = "页码", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "page_size", value = "每页显示条数", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "name", value = "用户名", paramType = "query", dataType = "String")})
    public ResultModel<PageCL<UserViewVO>> listAllUser(@RequestParam(value = "page_num", defaultValue = "0", required = false) int pageNum,
                                                       @RequestParam(value = "page_size", defaultValue = "15", required = false) int pageSize,
                                                       @RequestParam(value = "name", required = false) String name) {
        try {
            PageCL<UserViewVO> userPage = userService.listAllUser(request.getHeader(HeaderParamConstant.USER_ID), pageNum, pageSize, name);
            return ResultModel.success(userPage);
        } catch (Exception e) {
            return ResultModel.error("查询所有用户列表失败");
        }
    }

    @ApiOperation("BM组织安全管理有、审计管理员查询当前租户所有用户")
    @GetMapping("/org/user")
    @ApiImplicitParams({@ApiImplicitParam(name = "page_num", value = "页码", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "page_size", value = "每页显示条数", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "name", value = "用户名", paramType = "query", dataType = "String")})
    public ResultModel<PageCL<UserViewVO>> listOrgUser(@RequestParam(value = "page_num", defaultValue = "0", required = false) int pageNum,
                                                       @RequestParam(value = "page_size", defaultValue = "15", required = false) int pageSize,
                                                       @RequestParam(value = "name", required = false) String name) {
        try {
            PageCL<UserViewVO> userPage = userService.listOrgUser(request.getHeader(HeaderParamConstant.USER_ID), pageNum, pageSize, name);
            return ResultModel.success(userPage);
        } catch (Exception e) {
            return ResultModel.error("查询所有用户列表失败");
        }
    }
    private void updateUserRoleHash(String userId) {
        if (encryptionEnabled) {
            List<String> ids = iamRoleService.listUseForHash(userId).stream().sorted().collect(Collectors.toList());
            String hash = encryptAndDecryptUtil.sign(JSONObject.toJSONString(ids));
            userService.lambdaUpdate().eq(User::getId, userId).set(User::getHashRole, hash).update();
        }
    }

    @ApiOperation("初始化用户hash值、加密存储邮箱、电话号码、iv、hashRole")
    @GetMapping("/hash")
    @ApiImplicitParams({@ApiImplicitParam(name = "user_id", value = "用户名", paramType = "query", dataType = "String")})
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    @DocumentIgnore
    public ResultModel initUserHash(@RequestParam(value = "user_id", required = false) String userId) {
        List<User> list = Lists.newArrayList();
        if (StringUtils.isNotEmpty(userId)) {
            User tempUser = userService.getById(userId);
            if (Objects.nonNull(tempUser)) {
                list.add(tempUser);
            }
        } else {
            list.addAll(userService.list());
        }
        log.info("初始化用户hash值、加密存储邮箱、电话号码、iv、hashRole, encryptionEnabled is {}", encryptionEnabled);
        if (encryptionEnabled) {
            for (User user : list) {
                String iv = user.getIv();
                //加密模式开启之前创建的用户没有IV,开启之后的模式有IV
                if (StringUtils.isBlank(iv)) {
                    iv  = encryptAndDecryptUtil.getIv();
                    user.setIv(iv);
                }
                JSONObject extraObj;
                if (StringUtils.isNotBlank(user.getExtra())) {
                    extraObj = JSONObject.parseObject(user.getExtra());
                    Boolean hasEncode = extraObj.getBoolean(CommonInstance.PASSWORD_ENCODE);
                    if (hasEncode == null || !hasEncode) {
                        String enPassword = encryptAndDecryptUtil.encryptByIv(user.getPassword(), iv);
                        if (StringUtils.isBlank(enPassword)) throw new BusinessException("密码加密之后为空");
                        user.setPassword (enPassword);
                        user.setPhone(encryptAndDecryptUtil.encryptByIv(user.getPhone(), iv));
                        user.setEmail(encryptAndDecryptUtil.encryptByIv(user.getEmail(), iv));
                        extraObj.put(CommonInstance.PASSWORD_ENCODE, true);
                    }
                } else {
                    extraObj = new JSONObject();
                    extraObj.put(CommonInstance.PASSWORD_ENCODE, true);
                    user.setPhone(encryptAndDecryptUtil.encryptByIv(user.getPhone(), iv));
                    user.setEmail(encryptAndDecryptUtil.encryptByIv(user.getEmail(), iv));
                    String enPassword = encryptAndDecryptUtil.encryptByIv(user.getPassword(), iv);
                    if (StringUtils.isBlank(enPassword)) throw new BusinessException("密码加密之后为空");
                    user.setPassword (enPassword);
                }
                user.setExtra(extraObj.toString());
                userService.lambdaUpdate()
                        .eq(User::getId, user.getId())
                        .set(User::getPhone, user.getPhone())
                        .set(User::getEmail, user.getEmail())
                        .set(User::getIv, user.getIv())
                        .set(User::getPassword, user.getPassword())
                        .set(User::getExtra, user.getExtra())
                        .update();
            }
            //password、邮箱、电话加密之后，在对数据做用户完整性、角色完整性保存
            for (User user : list) {
                userService.updateUserHash(user.getId());
                this.updateUserRoleHash(user.getId());
            }
        }
        return new ResultModel();
    }

    @ApiOperation("将处理的用户数据进行恢复")
    @GetMapping("/info/restore")
    @ApiImplicitParams({@ApiImplicitParam(name = "user_id", value = "用户名", paramType = "query", dataType = "String")})
    @DocumentIgnore
    public ResultModel restoreUserInfo(@RequestParam(value = "user_id", required = false) String userId) {
        List<User> list = Lists.newArrayList();
        if (StringUtils.isNotEmpty(userId)) {
            User tempUser = userService.getById(userId);
            if (Objects.nonNull(tempUser)) {
                list.add(tempUser);
            }
        } else {
            list.addAll(userService.list());
        }

        if (encryptionEnabled) {
            for (User user : list) {
                String iv = user.getIv();
                JSONObject extraObj;
                if (StringUtils.isNotBlank(user.getExtra())) {
                    extraObj = JSONObject.parseObject(user.getExtra());
                    Boolean hasEncode = extraObj.getBoolean(CommonInstance.PASSWORD_ENCODE);
                    if (hasEncode != null && hasEncode) {
                        String newPassword = encryptAndDecryptUtil.decryptByIv(user.getPassword(), iv);
                        if (StringUtils.isNotBlank(newPassword)) {
                            user.setPassword(newPassword);
                        } else {
                            throw new BusinessException("密码解密之后为空");
                        }
                        user.setPhone(encryptAndDecryptUtil.decryptByIv(user.getPhone(), iv));
                        user.setEmail(encryptAndDecryptUtil.decryptByIv(user.getEmail(), iv));
                        extraObj.put(CommonInstance.PASSWORD_ENCODE, false);
                    } else if (hasEncode == null) {
                        extraObj.put(CommonInstance.PASSWORD_ENCODE, false);
                    }
                } else {
                    extraObj = new JSONObject();
                    extraObj.put(CommonInstance.PASSWORD_ENCODE, false);
                }
                user.setExtra(extraObj.toString());
                userService.lambdaUpdate()
                        .eq(User::getId, user.getId())
                        .set(User::getPhone, user.getPhone())
                        .set(User::getEmail, user.getEmail())
                        .set(User::getIv, null)
                        .set(User::getHash, null)
                        .set(User::getHashRole, null)
                        .set(User::getPassword, user.getPassword())
                        .set(User::getExtra, user.getExtra())
                        .update();
            }
        }
        return new ResultModel();
    }

    /**
     *  bug 3425 bug 3397
     * @param userId
     * @return
     */
    @ApiOperation("判断是否需要修改密码")
    @GetMapping("/{user_id}/should-change-password")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "user_id", value = "用户ID", required = true, dataType = "String", paramType = "path"),
    })
    @ResourceAuth(handler = UserIdResourceAuthHandler.class, resources = "#userId")
    public ResultModel<Boolean> shouldChangePassword(@PathVariable("user_id") String userId) {


        User user = userService.getById(userId);
        if (user == null) {
            return ResultModel.error("用户不存在", null);
        }
        //判断长时间未修改密码
        GlobalsettingsEntity changePasswordEntity = globalsettingsService.getGlobalsettingsEntity("password", "change_password_interval");
        int changePasswordInterval = Integer.parseInt(changePasswordEntity == null
                ? "90" : changePasswordEntity.getPolicyDocument());
        boolean shouldChangePassword = CommonUtils.calculateTotalDay(user.getLastPasswordTime() == null
        ? user.getCreatedAt() : user.getLastPasswordTime()) -1 >= changePasswordInterval;

        //判断是否开启第一次登录修改密码
        GlobalsettingsEntity firstLoginEntity = globalsettingsService.getGlobalsettingsEntity("user", "update_password_first");
        boolean firstLogin = Boolean.parseBoolean(firstLoginEntity == null
                ? "false" : firstLoginEntity.getPolicyDocument());
        long createAt = user.getCreatedAt().getTime();
        long lastPasswordTime = user.getLastPasswordTime() == null
                ? user.getCreatedAt().getTime() : user.getLastPasswordTime().getTime();
        //用户创建时间和修改密码的时间相等->用户还没有修改过密码
        boolean flag = lastPasswordTime==createAt;
        boolean firstLoginChangePassword =  firstLogin && flag;
        return ResultModel.success("", firstLoginChangePassword || shouldChangePassword);
    }


    @ApiOperation("设置/取消用户的默认资源租户")
    @PostMapping("/{user_id}/default-project")
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    @DocumentIgnore
    public ResultModel<String> setUserDefaultResourceProject(@PathVariable("user_id") String userId,
                                                             @RequestParam("project_id") String projectId,
                                                             @RequestParam(required = false) boolean cancel) {
            try {
                userService.setUserDefaultResourceProject(userId, projectId, cancel);
                return ResultModel.success((cancel ? "取消": "设置") + "用户的默认资源项目成功",projectId);
            } catch (Exception e) {
                if (CommonUtils.isContainChinese(e.getMessage())) {
                    return ResultModel.error(e.getMessage());
                }
                return ResultModel.error((cancel ? "取消": "设置") + "用户的默认资源项目失败");
            }
    }

    /**
     *  bug 3397
     * @param usrId
     * @return
     *//*
    @GetMapping("/{user_id}/should-update-password-first-login")
    public ResultModel shouldChangePasswordFirstLogin(@PathVariable("user_id") String usrId) {
        User user = userService.getById(usrId);
        if (user == null) {
            return ResultModel.error("用户不存在", null);
        }
        GlobalsettingsEntity globalsettingsEntity = globalsettingsService.getGlobalsettingsEntity("user", "update_password_first");
        boolean firstLogin = Boolean.parseBoolean(globalsettingsEntity == null
                ? "false" : globalsettingsEntity.getPolicyDocument());
        long createAt = user.getCreatedAt().getTime();
        long lastPasswordTime = user.getLastPasswordTime() == null
                ? new Date().getTime() : user.getLastPasswordTime().getTime();
        //用户创建时间和修改密码的时间相等->用户还没有修改过密码
        boolean flag = lastPasswordTime==createAt;
        return ResultModel.success("", firstLogin && flag);
    }*/

    /**
     * 云运营管理专用接口
     * @param userId 用户ID
     * @return
     */
    @ApiOperation("用户所属的项目、组织")
    @GetMapping("/{user_id}/departments-and-projects")
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    @DocumentIgnore
    public ResultModel<UserOwnDeptAndProjectDTO> getUserOwnDepartmentsAndProjects(@PathVariable("user_id") String userId, @RequestParam(value = "project_type", required = false)String projectType) {
        try {
            return ResultModel.success("设置用户的默认资源租户成功",userService.getUserOwnDepartmentsAndProjects(userId, projectType));
        } catch (Exception e) {
            if (CommonUtils.isContainChinese(e.getMessage())) {
                return ResultModel.error(e.getMessage());
            }
            return ResultModel.error("设置用户的默认资源租户失败");
        }
    }

    @ApiOperation("通过用户IDs查询用户")
    @PostMapping("/by-ids")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "用户ID数组", required = true, allowMultiple = true, dataType = "String", paramType = "body"),
    })
    @DocumentIgnore
    public ResultModel<List<IdNameInfo>> userInfos(@RequestBody List<String> ids) {
        return ResultModel.success(userService.listByIds(ids).stream().map(u -> {
            IdNameInfo idNameInfo = new IdNameInfo();
            idNameInfo.setId(u.getId());
            idNameInfo.setName(StringUtils.isBlank(u.getAlias()) ? u.getName() : u.getAlias());
            return idNameInfo;
        }).collect(Collectors.toList()));
    }

    /**
     * 根据组织id和用户id集合获取用户列表
     */
    @ApiOperation("根据组织id和用户id集合获取用户列表")
    @PostMapping("getUsersByDeptIdAndUserIds")
    @DocumentIgnore
    public ResultModel<PageCL<UserViewVO>> getUsersByDeptIdAndUserIds(@RequestBody UserDeptAndUserIdsDTO userDeptAndUserIdsDTO) {
        try {
            PageCL<UserViewVO> userVos = userService.getUsersByDeptIdAndUserIds(userDeptAndUserIdsDTO);
            return ResultModel.success("", userVos);
        } catch (Exception e) {
            if (CommonUtils.isContainChinese(e.getMessage())) {
                return ResultModel.error(e.getMessage());
            }
            return ResultModel.error("根据组织id和用户id集合获取用户列表失败");
        }
    }

    /**
     * 根据当前用户和组织id并且不包含已有的用户列表
     */
    @ApiOperation("根据当前用户和组织id并且不包含已有的用户列表")
    @PostMapping("getUsersByCurrentUserIdAndDeptIdAndNotUserIds")
    @DocumentIgnore
    public ResultModel<PageCL<UserViewVO>> getUsersByCurrentUserIdAndDeptIdAndNotUserIds(@RequestBody UserDeptAndUserIdsDTO userDeptAndUserIdsDTO) {
        try {
            PageCL<UserViewVO> userVos = userService.getUsersByCurrentUserIdAndDeptIdAndNotUserIds(userDeptAndUserIdsDTO);
            return ResultModel.success("", userVos);
        } catch (Exception e) {
            if (CommonUtils.isContainChinese(e.getMessage())) {
                return ResultModel.error(e.getMessage());
            }
            return ResultModel.error("根据当前用户和组织id并且不包含已有的用户列表失败");
        }
    }

    @ApiOperation("解锁用户")
    @PutMapping("/unlock/{user_id}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "user_id", value = "用户ID", required = true, dataType = "String", paramType = "path"),
    })
    @ResourceAuth(handler = UserResourceAuthHandler.class, resources = "#userId")
    @LogRecordAnnotation(value = "解锁用户", detail = "解锁用户:{{#_ret.resource}}", resourceId = "{{#userId}}")
    public ResultModel<String> unlockUser(@PathVariable("user_id") String userId) {
        try {
            return ResultModel.success(userService.unlockUser(userId));
        }catch (Exception e) {
            log.error("解锁用户发生异常", e);
            if (CommonUtils.isContainChinese(e.getMessage())) {
                return ResultModel.error(e.getMessage());
            }
            return ResultModel.error("服务器异常，请稍后重试");
        }
    }

    @GetMapping("/enc")
    @ResourceAuth(handler = SecurityUserTypeAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    @DocumentIgnore
    public ResultModel<String> encMsg(@RequestParam String msg){
        try {
            //return ResultModel.success(PBEWithMD5AndDESUtils.encrypt(msg, "548ab5974c65d3ec94fe0c5b48de70e6", "548ab597"));
            return ResultModel.success("ENC(" + encryptor.encrypt(msg) + ")");
        }catch (Exception e) {
            log.error("加密异常：{}", msg, e);
            if (CommonUtils.isContainChinese(e.getMessage())) {
                return ResultModel.error(e.getMessage());
            }
            return ResultModel.error("加密失败");
        }

    }

    @GetMapping("/dec")
    @ResourceAuth(handler = SecurityUserTypeAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    @DocumentIgnore
    public ResultModel<String> decMsg(@RequestParam String msg){
        try {
            // return ResultModel.success(PBEWithMD5AndDESUtils.decrypt(msg, "548ab5974c65d3ec94fe0c5b48de70e6", "548ab597"));
            if (msg.startsWith("ENC(") && msg.endsWith(")")) {
                msg = msg.substring(4, msg.length() - 1);
            }
            return ResultModel.success(encryptor.decrypt(msg));
        }catch (Exception e) {
            log.error("解密异常：{}", msg, e);
            if (CommonUtils.isContainChinese(e.getMessage())) {
                return ResultModel.error(e.getMessage());
            }
            return ResultModel.error("解密失败");
        }

    }

    @ApiOperation("查询用户联系信息")
    @GetMapping("/contact-info")
    @ApiImplicitParams({@ApiImplicitParam(name = "username", value = "用户名", required = true, dataType = "String", paramType = "query")})
    public ResultModel<UserViewVO> userContactInfo(@RequestParam("username") String username) {
        UserViewVO userViewVO = userService.getContactInfo(username);
        // 脱敏处理
        if (maskEnabled) {
            userViewVO.setPhone(CommonUtils.maskPhoneNumber(userViewVO.getPhone()));
            userViewVO.setEmail(CommonUtils.maskEmail(userViewVO.getEmail()));
        }
        return ResultModel.success(userViewVO);
    }

    @ApiOperation("批量绑定用户-角色")
    @PostMapping("bind/users-roles")
    @LogRecordAnnotation(value = "批量绑定用户角色", detail = "批量绑定用户角色")
    public ResultModel bindUserRole(@RequestBody List<UserRoleVO> userRoleVOS) {
        if (CollectionUtils.isEmpty(userRoleVOS)) return ResultModel.error("列表不能为空");
        this.verifyUserRoleAuth(userRoleVOS);
        userService.bindUserRole(userRoleVOS);
        return ResultModel.success("绑定成功");
    }
    @ApiOperation("批量解绑用户-角色")
    @PostMapping("unbind/users-roles")
    @LogRecordAnnotation(value = "批量解绑用户角色", detail = "批量解绑用户角色")
    public ResultModel unbindUserRole(@RequestBody List<UserRoleVO> userRoleVOS) {
        if (CollectionUtils.isEmpty(userRoleVOS)) return ResultModel.error("列表不能为空");
        this.verifyUserRoleAuth(userRoleVOS);
        userService.unbindUserRole(userRoleVOS);
        return ResultModel.success("解绑成功");
    }

    private void verifyUserRoleAuth(List<UserRoleVO> userRoleVOS) {
        //越权校验
        ResourceAuthHandler userResourceAuthHandler = ResourceAuthUtils.getInstance(UserResourceAuthHandler.class);
        userResourceAuthHandler.verify(userRoleVOS.stream().map(UserRoleVO::getUserId).collect(Collectors.toList()));
        ResourceAuthHandler roleAuthExtentHandler = ResourceAuthUtils.getInstance(RoleAuthExtentHandler.class);
        roleAuthExtentHandler.verify(userRoleVOS.stream().map(UserRoleVO::getRoleId).collect(Collectors.toList()));
    }

    @ApiOperation("批量绑定用户-项目")
    @PostMapping("bind/users-projects")
    @LogRecordAnnotation(value = "批量绑定用户项目", detail = "批量绑定用户项目")
    public ResultModel bindUserProject(@RequestBody List<UserProjectVO> userProjectVOS) {
        if (CollectionUtils.isEmpty(userProjectVOS)) return ResultModel.error("列表不能为空");
        this.verifyUserProjectAuth(userProjectVOS);
        userService.bindUserProject(userProjectVOS);
        return ResultModel.success("绑定成功","绑定成功");
    }
    @ApiOperation("批量解绑用户-项目")
    @PostMapping("unbind/users-projects")
    @LogRecordAnnotation(value = "批量解绑用户项目", detail = "批量解绑用户项目")
    public ResultModel unbindUserProject(@RequestBody List<UserProjectVO> userProjectVOS) {
        if (CollectionUtils.isEmpty(userProjectVOS)) return ResultModel.error("列表不能为空");
        this.verifyUserProjectAuth(userProjectVOS);
        userService.unbindUserProject(userProjectVOS);
        return ResultModel.success("解绑成功","解绑成功");
    }

    private void verifyUserProjectAuth(List<UserProjectVO> userProjectVOS) {
        //越权校验
        ResourceAuthHandler userResourceAuthHandler = ResourceAuthUtils.getInstance(UserResourceAuthHandler.class);
        userResourceAuthHandler.verify(userProjectVOS.stream().map(UserProjectVO::getUserId).collect(Collectors.toList()));
        ResourceAuthHandler roleAuthExtentHandler = ResourceAuthUtils.getInstance(ProjectResourceAuthHandler.class);
        roleAuthExtentHandler.verify(userProjectVOS.stream().map(UserProjectVO::getProjectId).collect(Collectors.toList()));
    }
}
