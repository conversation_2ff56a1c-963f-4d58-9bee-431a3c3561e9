package com.sugon.cloud.iam.api.entity.keystone.VO;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * for openstack
 */
@Data

public class UserDetailNativeVO {
    private Map links;
    private Object description;
    private String name;
    private boolean enabled;
    private Object federated;
    private String email;
    private Object options;
    @JsonProperty(value = "default_project_id")
    private String defaultProjectId;
    private String id;
    @JsonProperty("domain_id")
    private String domainId;
    @JsonProperty(value = "password_expires_at")
    private Date passwordExpiresAt;
}
