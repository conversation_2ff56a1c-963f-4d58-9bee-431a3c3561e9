package com.sugon.cloud.iam.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sugon.cloud.common.model.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-04-19 15:09
 */
@Data
@TableName("user_access")
@ApiModel("用户权限实体")
public class UserAccessEntity extends BaseEntity {

    @TableId(type = IdType.UUID)
    private String id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 是否开启时间限制
     */
    private Boolean limitFlag;

    /**
     * 限制时间
     */
    private String limitTime;

    /**
     * 是否删除
     */
    private Boolean isDelete;
}
