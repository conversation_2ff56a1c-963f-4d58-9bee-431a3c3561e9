package com.sugon.cloud.iam.api.utils;

import lombok.extern.slf4j.Slf4j;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.Locale;

/**
 * <AUTHOR>
 * 时间工具类
 * @version 1.0.0
 * @date 2024-04-19 14:14
 */
@Slf4j
public class DateUtils {

    public static final String DEFAULT_LOCAL_DATE_PATTERN = "yyyy-MM-dd";

    public static final String DEFAULT_LOCAL_DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    /**
     * 将字符串转为LocalDate
     * @param dateStr
     * @return
     */
    public static LocalDate parseDate(String dateStr) {
        return parseDate(dateStr, DEFAULT_LOCAL_DATE_PATTERN);
    }
    public static LocalDate parseDate(String dateStr, String pattern) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        LocalDate date = LocalDate.parse(dateStr, formatter);
        return date;
    }

    /**
     * 将字符串转为LocalDateTime
     * @param dateStr
     * @return
     */
    public static LocalDateTime parseDateTime(String dateStr) {
        return parseDateTime(dateStr, DEFAULT_LOCAL_DATE_TIME_PATTERN);
    }
    public static LocalDateTime parseDateTime(String dateStr, String pattern) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        LocalDateTime date = LocalDateTime.parse(dateStr, formatter);
        return date;
    }

    /**
     * 将LocalDate转为String
     * @param localDate
     * @return
     */
    public static String formatDate(LocalDate localDate) {
        return formatDate(localDate, DEFAULT_LOCAL_DATE_PATTERN);
    }
    public static String formatDate(LocalDate localDate, String pattern) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return localDate.format(formatter);
    }

    /**
     * 将LocalDateTime转为String
     * @param localDateTime
     * @return
     */
    public static String formatDateTime(LocalDateTime localDateTime) {
        return formatDateTime(localDateTime, DEFAULT_LOCAL_DATE_PATTERN);
    }
    public static String formatDateTime(LocalDateTime localDateTime, String pattern) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return localDateTime.format(formatter);
    }

    /**
     * 根据时间获取星期几（英文）
     * @param localDate
     * @return
     */
    public static String getWeekByDate(LocalDate localDate) {
        DayOfWeek dayOfWeek = localDate.getDayOfWeek();
        return dayOfWeek.name();
    }
    public static String getWeekByDate(LocalDateTime localDateTime) {
        DayOfWeek dayOfWeek = localDateTime.getDayOfWeek();
        return dayOfWeek.name();
    }

    /**
     * 根据时间获取星期几（中文）
     * @param localDate
     * @return
     */
    public static String getWeekNameByDate(LocalDate localDate) {
        DayOfWeek dayOfWeek = localDate.getDayOfWeek();
        return dayOfWeek.getDisplayName(TextStyle.FULL, Locale.getDefault());
    }
    public static String getWeekNameByDate(LocalDateTime localDateTime) {
        DayOfWeek dayOfWeek = localDateTime.getDayOfWeek();
        return dayOfWeek.getDisplayName(TextStyle.FULL, Locale.getDefault());
    }


}
