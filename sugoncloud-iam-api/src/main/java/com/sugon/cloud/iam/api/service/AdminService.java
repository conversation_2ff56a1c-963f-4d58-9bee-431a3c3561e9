package com.sugon.cloud.iam.api.service;

import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.vo.AdminUserViewVO;
import com.sugon.cloud.iam.api.vo.RoleCreateOrUpdate;
import com.sugon.cloud.iam.common.model.vo.RoleResponseVO;
import com.sugon.cloud.iam.common.model.vo.UserViewVO;

public interface AdminService {

    /**
     * 查询根账号及其自用户列表接口
     * @param name
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageCL<AdminUserViewVO> masterUserAndSubUserList(String name, int pageNum, int pageSize);

    PageCL<UserViewVO> adminUserList(String currentUserId, String name, int pageNum, int pageSize);

    PageCL<RoleResponseVO> listAdminRole(String name, int pageNum, int pageSize, String userType);

    int createIamAdminRole(RoleCreateOrUpdate role);

    String deleteIamAdminRole(String roleId);
}
