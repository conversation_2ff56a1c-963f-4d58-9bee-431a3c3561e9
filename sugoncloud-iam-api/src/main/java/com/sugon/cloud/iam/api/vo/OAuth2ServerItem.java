package com.sugon.cloud.iam.api.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2023/05/23 10:11
 **/
@Data
@ApiModel("OAuth2服务提供商")
public class OAuth2ServerItem {
    @JsonProperty("client_id")
    @ApiModelProperty("客户端ID")
    private String clientId;
    @JsonProperty("user_id")
    @ApiModelProperty("用户ID")
    private String userId;
    @ApiModelProperty("用户名")
    private String username;
    @ApiModelProperty("授权范围")
    private String scope;
    @JsonProperty("authorize_url")
    @ApiModelProperty("授权端点地址")
    private String authorizeUrl;
    @JsonProperty("service_provider_name")
    @ApiModelProperty("服务提供商")
    private String serviceProviderName;
}
