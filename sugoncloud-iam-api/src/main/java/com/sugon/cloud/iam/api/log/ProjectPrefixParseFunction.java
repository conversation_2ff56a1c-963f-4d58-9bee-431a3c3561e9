package com.sugon.cloud.iam.api.log;

import com.sugon.cloud.iam.api.entity.keystone.Project;
import com.sugon.cloud.iam.api.service.ProjectService;
import com.sugon.cloud.log.client.service.IParseFunction;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Objects;

@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ProjectPrefixParseFunction implements IParseFunction {

    private final ProjectService projectService;

    @Override
    public String functionName() {
        return "PROJECT_PREFIX";
    }

    @Override
    public String apply(String value) {
        Project project;
        if (StringUtils.isEmpty(value) || Objects.isNull(project = projectService.getById(value))) {
            return "";
        }
        return "项目[" + project.getAlias() + "]";
    }
}
