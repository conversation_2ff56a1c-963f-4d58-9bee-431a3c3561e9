package com.sugon.cloud.iam.api.entity;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel("策略FORM")
public class StrategyForm implements Serializable {

    private static final long serialVersionUID = 3468880163252201294L;

//    @ApiModelProperty("策略")
//    private PolicyEntity policy;

    @ApiModelProperty("策略ID")
    private String id;

    @ApiModelProperty("策略")
    private JSONObject policy;

    @ApiModelProperty("名称")
    @NotBlank(message = "名称不能为空")
    @Size(min = 2, max = 64)
    private String name;

    @Size(max = 128)
    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("创建时间")
    private Date createAt;

    @ApiModelProperty("日志")
    private String catalog;

    @ApiModelProperty("创建人")
    private String creatBy;
}
