package com.sugon.cloud.iam.api.entity;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.util.Date;

@Data
@Accessors(chain = true)
@ApiModel("临时凭证KEY")
public class TemporarySecretKeyEntity implements Serializable {


    @ApiModelProperty("访问KEY")
    private String accessKey;
    @ApiModelProperty("凭证key")
    private String secretKey;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty("过期时间")
    private Date expiresAt;
    @ApiModelProperty("安全token")
    private String securityToken;

}
