package com.sugon.cloud.iam.api.entity.keystone.VO;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sugon.cloud.iam.api.entity.keystone.Domain;
import lombok.Data;

import java.util.Date;

@Data
public class UserVO {
    @JsonProperty(value = "password_expires_at")
    private Date passwordExpiresAt;
    @TableField(exist = false)
    private Domain domain;
    private String id;
    private String name;
}
