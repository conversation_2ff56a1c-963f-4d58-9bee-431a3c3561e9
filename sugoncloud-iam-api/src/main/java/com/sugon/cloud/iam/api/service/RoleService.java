package com.sugon.cloud.iam.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sugon.cloud.iam.api.entity.keystone.CreateRoleResponse;
import com.sugon.cloud.iam.api.entity.keystone.CreateRoleResponseParam;
import com.sugon.cloud.iam.api.entity.keystone.Role;
import com.sugon.cloud.iam.api.entity.keystone.VO.CreateRoleVO;
import com.sugon.cloud.iam.api.vo.RoleCreateOrUpdate;

import java.util.List;

public interface RoleService extends IService<Role> {

    List<Role> findByUserId(String userId);

    List<Role> findByUserIdAndProjectId(String userId, String projectId);

    /**
     * 创建role
     * @param createRoleVO
     * @return
     */
    CreateRoleResponse createRole(CreateRoleVO createRoleVO);

    /**
     * 列表查询
     * @param name
     * @param domainId
     * @return
     */
    List<CreateRoleResponseParam> listRole(String name, String domainId);

    /**
     * 详情
     * @param roleId
     * @return
     */
    CreateRoleResponse roleDetail(String roleId);
}
