package com.sugon.cloud.iam.api.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sugon.cloud.iam.api.entity.VmwareParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("更新区域")
public class UpdateRegionVO {
    @ApiModelProperty(value = "区域管理描述", required = true)
    @JsonProperty("description")
    @NotBlank(message = "区域管理描述不能为空")
    @Size(min = 2, max = 255)
    private String description;

    @JsonProperty("vmware_param")
    private VmwareParam vmwareParam;
}
