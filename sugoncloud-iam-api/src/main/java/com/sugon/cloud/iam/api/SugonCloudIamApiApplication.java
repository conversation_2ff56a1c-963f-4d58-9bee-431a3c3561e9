package com.sugon.cloud.iam.api;

import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.sugon.cloud.common.interceptors.FeignRequestInterceptor;
import com.sugon.cloud.log.client.starter.annotation.EnableLogRecord;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Import;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.client.RestTemplate;
import springfox.documentation.schema.DefaultModelProvider;

import java.util.Arrays;

@SpringBootApplication
@EnableDiscoveryClient
@EnableTransactionManagement
@MapperScan("com.sugon.cloud.iam.api.mapper*")
@Import({PaginationInterceptor.class, FeignRequestInterceptor.class, RestTemplate.class})
@EnableLogRecord(serviceId = "spring.application.name")
@EnableFeignClients(basePackages = {"com.sugon.cloud.log.client.service","com.sugon.cloud.tools.service"})
public class SugonCloudIamApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(SugonCloudIamApiApplication.class, args);
    }
}
