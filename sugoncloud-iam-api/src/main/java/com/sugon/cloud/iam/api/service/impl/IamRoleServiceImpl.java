package com.sugon.cloud.iam.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.common.mapper.ModelMapper;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.common.utils.UUIDUtil;
import com.sugon.cloud.iam.api.common.RecursionDeptIdUtils;
import com.sugon.cloud.iam.api.entity.Department;
import com.sugon.cloud.iam.api.entity.IamRole;
import com.sugon.cloud.iam.api.entity.UserRoleMapping;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.exception.BusinessException;
import com.sugon.cloud.iam.api.mapper.DepartmentMapper;
import com.sugon.cloud.iam.api.mapper.IamRoleMapper;
import com.sugon.cloud.iam.api.mapper.UserMapper;
import com.sugon.cloud.iam.api.mapper.UserRoleMappingMapper;
import com.sugon.cloud.iam.api.service.DepartmentService;
import com.sugon.cloud.iam.api.service.GlobalsettingsService;
import com.sugon.cloud.iam.api.service.IamRoleService;
import com.sugon.cloud.iam.api.utils.DepartmentPathUtils;
import com.sugon.cloud.iam.api.vo.RoleCreateOrUpdate;
import com.sugon.cloud.iam.common.model.vo.DepartmentTreeDetailVO;
import com.sugon.cloud.iam.common.model.vo.RoleResponseVO;
import com.sugon.cloud.log.client.context.LogRecordContext;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RefreshScope
public class IamRoleServiceImpl extends ServiceImpl<IamRoleMapper, IamRole> implements IamRoleService {

    private final ModelMapper mapper;
    private final DepartmentMapper departmentMapper;
    private final DepartmentService departmentService;
    private final UserRoleMappingMapper userRoleMappingMapper;
    private final UserMapper userMapper;
    private final IamRoleMapper roleMapper;
    private final GlobalsettingsService globalsettingsService;

    @Override
    public List<IamRole> findByUserIdAndProjectId(String userId, String projectId) {
        return roleMapper.findByUserIdAndProjectId(userId, projectId);
    }

    @Override
    public int createIamRole(RoleCreateOrUpdate role) {
        //非平台自定义管理员角色进行组织校验
        if (!Objects.equals(role.getType(), TypeUtil.TYPE_CUSTOM_PLATFORM)) {
            Department department = departmentMapper.selectById(role.getDeptId());
            if (Objects.isNull(department)) throw new BusinessException("组织不存在ID: " + role.getDeptId());
        }
        IamRole r = mapper.map(role, IamRole.class);
        r.setId(UUIDUtil.get32UUID());
        r.setDeptId(role.getDeptId());
        boolean existed = roleMapper.selectCount(new QueryWrapper<IamRole>()
                        .lambda()
                        .eq(IamRole::getName, r.getName())
                        .eq(IamRole::getDeptId, r.getDeptId()))
                .intValue() > 0;
        if (existed) throw new BusinessException("角色名已存在: " + r.getName());
        role.setId(r.getId());
        LogRecordContext.putVariable("id", r.getId());
        return roleMapper.insert(r);
    }

    @Override
    public String deleteRole(String roleId) {
        IamRole iamRole = roleMapper.selectById(roleId);
        if (Objects.isNull(iamRole)) throw new BusinessException("角色不存在: " + roleId);
        if (TypeUtil.isInnerRole(iamRole.getType())) {
            throw new BusinessException("内置角色不能被删除");
        }
        userRoleMappingMapper.delete(new QueryWrapper<UserRoleMapping>().lambda().eq(UserRoleMapping::getRoleId, roleId));
        roleMapper.deleteById(roleId);
        return iamRole.getName();
    }

    /**
     * 该方法专给策略微服务提供，勿改
     *
     * @param userId
     * @return
     */
    @Override
    public List<RoleResponseVO> list(String userId, String projectId, boolean forOperations) {
        User user = userMapper.selectById(userId);
        if (Objects.isNull(user)) throw new BusinessException("用户不存在ID:" + userId);
        String userType = user.getType();
        List<IamRole> roles = Lists.newArrayList();
        // auditadmin、security直接查内置角色
        if(TypeUtil.TYPE_AUDIT.equals(userType) ||
                TypeUtil.TYPE_SECURITY.equals(userType) ||
                TypeUtil.TYPE_SEC_ADMIN.equals(userType) ||
                TypeUtil.TYPE_SEC_AUDITOR.equals(userType)){
            // 查询出内置角色
            IamRole innerIamRole = roleMapper.selectOne(new QueryWrapper<IamRole>().lambda().eq(IamRole::getType, userType));
            if (Objects.nonNull(innerIamRole)) {
                roles.add(innerIamRole);
            }
        }
        // 根账号或组织管理员
        if (TypeUtil.TYPE_DEPT_MASTER.equals(userType) ||
                TypeUtil.TYPE_MASTER.equals(userType)) {
            List<String> deptIds = Lists.newArrayList();
            Department department = departmentMapper.selectById(user.getDeptId());
            if (Objects.isNull(department)) throw new BusinessException("用户所在组织不存在,用户ID" + userId);
            deptIds.add(department.getId());
            //组织管理员查询当前组织以及子组织的角色
            List<DepartmentTreeDetailVO> treeDepartment = departmentMapper.findTreeDepartment(user.getDeptId());
            deptIds = RecursionDeptIdUtils.getDeptIds(treeDepartment, deptIds);
            roles = roleMapper.selectList(new QueryWrapper<IamRole>().lambda().in(IamRole::getDeptId, deptIds));
            // 查询出内置角色
            IamRole innerIamRole = roleMapper.selectOne(new QueryWrapper<IamRole>().lambda().eq(IamRole::getType, userType));
            if (Objects.nonNull(innerIamRole)) {
                roles.add(innerIamRole);
            }
        }
        // admin
        if (TypeUtil.TYPE_ADMIN.equals(userType) || TypeUtil.TYPE_SYS_ADMIN.equals(userType)) {
            roles = roleMapper.findByUserId(userId, null);
        }
        // 普通用户
        if (StringUtils.isBlank(userType)
                || TypeUtil.TYPE_ORG_SECAUDITOR.equals(userType)
                || TypeUtil.TYPE_ORG_SYSADMIN.equals(userType)
                || TypeUtil.TYPE_ORG_SECADMIN.equals(userType)) {
            roles = roleMapper.findByUserId(userId, projectId);
        }
        // 保密用户角色过滤
        if (globalsettingsService.getBmStatus()
                && !TypeUtil.TYPE_SECURITY.equals(userType)
                && !"inner".equals(user.getName())) {
            roles = roles.stream().filter(r -> TypeUtil.getBmInnerRoleTypes().contains(r.getType())).collect(Collectors.toList());
        } else {
            roles = roles.stream().filter(r -> !TypeUtil.getBmInnerRoleTypes().contains(r.getType())).collect(Collectors.toList());
        }
        return mapper.mapList(roles, RoleResponseVO.class);
    }

    /**
     * use for hash
     *
     * @param userId
     * @return
     */
    @Override
    public List<String> listUseForHash(String userId) {
        List<UserRoleMapping> list = Lists.newArrayList();
        User user = userMapper.selectById(userId);
        if (Objects.isNull(user)) throw new BusinessException("用户不存在ID:" + userId);
        List<String> roleIds = Lists.newArrayList();
        list = userRoleMappingMapper.selectList(new QueryWrapper<UserRoleMapping>().lambda()
                .eq(UserRoleMapping::getUserId, userId)
                .isNotNull(UserRoleMapping::getRoleId));
        if (CollectionUtils.isNotEmpty(list)) {
            roleIds = list.stream().map(UserRoleMapping::getRoleId).collect(Collectors.toList());
        }
        return roleIds;
    }

    @Override
    public PageCL<RoleResponseVO> listByDept(String deptId, String name, int pageNum, int pageSize, String userType) {
        if (StringUtils.isEmpty(deptId)) {
            throw new BusinessException("组织不存在");
        }
        IPage<IamRole> page = new Page<>(pageNum, pageSize);
        if (globalsettingsService.getBmStatus()) {
            page = roleMapper.selectPage(page, new QueryWrapper<IamRole>().lambda()
                    .eq(IamRole::getType, StrUtil.isNotBlank(userType) ? userType : TypeUtil.TYPE_COMMON));
        } else {
            //查询根组织角色
            Department department = departmentService.getRootDepartmentByDeptId(deptId);
            Set<String> deptIds = new HashSet<>();
            deptIds.add(department.getId());
            deptIds.add(deptId);
            page = roleMapper.selectPage(page, new QueryWrapper<IamRole>().lambda()
                    .and(q -> q.in(IamRole::getDeptId, deptIds)
                            .or()
                            .eq(IamRole::getType, TypeUtil.TYPE_DEFAULT)
                            .or()
                            .eq(IamRole::getType, TypeUtil.TYPE_CUSTOM_PLATFORM))
                    .like(StringUtils.isNotBlank(name), IamRole::getName, name)
                    .orderByDesc(IamRole::getCreateTime));
        }

        try {
            return new PageCL<RoleResponseVO>().
                    getPageByPageHelper(page, mapper.mapList(page.getRecords(), RoleResponseVO.class));
        } catch (Exception e) {
            log.error("com.sugon.cloud.iam.api.service.impl.IamRoleServiceImpl.listByDept error:", e);
            throw new BusinessException("查询列表报错");
        }
    }

    @Override
    public PageCL<RoleResponseVO> userRoles(String userId, String name, int pageNum, int pageSize, boolean queryInnerRole, String projectId) {
        IPage<IamRole> page = new Page(pageNum, pageSize);
        User user = userMapper.selectById(userId);
        if (Objects.isNull(user)) throw new BusinessException("用户不存在ID:" + userId);
        String userType = user.getType();
        try {
            // 内置管理员类型（audit/security）
            if (queryInnerRole && (TypeUtil.TYPE_AUDIT.equals(userType)
                    || TypeUtil.TYPE_SECURITY.equals(userType))
                    || TypeUtil.TYPE_SEC_ADMIN.equals(userType)
                    || TypeUtil.TYPE_SEC_AUDITOR.equals(userType)) {
                // 查询出内置角色
                roleMapper.selectPage(page, new QueryWrapper<IamRole>().lambda().eq(IamRole::getType, userType));
                return new PageCL<RoleResponseVO>().
                        getPageByPageHelper(page, mapper.mapList(page.getRecords(), RoleResponseVO.class));
            }
            // 根账号/组织管理员
            if (TypeUtil.TYPE_DEPT_MASTER.equals(userType)
                    || TypeUtil.TYPE_MASTER.equals(userType)) {
                List<String> deptIds = Lists.newArrayList();
                Department department = departmentMapper.selectById(user.getDeptId());
                if (Objects.isNull(department)) throw new BusinessException("用户所在组织不存在,用户ID" + userId);
                deptIds.add(department.getId());
                //组织管理员查询当前组织以及子组织的角色
                List<DepartmentTreeDetailVO> treeDepartment = departmentMapper.findTreeDepartment(user.getDeptId());
                deptIds = RecursionDeptIdUtils.getDeptIds(treeDepartment, deptIds);
                roleMapper.selectPage(page, new QueryWrapper<IamRole>().lambda()
                        .in(IamRole::getDeptId, deptIds)
                        .and(e -> e.isNull(IamRole::getType)
                                .or(wq -> wq.eq(IamRole::getType, TypeUtil.TYPE_CUSTOM_REGULAR)))
                        .or(queryInnerRole, wq -> wq.eq(IamRole::getType, userType))
                        .or(e -> e.eq(IamRole::getType, TypeUtil.TYPE_CUSTOM_PLATFORM))
                        .orderByDesc(IamRole::getCreateTime)
                );
                return new PageCL<RoleResponseVO>().
                        getPageByPageHelper(page, mapper.mapList(page.getRecords(), RoleResponseVO.class));
            }
            // admin用户
            if (TypeUtil.TYPE_ADMIN.equals(userType)
                    || TypeUtil.TYPE_SYS_ADMIN.equals(userType)
                    || TypeUtil.TYPE_ORG_SECAUDITOR.equals(userType)
                    || TypeUtil.TYPE_ORG_SYSADMIN.equals(userType)
                    || TypeUtil.TYPE_ORG_SECADMIN.equals(userType)) {
                roleMapper.findByUserIdPage(page, userId, true, null);
            }
            // 普通用户
            if (StringUtils.isBlank(userType)) {
                roleMapper.findByUserIdPage(page, userId, queryInnerRole, projectId);
            }
            return new PageCL<RoleResponseVO>().
                    getPageByPageHelper(page, mapper.mapList(page.getRecords(), RoleResponseVO.class));
        } catch (Exception e) {
            log.error("com.sugon.cloud.iam.api.service.impl.IamRoleServiceImpl.userRoles error:", e);
            throw new BusinessException("查询列表报错");
        }
    }


    @Override
    public PageCL<RoleResponseVO> listAll(String currentUserId, String name, int pageNum, int pageSize) {
        PageCL<RoleResponseVO> pageCL = new PageCL<>();
        IPage<IamRole> page = new Page<>(pageNum, pageSize);
        User user = userMapper.selectById(currentUserId);
        try {
            // 根账号或是组织管理员
            if (TypeUtil.TYPE_MASTER.equals(user.getType()) || TypeUtil.TYPE_DEPT_MASTER.equals(user.getType())) {
                List<String> deptIds = Lists.newArrayList();
                deptIds.add(user.getDeptId());
                List<DepartmentTreeDetailVO> treeDepartments = departmentMapper.findTreeDepartment(user.getDeptId());
                deptIds = RecursionDeptIdUtils.getDeptIds(treeDepartments, deptIds);
                List<String> finalDeptIds = deptIds;
                IPage<IamRole> roleIPage = roleMapper.selectPage(page, new QueryWrapper<IamRole>().lambda()
                        .and(wq -> wq.in(IamRole::getDeptId, finalDeptIds)
                                .or(e -> e.eq(IamRole::getType, TypeUtil.TYPE_CUSTOM_PLATFORM)))
                        .like(StringUtils.isNotEmpty(name), IamRole::getName, name)
                        .orderByDesc(IamRole::getCreateTime));
                pageCL = new PageCL<RoleResponseVO>().
                        getPageByPageHelper(page, mapper.mapList(roleIPage.getRecords(), RoleResponseVO.class));
            }
            // 安全管理员/管理员
            if (TypeUtil.TYPE_SECURITY.equals(user.getType()) || TypeUtil.TYPE_ADMIN.equals(user.getType())) {
                List<String> innerAndSubAdminRoleIds = Lists.newArrayList(
                        TypeUtil.TYPE_ADMIN, TypeUtil.TYPE_AUDIT, TypeUtil.TYPE_SECURITY,
                        TypeUtil.TYPE_MASTER, TypeUtil.TYPE_DEPT_MASTER, TypeUtil.TYPE_DEFAULT, TypeUtil.TYPE_SUB_ADMIN);
                innerAndSubAdminRoleIds.addAll(TypeUtil.getBmInnerRoleTypes());
                IPage<IamRole> roleIPage = roleMapper.selectPage(page, new QueryWrapper<IamRole>().lambda()
                        .and(wq -> wq.isNull(IamRole::getType)
                                .or(e -> e.notIn(IamRole::getType, innerAndSubAdminRoleIds)))
                        .like(StringUtils.isNotEmpty(name), IamRole::getName, name)
                        .orderByDesc(IamRole::getCreateTime));
                pageCL = new PageCL<RoleResponseVO>().
                        getPageByPageHelper(page, mapper.mapList(roleIPage.getRecords(), RoleResponseVO.class));
            }
            // 普通用户 bug:414212
            if (StrUtil.isBlank(user.getType())) {
                List<String> roleIds = userRoleMappingMapper.selectList(new QueryWrapper<UserRoleMapping>()
                                .lambda()
                                .eq(UserRoleMapping::getUserId, currentUserId))
                        .stream().map(UserRoleMapping::getRoleId).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
                if (CollUtil.isEmpty(roleIds)) {
                    return pageCL;
                }
                IPage<IamRole> roleIPage = roleMapper.selectPage(page, new QueryWrapper<IamRole>().lambda()
                        .in(IamRole::getId, roleIds)
                        .like(StringUtils.isNotEmpty(name), IamRole::getName, name)
                        .orderByDesc(IamRole::getCreateTime));
                pageCL = new PageCL<RoleResponseVO>().
                        getPageByPageHelper(page, mapper.mapList(roleIPage.getRecords(), RoleResponseVO.class));
            }
        } catch (Exception e) {
            log.error("com.sugon.cloud.iam.api.service.impl.IamRoleServiceImpl.listAll error:", e);
            throw new BusinessException("查询所有角色列表报错");
        }
        if (CollectionUtils.isNotEmpty(pageCL.getList())) {
            List<DepartmentTreeDetailVO> departments = departmentMapper.findAllDepartmentList();
            for (RoleResponseVO roleResponseVO : pageCL.getList()) {
                roleResponseVO.setDeptPath(DepartmentPathUtils.buildDeptPath(roleResponseVO.getDeptId(), departments));
            }
        }
        return pageCL;
    }


    @Override
    public List<UserRoleMapping> listUserRoleMappingByRoleId(String roleId) {
        List<UserRoleMapping> userRoleMappings = userRoleMappingMapper.selectList(new QueryWrapper<UserRoleMapping>().
                lambda()
                .eq(UserRoleMapping::getRoleId, roleId));
        return userRoleMappings;
    }
}
