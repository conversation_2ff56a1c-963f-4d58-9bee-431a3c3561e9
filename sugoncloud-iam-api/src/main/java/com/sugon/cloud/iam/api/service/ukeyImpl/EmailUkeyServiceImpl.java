package com.sugon.cloud.iam.api.service.ukeyImpl;

import com.sugon.cloud.iam.api.service.UkeyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Service;

/**
 * @Author: yangdingshan
 * @Date: 2025/2/9 15:58
 * @Description:
 */
@Service
@Slf4j
@ConditionalOnExpression("'${mfa.type}'.contains('email')")
public class EmailUkeyServiceImpl implements UkeyService {
}
