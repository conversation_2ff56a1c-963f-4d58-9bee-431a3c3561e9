package com.sugon.cloud.iam.api.controller;

import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.constants.HeaderParamConstant;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.ann.CreateGroup;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.mapper.UserMapper;
import com.sugon.cloud.iam.api.service.AdminService;
import com.sugon.cloud.iam.api.service.UserService;
import com.sugon.cloud.iam.api.vo.AdminUserViewVO;
import com.sugon.cloud.iam.api.vo.RoleCreateOrUpdate;
import com.sugon.cloud.iam.api.vo.UserCreateOrUpdateVO;
import com.sugon.cloud.iam.common.model.vo.RoleResponseVO;
import com.sugon.cloud.log.client.context.LogRecordContext;
import com.sugon.cloud.log.client.starter.annotation.LogRecordAnnotation;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

@Api(tags = "管理员API")
@RequestMapping(value = "/api/admins")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RestController
@DocumentIgnore
public class AdminUserController {
    private final AdminService adminService;
    private final UserService userService;
    private final UserMapper userMapper;

    @ApiOperation("根账号列表")
    @GetMapping("/master-list")
    @ApiImplicitParams({@ApiImplicitParam(name = "name", value = "名称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "page_num", value = "页码", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "page_size", value = "每页显示条数", paramType = "query", dataType = "int")})
    public ResultModel<PageCL<AdminUserViewVO>> masterUserList(@RequestParam(value = "name", required = false) String name,
                                                               @RequestParam(value = "page_num", defaultValue = "1", required = false) int pageNum,
                                                               @RequestParam(value = "page_size", defaultValue = "15", required = false) int pageSize,
                                                               HttpServletRequest request) {
        if(!checkAdminAccess(request.getHeader(HeaderParamConstant.USER_ID))){
            return ResultModel.error("只有超级管理员或安全管理员才能创建管理员");
        }
        return ResultModel.success(adminService.masterUserAndSubUserList(name, pageNum, pageSize));
    }

    @ApiOperation("创建管理员")
    @PostMapping
    @LogRecordAnnotation(value = "创建管理员", detail = "创建管理员:{{#user.name}}", resourceId = "{{#userId}}", resource = "{{#user.name}}", auditFlag = "true")
    public ResultModel<String> createAdmin(@Validated({CreateGroup.class}) @RequestBody UserCreateOrUpdateVO user, HttpServletRequest request) {
        if(!checkAdminAccess(request.getHeader(HeaderParamConstant.USER_ID))){
            return ResultModel.error("只有超级管理员或安全管理员才能创建管理员");
        }
        String userId = userService.createAdmin(user, true);
        LogRecordContext.putVariable("userId", userId);
        return ResultModel.success("创建管理员成功", userId);
    }

    @ApiOperation("管理员列表")
    @GetMapping
    @ApiImplicitParams({@ApiImplicitParam(name = "name", value = "名称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "page_num", value = "页码", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "page_size", value = "每页显示条数", paramType = "query", dataType = "int")})
    public ResultModel<PageCL<AdminUserViewVO>> adminUserList(@RequestParam(value = "name", required = false) String name,
                                                               @RequestParam(value = "page_num", defaultValue = "1", required = false) int pageNum,
                                                               @RequestParam(value = "page_size", defaultValue = "15", required = false) int pageSize,
                                                              HttpServletRequest request) {
        String loginUserId = request.getHeader(HeaderParamConstant.USER_ID);
        if(!checkAdminAccess(loginUserId)){
            return ResultModel.error("只有超级管理员或安全管理员才能查询管理员");
        }
        return ResultModel.success(adminService.adminUserList(loginUserId,name, pageNum, pageSize));
    }

    @ApiOperation("删除管理员")
    @DeleteMapping("{user_id}")
    @LogRecordAnnotation(value = "删除管理员", detail = "删除管理员:{USER{#userId}}", resourceId = "{{#userId}}", resource = "{USER{#userId}}")
    public ResultModel deleteAdmin(@PathVariable("user_id") String userId, HttpServletRequest request) {
        if(!checkAdminAccess(request.getHeader(HeaderParamConstant.USER_ID))){
            return ResultModel.error("只有超级管理员或安全管理员才能删除管理员");
        }
        return ResultModel.success("删除管理员成功", userService.deleteAdmin(userId), null);
    }

    @ApiOperation("创建管理员角色")
    @PostMapping("/role")
    @LogRecordAnnotation(value = "创建管理员角色", operatorId = "{{#_ret.resource}}", operatorName = "{{#role.name}}",
            detail = "创建管理员角色:{{#role.name}}", resourceId = "{{#uuid}}", resource = "{{#role.name}}", auditFlag = "true")
    public ResultModel<String> createAdminRole(@RequestBody RoleCreateOrUpdate role, HttpServletRequest request) {
        if(!checkAdminAccess(request.getHeader(HeaderParamConstant.USER_ID))){
            return ResultModel.error("只有超级管理员或安全管理员才能创建管理员角色");
        }
        adminService.createIamAdminRole(role);
        return ResultModel.success("创建管理员角色成功", role.getId());
    }

    @ApiOperation("删除管理员角色")
    @DeleteMapping("/role/{role_id}")
    @LogRecordAnnotation(value = "删除管理员角色", detail = "删除管理员角色:{{#_ret.resource}}")
    public ResultModel deleteAdminRole(@PathVariable("role_id") String roleId, HttpServletRequest request) {
        if(!checkAdminAccess(request.getHeader(HeaderParamConstant.USER_ID))){
            return ResultModel.error("只有超级管理员或安全管理员才能删除管理员角色");
        }
        return ResultModel.success("删除管理员角色成功", adminService.deleteIamAdminRole(roleId), null);
    }

    @GetMapping("/role/all")
    @ApiOperation("查询所有管理员角色")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "模糊查询名称",paramType = "query" ,dataType = "String"),
            @ApiImplicitParam(name = "page_num", value = "页码",paramType = "query" ,dataType = "int"),
            @ApiImplicitParam(name = "page_size", value = "每页显示条数", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "user_type", value = "用户类型", paramType = "query", dataType = "String"),
    })
    public ResultModel<PageCL<RoleResponseVO>> listAdminRole(HttpServletRequest request,
                                                             @RequestParam(value = "name", required = false) String name,
                                                             @RequestParam(value = "page_num", required = false) int pageNum,
                                                             @RequestParam(value = "page_size", required = false) int pageSize,
                                                             @RequestParam(value = "user_type", required = false) String userType) {
        if(!checkAdminAccess(request.getHeader(HeaderParamConstant.USER_ID))){
            return ResultModel.error("只有超级管理员或安全管理员才能查询管理员角色");
        }
        PageCL<RoleResponseVO> pageResult = adminService.listAdminRole(name,pageNum,pageSize,userType);
        return ResultModel.success(pageResult);
    }

    private Boolean checkAdminAccess(String loginUserId){
        User loginUser = userMapper.selectById(loginUserId);
        if(Objects.isNull(loginUser)){
            return false;
        }
        if (!"admin".equals(loginUser.getName())
                && !TypeUtil.TYPE_SECURITY.equals(loginUser.getType())
                && !TypeUtil.TYPE_SYS_ADMIN.equals(loginUser.getType())
                && !TypeUtil.TYPE_SEC_ADMIN.equals(loginUser.getType())) {
            return false;
        }
        return true;
    }
}
