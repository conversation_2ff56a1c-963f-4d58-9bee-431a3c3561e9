package com.sugon.cloud.iam.api.entity;

//import net.sf.json.JSONObject;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Licens实体 20160530
 *
 * <AUTHOR>
 *
 */
@ApiModel("许可服务信息")
public class LicenseServiceInfo {

	/**
	 * 产品版本
	 */
	@ApiModelProperty("产品版本")
	private String softLicenseVersion;

	/**
	 * 产品名称
	 */
	@ApiModelProperty("产品名称")
	private String productName;

	/**
	 * CPU授权数量
	 */
	@ApiModelProperty("CPU授权数量")
	private String softLicenseCpuNum;

	/**
	 * 软件授权起始时间
	 */
	@ApiModelProperty("软件授权起始时间")
	private String softLicenseStartTime;
	/**
	 * 软件授权过期时间
	 */
	@ApiModelProperty("软件授权过期时间")
	private String softLicenseExpiredTime;
	/**
	 * 服务名称
	 */
	@ApiModelProperty("服务名称")
	private String softName;

	@ApiModelProperty("数据库授权数量")
	private int cdbNum;

	@ApiModelProperty("CPU授权数量")
	private int cpuNum;

	@ApiModelProperty("CPU已使用数量")
	private int cpuUsedNum;

	@ApiModelProperty("存储授权数量")
	private int storageNum;

	public int getStorageNum() {
		return storageNum;
	}

	public void setStorageNum(int storageNum) {
		this.storageNum = storageNum;
	}

	public int getCpuUsedNum() {
		return cpuUsedNum;
	}

	public void setCpuUsedNum(int cpuUsedNum) {
		this.cpuUsedNum = cpuUsedNum;
	}

	public int getCpuNum() {
		return cpuNum;
	}

	public void setCpuNum(int cpuNum) {
		this.cpuNum = cpuNum;
	}

	public int getCdbNum() {
		return cdbNum;
	}

	public void setCdbNum(int cdbNum) {
		this.cdbNum = cdbNum;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getSoftName() {
		return softName;
	}

	public void setSoftName(String softName) {
		this.softName = softName;
	}

	public String getSoftLicenseVersion() {
		return softLicenseVersion;
	}

	public void setSoftLicenseVersion(String softLicenseVersion) {
		this.softLicenseVersion = softLicenseVersion;
	}

	public String getSoftLicenseCpuNum() {
		return softLicenseCpuNum;
	}

	public void setSoftLicenseCpuNum(String softLicenseCpuNum) {
		this.softLicenseCpuNum = softLicenseCpuNum;
	}

	public String getSoftLicenseStartTime() {
		return softLicenseStartTime;
	}

	public void setSoftLicenseStartTime(String softLicenseStartTime) {
		this.softLicenseStartTime = softLicenseStartTime;
	}

	public String getSoftLicenseExpiredTime() {
		return softLicenseExpiredTime;
	}

	public void setSoftLicenseExpiredTime(String softLicenseExpiredTime) {
		this.softLicenseExpiredTime = softLicenseExpiredTime;
	}

	@Override
	public String toString() {
		return "JSONObject.fromObject(this).toString()";
	}
}
