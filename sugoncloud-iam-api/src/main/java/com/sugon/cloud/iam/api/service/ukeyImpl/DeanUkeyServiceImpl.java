package com.sugon.cloud.iam.api.service.ukeyImpl;

import com.cslc.netsignagent.DtsvsHandle;
import com.cslc.netsignagent.NetSignAgent;
import com.cslc.netsignagent.NetSignResult;
import com.cslc.netsignagent.NetSignException;
import com.sugon.cloud.iam.api.config.DeanSignConfig;
import com.sugon.cloud.iam.api.service.UkeyService;
import com.sugon.cloud.iam.common.model.vo.MfaVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Base64;

/**
 * 渔翁签名验签服务器 渔翁的key
 */
@Service
@Slf4j
@ConditionalOnExpression("'${mfa.type}'.contains('dean')")
@RefreshScope
public class DeanUkeyServiceImpl implements UkeyService {

    @Autowired(required = false)
    private DeanSignConfig deanSignConfig;

    private static String plainData = "12345678123456781234567812345678";

    public static void main(String[] args) {
        NetSignAgent agent = new NetSignAgent();
        DtsvsHandle handle = new DtsvsHandle();
        try {
            /* 1. 初始化 */
            handle = agent.initialize("218.56.58.206", 6006, "12345678", 3000, 10, "", "", handle);
            println("init ok!", getClassName(), getFuncName(), getLineNumber(new Throwable()));

            String singData = "MEQCIAvv3mx9dzaq+Vjac1cHpE50rVgVSIvXiX5f9godvRmFAiB5OE6rpe2bgfpB2a7ZT8j3V7cIyaR9dIXA90MIWWAEPQ==";
            byte[] signDataByte = Base64.getDecoder().decode(singData);

            String cert = "MIIEdjCCBBqgAwIBAgIIaeMAQgBG71swDAYIKoEcz1UBg3UFADB2MQswCQYDVQQGEwJDTjEOMAwGA1UECAwFQW5IdWkxDjAMBgNVBAcMBUhlRmVpMSYwJAYDVQQKDB1Bbkh1aSBDZXJ0aWZpY2F0aW9uIEF1dGhvcml0eTENMAsGA1UECwwEQUhDQTEQMA4GA1UEAwwHQUhDQVNNMjAeFw0yMzEyMTQxNjAwMDBaFw0yNDEyMTUxNTU5NTlaMIGTMQswCQYDVQQGEwJDTjEOMAwGA1UECAwFdGVzdDExGzAZBgNVBAoMEjM0MDEyMzAwMDAwMDAwMDAwMTELMAkGA1UECwwCOTkxITAfBgNVBA0MGOa1i+ivleezu+e7n+ato+W8j+aguSM5OTEnMCUGA1UEAwwe5YWt5a6J6KOV5a6J5Yy66aG555uu5rWL6K+V5LiAMFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEaCg6GRV8hm9rTbINnPX04EVAJ90g0lJCD+U8c9jM2g6E2xjxnEmCGKTF8MlVeFFXzYSsaBjzKPHzWLq7FKjGpaOCAnAwggJsMAwGA1UdEwQFMAMBAQAwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMEMAsGA1UdDwQEAwIAwDBOBgNVHSAERzBFMEMGCCqBHIFFCAEBMDcwNQYIKwYBBQUHAgEWKWh0dHA6Ly93d3cuYWhlY2EuY24vZG93bmxvYWQvQUhDQS1DUFMucGRmMB8GA1UdIwQYMBaAFEaZvGFi4rpTqQyI0s1dlsDIMLrPMIHKBgNVHR8EgcIwgb8wgbyggbmggbaGgY5sZGFwOi8vbGRhcC5haGVjYS5jbjozODkvQ049QUhDQVNNMixDTj1BSENBU00yLCBPVT1DUkxEaXN0cmlidXRlUG9pbnRzLCBvPWFoY2E/Y2VydGlmaWNhdGVSZXZvY2F0aW9uTGlzdD9iYXNlP29iamVjdGNsYXNzPWNSTERpc3RyaWJ1dGlvblBvaW50hiNodHRwOi8vd3d3LmFoZWNhLmNuL2NybC9BSENBU00yLmNybDCB0gYIKwYBBQUHAQEEgcUwgcIwgYsGCCsGAQUFBzAChn9sZGFwOi8vbGRhcC5haGVjYS5jbjozODkvQ049QUhDQVNNMixDTj1BSENBU00yLCBPVT1jQUNlcnRpZmljYXRlcywgbz1haGNhP2NBQ2VydGlmaWNhdGU/YmFzZT9vYmplY3RDbGFzcz1jZXJ0aWZpY2F0aW9uQXV0aG9yaXR5MDIGCCsGAQUFBzAChiZodHRwOi8vd3d3LmFoZWNhLmNuL2NhY2VydC9BSENBU00yLmNlcjAdBgNVHQ4EFgQUVMhC7qTl3WtRB4hWkqxsGyLPWfYwDAYIKoEcz1UBg3UFAANIADBFAiEApr+yHkl6fwr83geofPoxULoelHoQ0e0XsrmNlnJGImkCIFYnSy4ZEaI0g23OTnF8EpmRWj5JTqCgKVYdb1lFAjAA";
            byte[] certArrByte = Base64.getDecoder().decode(cert);
            agent.rawVerify(plainData.getBytes(), signDataByte, certArrByte, NetSignResult.SIGN_SM3, handle);
            println("raw verify ok!", getClassName(), getFuncName(), getLineNumber(new Throwable()));

        } catch (NetSignException e) {
            System.out.println("ErrorCode:0x" + Integer.toHexString(e.getErrorCode()));
            e.printStackTrace();
        } catch (Exception e2) {
            e2.printStackTrace();
        } finally {
            try {
                /* 15. 清除句柄 */
                agent.clearEnvironment(handle);
                println("clearEnvironment ok! ", getClassName(), getFuncName(), getLineNumber(new Throwable()));
            } catch (NetSignException e) {
                e.printStackTrace();
            }
        }
    }

    public static void println(String info, String className, String FuncName, int lineNum) {
        System.out.println(className + "@" + FuncName + "@" + lineNum + "\t\t" + info);
    }

    /**
     * 获取当前代码行行号
     *
     * @return
     */
    public static int getLineNumber(Throwable tr) {
        return tr.getStackTrace()[0].getLineNumber();
    }

    /**
     * 获取当前代码所在类名
     *
     * @return
     */
    public static String getClassName() {
        return Thread.currentThread().getStackTrace()[1].getClassName();
    }

    /**
     * 获取当前代码所在方法名
     *
     * @return
     */
    public static String getFuncName() {
        return Thread.currentThread().getStackTrace()[1].getMethodName();
    }

    /**
     * 双因子验签
     *
     * @param mfaVo
     * @return
     */
    @Override
    public boolean verifyData(MfaVo mfaVo) {
        NetSignAgent agent = deanSignConfig.getAgent();
        DtsvsHandle handle = deanSignConfig.getHandle();

        String inData = mfaVo.getInData();
        String singData = mfaVo.getSignNature();
        byte[] signDataByte = Base64.getDecoder().decode(singData);
        String cert = mfaVo.getCert();
        byte[] certArrByte = Base64.getDecoder().decode(cert);
        try {
            agent.rawVerify(inData.getBytes(), signDataByte, certArrByte, NetSignResult.SIGN_SM3, handle);
            log.debug("raw verify ok!", getClassName(), getFuncName(), getLineNumber(new Throwable()));
        } catch (NetSignException e) {
            String errorCode = "0x" + Integer.toHexString(e.getErrorCode());
            String errorDescription = deanSignConfig.getErrorDescription(errorCode);
            log.error("ErrorDescription:" + errorDescription);
            log.error("error:" + e);
            return false;
        } catch (Exception e2) {
            log.error("error:" + e2);
            return false;
        }
        return true;
    }

}
