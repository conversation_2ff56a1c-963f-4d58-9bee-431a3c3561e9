package com.sugon.cloud.iam.api.log;

import com.sugon.cloud.iam.api.service.DepartmentService;
import com.sugon.cloud.iam.api.vo.DepartmentDetailVO;
import com.sugon.cloud.log.client.service.IParseFunction;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Objects;

@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DepartmentParseFunction implements IParseFunction {

    private final DepartmentService departmentService;

    @Override
    public String functionName() {
        return "DEPARTMENT";
    }

    @Override
    public String apply(String value) {
        DepartmentDetailVO departmentDetailVO;
        if (StringUtils.isEmpty(value) || Objects.isNull(departmentDetailVO = departmentService.queryById(value))) {
            return "";
        }
        return departmentDetailVO.getName();
    }
}
