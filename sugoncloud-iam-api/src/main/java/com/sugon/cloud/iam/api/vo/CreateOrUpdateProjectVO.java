package com.sugon.cloud.iam.api.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("创建/修改项目")
public class CreateOrUpdateProjectVO {
    @ApiModelProperty(value = "id", hidden = true)
    private String id;
    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "项目名称不能为空")
    private String name;
    @ApiModelProperty(value = "描述")
    private String description;
    @ApiModelProperty(value = "组织ID")
    @NotEmpty(message = "组织不能为空")
    @JsonProperty("dept_id")
    private String deptId;

    @ApiModelProperty("项目类型：正式项目(formal)、试用项目(test)")
    private String type;


    @JsonProperty("meter_types")
    @ApiModelProperty("计费类型（统一计费：contract_meter_unification | 合同计费：contract_meter_contract）")
    private List<String> meterTypes;

    @JsonProperty(value = "start_time")
    @ApiModelProperty("(试用开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    @JsonProperty(value = "end_time")
    @ApiModelProperty("试用结束时间")
    @DateTimeFormat(pattern="yyyy-MM-dd")//页面写入数据库时格式化
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;
}