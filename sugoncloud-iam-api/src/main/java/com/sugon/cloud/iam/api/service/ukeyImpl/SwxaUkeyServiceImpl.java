package com.sugon.cloud.iam.api.service.ukeyImpl;

import com.sugon.cloud.iam.api.service.UkeyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * 三未信安动态口令
 */
@Service
@Slf4j
@ConditionalOnExpression("'${mfa.type}'.contains('swxa')")
@RefreshScope
public class SwxaUkeyServiceImpl implements UkeyService {
}
