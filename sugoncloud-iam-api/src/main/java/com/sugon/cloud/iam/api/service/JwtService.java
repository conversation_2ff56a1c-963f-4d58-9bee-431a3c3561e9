package com.sugon.cloud.iam.api.service;


import com.sugon.cloud.iam.api.entity.keystone.LoginUser;
import com.sugon.cloud.iam.common.model.vo.ValidateTokenUserResponseVO;

import javax.servlet.http.HttpServletRequest;

public interface JwtService {
    /**
     * isNative 是否是底层创建token
     * @param username
     * @param password
     * @param isNative
     * @return
     */
    String login(String username, String password, boolean isNative);

    void initOnePlaceLoginInfo(LoginUser loginUser, String token);

    /**
     * 验证token接口
     * @param header
     * @return
     */
    ValidateTokenUserResponseVO validateToken(String header, HttpServletRequest httpRequest);

    void setBlack(String token);

    long getExpiredTime();

    Long currentUserExpiredDate(String token);

    void checkTime(String userId);
}
