package com.sugon.cloud.iam.api.entity.keystone.VO;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "创建endpoint参数")
public class CreateEndpointParam {
    @JsonProperty("interface")
    @ApiModelProperty(value = "接口")
    private String interfaceParam;
    @JsonProperty("region_id")
    @ApiModelProperty(value = "区域id")
    private String regionId;
    @ApiModelProperty(value = "url")
    private String url;
    @JsonProperty("service_id")
    @ApiModelProperty(value = "服务id")
    private String serviceId;
    @JsonProperty("region")
    @ApiModelProperty(value = "区域")
    private String region;
}
