package com.sugon.cloud.iam.api.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sugon.cloud.iam.api.entity.StrategyForm;
import com.sugon.cloud.iam.api.entity.StrategyPO;
import ma.glasnost.orika.MapperFacade;
import ma.glasnost.orika.MapperFactory;
import ma.glasnost.orika.MappingContext;
import ma.glasnost.orika.converter.BidirectionalConverter;
import ma.glasnost.orika.impl.DefaultMapperFactory;
import ma.glasnost.orika.metadata.Type;

public class String2JSONMapper {

    public static MapperFacade mapper() {
        MapperFactory mapperFactory = new DefaultMapperFactory.Builder().build();
        mapperFactory.getConverterFactory().registerConverter("jsonConfigConvert", new BidirectionalConverter<JSONObject, String>() {
            @Override
            public String convertTo(JSONObject t, Type<String> type, MappingContext mappingContext) {
                return null;
            }
            @Override
            public JSONObject convertFrom(String source, Type<JSONObject> destinationType, MappingContext mappingContext) {
                return JSON.parseObject(source, destinationType.getRawType());
            }
        });
        mapperFactory.classMap(StrategyPO.class, StrategyForm.class)
                .fieldMap("policy", "policy").converter("jsonConfigConvert").add()
                .byDefault()
                .register();
        return mapperFactory.getMapperFacade();
    }
}
