package com.sugon.cloud.iam.api.jwt;


import com.sugon.cloud.common.constants.HeaderParamConstant;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.entity.keystone.LoginUser;
import com.sugon.cloud.iam.api.service.JwtService;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.security.Key;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Data
@Component
@Log4j2
@RefreshScope
public class JwtTokenUtils {

    @Value("${token.secret}")
    private String secret;

//    @Value("${token.expireTime}")
//    private Long expiration;

    @Autowired
    private JwtService jwtService;

    private static Key KEY = null;

    /**
     * 生成token令牌
     *
     * @param userDetails 用户
     * @return 令token牌
     */
    public String generateToken(LoginUser userDetails, boolean isNative) {
        log.debug("[JwtTokenUtils] generateToken username {}", userDetails.getUsername());
        Map<String, Object> claims = new HashMap<>(8);
        claims.put(CommonInstance.USER_ALIAS, userDetails.getAlias());
        claims.put(HeaderParamConstant.USER_NAME, userDetails.getUsername());
        claims.put(CommonInstance.PROJECT_ID, userDetails.getProjectId());
        claims.put(CommonInstance.CREATED, new Date());
        claims.put(HeaderParamConstant.USER_ID, userDetails.getUserId());
        claims.put(HeaderParamConstant.USER_TYPE, userDetails.getUserType());
        claims.put(CommonInstance.DEPT_ID, userDetails.getDeptId());
        if (isNative) {
            return generateTokenWithExpiration(claims);
        } else {
            return generateToken(claims);
        }
    }
    /**
     * 从令牌中获取用户名
     *
     * @param token 令牌
     * @return 用户名
     */
    public String getUsernameFromToken(String token) {
        String username;
        try {
            Claims claims = getClaimsFromToken(token);
            username = claims.get(HeaderParamConstant.USER_NAME,String.class);
            log.debug("getUsernameFromToken:{}",username);
        } catch (Exception e) {
            username = null;
        }
        return username;
    }

    /**
     * 判断令牌是否过期
     *
     * @param token 令牌
     * @return 是否过期
     */
    public Boolean isTokenExpired(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            Date expiration = claims.getExpiration();
            return expiration.before(new Date());
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 刷新令牌
     *
     * @param claims claims
     * @return 新令牌
     */
   /* public String refreshToken(Claims claims) {
        String refreshedToken;
        try {
            claims.put(CommonInstance.CREATED, new Date());
            refreshedToken = generateToken(claims);
        } catch (Exception e) {
            refreshedToken = null;
        }
        return refreshedToken;
    }*/

    /**
     * 验证令牌
     *
     * @param token       令牌
     * @param userDetails 用户
     * @return 是否有效
     */
    public Boolean validateToken(String token, UserDetails userDetails) {
        String username = getUsernameFromToken(token);
        return (username.equals(userDetails.getUsername()) &&
                !isTokenExpired(token));
    }

    /**
     * 从claims生成令牌
     *
     * @param claims 数据声明
     * @return 令牌
     */
    private String generateToken(Map<String, Object> claims) {

        return Jwts.builder().setClaims(claims)
                .signWith(SignatureAlgorithm.HS256, getKeyInstance())
                .compact();
    }

    private String generateTokenWithExpiration(Map<String, Object> claims) {

        return Jwts.builder().setClaims(claims)
                .setExpiration(getExpiredDate())
                .signWith(SignatureAlgorithm.HS256, getKeyInstance())
                .compact();
    }

    /**
     * 从令牌中获取数据声明
     *
     * @param token 令牌
     * @return 数据声明
     */
    public Claims getClaimsFromToken(String token) {
        Claims claims = null;

        try {
            claims = Jwts.parser().setSigningKey(getKeyInstance()).parseClaimsJws(token).getBody();
        } catch (Exception e) {
            log.error("getClaimsFromToken error ", e);
            claims = null;
        }
        return claims;
    }


    private Key getKeyInstance() {
        if (KEY == null) {
            synchronized (JwtTokenUtils.class) {
                if (KEY == null) {// 双重锁
                    byte[] apiKeySecretBytes = DatatypeConverter.parseBase64Binary(secret);
                    KEY = new SecretKeySpec(apiKeySecretBytes, SignatureAlgorithm.HS256.getJcaName());
                }
            }
        }
        return KEY;
    }

    private Date getExpiredDate() {
        long expiration = jwtService.getExpiredTime();
       return new Date(System.currentTimeMillis() + expiration* 1000L);
    }
}
