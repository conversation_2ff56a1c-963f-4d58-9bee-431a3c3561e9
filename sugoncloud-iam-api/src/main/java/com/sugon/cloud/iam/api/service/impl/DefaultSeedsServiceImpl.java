package com.sugon.cloud.iam.api.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ft.otp.core.ReturnResult;
import com.sugon.cloud.iam.api.entity.SeedEntity;
import com.sugon.cloud.iam.api.mapper.SeedsMapper;
import com.sugon.cloud.iam.api.service.SeedsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;


@Service("seedsService")
@Slf4j
@ConditionalOnExpression("!'${mfa.type}'.contains('ftng')")
public class DefaultSeedsServiceImpl  extends ServiceImpl<SeedsMapper, SeedEntity> implements SeedsService {
    @Override
    public ReturnResult upload(MultipartFile file1, MultipartFile file2) {
        log.warn("Default ");
        return null;
    }


}
