package com.sugon.cloud.iam.api.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

@Data
@ApiModel("创建/修改微服务类别")
public class CreateOrUpdateMicroServiceCategoryVO {
    @ApiModelProperty(value = "id", hidden = true)
    private String id;
    @ApiModelProperty(value = "名称")
    @NotBlank(message = "微服务类别名称不能为空")
    private String name;
    @ApiModelProperty(value = "描述")
    private String description;
    @ApiModelProperty(value = "是否隐藏")
    @JsonProperty("nav_hidden")
    private Boolean navHidden;
}