package com.sugon.cloud.iam.api.service.message.messagetype;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sugon.cloud.iam.api.entity.GlobalsettingsEntity;
import com.sugon.cloud.iam.api.service.GlobalsettingsService;
import com.sugon.cloud.iam.api.utils.JacksonUtils;
import com.sugon.cloud.iam.common.model.dto.SendDTO;
import com.sugon.cloud.iam.common.model.dto.SendSmsResponseDTO;
import com.sugoncloudapi.common.Credential;
import com.sugoncloudapi.common.JsonResponseModel;
import com.sugoncloudapi.common.exception.SugonCloudSDKException;
import com.sugoncloudapi.common.profile.ClientProfile;
import com.sugoncloudapi.common.profile.HttpProfile;
import com.sugoncloudapi.sms.SmsClient;
import com.sugoncloudapi.sms.models.DescribeSendRecordResponse;
import com.sugoncloudapi.sms.models.SendRecordRequest;
import com.sugoncloudapi.sms.models.SendSmsRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @Author: yangdingshan
 * @Date: 2025/1/25 14:20
 * @Description:
 */
@Slf4j
@Service(value = "message_sms")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RefreshScope
public class SmsMessageService implements MessageTypeService {

    private SmsClient smsClient;

    private final GlobalsettingsService globalsettingsService;

    private String projectId;

    @Value("${sms.ak:}")
    private String ak;

    @Value("${sms.sk:}")
    private String sk;

    public void init() {

        if (StrUtil.isBlank(ak) || StrUtil.isBlank(sk)) {
            throw new RuntimeException("ak或sk未配置");
        }
        GlobalsettingsEntity smsProject = globalsettingsService.getGlobalsettingsEntity("sms", "project_id");
        if (Objects.isNull(smsProject)) {
            throw new RuntimeException("短信项目未配置");
        }
        projectId = smsProject.getPolicyDocument();
        // 全局配置
        Credential cred = new Credential(ak, sk);

        // 实例化一个http选项，可选的，没有特殊需求可以跳过
        HttpProfile httpProfile = new HttpProfile();

        // 请求连接超时时间，单位为秒(默认60秒)
        httpProfile.setConnTimeout(30);
        // 设置写入超时时间，单位为秒(默认0秒)
        httpProfile.setWriteTimeout(30);
        // 设置读取超时时间，单位为秒(默认0秒)
        httpProfile.setReadTimeout(30);

        // 全局配置
        GlobalsettingsEntity endpoint = globalsettingsService.getGlobalsettingsEntity("sms", "endpoint");
        if (Objects.isNull(endpoint) || StrUtil.isBlank(endpoint.getPolicyDocument())) {
            throw new RuntimeException("短信服务器地址未配置");
        }
        // 指定接入地域域名(默认就近接入)
        String endpointValue = endpoint.getPolicyDocument();
        httpProfile.setProtocol(HttpProfile.REQ_HTTPS);
        httpProfile.setEndpoint(endpointValue);
        if (endpointValue.contains("//")) {
            String[] split = endpointValue.split("//");
            httpProfile.setProtocol(split[0] + "//");
            httpProfile.setEndpoint(split[1]);
        }

        ClientProfile clientProfile = new ClientProfile();
        // 指定签名算法(默认为HmacSHA256)
        clientProfile.setSignMethod("HmacSHA256");
        clientProfile.setHttpProfile(httpProfile);

        // 实例化要请求产品(以vod为例)的client对象,clientProfile是可选的
        smsClient = new SmsClient( null, cred, "RegionOne" , clientProfile);
    }

    @Override
    public void send(SendDTO dto) {
        init();
        SendSmsRequest req = getSendSmsRequest(dto);
        try {
            JsonResponseModel send = smsClient.send(req);
            log.debug("发送短信结果:{}", JacksonUtils.writeAsJsonString(send));
            List<SendSmsResponseDTO> responses = JacksonUtils.parseObject(send.content, new TypeReference<List<SendSmsResponseDTO>>() {});
            Assert.isTrue(CollUtil.isNotEmpty(responses), "发送短信失败");
            SendSmsResponseDTO response = responses.get(0);
            Assert.isTrue("Ok".equals(response.getCode()), response.getMessage());
        } catch (Exception e) {
            log.error("发送短信失败", e);
            throw new RuntimeException(e.getMessage());
        }
    }

    private SendSmsRequest getSendSmsRequest(SendDTO dto) {
        SendSmsRequest req = new SendSmsRequest();
        req.setPhoneNumberSet(dto.getContacts());
        req.setTemplateId(dto.getTemplateId());
        // 使用admin发送短信
        req.setProjectId(projectId);
        req.setTemplateParams(dto.getTemplateParams());
        return req;
    }

    /**
     * 获取计费条数，用户配额同步
     *
     * @return
     */
    public int getFeeCount(String projectId) {
        init();
        SendRecordRequest req = new SendRecordRequest();
        req.setProjectId(projectId);
        try {
            DescribeSendRecordResponse response = smsClient.describeSendRecordCount(req);
            if (Objects.isNull(response)) {
                return 0;
            }
            return response.getFeeCount();
        } catch (SugonCloudSDKException e) {
            log.error("获取短信计费条数失败", e);
            throw new RuntimeException(e);
        }
    }
}
