package com.sugon.cloud.iam.api.service.ukeyImpl;

import com.alibaba.fastjson.JSONObject;
import com.sugon.cloud.iam.api.config.MFAMappingJackson2HttpMessageConverter;
import com.sugon.cloud.iam.api.entity.swxa.SwxaVerifyToken;
import com.sugon.cloud.iam.api.entity.swxa.VerifyData;
import com.sugon.cloud.iam.api.service.MFAVerifyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.ssl.TrustStrategy;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import javax.net.ssl.SSLContext;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;

/**
 * 三未信安动态口令验证
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-02-02 14:06
 */
@Slf4j
@Service("swxaMFAVerify")
public class SwxaMFAVerifyServiceImpl implements MFAVerifyService {

    @Value("${user.authorize.mfa.server}")
    private String mfaOTPServer;
    @Value("${mfa.swxa.strategy:SM3}")
    private String strategy;
    @Value("${mfa.swxa.wordSource:1}")
    private String wordSource;
    @Value("${mfa.swxa.companyId:100022}")
    private String companyId;

    private RestTemplate mfaRestTemplate;

    private RestTemplate httpsRestTemplate;

    @PostConstruct
    public void initMFARestTemplate() {
        HttpComponentsClientHttpRequestFactory HTTP_REQUEST_FACTORY = new HttpComponentsClientHttpRequestFactory();
        HTTP_REQUEST_FACTORY.setConnectTimeout(5000);
        // 自定义超时时间
        HTTP_REQUEST_FACTORY.setReadTimeout(30000);
        mfaRestTemplate = new RestTemplate(HTTP_REQUEST_FACTORY);
        mfaRestTemplate.getMessageConverters().add(new MFAMappingJackson2HttpMessageConverter());
    }

    @PostConstruct
    public void initHttpsRestTemplate() throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
        TrustStrategy acceptingTrustStrategy = (x509Certificates, authType) -> true;
        SSLContext sslContext = SSLContexts.custom()
                .loadTrustMaterial(null, acceptingTrustStrategy)
                .build();
        SSLConnectionSocketFactory sslFactory = new SSLConnectionSocketFactory(
                sslContext, new NoopHostnameVerifier());
        CloseableHttpClient httpClient = HttpClients.custom()
                .setSSLSocketFactory(sslFactory)
                .build();
        HttpComponentsClientHttpRequestFactory factory =
                new HttpComponentsClientHttpRequestFactory();
        factory.setConnectTimeout(3600000);
        factory.setReadTimeout(3600000);
        factory.setHttpClient(httpClient);
        httpsRestTemplate = new RestTemplate(factory);
    }

    @Override
    public Boolean verifyMfaToken(String tokenSN, String mfaCode, String challenge) {
        try {
            SwxaVerifyToken swxaVerifyToken = new SwxaVerifyToken();
            VerifyData verifyData = new VerifyData();
            verifyData.setStrategy(strategy);
            verifyData.setCommandWord(mfaCode);
            verifyData.setWordSource(wordSource);
            verifyData.setTokenSN(tokenSN);
            swxaVerifyToken.setData(verifyData);
            swxaVerifyToken.setCompanyId(companyId);
            log.info("swxa request body {}", JSONObject.toJSONString(swxaVerifyToken));
            JSONObject result = null;
            if (mfaOTPServer.startsWith("https")) {
                result = httpsRestTemplate.postForObject(mfaOTPServer, swxaVerifyToken, JSONObject.class);
            } else {
                result = mfaRestTemplate.postForObject(mfaOTPServer, swxaVerifyToken, JSONObject.class);
            }
            log.info("call swxa verifyToken result {}", result);
            return result.getBoolean("success");
        }catch (Exception e) {
            log.error("call swxa otp server error", e);
            return false;
        }
    }
}
