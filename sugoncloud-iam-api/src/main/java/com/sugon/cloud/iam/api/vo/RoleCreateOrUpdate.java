package com.sugon.cloud.iam.api.vo;

import com.sugon.cloud.iam.api.ann.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel("创建或者更新角色FORM")
public class RoleCreateOrUpdate {

    @ApiModelProperty("角色ID(创建时不传)")
    @NotBlank(message = "角色ID不能为空", groups = UpdateGroup.class)
    private String id;
    @ApiModelProperty("角色名称")
    @NotBlank(message = "角色名称不能为空")
    private String name;
    @ApiModelProperty("组织Id")
    private String deptId;
    @ApiModelProperty("描述")
    private String description;
    @ApiModelProperty("自定义角色类型 custom_platform:平台 | custom_root_department:根组织 | custom_regular:普通用户")
    private String type;
}
