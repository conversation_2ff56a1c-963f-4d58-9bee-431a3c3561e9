package com.sugon.cloud.iam.api.controller.jwt;

import com.alibaba.fastjson.JSONObject;
import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.service.IamRoleService;
import com.sugon.cloud.iam.api.service.JwtService;
import com.sugon.cloud.iam.api.service.MFAAuthorizeService;
import com.sugon.cloud.iam.common.model.vo.RoleResponseVO;
import com.sugon.cloud.iam.common.model.vo.ValidateTokenUserResponseVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 适配cv6 parstor单点登录逻辑
 */
@RestController
@Log4j2
@RequestMapping("/api/iam/users/login-user")
@RefreshScope
@Api(value = "适配CV6单点登录控制器", tags = "适配CV6单点登录控制器")
@DocumentIgnore
public class ParstorSingleLoginController {
    @Autowired
    private JwtService jwtService;

    @Autowired
    private IamRoleService iamRoleService;


    @GetMapping("")
    @ApiOperation(value = "单点登录")
    public ResultModel<JSONObject> singleLogin(HttpServletRequest httpRequest) throws Exception {
        String token = httpRequest.getHeader("Authorization");
        ValidateTokenUserResponseVO validateTokenUserResponseVO = jwtService.validateToken(token,httpRequest);
        if (Objects.isNull(validateTokenUserResponseVO)) {
            return ResultModel.error("token validate error!");
        }
        PageCL<RoleResponseVO> roleResponseVOPageCL = iamRoleService.userRoles(validateTokenUserResponseVO.getUserId(), null, 1, 9999, true, null);
        String username = validateTokenUserResponseVO.getUsername();
        if(!"admin".equals(username)) {
            return ResultModel.error("仅admin可以访问");
        }
        List<RoleResponseVO> roleList = roleResponseVOPageCL.getList();
        List<JSONObject> returnRoleList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(roleList)){
            for(RoleResponseVO roleResponseVO : roleList){
                if("admin内置角色".equals(roleResponseVO.getName())){
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("uuid",roleResponseVO.getId());
                    jsonObject.put("role_name","超级管理员");//cv6为超级管理员
                    jsonObject.put("description",roleResponseVO.getDescription());
                    jsonObject.put("role_type",roleResponseVO.getType());
                    returnRoleList.add(jsonObject);
                }
            }
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("userId", validateTokenUserResponseVO.getUserId());
        jsonObject.put("username", username);
        jsonObject.put("userType", validateTokenUserResponseVO.getUserType());
        jsonObject.put("roles", returnRoleList);
        return ResultModel.success(jsonObject);
    }

}
