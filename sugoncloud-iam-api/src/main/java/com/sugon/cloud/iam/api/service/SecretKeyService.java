package com.sugon.cloud.iam.api.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.entity.SecretKeyEntity;
import com.sugon.cloud.iam.common.model.vo.ValidateTokenUserResponseVO;

import java.util.List;

public interface SecretKeyService extends IService<SecretKeyEntity> {

    PageCL<SecretKeyEntity> getSecretKeyPage(List<String> userIds, int pageNumber, int pageSize);

    ResultModel<ValidateTokenUserResponseVO> validateBasicToken(String token);
}
