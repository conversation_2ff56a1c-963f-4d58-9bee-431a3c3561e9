package com.sugon.cloud.iam.api.controller;


import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.iam.api.service.UkeyService;
import com.sugon.cloud.iam.api.utils.EncryptAndDecryptUtil;
import com.sugon.cloud.iam.common.model.vo.MfaVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.Base64;

@RestController
@RequestMapping("/enc-dec")
@Api(tags = "加密解密接口", description = "  ")
@Slf4j
@RefreshScope
@DocumentIgnore
public class EncAndDecController {

    @Autowired
    private UkeyService ukeyService;

    @Value("${encryption.enabled}")
    private boolean encEnabled;

    @Value("${mfa.enabled}")
    private boolean mfaEnabled;

    @Autowired
    private EncryptAndDecryptUtil encryptAndDecryptUtil;


    @GetMapping("/iv")
    @ApiOperation("随机数生成初始化向量iv")
    public ResultModel<String> getIV(@RequestParam(value = "length", defaultValue = "16", required = false) int length) {

        return ResultModel.success("执行成功", encryptAndDecryptUtil.getIv());
    }


    @PostMapping("/verify")
    @ApiOperation("验签")
    public ResultModel<Boolean> verify(@RequestBody MfaVo mfaVo){
        // 7.2直接将证书放入验签
        return ResultModel.success("执行成功",ukeyService.verifyData(mfaVo));
    }

    @GetMapping("/mfa-switch")
    @ApiOperation("开关")
    public boolean getMfaSwitch() {
        return mfaEnabled;
    }

    @GetMapping("/enc-switch")
    @ApiOperation("开关")
    public boolean getEncSwitch() {
        return encEnabled;
    }

}
