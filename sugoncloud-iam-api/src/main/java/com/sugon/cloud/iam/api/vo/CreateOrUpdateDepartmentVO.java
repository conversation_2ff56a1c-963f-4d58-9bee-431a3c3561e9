package com.sugon.cloud.iam.api.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
@ApiModel("创建/修改组织")
public class CreateOrUpdateDepartmentVO {
    @ApiModelProperty(value = "id", hidden = true)
    private String id;
    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "组织名称不能为空")
    private String name;
    @ApiModelProperty(value = "描述")
    private String description;
    @ApiModelProperty(value = "父级组织ID")
    @JsonProperty("parent_id")
    private String parentId;
}