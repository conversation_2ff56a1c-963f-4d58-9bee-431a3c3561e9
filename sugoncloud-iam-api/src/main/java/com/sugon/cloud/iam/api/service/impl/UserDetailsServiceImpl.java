package com.sugon.cloud.iam.api.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sugon.cloud.common.utils.IPUtil;
import com.sugon.cloud.iam.api.entity.GlobalsettingsEntity;
import com.sugon.cloud.iam.api.entity.keystone.LoginUser;
import com.sugon.cloud.iam.api.entity.keystone.Project;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.exception.BusinessException;
import com.sugon.cloud.iam.api.mapper.GlobalsettingsMapper;
import com.sugon.cloud.iam.api.mapper.ProjectMapper;
import com.sugon.cloud.iam.api.mapper.UserMapper;
import com.sugon.cloud.iam.api.service.JwtService;
import com.sugon.cloud.iam.api.service.UserService;
import com.sugon.cloud.iam.api.utils.CommonUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.event.AuthenticationFailureBadCredentialsEvent;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 实现spring security UserDetailsService
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class UserDetailsServiceImpl implements UserDetailsService {
    private final UserMapper userMapper;
    private final ProjectMapper projectMapper;
    private final HttpServletRequest request;
    private final GlobalsettingsMapper globalsettingsMapper;
    private final AnnotationConfigServletWebServerApplicationContext applicationContext;
    private final JwtService jwtService;
    private final UserService userService;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        LambdaQueryWrapper<User> userQueryWrapper = new LambdaQueryWrapper<>();
        userQueryWrapper.eq(User::getName, username);
        User user = userMapper.selectOne(userQueryWrapper);
        if (user == null) {
            UsernamePasswordAuthenticationToken token = new UsernamePasswordAuthenticationToken(username, "");
            UsernameNotFoundException usernameNotFoundException = new UsernameNotFoundException("");
            AuthenticationFailureBadCredentialsEvent event = new AuthenticationFailureBadCredentialsEvent(token, usernameNotFoundException);
            applicationContext.publishEvent(event);
            throw usernameNotFoundException;
        }
        if (!user.getEnabled()) throw new BusinessException("用户未激活或已冻结，请联系管理员激活");
        if (user.getExpired() != null && user.getExpired().before(new Date()))
            throw new BusinessException("用户已过期");
        //start bug 3424
        List<GlobalsettingsEntity> globalSettingsEntities = globalsettingsMapper.selectList(new LambdaQueryWrapper<GlobalsettingsEntity>()
                .eq(GlobalsettingsEntity::getPolicyName, "long_time_no_login")
                .eq(GlobalsettingsEntity::getPolicyType, "user"));
        //future inner 内置用不不做校验
        if (CollectionUtil.isNotEmpty(globalSettingsEntities) && !"inner".equals(username)) {
            GlobalsettingsEntity globalsettingsEntity = globalSettingsEntities.get(0);
            int noLoginDays = Integer.parseInt(globalsettingsEntity.getPolicyDocument());
            if (CommonUtils.calculateTotalDay(user.getLastActiveAt()) -1 >= noLoginDays) {
                //bug 3724 start
                userMapper.update(null, new LambdaUpdateWrapper<User>()
                        .set(User::getEnabled, false)
                        .eq(User::getId, user.getId()));
                userService.updateUserHash(user.getId());
                //bug 3724 end
                throw new BusinessException("用户长时间未登录，账号被冻结，请联系管理员。");
            }
        }
        //end bug 3424
        String ipAddr = IPUtil.getIpAddr(request);
        this.checkIP(user.getAllowIp(), ipAddr);

        // 进行时间访问策略校验
        jwtService.checkTime(user.getId());

        List<SimpleGrantedAuthority> authorityList = new ArrayList<>();
        LoginUser loginUser = new LoginUser(user.getName(), user.getPassword(), authorityList);
        if (StringUtils.isBlank(user.getDefaultProjectId())) {
            List<Project> projects = projectMapper.findByUserId(user.getId());
            if (!CollectionUtils.isEmpty(projects)) {
                user.setDefaultProjectId(projects.get(0).getId());
                //如果没有default project id 设置一个default id
                userMapper.update(null, new LambdaUpdateWrapper<User>()
                        .eq(User::getId, user.getId())
                        .set(User::getDefaultProjectId, user.getDefaultProjectId()));
                userService.updateUserHash(user.getId());
            }
        }

        loginUser.setProjectId(user.getDefaultProjectId());
        loginUser.setUserId(user.getId());
        loginUser.setUserType(user.getType());
        loginUser.setDeptId(user.getDeptId());
        if (StringUtils.isBlank(user.getAlias())) {
            loginUser.setAlias(user.getName());
        } else {
            loginUser.setAlias(user.getAlias());
        }
        return loginUser;
    }

    private void checkIP(String allowIP, String ipAddr) {
        if (StringUtils.isNotBlank(allowIP) && !ipAddr.equals(allowIP)) throw new BusinessException("当前IP不允许登录");
    }

}
