package com.sugon.cloud.iam.api.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "全局设置-短信")
public class GlobalSettingsSmsVO {

    @ApiModelProperty(value = "短信服务器地址")
    private String endpoint;
    @ApiModelProperty(value = "APPID")
    @JsonProperty("project_id")
    private String projectId;
}
