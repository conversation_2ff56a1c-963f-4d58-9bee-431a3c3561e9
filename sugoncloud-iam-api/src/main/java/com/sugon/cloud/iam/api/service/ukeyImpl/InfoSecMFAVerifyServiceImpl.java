package com.sugon.cloud.iam.api.service.ukeyImpl;

import cn.com.infosec.netpass.authapi.AuthAPIParam;
import cn.com.infosec.netpass.authapi.CardAuthManager;
import com.sugon.cloud.iam.api.service.MFAVerifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 信安世纪动态口令验证
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-02-02 14:06
 */
@Slf4j
@Service("infoSecMFAVerify")
public class InfoSecMFAVerifyServiceImpl implements MFAVerifyService {

    @Override
    public Boolean verifyMfaToken(String tokenSN, String mfaCode, String challenge) {
        try {
            AuthAPIParam authAPIParam = new AuthAPIParam();
            // 种子编号
            authAPIParam.setCardSN(tokenSN);
            // 动态口令
            authAPIParam.setPassword(mfaCode);
            // 挑战值
            authAPIParam.setChallenge(challenge);
            boolean result = CardAuthManager.authCard(authAPIParam);
            log.info("信安世纪动态口令验证结果:{}", result);
            return result;
        }catch (Exception e) {
            log.error("信安世纪动态口令异常", e);
            return false;
        }
    }
}
