package com.sugon.cloud.iam.api.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/5 16:14
 */

@Data
@ApiModel("vmware")
public class VmwareParam {

    @ApiModelProperty(value = "url")
    private String url;
    @JsonProperty("public_key")
    @JSONField(name = "public_key")
    @ApiModelProperty(value = "public_key")
    private String publicKey;
    @JsonProperty("vmware_name")
    @JSONField(name = "vmware_name")
    @ApiModelProperty(value = "用户")
    private String vmwareName;
    @JsonProperty("vmware_password")
    @JSONField(name = "vmware_password")
    @ApiModelProperty(value = "密码")
    private String vmwarePassword;
}
