package com.sugon.cloud.iam.api.controller;

import cn.hutool.json.JSONUtil;
import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.iam.api.entity.Department;
import com.sugon.cloud.iam.api.entity.keystone.Project;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.mapper.DepartmentMapper;
import com.sugon.cloud.iam.api.mapper.ProjectMapper;
import com.sugon.cloud.iam.api.mapper.UserMapper;
import com.sugon.cloud.iam.api.service.resourceauth.AdminUserTypeAuthHandler;
import com.sugon.cloud.iam.api.service.resourceauth.RoleAuthHandler;
import com.sugon.cloud.iam.api.service.resourceauth.UserCreateAuthHandler;
import com.sugon.cloud.resource.auth.common.aop.ResourceAuth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "iam资源全查询")
@RequestMapping(value = "/api/resource")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RestController
@DocumentIgnore
public class IamResourceShowController {

    /**
     * 服务对象
     */
    private final UserMapper userMapper;
    private final ProjectMapper projectMapper;
    private final DepartmentMapper departmentMapper;

    @GetMapping("/user")
    @ApiOperation("查询所有用户")
    @ResourceAuth(handler = AdminUserTypeAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    public ResultModel<String> listAllUser() {
        List<User> users = userMapper.selectList(null);
        return ResultModel.success("成功",JSONUtil.toJsonStr(users));
    }


    @GetMapping("dept")
    @ApiOperation("查询所有组织")
    @ResourceAuth(handler = AdminUserTypeAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    public ResultModel<String> listAllDept() {
        List<Department> departments = departmentMapper.selectList(null);
        return ResultModel.success("成功",JSONUtil.toJsonStr(departments));
    }

    @GetMapping("/project")
    @ApiOperation("查询所有租户")
    @ResourceAuth(handler = AdminUserTypeAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    public ResultModel<String> listAllProject() {
        List<Project> projects = projectMapper.selectList(null);
        return ResultModel.success("成功",JSONUtil.toJsonStr(projects));
    }


}
