package com.sugon.cloud.iam.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sugon.cloud.iam.api.entity.UserAuthorizeSettings;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * (UserAuthorizeSettings)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-05-16 14:12:47
 */
@Repository
public interface UserAuthorizeSettingsMapper extends BaseMapper<UserAuthorizeSettings> {
}
