package com.sugon.cloud.iam.api.config;

import com.sugon.cloud.redis.distributed.lock.config.RedisDistributedLock;
import lombok.extern.slf4j.Slf4j;
import org.flywaydb.core.Flyway;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @date 2022/07/28
 **/
@Configuration
@Slf4j
public class FlywayBeanConfig {

    @Value("${spring.application.name}")
    private String appName;

    @Bean
    @ConditionalOnProperty(prefix = "spring.flyway", name = "enabled", matchIfMissing = true)
    public Flyway flyway(DataSource dataSource, RedisDistributedLock redisDistributedLock) {
        String lockKey = "Flyway-" + appName;
        boolean locked = redisDistributedLock.lock(lockKey, 60_000L);
        if (!locked) {
            log.error("Flyway获取lockKey=[{}]的redis锁超时", lockKey);
            // 停掉应用,静待下一次的重启
            Runtime.getRuntime().halt(1);
        }
        try {
            log.info("::::::::::::::flyway begin");
            Flyway flyway = Flyway.configure().dataSource(dataSource).placeholderReplacement(false).load();
            flyway.migrate();
            log.info("::::::::::::::flyway end");
            return flyway;
        } finally {
            redisDistributedLock.unLock(lockKey);
        }
    }
}
