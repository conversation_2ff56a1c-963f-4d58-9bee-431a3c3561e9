package com.sugon.cloud.iam.api.listener;

import cn.hutool.core.util.StrUtil;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.service.MonitorService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.event.AuthenticationSuccessEvent;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;


@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class ApplicationListenerAuthenticationSuccess implements ApplicationListener<AuthenticationSuccessEvent> {

    private final RedisTemplate redisTemplate;

    private final HttpServletRequest request;

    private final MonitorService monitorService;

    //设置登录认证时target class
    private static final String className = "org.springframework.security.authentication.UsernamePasswordAuthenticationToken";
    @Override
    public void onApplicationEvent(AuthenticationSuccessEvent event) {
        Authentication authentication = event.getAuthentication();
        String targetClassName = event.getSource().getClass().getName();
        if (className.equals(targetClassName)) {
            redisTemplate.delete(CommonInstance.UNAME_ERROR_LOCK + authentication.getName());
            redisTemplate.delete(CommonInstance.UNAME_ERROR_TIMES + authentication.getName());
            String validateCode = request.getHeader("Validate-Code");
            if (StrUtil.isNotBlank(validateCode)) {
                redisTemplate.delete(validateCode);
            }
            //inner推送指标
            if ("inner".equals(authentication.getName())) return;
            // 解锁推送指标
            monitorService.userUnLockAlarm(authentication.getName(), CommonInstance.MONITOR_USER_LOCK_REASON_ONE);
        }
    }
}
