package com.sugon.cloud.iam.api.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;


@Data
@ApiModel("创建区域")
public class RegionForm implements Serializable {

    private static final long serialVersionUID = 2275270781859756520L;
    @ApiModelProperty(value = "区域管理描述", required = true)
    @JsonProperty("description")
    @NotBlank(message = "区域管理描述不能为空")
    @Size(min = 2, max = 255)
    private String description;

    @ApiModelProperty(value = "区域ID")
    @Size(max = 64, message = "区域ID长度不能超过64")
    private String id;
    @ApiModelProperty(value = "区域管理类型:sugoncloud(曙光云)|vmware", required = true)
    private String type;
    @JsonProperty("service_group_id")
    @ApiModelProperty(value = "服务目录ID")
    private String serviceGroupId;

    @JsonProperty("vmware_param")
    private VmwareParam vmwareParam;

    public static String FULL_STACK_TYPE = "sugoncloud";
    public static String VMWARE_TYPE = "vmware";
}
