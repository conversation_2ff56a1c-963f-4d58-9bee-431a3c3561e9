package com.sugon.cloud.iam.api.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sugon.cloud.common.mapper.ModelMapper;
import com.sugon.cloud.common.utils.UUIDUtil;
import com.sugon.cloud.iam.api.entity.keystone.*;
import com.sugon.cloud.iam.api.entity.keystone.VO.CreateRoleParam;
import com.sugon.cloud.iam.api.entity.keystone.VO.CreateRoleVO;
import com.sugon.cloud.iam.api.entity.exception.ResourceConflictException;
import com.sugon.cloud.iam.api.entity.exception.ResourceNotFoundException;
import com.sugon.cloud.iam.api.mapper.AssignmentMapper;
import com.sugon.cloud.iam.api.mapper.DepartmentMapper;
import com.sugon.cloud.iam.api.mapper.RoleMapper;
import com.sugon.cloud.iam.api.mapper.UserMapper;
import com.sugon.cloud.iam.api.service.RoleService;
import com.sugon.cloud.iam.common.model.vo.CreateResourceResponseLinksParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RefreshScope
public class RoleServiceImpl extends ServiceImpl<RoleMapper, Role> implements RoleService {

    private final RoleMapper roleMapper;
    private final ModelMapper mapper;
    private final DepartmentMapper departmentMapper;
    private final AssignmentMapper assignmentMapper;
    private final UserMapper userMapper;

    @Value("#{'${vip.ip}'.concat(':').concat('${vip.port}')}")
    private String linksPrefix;

    private static String defaultDomainId = "<<null>>";

    @Override
    public List<Role> findByUserId(String userId) {
        return roleMapper.findByUserId(userId);
    }

    @Override
    public List<Role> findByUserIdAndProjectId(String userId, String projectId) {
        return roleMapper.findByUserIdAndProjectId(userId, projectId);
    }

    @Override
    public CreateRoleResponse createRole(CreateRoleVO createRoleVO) {
        CreateRoleParam role = createRoleVO.getRole();
        boolean existed = roleMapper.selectCount(new QueryWrapper<Role>()
                .lambda()
                .eq(Role::getName, role.getName())
                .eq(Objects.nonNull(role.getDomainId()), Role::getDomainId, role.getDomainId()))
                .intValue() > 0;
        if (existed) {
            throw new ResourceConflictException("Conflict occurred attempting to store role - " +
                    "Duplicate entry found with name " + role.getName() + ".");
        }
        Role roleEntity = mapper.map(role, Role.class);
        roleEntity.setId(UUIDUtil.get32UUID());
        roleEntity.setExtra(new JSONObject().toString());
        roleMapper.insert(roleEntity);
        CreateRoleResponse result = new CreateRoleResponse();
        result.setRole(role2CreateRoleResponseParam(roleEntity));
        return result;
    }

    @Override
    public List<CreateRoleResponseParam> listRole(String name, String domainId) {
        domainId = StringUtils.isEmpty(domainId) ? defaultDomainId : domainId;
        List<Role> roles = roleMapper.selectList(new QueryWrapper<Role>().lambda()
                .eq(!StringUtils.isEmpty(name), Role::getName, name)
                .eq(Role::getDomainId, domainId));
        return roles.stream().map(role -> role2CreateRoleResponseParam(role)).collect(Collectors.toList());
    }

    @Override
    public CreateRoleResponse roleDetail(String roleId) {
        LambdaQueryWrapper<Role> roleQueryWrapper = new LambdaQueryWrapper<>();
        roleQueryWrapper.eq(Role::getId, roleId);
        Role role = roleMapper.selectOne(roleQueryWrapper);
        if(Objects.isNull(role)){
            throw new ResourceNotFoundException("Could not find role: " + roleId + ".");
        }
        CreateRoleResponse result = new CreateRoleResponse();
        result.setRole(role2CreateRoleResponseParam(role));
        return result;
    }

    /**
     * 将数据库查询到的catalog数据结构转为需要展示的数据结构
     * @param role
     * @return
     */
    private CreateRoleResponseParam role2CreateRoleResponseParam(Role role){
        CreateRoleResponseParam roleResponseParam = mapper.map(role,CreateRoleResponseParam.class);
        roleResponseParam.setLinks(new CreateResourceResponseLinksParam("http://" + linksPrefix +
                "/v3/roles/" + role.getId()));
        if (defaultDomainId.equals(roleResponseParam.getDomainId())) {
            roleResponseParam.setDomainId(null);
        }
        return roleResponseParam;
    }


}
