package com.sugon.cloud.iam.api.controller;


import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.entity.ActionForm;
import com.sugon.cloud.iam.api.entity.MicroService;
import com.sugon.cloud.iam.api.service.MicroServiceService;
import com.sugon.cloud.iam.api.service.ServiceActionService;
import com.sugon.cloud.iam.api.service.resourceauth.SecurityUserTypeAuthHandler;
import com.sugon.cloud.log.client.context.LogRecordContext;
import com.sugon.cloud.log.client.starter.annotation.LogRecordAnnotation;
import com.sugon.cloud.resource.auth.common.aop.ResourceAuth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Api(tags = "资源操作配置")
@RequestMapping(value = "/api/action")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RestController
@Slf4j
@Validated
@DocumentIgnore
public class ServiceActionController {

    private final ServiceActionService serviceAction;

    private final MicroServiceService microServiceService;
    @PostMapping
    @ApiOperation("添加操作")
    @LogRecordAnnotation(value = "添加操作", detail = "添加操作:{{#resource}}", resource = "{{#resource}}", auditFlag = "true")
    @ResourceAuth(handler = SecurityUserTypeAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    public ResultModel add(@RequestBody @Valid List<ActionForm> actionForm) {
        serviceAction.saveBatch(actionForm);
        String resource = "";
        if (CollectionUtil.isNotEmpty(actionForm)) {
            resource = actionForm.stream().map(ActionForm::getAction).collect(Collectors.joining(","));
        }
        LogRecordContext.putVariable("resource", resource);
        return ResultModel.success("添加操作成功");
    }

    @GetMapping("{id}")
    @ApiOperation("操作详情")
    @LogRecordAnnotation(value = "操作详情", detail = "操作详情:{{#resource}}", resourceId = "{{#id}}", resource = "{{#resource}}", auditFlag = "true")
    @ResourceAuth(handler = SecurityUserTypeAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    public ResultModel<ActionForm> get(@PathVariable("id") String id) {
        ActionForm byId = serviceAction.getById(id);
        String resource = "";
        if (!Objects.isNull(byId)) {
            resource = byId.getAction();
        }
        LogRecordContext.putVariable("resource", resource);
        return ResultModel.success("获取详情成功", byId);
    }

    @DeleteMapping("{id}")
    @ApiOperation("删除操作")
    @LogRecordAnnotation(value = "删除操作", detail = "删除操作:{{#resource}}", resourceId = "{{#id}}", resource = "{{#resource}}", auditFlag = "true")
    @ResourceAuth(handler = SecurityUserTypeAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    public ResultModel delete(@PathVariable("id") String id) {
        ActionForm byId = serviceAction.getById(id);
        String resource = "";
        if (!Objects.isNull(byId)) {
            resource = byId.getAction();
        }
        LogRecordContext.putVariable("resource", resource);
        serviceAction.removeById(id);
        return ResultModel.success("删除操作成功");
    }

    @GetMapping("list/{service_id}")
    @ApiOperation("服务操作列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "service_id", value = "服务ID", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "action", value = "操作", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "description", value = "描述", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "page_num", value = "页码", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "page_size", value = "每页显示条数", paramType = "query", dataType = "int")})
    @LogRecordAnnotation(value = "查询特殊权限管理列表",detail = "查询特殊权限管理成功",auditFlag = "true")
    public ResultModel<PageCL<ActionForm>> getList(@PathVariable("service_id") String serviceId,
                                                   @RequestParam(value = "action", required = false) String action,
                                                   @RequestParam(value = "description", required = false) String description,
                                                   @RequestParam(value = "page_num", defaultValue = "1", required = false) int pageNum,
                                                   @RequestParam(value = "page_size", defaultValue = "15" ,required = false) int pageSize) {
        return ResultModel.success("获取列表成功",serviceAction.actionPage(serviceId, action, description, pageNum, pageSize));
    }

    @PutMapping
    @ApiOperation("修改操作")
    @LogRecordAnnotation(value = "修改操作", detail = "修改操作:{{#actionForm.action}}", resourceId = "{{#actionForm.id}}", resource = "{{#actionForm.action}}", auditFlag = "true")
    @ResourceAuth(handler = SecurityUserTypeAuthHandler.class, resources = TypeUtil.TYPE_ADMIN)
    public ResultModel update(@RequestBody ActionForm actionForm) {
        serviceAction.updateById(actionForm);
        return ResultModel.success("修改操作成功");
    }

    @GetMapping("get_by_action")
    @ApiOperation("通过action获取服务详情")
    public ResultModel<MicroService> getByAction(@RequestParam("action") String action) {
        List<ActionForm> list = serviceAction.list(new LambdaQueryWrapper<ActionForm>()
                .eq(ActionForm::getAction, action));
        if (CollectionUtil.isNotEmpty(list)) {
           return ResultModel.success("获取详情成功", microServiceService.getById(list.get(0).getServiceId()));
        }
        return ResultModel.error("当前action没有对应的服务");
    }
}
