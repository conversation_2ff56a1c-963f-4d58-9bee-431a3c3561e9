package com.sugon.cloud.iam.api.service;

import com.sugon.cloud.iam.api.entity.BlacklistMicroService;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sugon.cloud.iam.common.enums.BlacklistTypeEnum;
import com.sugon.cloud.policy.entity.RamPolicyEntity;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【blacklist_micro_service】的数据库操作Service
* @createDate 2024-02-05 15:54:15
*/
public interface BlacklistMicroServiceService extends IService<BlacklistMicroService> {


    List<BlacklistMicroService> listByType(BlacklistTypeEnum type);

    List<BlacklistMicroService> listByTypes(List<BlacklistTypeEnum> types);

}
