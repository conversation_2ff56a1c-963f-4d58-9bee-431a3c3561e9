package com.sugon.cloud.iam.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.common.mapper.ModelMapper;
import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.entity.IamRole;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.exception.BusinessException;
import com.sugon.cloud.iam.api.mapper.IamRoleMapper;
import com.sugon.cloud.iam.api.mapper.UserMapper;
import com.sugon.cloud.iam.api.service.GlobalsettingsService;
import com.sugon.cloud.iam.api.service.SecurityService;
import com.sugon.cloud.iam.common.model.vo.UserViewVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class SecurityServiceImpl implements SecurityService {

    private final UserMapper userMapper;
    private final ModelMapper mapper;
    private final IamRoleMapper iamRoleMapper;
    private final GlobalsettingsService globalsettingsService;

    @Override
    public PageCL<UserViewVO> masterUserList(String name, int pageNum, int pageSize, String currentUserId) {
        User user = userMapper.selectById(currentUserId);
        IPage<User> page = new Page(pageNum, pageSize);
        if(TypeUtil.TYPE_ADMIN.equals(user.getType())){
            userMapper.selectPage(page, new QueryWrapper<User>().lambda()
                    .like(StringUtils.isNotBlank(name), User::getName, name)
                    .eq(User::getType, TypeUtil.TYPE_MASTER)
                    .orderByDesc(User::getCreatedAt));
        }else {
            userMapper.selectPage(page, new QueryWrapper<User>().lambda()
                    .like(StringUtils.isNotBlank(name), User::getName, name)
                    .eq(User::getType, TypeUtil.TYPE_MASTER)
                    .or()
                    .eq(User::getType, TypeUtil.TYPE_ADMIN)
                    .or()
                    .eq(User::getType, TypeUtil.TYPE_AUDIT)
                    .orderByDesc(User::getCreatedAt));
        }
        page.getRecords().stream().forEach(
                u -> u.setExtra(UserServiceImpl.parseUserExtra(u.getExtra(), CommonInstance.USER_EXTRA_DESCRIPTION_KEY)));
        try {
            return new PageCL<UserViewVO>().
                    getPageByPageHelper(page, mapper.mapList(page.getRecords(), UserViewVO.class));
        } catch (Exception e) {
            log.error("com.sugon.cloud.iam.api.service.impl.SecurityServiceImpl.masterUserList error:", e);
            throw new BusinessException("查询根账户列表失败");
        }
    }

    @Override
    public PageCL<IamRole> innerRoleList(String name, int pageNum, int pageSize) {
        IPage<IamRole> page = new Page(pageNum, pageSize);
        List<String> types = new ArrayList<>();
        // 内置角色是否需要区分BM和非BM
        if (globalsettingsService.getBmStatus()) {
            types.addAll(TypeUtil.getBmInnerRoleTypes());
        } else {
            types.addAll(TypeUtil.getInnerRoleTypes());
        }
        iamRoleMapper.selectPage(page, new QueryWrapper<IamRole>().lambda()
                .like(StringUtils.isNotBlank(name), IamRole::getName, name)
                .in(IamRole::getType, types));
        try {
            return new PageCL<IamRole>().
                    getPageByPageHelper(page, page.getRecords());
        } catch (Exception e) {
            log.error("com.sugon.cloud.iam.api.service.impl.SecurityServiceImpl.innerRoleList error:", e);
            throw new BusinessException("查询内置角色列表失败");
        }
    }
}
