package com.sugon.cloud.iam.api.controller.keystone;

import com.google.common.collect.Maps;
import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.iam.api.entity.keystone.VO.VersionV3VO;
import com.sugon.cloud.iam.api.entity.keystone.VO.VersionVO;
import com.sugon.cloud.iam.api.entity.keystone.Version;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import java.util.*;

@RestController
@RequestMapping("")
@Api(tags = {"keystone-版本信息"})
@DocumentIgnore
public class VersionController {
    @Value("${vip.ip}")
    private String ramIP;
    @Value("${vip.port}")
    private String ramPort;

    @GetMapping
    @ResponseStatus(HttpStatus.MULTIPLE_CHOICES)
    @ApiOperation(value = "获取版本信息")
    public VersionVO versions() {
        return initVersion();
    }

    @GetMapping("v3")
    @ApiOperation(value = "获取版本信息")
    public VersionV3VO versionsV3() {
        VersionVO versionVO = initVersion();
        com.sugon.cloud.iam.api.entity.keystone.Value value = versionVO.getVersions().getValues().get(0);
        VersionV3VO versionV3VO = new VersionV3VO();
        versionV3VO.setVersion(value);
        return versionV3VO;
    }

    private VersionVO initVersion () {
        String href = "http://"+ramIP+":"+ramPort+"/v3/";
        Version version = new Version();
        List<Map> links = new ArrayList<>();
        Map map = new HashMap();
        map.put("href", href);
        map.put("rel", "self");
        links.add(map);
        List<Map>  mediaTypes = new ArrayList<>();
        Map mediaType = Maps.newHashMap();
        mediaType.put("base", "application/json");
        mediaType.put("type", "application/vnd.openstack.identity-v3+json");
        mediaTypes.add(mediaType);
        com.sugon.cloud.iam.api.entity.keystone.Value value = new com.sugon.cloud.iam.api.entity.keystone.Value();
        value.setLinks(links);
        value.setMediaTypes(mediaTypes);
        List<com.sugon.cloud.iam.api.entity.keystone.Value> values = new ArrayList<>();
        values.add(value);
        version.setValues(values);
        VersionVO versions= new VersionVO();
        versions.setVersions(version);
        return versions;
    }
}
