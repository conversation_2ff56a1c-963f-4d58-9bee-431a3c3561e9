package com.sugon.cloud.iam.api.service.resourceauth;

import com.sugon.cloud.resource.auth.common.configuration.AuthHandlerConfig;
import com.sugon.cloud.resource.auth.common.service.ResourceAuthException;
import com.sugon.cloud.resource.auth.common.service.impl.BaseResourceAuthHandler;
import org.springframework.util.CollectionUtils;

import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/24 14:32
 */
public abstract class BaseResourceExtentAuthHandler extends BaseResourceAuthHandler {


    public void checkUserResource(List<String> userResources, List<String> resources) {
        if (!userResources.contains("admin") && !CollectionUtils.isEmpty(resources)) {
            Iterator<String> var3 = resources.iterator();

            String resource;
            do {
                if (!var3.hasNext()) {
                    return;
                }

                resource = var3.next();
            } while(userResources.contains(resource));

            throw new ResourceAuthException("您没有操作该资源的权限");
        }
    }
}
