package com.sugon.cloud.iam.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sugon.cloud.iam.api.entity.StrategyPO;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;


@Repository
public interface StrategyMapper extends BaseMapper<StrategyPO> {

    @Select("select id, name, description, policy from strategy inner join user_strategy on  strategy.id = user_strategy.strategy_id where user_strategy.user_id=#{userId}")
    IPage<StrategyPO> findByUserIdPage(IPage<StrategyPO> page, String userId);
}
