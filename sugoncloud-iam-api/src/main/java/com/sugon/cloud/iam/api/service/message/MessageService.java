package com.sugon.cloud.iam.api.service.message;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.sugon.cloud.common.constants.HeaderParamConstant;
import com.sugon.cloud.iam.api.common.SecretCertificate;
import com.sugon.cloud.iam.api.entity.BusinessMessageRelation;
import com.sugon.cloud.iam.api.service.GlobalsettingsService;
import com.sugon.cloud.iam.api.service.UserService;
import com.sugon.cloud.iam.api.service.message.messagetype.MessageTypeFactory;
import com.sugon.cloud.iam.api.vo.message.AuthorizeSendCodeVO;
import com.sugon.cloud.iam.api.vo.message.SendVO;
import com.sugon.cloud.iam.common.model.dto.SendDTO;
import com.sugon.cloud.iam.common.model.vo.PlatformSendNoticeVO;
import com.sugon.cloud.iam.common.model.vo.UserViewVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Author: yangdingshan
 * @Date: 2025/1/25 14:19
 * @Description:
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
@RefreshScope
public class MessageService {

    private final MessageTypeFactory messageTypeFactory;

    private final HttpServletRequest request;

    private final UserService userService;

    private final BusinessMessageRelationService businessMessageRelationService;

    private final RedisTemplate<String, Object> redisTemplate;

    private final GlobalsettingsService globalsettingsService;

    @Value("${mfa.type}")
    private String mfaType;

    @Value("#{'${domains.to.replace:.cloud.local,.sugon.local}'.split(',')}")
    private List<String> domainsToReplace;

    /**
     * 通用发送
     *
     * @param vo
     */
    public void send(SendVO vo) {
        SendDTO dto = new SendDTO();
        dto.setContacts(vo.getContacts());
        dto.setTemplateId(vo.getTemplateId());
        dto.setTemplateParams(vo.getTemplateParams());
        messageTypeFactory.getInstance(vo.getSendMode()).send(dto);
    }

    /**
     * 认证发送验证码
     *
     * @param vo
     */
    public void authorizeSendCode(AuthorizeSendCodeVO vo) {
        String username = StrUtil.isNotBlank(vo.getUsername()) ? vo.getUsername() : request.getHeader(HeaderParamConstant.USER_NAME);
        Assert.isTrue(StrUtil.isNotBlank(username), "用户名不能为空");

        SendVO sendVO = new SendVO();
        UserViewVO contactInfo = userService.getContactInfo(username);
        String contact = contactInfo.getEmail();
        if (SecretCertificate.SMS.equals(mfaType)) {
            contact = contactInfo.getPhone();
        }
        Assert.isTrue(StrUtil.isNotBlank(contact), "用户未配置手机号或邮箱");
        // 如果存在验证码，则冲掉上一个验证
        if (Boolean.TRUE.equals(redisTemplate.hasKey(contact))) {
            String redisCode = String.valueOf(redisTemplate.opsForValue().get(contact));
            redisTemplate.delete(redisCode);
            redisTemplate.delete(contact);
        }

        sendVO.setContacts(CollUtil.newHashSet(contact));
        // 业务未开启
        BusinessMessageRelation relation = businessMessageRelationService.getRelation(vo.getBusinessType(), mfaType, 3);
        Assert.isTrue(relation.getEnabled(), "业务未开启" + mfaType + "方式验证码");
        sendVO.setTemplateId(relation.getTemplateId());
        sendVO.setSendMode(mfaType);
        // 组装模板参数
        SecureRandom secureRandom = new SecureRandom();
        int code = 100000 + secureRandom.nextInt(900000);
        List<String> templateParams = new ArrayList<>();
        templateParams.add(String.valueOf(code));
        sendVO.setTemplateParams(templateParams);
        // 将验证码存入redis，登录过期时间是10分钟有效
        redisTemplate.opsForValue().set(String.valueOf(code), contact, 10, TimeUnit.MINUTES);
        redisTemplate.opsForValue().set(contact, String.valueOf(code), 10, TimeUnit.MINUTES);
        send(sendVO);
    }


    /**
     * 平台发送通知
     *
     * @param vo
     */
    public void sendPlatformNotice(PlatformSendNoticeVO vo) {
        // 发送短信和邮件
        if ("alarm".equals(vo.getBusinessType())) {
            String smsStatus = globalsettingsService.getGlobalsettingsEntity("sms", "status").getPolicyDocument();
            if ("true".equals(smsStatus)) {
                sendPlatformNoticeBySms(vo);
            }
            String emailStatus = globalsettingsService.getGlobalsettingsEntity("email", "status").getPolicyDocument();
            if ("true".equals(emailStatus)) {
                sendPlatformNoticeByEmail(vo);
            }
        } else {
            sendPlatformNoticeBySms(vo);
            sendPlatformNoticeByEmail(vo);
        }
    }

    private void sendPlatformNoticeByEmail(PlatformSendNoticeVO vo) {
        try {
            if (CollUtil.isEmpty(vo.getEmails())) {
                return;
            }
            SendVO noticeVO = new SendVO();
            noticeVO.setSendMode(SecretCertificate.EMAIL);
            BusinessMessageRelation relation = businessMessageRelationService.getRelation(vo.getBusinessType(), SecretCertificate.EMAIL, 2);
            if (!relation.getEnabled()) {
                // 通知类不报错
                log.info("{}业务未开启邮箱方式通知", vo.getBusinessType());
                return;
            }
            noticeVO.setTemplateId(relation.getTemplateId());
            noticeVO.setContacts(vo.getEmails());
            noticeVO.setTemplateParams(vo.getTemplateParams());
            // 发送通知
            send(noticeVO);
        } catch (Exception e) {
            log.error("平台通知发送邮件失败", e);
        }
    }

    private void sendPlatformNoticeBySms(PlatformSendNoticeVO vo) {
        try {
            if (CollUtil.isEmpty(vo.getPhones())) {
                return;
            }
            SendVO noticeVO = new SendVO();
            noticeVO.setSendMode(SecretCertificate.SMS);
            BusinessMessageRelation relation = businessMessageRelationService.getRelation(vo.getBusinessType(), SecretCertificate.SMS, 2);
            if (!relation.getEnabled()) {
                log.info("{}业务未开启短信方式通知", vo.getBusinessType());
                return;
            }
            noticeVO.setTemplateId(relation.getTemplateId());
            noticeVO.setContacts(vo.getPhones());
            noticeVO.setTemplateParams(domainsToReplace(vo.getTemplateParams()));
            // 发送通知
            send(noticeVO);
        } catch (Exception e) {
            log.error("平台通知发送短信失败", e);
        }
    }

    private List<String> domainsToReplace(List<String> templateParams) {
        if (CollUtil.isEmpty(templateParams)) {
            return templateParams;
        }
        String regex = domainsToReplace.stream().map(Pattern::quote).collect(Collectors.joining("|"));
        return templateParams.stream()
                .map(param -> param.replaceAll(regex, ""))
                .collect(Collectors.toList());
    }
}
