package com.sugon.cloud.iam.api.jwt;

import cn.hutool.core.util.StrUtil;
import com.auth0.jwt.exceptions.TokenExpiredException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sugon.cloud.common.constants.HeaderParamConstant;
import com.sugon.cloud.common.model.ResultModel;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.config.SkipValidateTokenConfig;
import com.sugon.cloud.iam.api.entity.DangerOperationUrl;
import com.sugon.cloud.iam.api.entity.SecretKeyEntity;
import com.sugon.cloud.iam.api.entity.exception.V3DeniedException;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.sca4.SCA4Match;
import com.sugon.cloud.iam.api.service.JwtService;
import com.sugon.cloud.iam.api.service.SecretKeyService;
import com.sugon.cloud.iam.api.service.UserDetails4NativeService;
import com.sugon.cloud.iam.api.service.UserService;
import com.sugon.cloud.iam.api.service.message.DangerOperationUrlService;
import com.sugon.cloud.iam.api.utils.IamMutableHttpServletRequest;
import com.sugon.cloud.iam.common.model.vo.ValidateTokenUserResponseVO;
import io.jsonwebtoken.Claims;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.annotation.Annotation;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;


@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Component
@Log4j2
@Order(1)
public class JwtTokenFilter extends OncePerRequestFilter implements Order {

    private final JwtTokenUtils jwtTokenUtils;

    private final UserDetails4NativeService userDetails4NativeService;

    private final SkipValidateTokenConfig jwtSkipUri;

    private final SecretKeyService secretKeyService;

    private final UserService userService;

    private final DangerOperationUrlService dangerOperationUrlService;

    private final RedisTemplate<String, Object> redisTemplate;

    private final JwtService jwtService;
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        // feign调用的直接返回，内部调用
        if (HeaderParamConstant.FEIGN_ENABLE_VALUE.equals(request.getHeader(HeaderParamConstant.FEIGN_ENABLE_KEY))) {
            filterChain.doFilter(request, response);
            return;
        }
        // 验证码
        DangerOperationUrl dangerOperationUrl = checkValidateCode(request);
        if (Objects.nonNull(dangerOperationUrl)) {
            String validateCode = request.getHeader("Validate-Code");
            if (StrUtil.isBlank(validateCode)) {
                log.error("*****需要验证码，头部验证不通过，请在头部输入Validate-Code");
                throw new TokenExpiredException("验证码不能为空");
            }
            log.debug("验证码：{}", validateCode);
            if (!NumberUtils.isDigits(validateCode)) {
                throw new TokenExpiredException("验证码必须为纯数字");
            }
            if (Boolean.FALSE.equals(redisTemplate.hasKey(validateCode))) {
                throw new TokenExpiredException("验证码错误,请重新输入");
            }
            if (Boolean.TRUE.equals(dangerOperationUrl.getEnabled())) {
                redisTemplate.delete(validateCode);
            }
        }

        // 白名单
        if (isSkipRequest(request)) {
            filterChain.doFilter(request, response);
            return;
        }

        String authorization = resolveToken(request);
        if (StrUtil.isBlank(authorization)) {
            throw new TokenExpiredException("token不能为空");
        }
        IamMutableHttpServletRequest mutableHttpServletRequest = new IamMutableHttpServletRequest(request);
        if (authorization.startsWith("SCA4-HMAC-SHA256")) {
            // SDK调用
            try {
                SCA4Match.SignValues signValues = SCA4Match.parseSignV4(authorization);
                String accessKey = signValues.getCredential().getAccessKey();
                SecretKeyEntity secretKeyEntity = secretKeyService.getOne(new LambdaQueryWrapper<SecretKeyEntity>().eq(SecretKeyEntity::getAccessKey, accessKey));
                if (Objects.isNull(secretKeyEntity)) {
                    throw new TokenExpiredException("认证无效或已过期");
                }
                String userId = secretKeyEntity.getUserId();
                User user = userService.getById(userId);
                if (Objects.isNull(user)) {
                    throw new TokenExpiredException("认证无效或已过期");
                }
                mutableHttpServletRequest.putHeader(HeaderParamConstant.USER_ID.toLowerCase(), user.getId());
                mutableHttpServletRequest.putHeader(HeaderParamConstant.USER_NAME.toLowerCase(), user.getName());
                mutableHttpServletRequest.putHeader(HeaderParamConstant.USER_TYPE.toLowerCase(), user.getType());
            } catch (Exception e) {
                throw new TokenExpiredException("认证无效或已过期");
            }
            // 重新设置用户信息
            mutableHttpServletRequest.putHeader(HeaderParamConstant.CURRENT_PROJECT_ID.toLowerCase(), request.getHeader("X-SCA4-Project"));
            mutableHttpServletRequest.putHeader(HeaderParamConstant.REGION_ID.toLowerCase(), request.getHeader("X-SCA4-Region"));
            mutableHttpServletRequest.putHeader("X-SCA4-Method".toLowerCase(), request.getMethod());
            mutableHttpServletRequest.putHeader("X-SCA4-URI".toLowerCase(), request.getRequestURI());
        } else if (authorization.startsWith("Basic")) {
            ResultModel<ValidateTokenUserResponseVO> resultModel = secretKeyService.validateBasicToken(authorization);
            if (resultModel.getStatusCode() == 0) {
                throw new TokenExpiredException("认证无效或已过期");
            }
            ValidateTokenUserResponseVO validateTokenUserResponseVO = resultModel.getContent();
            mutableHttpServletRequest.putHeader(HeaderParamConstant.USER_ID.toLowerCase(), validateTokenUserResponseVO.getUserId());
            mutableHttpServletRequest.putHeader(HeaderParamConstant.USER_NAME.toLowerCase(), validateTokenUserResponseVO.getUsername());
            mutableHttpServletRequest.putHeader(HeaderParamConstant.USER_TYPE.toLowerCase(), validateTokenUserResponseVO.getUserType());
            mutableHttpServletRequest.putHeader(HeaderParamConstant.CURRENT_PROJECT_ID.toLowerCase(), request.getHeader(HeaderParamConstant.CURRENT_PROJECT_ID));
            mutableHttpServletRequest.putHeader(HeaderParamConstant.REGION_ID.toLowerCase(), request.getHeader(HeaderParamConstant.REGION_ID));
        } else {
            validateToken(request);
            if (org.apache.commons.lang3.StringUtils.isNotBlank(request.getRequestURI()) &&
                    !request.getRequestURI().startsWith("/v3")) {
                    ValidateTokenUserResponseVO validateTokenUserResponseVO = jwtService.validateToken(authorization, request);
                    if (Objects.isNull(validateTokenUserResponseVO))  throw new TokenExpiredException("认证无效或已过期");
            }
            Claims claims = jwtTokenUtils.getClaimsFromToken(authorization);
            mutableHttpServletRequest.putHeader(HeaderParamConstant.USER_ID.toLowerCase(), claims.get(HeaderParamConstant.USER_ID, String.class));
            mutableHttpServletRequest.putHeader(HeaderParamConstant.USER_NAME.toLowerCase(), claims.get(HeaderParamConstant.USER_NAME, String.class));
            mutableHttpServletRequest.putHeader(HeaderParamConstant.USER_TYPE.toLowerCase(), claims.get(HeaderParamConstant.USER_TYPE, String.class));
            mutableHttpServletRequest.putHeader(HeaderParamConstant.CURRENT_PROJECT_ID.toLowerCase(), request.getHeader(HeaderParamConstant.CURRENT_PROJECT_ID));
            mutableHttpServletRequest.putHeader(HeaderParamConstant.REGION_ID.toLowerCase(), request.getHeader(HeaderParamConstant.REGION_ID));
        }

        filterChain.doFilter(mutableHttpServletRequest, response);
    }

    private DangerOperationUrl checkValidateCode(HttpServletRequest request) {
        String userId = request.getHeader(HeaderParamConstant.USER_ID);
        if (StrUtil.isBlank(userId)) {
            return null;
        }
        List<DangerOperationUrl> dangerUrls = dangerOperationUrlService.getDangerOperationUrl(userId);
        if (CollectionUtils.isEmpty(dangerUrls)) {
            return null;
        }
        String url = request.getRequestURI();
        String method = request.getMethod();
        log.debug("check validate url is:[{}],method is :[{}]", url, method);
        AntPathMatcher matcher = new AntPathMatcher();
        for (DangerOperationUrl item : dangerUrls) {
            String pattern = item.getUrl();
            if (StrUtil.isBlank(pattern) || !pattern.contains("sugoncloud-iam-api")) {
                continue;
            }
            pattern = pattern.replaceAll("/sugoncloud-iam-api", "");
            if (pattern.contains(":")) {
                pattern = convertRouteTemplate(pattern);
            }
            if (matcher.match(pattern, url) && method.equals(item.getMethod())) {
                return item;
            }
        }
        return null;
    }

    public static String convertRouteTemplate(String routeTemplate) {
        // 正则替换 :param → {param}
        return routeTemplate.replaceAll(":(\\w+)", "{$1}");
    }

    private boolean isSkipRequest(HttpServletRequest request) {
        List<String> uri = jwtSkipUri.getUri();
        if (CollectionUtils.isEmpty(uri)) {
            return false;
        }
        boolean match = uri.stream().anyMatch(e -> request.getRequestURI().matches(e));
        if (match) {
            // 如果是修改用户信息接口，需要token
            if ("/api/users".equals(request.getRequestURI()) && request.getMethod().equals("PUT")) {
                return false;
            }
        }
        return match;
    }

    private void validateToken(HttpServletRequest request) {
        String token = resolveToken(request);
        if (token!=null && token.length()>0) {
            String username = jwtTokenUtils.getUsernameFromToken(token);
            if (username == null) {
                //以V3开头默认是OpenStack接口，返回OpenStack特定异常
                if (request.getRequestURI().startsWith("/v3")) {
                    throw new V3DeniedException("{\n" +
                            "    \"error\": {\n" +
                            "        \"message\": \"The request you have made requires authentication.\",\n" +
                            "        \"code\": 401,\n" +
                            "        \"title\": \"Unauthorized\"\n" +
                            "    }\n" +
                            "}");
                }
                throw new TokenExpiredException("认证无效或已过期");
            }
            if (SecurityContextHolder.getContext().getAuthentication()==null) {
                UserDetails userDetails = userDetails4NativeService.loadUserByUsername(username);
                if (jwtTokenUtils.validateToken(token, userDetails)) {
                    //给使用该JWT令牌的用户进行授权
                    UsernamePasswordAuthenticationToken authenticationToken =
                            new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
                    //设置用户身份授权
                    SecurityContextHolder.getContext().setAuthentication(authenticationToken);
                }
            }
            String cn = null;
            try {
                Cookie[] cookies = request.getCookies();
                if(ArrayUtils.isNotEmpty(cookies)){
                    for (Cookie cookie : cookies) {
                        log.debug("cookie name {}, cookie value {}",
                                URLDecoder.decode(cookie.getName(), StandardCharsets.UTF_8.name()),
                                URLDecoder.decode(cookie.getValue(), StandardCharsets.UTF_8.name()));
                        if(CommonInstance.KOAL_CERT_CN.equals(cookie.getName())){
                            cn = URLDecoder.decode(cookie.getValue(), StandardCharsets.UTF_8.name());
                            break;
                        }
                    }
                }

            }catch (Exception e){
                log.error("analysis cookie error", e);
            }
            log.debug("iam validate cn:" + cn);
            if (org.apache.commons.lang3.StringUtils.isNotBlank(cn)) {
                if (!cn.equals(username))  throw new TokenExpiredException("认证无效或已过期");
            }
        } else {
            //以V3开头默认是OpenStack接口，返回OpenStack特定异常
            if (request.getRequestURI().startsWith("/v3")) {
                throw new V3DeniedException("{\n" +
                        "    \"error\": {\n" +
                        "        \"message\": \"The request you have made requires authentication.\",\n" +
                        "        \"code\": 401,\n" +
                        "        \"title\": \"Unauthorized\"\n" +
                        "    }\n" +
                        "}");
            }
            throw new TokenExpiredException("认证无效或已过期");
        }
    }

    private static String resolveToken(HttpServletRequest request){
        String bearerToken = request.getHeader(CommonInstance.AUTHORIZATION);
        //for openstack
        if (bearerToken == null) bearerToken = request.getHeader("X-Auth-Token");
        if (bearerToken == null) bearerToken = request.getHeader("X-Subject-Token");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return bearerToken;
    }

    public static String resolveTokenPublic(HttpServletRequest request){
        return resolveToken(request);
    }

    @Override
    public int value() {
        return 1;
    }

    @Override
    public Class<? extends Annotation> annotationType() {
        return null;
    }
}
