/**
 *
 */
package com.sugon.cloud.iam.api.utils;

/**
 * 存储License模块使用的常量信息
 *
 * <AUTHOR>
 *
 */
public class LicenseConstants {

	/**
	 * 软件授权的CPU数量，不限
	 */
	public static final String SOFTLICENSE_CPUNUM_NOTLIMIT = "不限";

	/**
	 * 软件授权的特性列表，默认
	 */
	public static final String SOFTLICENSE_FEATURELIST_DEFAULT = "默认";

	/**
	 * 软件授权的默认评估授权的评估日期
	 */
	public static final int DEFAULT_USE_DAYS = 60;

	/**
	 * License的软件匹配版本
	 */
	public static final String DEFALUE_LICENSE_VERSION = "3.0.1";

	/**
	 * 软件默认License的最终用户名称
	 */
	public static final String DEFAULT_ENDUSER_NAME = "评估用户";

	/**
	 * 用于进行License信息加密和解密用的秘钥
	 */
	public static String SECRET_KEY = "SugonCloud";

	/**
	 * 软件默认的授权类型
	 */
	public static String SOFTLICENSE_EVALUATION = "评估授权";

	/**
	 * 默认的日期格式
	 */
	public static String DEFAULT_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

	/**
	 * license软件版本名称
	 */
	public static final String SOFT_LICENSE_VERSION = "V5.0";

}
