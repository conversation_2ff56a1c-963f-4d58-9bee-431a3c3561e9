package com.sugon.cloud.iam.api.entity;

//import net.sf.json.JSONObject;

import java.io.Serializable;

/**
 * Licens实体 20160530
 *
 * <AUTHOR>
 *
 */
public class LicenseInfoVO implements Serializable {

	/**
	 * 主键
	 */
	private String licenseId;

	/**
	 * License的版本，不同License版本的软件能够激活不同版本的软件<br>
	 * 后续使用时会对比软件版本和License版本，如果头量部分相同则允许
	 * 未使用
	 */
	private String licenseVersion;

	/**
	 * 客户名称
	 */
	private String endUserName;

	/**
	 * 系统类型, gbk, utf-8
	 */
	private String systemType;

	/**
	 * 产品版本 =  V5.0
	 */
	private String softLicenseVersion;

	/**
	 * 产品名称 =  Cloudview OVM服务器虚拟化系统
	 */
	private String productName;

	/**
	 * CPU授权数量
	 */
	private String softLicenseCpuNum;

	/**
	 * 软件授权起始时间
	 */
	private String softLicenseStartTime;
	/**
	 * 软件授权过期时间
	 */
	private String softLicenseExpiredTime;
	/**
	 * 项目名称
	 */
	private String softName;

	private int cdbNum;

	private int cpuNum;

	private int cpuUsedNum;

	private int storageNum;

	public int getStorageNum() {
		return storageNum;
	}

	public void setStorageNum(int storageNum) {
		this.storageNum = storageNum;
	}

	public int getCpuUsedNum() {
		return cpuUsedNum;
	}

	public void setCpuUsedNum(int cpuUsedNum) {
		this.cpuUsedNum = cpuUsedNum;
	}

	public int getCpuNum() {
		return cpuNum;
	}

	public void setCpuNum(int cpuNum) {
		this.cpuNum = cpuNum;
	}

	public int getCdbNum() {
		return cdbNum;
	}

	public void setCdbNum(int cdbNum) {
		this.cdbNum = cdbNum;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getSoftName() {
		return softName;
	}

	public void setSoftName(String softName) {
		this.softName = softName;
	}

	public String getSystemType() {
		return systemType;
	}

	public void setSystemType(String systemType) {
		this.systemType = systemType;
	}

	public String getLicenseId() {
		return licenseId;
	}

	public void setLicenseId(String licenseId) {
		this.licenseId = licenseId;
	}

	public String getLicenseVersion() {
		return licenseVersion;
	}

	public void setLicenseVersion(String licenseVersion) {
		this.licenseVersion = licenseVersion;
	}

	public String getEndUserName() {
		return endUserName;
	}

	public void setEndUserName(String endUserName) {
		this.endUserName = endUserName;
	}

	public String getSoftLicenseVersion() {
		return softLicenseVersion;
	}

	public void setSoftLicenseVersion(String softLicenseVersion) {
		this.softLicenseVersion = softLicenseVersion;
	}

	public String getSoftLicenseCpuNum() {
		return softLicenseCpuNum;
	}

	public void setSoftLicenseCpuNum(String softLicenseCpuNum) {
		this.softLicenseCpuNum = softLicenseCpuNum;
	}

	public String getSoftLicenseStartTime() {
		return softLicenseStartTime;
	}

	public void setSoftLicenseStartTime(String softLicenseStartTime) {
		this.softLicenseStartTime = softLicenseStartTime;
	}

	public String getSoftLicenseExpiredTime() {
		return softLicenseExpiredTime;
	}

	public void setSoftLicenseExpiredTime(String softLicenseExpiredTime) {
		this.softLicenseExpiredTime = softLicenseExpiredTime;
	}

	@Override
	public String toString() {
		return "JSONObject.fromObject(this).toString()";
	}
}
