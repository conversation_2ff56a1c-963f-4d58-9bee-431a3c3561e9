package com.sugon.cloud.iam.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("user_role_project_mapping")
public class UserRoleMapping {
    @TableId(type = IdType.UUID)
    private String id;
    private String roleId;
    private String userId;
    private String deptId;
    private String projectId;
}
