package com.sugon.cloud.iam.api.entity.keystone;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@ApiModel(value = "版本信息值")
public class Value {

    private String status = "stable";
    private Date updated = new Date();
    @JsonProperty("media-types")
    private List<Map> mediaTypes;
    private String id = "v3.10";
    private List<Map> links;

    public List<Map> getMediaTypes() {

        return mediaTypes;
    }

    public void setMediaTypes(List<Map> mediaTypes) {
        this.mediaTypes = mediaTypes;
    }

    public List<Map> getLinks() {
        return links;
    }

    public void setLinks(List<Map> links) {
        this.links = links;
    }
}
