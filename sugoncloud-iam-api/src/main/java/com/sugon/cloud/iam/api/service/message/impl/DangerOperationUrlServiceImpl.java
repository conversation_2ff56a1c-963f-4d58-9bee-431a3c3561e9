package com.sugon.cloud.iam.api.service.message.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sugon.cloud.common.constants.HeaderParamConstant;
import com.sugon.cloud.iam.api.common.MessageConstants;
import com.sugon.cloud.iam.api.common.SecretCertificate;
import com.sugon.cloud.iam.api.entity.DangerOperationUrl;
import com.sugon.cloud.iam.api.mapper.DangerOperationUrlMapper;
import com.sugon.cloud.iam.api.service.MFAAuthorizeService;
import com.sugon.cloud.iam.api.service.message.BusinessMessageRelationService;
import com.sugon.cloud.iam.api.service.message.DangerOperationUrlService;
import com.sugon.cloud.iam.api.vo.MFADetailVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Author: yangdingshan
 * @Date: 2025/2/9 14:48
 * @Description:
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class DangerOperationUrlServiceImpl extends ServiceImpl<DangerOperationUrlMapper, DangerOperationUrl> implements DangerOperationUrlService {

    private final HttpServletRequest request;

    private final MFAAuthorizeService mfaAuthorizeService;

    private final BusinessMessageRelationService relationService;

    @Value("${mfa.enabled}")
    private boolean mfaEnabled;

    @Value("${mfa.type}")
    private String mfaType;

    @Override
    public List<DangerOperationUrl> getDangerOperationUrl(String userId) {
        if (!mfaEnabled) {
            return CollUtil.newArrayList();
        }
        if (!SecretCertificate.SMS.equals(mfaType) && !SecretCertificate.EMAIL.equals(mfaType)) {
            return CollUtil.newArrayList();
        }
        // 判断当前用户是否配置mfa
        if (StrUtil.isBlank(userId)) {
            userId = request.getHeader(HeaderParamConstant.USER_ID);
        }
        if (StrUtil.isBlank(userId)) {
            return CollUtil.newArrayList();
        }
        MFADetailVO mfaDetailVO = mfaAuthorizeService.get(userId, false);
        if (!mfaAuthorizeService.checkValidateCode(MessageConstants.BUSINESS_TYPE_DANGER, mfaDetailVO)) {
            return CollUtil.newArrayList();
        }
        // 业务开关
        if (!relationService.isEnableDangerCode()) {
            return CollUtil.newArrayList();
        }
        return this.list(new LambdaQueryWrapper<DangerOperationUrl>().eq(DangerOperationUrl::getEnabled, true));
    }
}
