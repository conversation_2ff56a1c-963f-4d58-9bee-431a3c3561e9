package com.sugon.cloud.iam.api.common;

import com.sugon.cloud.iam.common.model.vo.DepartmentTreeDetailVO;
import org.springframework.util.CollectionUtils;

import java.util.List;

public class RecursionDeptIdUtils {

    public static List<String> getDeptIds(List<DepartmentTreeDetailVO> list, List<String> deptIds) {
        list.forEach(department-> {
            if (CollectionUtils.isEmpty(department.getChildren())) {
                deptIds.add(department.getId());
            } else {
                deptIds.add(department.getId());
                getDeptIds(department.getChildren(), deptIds);
            }
        } );
        return deptIds;
    }



}
