package com.sugon.cloud.iam.api.service;

import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.entity.IamRole;
import com.sugon.cloud.iam.common.model.vo.UserViewVO;

public interface SecurityService {

    /**
     * 查询根账号列表接口
     * @param name
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageCL<UserViewVO> masterUserList(String name, int pageNum, int pageSize, String currentUserId);

    /**
     * 查询内置角色列表接口
     * @param name
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageCL<IamRole> innerRoleList(String name, int pageNum, int pageSize);
}
