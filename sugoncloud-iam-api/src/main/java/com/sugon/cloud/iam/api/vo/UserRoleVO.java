package com.sugon.cloud.iam.api.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;


@Data
@ApiModel("用户与角色")
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class UserRoleVO {
   @NotBlank
   @ApiModelProperty("用户id")
   private String userId;
   @NotBlank
   @ApiModelProperty("角色id")
   private String roleId;
}