package com.sugon.cloud.iam.api.controller;

import com.sugon.cloud.common.annotations.DocumentIgnore;
import com.sugon.cloud.policy.api.controller.CommonPolicyController;
import io.swagger.annotations.Api;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/7/28 14:36
 */
@RestController
@RequestMapping("/api/common/ram/policy")
@Api(tags = "ram策略管理", description = "  ")
@RefreshScope
@DocumentIgnore
public class PolicyController extends CommonPolicyController {
}
