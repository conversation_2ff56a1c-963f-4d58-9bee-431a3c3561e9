package com.sugon.cloud.iam.api.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "全局锁设置")
public class GlobalLockEntity {

    @ApiModelProperty("全局锁名称")
    private String name;

    @JsonProperty("error_minutes")
    @ApiModelProperty("错误分钟")
    private String errorMinutes;
    @JsonProperty("error_times")
    @ApiModelProperty("错误次数")
    private String errorTimes;
    @JsonProperty("lock_minutes")
    @ApiModelProperty("锁定分钟")
    private String lockMinutes;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getErrorMinutes() {
        return errorMinutes;
    }

    public void setErrorMinutes(String errorMinutes) {
        this.errorMinutes = errorMinutes;
    }

    public String getErrorTimes() {
        return errorTimes;
    }

    public void setErrorTimes(String errorTimes) {
        this.errorTimes = errorTimes;
    }

    public String getLockMinutes() {
        return lockMinutes;
    }

    public void setLockMinutes(String lockMinutes) {
        this.lockMinutes = lockMinutes;
    }

    public GlobalLockEntity(String name, String errorMinutes, String errorTimes, String lockMinutes) {
        this.name = name;
        this.errorMinutes = errorMinutes;
        this.errorTimes = errorTimes;
        this.lockMinutes = lockMinutes;
    }

    public GlobalLockEntity() {
    }
}
