package com.sugon.cloud.iam.api.service;

import com.sugon.cloud.common.page.PageCL;
import com.sugon.cloud.iam.api.entity.MicroServiceCategory;
import com.sugon.cloud.iam.api.vo.CreateOrUpdateMicroServiceCategoryVO;
import com.sugon.cloud.iam.api.vo.MicroServiceCategoryDetailVO;

import java.util.List;

/**
 * (MicroServiceCategory)表服务接口
 *
 * <AUTHOR>
 * @since 2021-04-23 09:45:36
 */
public interface MicroServiceCategoryService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    MicroServiceCategory queryById(String id);

    /**
     * 查询多条数据
     * @param name 查询名称
     * @param pageNum 查询起始位置
     * @param pageSize 查询条数
     * @return 对象列表
     */
    PageCL<MicroServiceCategoryDetailVO> pageList(String name, int pageNum, int pageSize, boolean  internetAccess, boolean checkChild);

    /**
     * 获取已授权的licenseKeys
     * @return 已授权的licenseKeys
     */
//    List<String> getAuthorizedLicenseKeys();

    PageCL<MicroServiceCategoryDetailVO> pageListAll(String name, int pageNum, int pageSize, boolean  internetAccess);

    List<MicroServiceCategoryDetailVO> ListAllCategoryByQuota();

    /**
     * 新增数据
     *
     * @param createOrUpdateMicroServiceCategoryVO 实例对象
     * @return 实例对象
     */
    MicroServiceCategory insert(CreateOrUpdateMicroServiceCategoryVO createOrUpdateMicroServiceCategoryVO);

    /**
     * 修改数据
     *
     * @param microServiceCategoryDetailVO 实例对象
     * @return 实例对象
     */
    MicroServiceCategory update(MicroServiceCategoryDetailVO microServiceCategoryDetailVO);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    void deleteById(String id);

}
