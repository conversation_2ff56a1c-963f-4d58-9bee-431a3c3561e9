package com.sugon.cloud.iam.api.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sugon.cloud.iam.api.entity.UserRoleMapping;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserRoleMappingMapper extends BaseMapper<UserRoleMapping> {

    @Select("SELECT a.id AS userId, b.role_id AS roleId FROM user a  LEFT JOIN user_role_project_mapping b ON a.id = b.user_id WHERE (a.type IS NOT  NULL OR a.dept_id IS NOT NULL) AND a.enabled IS TRUE")
    List<UserRoleMapping> leftJoinUser();
}
