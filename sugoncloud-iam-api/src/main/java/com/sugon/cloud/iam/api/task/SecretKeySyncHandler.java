package com.sugon.cloud.iam.api.task;

import com.alibaba.fastjson.JSONObject;
import com.sugon.cloud.iam.api.common.CommonInstance;
import com.sugon.cloud.iam.api.entity.SecretKeyEntity;
import com.sugon.cloud.iam.api.service.SecretKeyService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;


@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
@RefreshScope
public class SecretKeySyncHandler {

    @Resource(name = "defaultRedisTemplate")
    private final RedisTemplate<String, Object> redisTemplate;

    private final SecretKeyService secretKeyService;

    @XxlJob(CommonInstance.SECRET_KEY_SYNC_HANDLER)
    public void run() {
        log.info("::::::::::::::ak-sk_sync start");
        final List<SecretKeyEntity> list = secretKeyService.list();
        list.forEach(secretKeyEntity -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("accessKey",secretKeyEntity.getAccessKey());
            jsonObject.put("status",secretKeyEntity.getEnabled()?1:0);
            jsonObject.put("secretKey",secretKeyEntity.getSecretKey());
            jsonObject.put("userId",secretKeyEntity.getUserId());
            redisTemplate.opsForValue().set(CommonInstance.IAM_USERS_REDIS_KEY+secretKeyEntity.getAccessKey(), jsonObject.toString());
        });
        log.info("::::::::::::::ak-sk_sync end");
    }
}
