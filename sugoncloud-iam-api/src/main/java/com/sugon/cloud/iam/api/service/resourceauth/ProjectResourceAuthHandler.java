package com.sugon.cloud.iam.api.service.resourceauth;

import com.beust.jcommander.internal.Lists;
import com.sugon.cloud.common.constants.TypeUtil;
import com.sugon.cloud.iam.api.entity.keystone.User;
import com.sugon.cloud.iam.api.mapper.UserMapper;
import com.sugon.cloud.iam.api.service.ProjectService;
import com.sugon.cloud.resource.auth.common.service.impl.BaseResourceAuthHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: yangdingshan
 * @Date: 2024/1/10 11:40
 * @Description:
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class ProjectResourceAuthHandler extends BaseResourceAuthHandler {

    private final ProjectService projectService;

    private final UserMapper userMapper;

    @Override
    public List<String> getUserResource(String userId) {
        User user = userMapper.selectById(userId);
        // 安全员权限直接放开
        if (TypeUtil.TYPE_SECURITY.equals(user.getType()) || TypeUtil.TYPE_SEC_ADMIN.equals(user.getType())) {
            return Lists.newArrayList(TypeUtil.TYPE_ADMIN);
        }
        return projectService.getProjectIdsByUserId(userId);
    }
}
