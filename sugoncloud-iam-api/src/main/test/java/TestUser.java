import com.sugon.cloud.iam.api.SugonCloudIamApiApplication;
import com.sugon.cloud.iam.api.entity.Department;
import com.sugon.cloud.iam.api.mapper.DepartmentMapper;
import com.sugon.cloud.iam.api.service.DepartmentService;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = SugonCloudIamApiApplication.class)
public class TestUser {

    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private DepartmentMapper departmentMapper;

   /* @Test
    public void updateUserName() {
        List<String> deptIds = com.google.common.collect.Lists.newArrayList();
        Department department = departmentMapper.selectById("1");
        deptIds.add(department.getId());
        DepartmentTreeDetailVO treeDepartmentByUserId = departmentService.findTreeDepartmentByUserId("0d2bbb018e8b44b985a169647379f413");
        deptIds = getDeptIds(treeDepartmentByUserId.getChildren(), deptIds);
        System.out.println(deptIds.size());
    }

    private List<String> getDeptIds(List<DepartmentTreeDetailVO> list, List<String> deptIds) {
        list.stream().forEach(department-> {
            if (CollectionUtils.isEmpty(department.getChildren())) {
                deptIds.add(department.getId());
            } else {
                deptIds.add(department.getId());
                getDeptIds(department.getChildren(), deptIds);
            }
        } );
        return deptIds;
    }*/
}
