<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sugon.cloud.iam.api.mapper.EndpointMapper">
    <resultMap id="resultMap" type="com.sugon.cloud.iam.api.entity.keystone.Endpoint" >
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="interface" property="interfaceParam" jdbcType="VARCHAR"/>
        <result column="region_id" property="region" jdbcType="VARCHAR"/>
        <result column="url" property="url" jdbcType="VARCHAR"/>
        <result column="region_id" property="regionId" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="findByServiceId" resultMap="resultMap" >
        select id, interface, region_id as region, url, region_id from endpoint where service_id = #{id}
    </select>

    <select id="findByServiceNameAndRegionId" resultMap="resultMap" >
        select endpoint.id, endpoint.interface, endpoint.region_id as region,
        endpoint.url, endpoint.region_id,endpoint.service_id from endpoint inner join
        service on service.id = endpoint.service_id
        where service.name = #{serviceName} and endpoint.region_id=#{regionId} and endpoint.enabled = true
        and endpoint.interface = "public"
    </select>
</mapper>
