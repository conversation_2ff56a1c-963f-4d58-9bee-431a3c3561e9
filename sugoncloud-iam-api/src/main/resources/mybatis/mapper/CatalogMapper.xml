<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sugon.cloud.iam.api.mapper.CatalogMapper">
    <resultMap id="resultMap" type="com.sugon.cloud.iam.api.entity.keystone.Catalog" >
        <!--service id qu bie ming sid-->
        <result column="sid" property="id" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <collection property="endpoints"
                    column="id"
                    ofType="com.sugon.cloud.iam.api.entity.keystone.Endpoint"
                    javaType="java.util.List">
            <result column="id" property="id" jdbcType="VARCHAR"/>
            <result column="interface" property="interfaceParam" jdbcType="VARCHAR"/>
            <result column="region_id" property="region" jdbcType="VARCHAR"/>
            <result column="url" property="url" jdbcType="VARCHAR"/>
            <result column="region_id" property="regionId" jdbcType="VARCHAR"/>
            <result column="service_id" property="serviceId" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <select id="getAll" resultMap="resultMap">
        SELECT
            service.id as sid,
            service.name,
            service.type,
            endpoint.id ,
            endpoint.interface ,
            endpoint.region_id ,
            endpoint.url ,
            endpoint.region_id ,
            endpoint.service_id
        FROM
            service
        LEFT JOIN endpoint ON service.id = endpoint.service_id
    </select>
</mapper>
