<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sugon.cloud.iam.api.mapper.TrustMapper">
    <resultMap id="resultMap" type="com.sugon.cloud.iam.api.entity.keystone.Trust" >
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="trustor_user_id" property="trustorUserId" jdbcType="VARCHAR"/>
        <result column="trustee_user_id" property="trusteeUserId" jdbcType="VARCHAR"/>
        <result column="project_id" property="projectId" jdbcType="VARCHAR"/>
        <result column="impersonation" property="impersonation" jdbcType="TINYINT"/>
        <result column="extra" property="extra" jdbcType="VARCHAR"/>
        <result column="expires_at" property="expiresAt" jdbcType="TIMESTAMP"/>
    </resultMap>
</mapper>
