<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sugon.cloud.iam.api.mapper.RegionMapper">
    <resultMap id="resultMap" type="com.sugon.cloud.iam.api.entity.Region" >
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="parent_region_id" property="parentRegionId" jdbcType="VARCHAR"/>
        <result column="extra" property="extra" jdbcType="VARCHAR"/>
    </resultMap>
</mapper>
