<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sugon.cloud.iam.api.mapper.SeedsMapper">

    <resultMap type="com.sugon.cloud.iam.api.entity.SeedEntity" id="SeedsMap">
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="privateKey" column="private_key" jdbcType="VARCHAR"/>
        <result property="seedLen" column="seed_len" jdbcType="INTEGER"/>
        <result property="seed" column="seed" jdbcType="VARCHAR"/>
        <result property="codeLen" column="code_len" jdbcType="INTEGER"/>
        <result property="codeTime" column="code_time" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="code" useGeneratedKeys="true">
        insert into iam.seeds(private_keyseed_lenseedcode_lencode_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.privateKey}#{entity.seedLen}#{entity.seed}#{entity.codeLen}#{entity.codeTime})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="code" useGeneratedKeys="true">
        insert into iam.seeds(private_keyseed_lenseedcode_lencode_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.privateKey}#{entity.seedLen}#{entity.seed}#{entity.codeLen}#{entity.codeTime})
        </foreach>
        on duplicate key update
        private_key = values(private_key) seed_len = values(seed_len) seed = values(seed) code_len = values(code_len) code_time = values(code_time)     </insert>

</mapper>

