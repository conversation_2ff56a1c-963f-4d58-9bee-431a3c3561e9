<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sugon.cloud.iam.api.mapper.LocalUserMapper">

    <resultMap id="CloudViewGatewayUserResultMap" type="com.sugon.cloud.iam.api.entity.CloudViewGatewayUser">
        <!--@Table gateway.ram_user-->
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="name" column="user_name" jdbcType="VARCHAR"/>
        <result property="ownerId" column="owner_id" jdbcType="VARCHAR"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
        <result property="password" column="password" jdbcType="VARCHAR"/>
        <result property="salt" column="salt" jdbcType="VARCHAR"/>
        <result property="createAt" column="create_at" jdbcType="DATE"/>
    </resultMap>
    <update id="updateIamProjectIdById">
        update iam.project set id = #{keystoneAdminProjectId} where id = #{iamAdminProjectId}
    </update>

    <!--查询单个-->
    <select id="queryGatewayUsers" resultMap="CloudViewGatewayUserResultMap">
        select id,user_name, email, mobile, password, salt,owner_id,create_at from gateway.ram_user
    </select>
    <select id="queryKeystoneUsers" resultType="com.sugon.cloud.iam.api.entity.OriginKeystoneUser">
        SELECT
            t1.id,
            t1.extra,
            t1.created_at createAt,
            t1.default_project_id defaultProjectId,
            t2.`name`
        FROM
            keystone.`user` t1,
            keystone.local_user t2
        WHERE
            t1.id = t2.user_id
        ORDER BY
            created_at DESC
    </select>

    <select id="getDefaultProjectByUserName" resultType="com.sugon.cloud.iam.api.entity.keystone.Project">
        SELECT t4.id,
               t4.`name`,
               t4.extra,
               t4.description
        FROM gateway.`ram_user` t1,
             keystone.local_user t2,
             keystone.`user` t3,
             keystone.project t4
        WHERE t1.user_name = t2.`name`
          AND t2.user_id = t3.id
          AND t3.default_project_id = t4.id
          AND t1.user_name = #{userName}
    </select>
    <select id="getKeystoneAdminProjectId" resultType="java.lang.String">
        SELECT id
        FROM keystone.project
        WHERE `name` = 'admin'
    </select>

    <select id="getEcsUsedQuotaByPurpose" resultType="com.sugon.cloud.iam.api.entity.EcsUsedQuota">
        SELECT IFNULL(SUM(VCPUS), 0) AS cpuUsedTotal,
               IFNULL(SUM(RAMS), 0)  AS ramUsedTotal
        FROM sugoncloud_ecs.VIEW_NOVA_INSTANCE t1,
             sugoncloud_ecs.VIEW_PURPOSE t2
        WHERE t1.TYPE_ID = t2.ID
          AND t2.`VALUE` = #{purpose}
          AND t1.TENANT_ID = #{projectId}
    </select>
    <select id="getTopDeptId" resultType="java.lang.String">
        SELECT dept_id FROM `user` WHERE BINARY `name` = #{masterUserName} LIMIT 1
    </select>
    <select id="getEvsVolumeUsedQuota" resultType="com.sugon.cloud.iam.api.entity.EvsUsedQuota">
        SELECT display_name displayName,size usedTotal FROM sugoncloud_evs.VIEW_CINDER_VOLUME
        where project_id = #{projectId}
    </select>
    <select id="getEvsVolumeSnapshotsUsedQuota" resultType="java.lang.Long">
        SELECT IFNULL(sum(volume_size),0) FROM sugoncloud_evs.VIEW_VOLUME_SNAPSHOTS
        where project_id = #{projectId}
    </select>

</mapper>
