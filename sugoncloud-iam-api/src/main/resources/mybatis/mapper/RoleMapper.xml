<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sugon.cloud.iam.api.mapper.RoleMapper">
    <resultMap id="resultMap" type="com.sugon.cloud.iam.api.entity.keystone.Role" >
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
    </resultMap>


    <select id="findByUserIdAndProjectId" resultMap="resultMap">
        SELECT
            id,
            NAME
        FROM
            role
            INNER JOIN assignment ON role.id = assignment.role_id
         WHERE
	        assignment.actor_id = #{userId} and assignment.target_id=#{projectId}
    </select>

    <select id="findByUserId" resultMap="resultMap">
        SELECT
            distinct(id),
            NAME
        FROM
            role
            LEFT JOIN assignment ON role.id = assignment.role_id
         WHERE
	        assignment.actor_id = #{userId}
    </select>
</mapper>
