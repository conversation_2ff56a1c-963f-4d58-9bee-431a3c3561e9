<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sugon.cloud.iam.api.mapper.QuotaMetricMapper">

    <resultMap id="BaseResultMap" type="com.sugon.cloud.iam.common.model.vo.QuotaMetric">
        <result property="departmentId" column="department_id" jdbcType="VARCHAR"/>
        <result property="departmentName" column="department_name" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="type_name" column="typeName" jdbcType="VARCHAR"/>
        <result property="total_name" column="totalName" jdbcType="VARCHAR"/>
        <result property="used_name" column="usedName" jdbcType="VARCHAR"/>
        <result property="total_value" column="totalValue" jdbcType="BIGINT"/>
        <result property="used_value" column="usedValue" jdbcType="BIGINT"/>
        <result property="region_id" column="regionId" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="RootUserQuotaMap" type="com.sugon.cloud.iam.common.model.vo.QuotaRootUserEntity">
        <result property="departmentId" column="department_id" jdbcType="VARCHAR"/>
        <result property="departmentName" column="department_name" jdbcType="VARCHAR"/>
        <result property="typeName" column="type_name" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="userPhone" column="user_phone" jdbcType="VARCHAR"/>
        <result property="userEmail" column="user_email" jdbcType="VARCHAR"/>
        <collection column="{departmentId=department_id,typeName=type_name,regionId=region_id}" property="quotaMetrics"
                    ofType="com.sugon.cloud.iam.common.model.vo.QuotaMetric"
                    javaType="java.util.List"
                    select="com.sugon.cloud.iam.api.mapper.QuotaMetricMapper.getDepartmentQuotaByType"/>
    </resultMap>
    <resultMap id="toployProjectResultMap" type="com.sugon.cloud.iam.common.model.vo.QuotaMetricTopology">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
        <result property="domainId" column="domain_id" jdbcType="VARCHAR"/>
        <result property="parentId" column="parent_id" jdbcType="VARCHAR"/>
        <result property="level" column="level" jdbcType="INTEGER"/>
        <result property="project_id" column="projectId" jdbcType="VARCHAR"/>
        <collection column="{projectId=id,typeName=type_name}" property="quotaMetrics"
                    ofType="com.sugon.cloud.iam.common.model.vo.QuotaMetric"
                    javaType="java.util.List"
                    select="getProjectQuotaByType"/>
    </resultMap>
    <resultMap id="toployDepartment" type="com.sugon.cloud.iam.common.model.vo.QuotaMetricTopology">
        <result property="departmentId" column="department_id" jdbcType="VARCHAR"/>
        <result property="departmentName" column="department_name" jdbcType="VARCHAR"/>
        <result property="typeName" column="type_name" jdbcType="VARCHAR"/>
        <collection column="{departmentId=id,typeName=type_name,regionId=region_id}" property="quotaMetrics"
                    ofType="com.sugon.cloud.iam.common.model.vo.QuotaMetric"
                    javaType="java.util.List"
                    select="com.sugon.cloud.iam.api.mapper.QuotaMetricMapper.getDepartmentQuotaByType"/>
        <collection property="children"
                    column="{parentId=id,typeName=type_name,regionId=region_id}"
                    ofType="com.sugon.cloud.iam.common.model.vo.DepartmentTreeDetailVO"
                    select="findTreeDepartment"/>
        <collection property="projects"
                    column="{deptId=id,typeName=type_name}"
                    ofType="com.sugon.cloud.iam.common.model.vo.DepartmentTreeDetailVO"
                    select="findTreeByProjectId"/>
    </resultMap>
    
    <select id="getQuotaMetricList" resultMap="BaseResultMap" parameterType="java.util.Map">
        select * from quota_metric
        <where>
            <if test="typeName != null and typeName != ''">
                and type_name = #{typeName, jdbcType=VARCHAR}
            </if>
            <if test="metricNames  != null and metricNames.size() >0 ">
                and name IN
                <foreach collection="metricNames" item="metricName" open="(" separator="," close=")">
                    #{metricName, jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getRootUserQuota" resultMap="RootUserQuotaMap" parameterType="java.util.Map">
        SELECT
             dept.id as department_id,
             dept.`name` as department_name,
             dept.`description` as description,
             case when #{region_id} is NULL then NULL else #{region_id} end as region_id,
             u.alias as user_name,
             u.phone as user_phone,
             u.email as user_email,
             #{typeName, jdbcType=VARCHAR} as type_name
        FROM department dept LEFT JOIN `user` u ON u.dept_id=dept.id
        WHERE dept.`level` = 0
        <if test="departmentName  != null and departmentName != ''" >
            AND dept.`name` LIKE concat('%',#{departmentName, jdbcType=VARCHAR},'%')
        </if>
            and dept.id != 'admin'
            and u.type = 'master'
    </select>

    <select id="getDepartmentQuotaByParam" resultMap="BaseResultMap" parameterType="java.util.Map">
        SELECT
            qm.*,
            sum(ifnull(qdv.total_value,0)) as total_value,
            sum(ifnull(qdv.used_value,0)) as used_value
        FROM
            quota_metric qm
        LEFT JOIN quota_department_value qdv ON qm.name = qdv.metric_name
        AND qdv.region_id = #{regionId,jdbcType=VARCHAR}
        <if test="departmentId != null and departmentId != ''" >
            AND qdv.department_id = #{departmentId, jdbcType=VARCHAR}
        </if>
        <if test="departmentId == null or departmentId == ''" >
            and qdv.department_id != 'admin'
        </if>
        <where>
            <if test="metricName != null and metricName != ''" >
                AND qm.`name` = #{metricName, jdbcType=VARCHAR}
            </if>
        </where>
        GROUP BY qm.`name`
    </select>

    <select id="getDepartmentQuotaByParamDisk" resultMap="BaseResultMap" parameterType="java.util.Map">
        SELECT
         *
        FROM
        quota_department_value qdv
        WHERE qdv.department_id = #{departmentId, jdbcType=VARCHAR} AND qdv.region_id = #{regionId,jdbcType=VARCHAR}
        AND qdv.`metric_name` = #{metricName, jdbcType=VARCHAR}
    </select>

    <select id="getProjectQuotaByParamDisk" resultMap="BaseResultMap" parameterType="java.util.Map">
        SELECT
            *
        FROM
            quota_project_value qpv
        <where>
            1=1
            <if test="projectId  != null and projectId  != ''" >
                and qpv.`project_id` = #{projectId, jdbcType=VARCHAR}
            </if>
        </where>
        AND qpv.region_id = #{regionId,jdbcType=VARCHAR}
        AND qpv.`metric_name` = #{metricName, jdbcType=VARCHAR}
        <if test="projectId == null or projectId == ''" >
            and qpv.project_id != 'admin-inner-project'
        </if>
    </select>

    <select id="getDepartmentQuotaByType" resultMap="BaseResultMap" parameterType="java.util.Map">
        SELECT
            #{departmentId, jdbcType=VARCHAR} as department_id,
            qm.*,
            ifnull(qdv.total_value,0) as total_value,
            ifnull(qdv.used_value,0) as used_value,
            qdv.region_id as region_id
        FROM
            quota_metric qm
        LEFT JOIN quota_department_value qdv ON qm.name = qdv.metric_name
        AND qdv.department_id = #{departmentId, jdbcType=VARCHAR}
        AND qdv.region_id = #{regionId,jdbcType=VARCHAR}
        <where>
            qm.type_name = #{typeName, jdbcType=VARCHAR}
        </where>
    </select>
    <select id="getProjectQuotaByType" resultMap="BaseResultMap" parameterType="java.util.Map">
        SELECT
        #{projectId, jdbcType=VARCHAR} as project_id,
        qm.*,
        ifnull(qpv.total_value,0) as total_value,
        ifnull(qpv.used_value,0) as used_value
        FROM
        quota_metric qm
        LEFT JOIN quota_project_value qpv ON qm.name = qpv.metric_name
        AND qpv.project_id = #{projectId, jdbcType=VARCHAR} where qm.type_name = #{typeName, jdbcType=VARCHAR}
    </select>
    <select id="getDepartmentQuotaValue" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
            qdv.metric_name as `name`,
            qdv.department_id,
            qdv.total_value,
            qdv.used_value,
            qdv.region_id
        FROM
        quota_department_value qdv
        where qdv.department_id = #{departmentId, jdbcType=VARCHAR}
            and qdv.`metric_name` = #{metricName, jdbcType=VARCHAR}
            and qdv.region_id = #{regionId,jdbcType=VARCHAR}
    </select>

    <select id="getProjectRootGbQuotaByParam" resultType="java.util.Map" parameterType="java.util.Map">
        select gi.project_id as `projectId`, sum(gi.root_gb) as `sum` from gova.instances gi where gi.purpose='kvm' and gi.deleted_at is null GROUP BY gi.project_id
    </select>

    <select id="getProjectQuotaByParam" resultMap="BaseResultMap" parameterType="java.util.Map">
        SELECT
        qm.*,
        sum(ifnull(qpv.total_value,0)) as total_value,
        sum(ifnull(qpv.used_value,0)) as used_value
        FROM
        quota_metric qm
        LEFT JOIN quota_project_value qpv ON qm.name = qpv.metric_name
        AND qpv.region_id = #{regionId,jdbcType=VARCHAR}
        <if test="projectId != null and projectId != ''" >
            and qpv.project_id = #{projectId, jdbcType=VARCHAR}
        </if>
        <if test="projectId == null or projectId == ''" >
            and qpv.project_id != 'admin-inner-project'
        </if>
        <where>
            <if test="metricName  != null and metricName  != ''" >
                and qm.`name` = #{metricName, jdbcType=VARCHAR}
            </if>
        </where>
        GROUP BY qm.`name`
    </select>

    <select id="getProjectQuotaValue" resultMap="BaseResultMap" parameterType="java.util.Map">
        SELECT
            qpv.metric_name as `name`,
            qpv.project_id,
            qpv.total_value,
            qpv.used_value
        FROM
        quota_project_value qpv
        <where>
            <if test="projectId  != null and projectId  != ''" >
               and qpv.project_id = #{projectId, jdbcType=VARCHAR}
            </if>
            <if test="projectId == null or projectId == ''" >
                and qpv.project_id != 'admin-inner-project'
            </if>
            <if test="metricName  != null and metricName  != ''" >
                and qpv.`metric_name` = #{metricName, jdbcType=VARCHAR}
            </if>
            <if test="regionId  != null and regionId  != ''" >
                and qpv.`region_id` = #{regionId, jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <select id="getQuotaOverview" resultMap="BaseResultMap" parameterType="java.lang.String">
       SELECT qm.`type_name`,
              qdv.metric_name as `name`,
              qm.total_name,
              sum(qdv.total_value) as total_value ,
              qm.used_name,
              sum(qdv.used_value) as used_value
       FROM quota_department_value qdv
       LEFT JOIN department dept ON dept.id = qdv.department_id
       LEFT JOIN quota_metric qm ON qm.`name` = qdv.metric_name
       WHERE dept.`level` = 0
         and qdv.region_id = #{regionId,jdbcType=VARCHAR}
         and dept.id != 'admin'
       GROUP BY metric_name
    </select>

    <insert id="insertDepartmentQuota" parameterType="com.sugon.cloud.iam.common.model.vo.QuotaMetric">
        insert into quota_department_value(metric_name,department_id,total_value,used_value,region_id)
        values(
                  #{name,jdbcType=VARCHAR},
                  #{departmentId,jdbcType=VARCHAR},
                  #{totalValue,jdbcType=BIGINT},
                  #{usedValue,jdbcType=BIGINT},
                  #{regionId,jdbcType=VARCHAR}
              )
    </insert>
    <insert id="insertProjectQuota" parameterType="com.sugon.cloud.iam.common.model.vo.QuotaMetric">
        insert into quota_project_value(metric_name,project_id,total_value,used_value,region_id)
        values(
                  #{name,jdbcType=VARCHAR},
                  #{projectId,jdbcType=VARCHAR},
                  #{totalValue,jdbcType=BIGINT},
                  #{usedValue,jdbcType=BIGINT},
                  #{regionId,jdbcType=VARCHAR}
              )
    </insert>

    <insert id="insertBatchProjectQuota">
        insert into quota_project_value(metric_name,project_id,total_value,used_value,region_id)
        values
        <foreach collection="quotaMetrics" item="quotaMetric" separator=",">
            (
                #{quotaMetric.name,jdbcType=VARCHAR},
                #{quotaMetric.projectId,jdbcType=VARCHAR},
                #{quotaMetric.totalValue,jdbcType=BIGINT},
                #{quotaMetric.usedValue,jdbcType=BIGINT},
                #{quotaMetric.regionId,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <update id="updateDepartmentQuota" parameterType="com.sugon.cloud.iam.common.model.vo.QuotaMetric">
        update quota_department_value
        <set>
            <if test="totalValue != null">
                `total_value` = #{totalValue,jdbcType=BIGINT},
            </if>
        </set>
        where metric_name = #{name,jdbcType=VARCHAR} and department_id = #{departmentId,jdbcType=VARCHAR} and region_id = #{regionId,jdbcType=VARCHAR}
    </update>
    <update id="updateDepartmentQuotaAndUsed" parameterType="com.sugon.cloud.iam.common.model.vo.QuotaMetric">
        update quota_department_value
        <set>
            <if test="totalValue != null">
                `total_value` = #{totalValue,jdbcType=BIGINT},
            </if>
            <if test="usedValue != null">
                `used_value` = #{usedValue,jdbcType=BIGINT},
            </if>
        </set>
        where metric_name = #{name,jdbcType=VARCHAR} and department_id = #{departmentId,jdbcType=VARCHAR} and region_id = #{regionId,jdbcType=VARCHAR}
    </update>
    <update id="updateProjectQuota" parameterType="com.sugon.cloud.iam.common.model.vo.QuotaMetric">
        update quota_project_value
        <set>
            <if test="totalValue != null">
                `total_value` = #{totalValue,jdbcType=BIGINT},
            </if>
        </set>
        where metric_name = #{name,jdbcType=VARCHAR} and project_id = #{projectId,jdbcType=VARCHAR} AND region_id = #{regionId,jdbcType=VARCHAR}
    </update>
    <update id="updateDepartmentUsed" parameterType="com.sugon.cloud.iam.common.model.vo.QuotaMetric">
        update quota_department_value
        <set>
            <if test="usedValue != null">
                `used_value` = #{usedValue,jdbcType=BIGINT},
            </if>
        </set>
        where metric_name = #{name,jdbcType=VARCHAR} and department_id = #{departmentId,jdbcType=VARCHAR} and region_id = #{regionId,jdbcType=VARCHAR}
    </update>

    <update id="updateProjectUsed" parameterType="com.sugon.cloud.iam.common.model.vo.QuotaProjectValue">
        update quota_project_value
        <set>
            <if test="usedValue != null">
                `used_value` = #{usedValue,jdbcType=BIGINT},
            </if>
        </set>
        where metric_name = #{name,jdbcType=VARCHAR} and project_id = #{projectId,jdbcType=VARCHAR} and region_id = #{regionId,jdbcType=VARCHAR}
    </update>

    <delete id="deleteProjectQuota" >
       delete from quota_project_value where project_id = #{projectId,jdbcType=VARCHAR} AND region_id = #{regionId,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteProjectQuotaByProjectsAndMetric" >
        delete from quota_project_value where
            region_id = #{regionId,jdbcType=VARCHAR}
            AND metric_name = #{metric,jdbcType=VARCHAR}
        <if test="projectIds !=null and projectIds.size()>0">
            AND project_id  in
            <foreach collection="projectIds" item="projectId" separator="," open="(" close=")">
                #{projectId}
            </foreach>
        </if>
    </delete>

    <delete id="deleteDepartmentQuota" >
       delete from quota_department_value where department_id = #{departmentId,jdbcType=VARCHAR} and region_id = #{regionId,jdbcType=VARCHAR}
    </delete>


    <select id="getTopology" resultMap="toployDepartment"  parameterType="java.util.Map">
        SELECT
        #{typeName, jdbcType=VARCHAR} as type_name,dept.*,
        case when #{regionId} is NULL then NULL else #{regionId} end as region_id
        FROM department dept
        WHERE dept.`level` = 0
        <if test="departmentId  != null and departmentId != ''" >
            AND  id = #{departmentId,jdbcType=VARCHAR}
        </if>
        <if test="departmentId  == null or departmentId == ''" >
            AND dept.id != 'admin'
        </if>

    </select>
    <select id="findTreeDepartment" resultMap="toployDepartment">
        select
            #{typeName, jdbcType=VARCHAR} as type_name,
            d.*,
            case when #{regionId} is NULL then NULL else #{regionId} end as region_id
        from department d
        where d.parent_id = #{parentId}
    </select>

    <select id="findTreeByProjectId" resultMap="toployProjectResultMap">
        select id, alias as name, #{typeName, jdbcType=VARCHAR} as type_name from project
        where parent_id is not null and enabled = true
        <if test="deptId  != null and deptId  != ''" >
            and dept_id = #{deptId, jdbcType=VARCHAR}
        </if>

    </select>

    <select id="getQuotaProjectsByProjectIds" resultMap="BaseResultMap" parameterType="java.util.Map">
        SELECT
        qpv.metric_name as `name`,
        qpv.project_id,
        qpv.total_value,
        qpv.used_value
        FROM
        quota_project_value qpv
        <where>
            <if test="projectIds  != null and projectIds.size() >0 " >
                and qpv.project_id IN
                <foreach collection="projectIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="metricName  != null and metricName  != ''" >
                and qpv.`metric_name` = #{metricName, jdbcType=VARCHAR}
            </if>
            <if test="regionId  != null and regionId  != ''" >
                and qpv.`region_id` = #{regionId, jdbcType=VARCHAR}
            </if>
            <if test="metricNames  != null and metricNames.size() >0 ">
                and qpv.`metric_name` IN
                <foreach collection="metricNames" item="metric" open="(" separator="," close=")">
                    #{metric, jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
