<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sugon.cloud.iam.api.mapper.BlacklistMicroServiceMapper">

    <resultMap id="BaseResultMap" type="com.sugon.cloud.iam.api.entity.BlacklistMicroService">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="VARCHAR" typeHandler="org.apache.ibatis.type.EnumTypeHandler"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,type
    </sql>
</mapper>
