<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sugon.cloud.iam.api.mapper.UserMapper">
    <resultMap id="resultMap" type="com.sugon.cloud.iam.api.entity.keystone.User" >
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="password_expires_at" property="passwordExpiresAt" jdbcType="TIMESTAMP"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="default_project_id" property="defaultProjectId" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <association property="project"
                     column="default_project_id"
                     javaType="com.sugon.cloud.iam.api.entity.keystone.Project"
                    select="com.sugon.cloud.iam.api.mapper.ProjectMapper.findById">
        </association>
    </resultMap>
    <select id="getProjectIdByUserName" resultType="String">
        select default_project_id from user where name = #{name }
    </select>

    <select id="getByName" resultMap="resultMap">
        select id, password_expires_at, name, default_project_id, password   from user where name = #{name}
    </select>

    <select id="getUsersByProjectId" parameterType="java.lang.String" resultType="com.sugon.cloud.iam.api.entity.keystone.User">
        select DISTINCT A.* from user A LEFT JOIN user_role_project_mapping B ON A.id=B.user_id
        WHERE project_id=#{projectId}
    </select>

    <insert id="insertUserDefaultResurceProject" parameterType="java.util.Map">
        insert into user_default_resource_project(user_id, project_id)
        values(
                  #{userId,jdbcType=VARCHAR},
                  #{projectId,jdbcType=VARCHAR}
              )
    </insert>
    <delete id="deleteUserDefaultResurceProject" parameterType="java.util.Map">
       delete from user_default_resource_project where user_id=#{userId,jdbcType=VARCHAR}
    </delete>
    <select id="getUserDefaultProjectId" resultType="String">
        select project_id from user_default_resource_project where user_id=#{userId,jdbcType=VARCHAR}
    </select>

</mapper>
