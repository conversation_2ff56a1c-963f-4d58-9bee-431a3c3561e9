<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sugon.cloud.iam.api.mapper.ProjectMapper">
    <resultMap id="resultMap" type="com.sugon.cloud.iam.api.entity.keystone.Project" >
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="domain_id" property="domainId" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="findById" resultMap="resultMap">
        select id, name, domain_id from project where id = #{id}
    </select>

    <select id="findByUserIdAndRoleId" resultMap="resultMap">
        select id, name, domain_id from project
        INNER JOIN assignment ON project.id = assignment.target_id
         WHERE
	        assignment.actor_id = #{userId} and assignment.role_id=#{roleId}
	        limit 0,1
    </select>

    <select id="findByUserId" resultMap="resultMap">
         select id, name, domain_id from project
        INNER JOIN assignment ON project.id = assignment.target_id
         WHERE
	        assignment.actor_id = #{userId}

    </select>

    <select id="findByMap" resultMap="resultMap">
         select id, name, domain_id from project
         <where>
             <if test="deptId  != null and deptId  != ''" >
                 and dept_id = #{deptId, jdbcType=VARCHAR}
             </if>
         </where>
    </select>
</mapper>
