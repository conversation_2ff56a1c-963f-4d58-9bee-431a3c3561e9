<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sugon.cloud.iam.api.mapper.AssignmentMapper">
    <resultMap id="resultMap" type="com.sugon.cloud.iam.api.entity.keystone.Assignment" >
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="actor_id" property="actorId" jdbcType="VARCHAR"/>
        <result column="target_id" property="targetId" jdbcType="VARCHAR"/>
        <result column="role_id" property="roleId" jdbcType="VARCHAR"/>
        <result column="inherited" property="inherited" jdbcType="TINYINT"/>
    </resultMap>
</mapper>
