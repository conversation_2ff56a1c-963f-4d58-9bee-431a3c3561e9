<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sugon.cloud.iam.api.mapper.DepartmentMapper">

    <resultMap id="BaseResultMap" type="com.sugon.cloud.iam.common.model.vo.DepartmentTreeDetailVO">
        <!--@Table department-->
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
        <result property="domainId" column="domain_id" jdbcType="VARCHAR"/>
        <result property="parentId" column="parent_id" jdbcType="VARCHAR"/>
        <result property="level" column="level" jdbcType="INTEGER"/>
        <collection property="children"
                    column="id"
                    ofType="com.sugon.cloud.iam.api.vo.DepartmentTreeDetailVO"
                    select="findTreeDepartment"/>
    </resultMap>

    <resultMap id="BaseResultMap2" type="com.sugon.cloud.iam.common.model.vo.DepartmentTreeDetailVO">
        <!--@Table department-->
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
        <result property="domainId" column="domain_id" jdbcType="VARCHAR"/>
        <result property="parentId" column="parent_id" jdbcType="VARCHAR"/>
        <result property="level" column="level" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="userType" column="user_type" jdbcType="VARCHAR"/>
        <collection property="children"
                    column="id"
                    ofType="com.sugon.cloud.iam.common.model.vo.DepartmentTreeDetailVO"
                    select="findTreeDepartment"/>
    </resultMap>

    <resultMap id="BaseResultMap3" type="com.sugon.cloud.iam.common.model.vo.DepartmentTreeDetailVO">
        <!--@Table department-->
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
        <result property="domainId" column="domain_id" jdbcType="VARCHAR"/>
        <result property="parentId" column="parent_id" jdbcType="VARCHAR"/>
        <result property="level" column="level" jdbcType="INTEGER"/>
    </resultMap>

    <select id="findTreeDepartment" resultMap="BaseResultMap">
        select * from department where parent_id = #{parentId}
    </select>
    <select id="findTreeDepartmentAndOwner" resultMap="BaseResultMap">
        select * from department where parent_id = #{departmentId} or id = #{departmentId}
    </select>

    <select id="findTreeAllDepartment" resultMap="BaseResultMap2">
        select * from department where level = 0 and id != 'admin'
    </select>

    <select id="findAllDepartment" resultMap="BaseResultMap2">
        SELECT
            u.id AS user_id,
            u.`name` AS user_name,
            u.type AS user_type,
            d.*
        FROM
            `user` u
        LEFT JOIN department d ON u.dept_id = d.id
        <if test="userType  == null " >
            WHERE u.type is null
        </if>
        <if test="userType  != null and userType != ''" >
            WHERE u.type=#{userType, jdbcType=VARCHAR}
        </if>
        <if test="userId  != null and userId != ''" >
            AND u.id=#{userId, jdbcType=VARCHAR}
        </if>
        <if test="getAdminDept  != null and getAdminDept" >
            or d.id = 'admin'
        </if>
    </select>

    <select id="findAllDepartmentList" resultMap="BaseResultMap3">
        SELECT
        d.*
        FROM
        department d where d.id != 'admin'
    </select>

    <select id="findAllDepartmentByRootDeptId" resultMap="BaseResultMap2">
        SELECT * FROM department WHERE id=#{id, jdbcType=VARCHAR}
    </select>
</mapper>
