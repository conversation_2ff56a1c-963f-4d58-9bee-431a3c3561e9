<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sugon.cloud.iam.api.mapper.UserRoleMappingMapper">
    <resultMap id="resultMap" type="com.sugon.cloud.iam.api.entity.UserRoleMapping" >
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="project_id" property="projectId" jdbcType="VARCHAR"/>
        <result column="role_id" property="roleId" jdbcType="VARCHAR"/>
        <result column="dept_id" property="deptId" jdbcType="VARCHAR"/>
    </resultMap>
</mapper>
