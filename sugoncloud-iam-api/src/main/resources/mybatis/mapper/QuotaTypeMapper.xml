<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sugon.cloud.iam.api.mapper.QuotaTypeMapper">

    <resultMap id="BaseResultMap" type="com.sugon.cloud.iam.common.model.vo.QuotaType">
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="categoryId" column="category_id" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getQuotaTypes" resultMap="BaseResultMap" parameterType="java.util.Map">
        select name,description,category_id,micro_id from quota_type qt
        <where>
            <if test="typeName  != null and typeName != ''" >
                AND qt.name = #{typeName}
            </if>
            <if test="categoryId  != null and categoryId != ''" >
                AND qt.category_id = #{categoryId}
            </if>
            <if test="microIds != null and microIds.size > 0">
                AND qt.micro_id not in
                <foreach collection="microIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="categoryIds != null and categoryIds.size > 0">
                AND qt.category_id not in
                <foreach collection="categoryIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        order by qt.sequence
    </select>
</mapper>
