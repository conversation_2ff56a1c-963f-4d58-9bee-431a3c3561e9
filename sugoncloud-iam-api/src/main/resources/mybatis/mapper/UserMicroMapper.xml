<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sugon.cloud.iam.api.mapper.UserMicroMapper">

    <select id="queryList" resultType="com.sugon.cloud.iam.api.entity.UserMicroViewer">
        select a.*,b.name as micro_name,b.*  from user_micro a left join micro_service b on a.micro_id=b.id where a.user_id = #{userId}
    </select>

</mapper>
