<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sugon.cloud.iam.api.mapper.IamRoleMapper">
    <resultMap id="resultMap" type="com.sugon.cloud.iam.api.entity.IamRole" >
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="dept_id" property="deptId" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="DATE"/>
        <result column="modify_time" property="modifyTime" jdbcType="DATE"/>
    </resultMap>

    <select id="findByUserIdAndProjectId" resultMap="resultMap">
        SELECT
            role.id,
            role.NAME,
            role.description,
            role.dept_id,
            role.type,
            role.create_time,
            role.modify_time
        FROM
            iam_role role
            INNER JOIN user_role_project_mapping mapping ON role.id = mapping.role_id
         WHERE
	        mapping.user_id = #{userId} and mapping.project_id=#{projectId}
        ORDER BY role.create_time DESC
    </select>

    <select id="findByUserId" resultMap="resultMap">
        SELECT
            DISTINCT
        role.id,
        role.NAME,
        role.description,
        role.dept_id,
        role.type,
        role.create_time,
        role.modify_time
        FROM
            iam_role role
            INNER JOIN user_role_project_mapping mapping ON role.id = mapping.role_id
         <where >
            mapping.user_id = #{userId}
            <if test="projectId != null and projectId != ''">
              and  (mapping.project_id = #{projectId} or mapping.project_id is null)
            </if>
         </where>


        ORDER BY role.create_time DESC
    </select>

    <select id="findByUserIdPage" resultMap="resultMap">
        SELECT
        role.id,
        role.NAME,
        role.description,
        role.dept_id,
        role.type,
        role.create_time,
        role.modify_time
        FROM
        iam_role role
        INNER JOIN user_role_project_mapping mapping ON role.id = mapping.role_id
        WHERE
        mapping.user_id = #{userId}
        <if test="queryInnerRole  != null and queryInnerRole == false">
            AND role.dept_id is not null
        </if>
        <if test="projectId != null and projectId != ''">
            AND mapping.project_id = #{projectId}
        </if>
        <if test="projectId == null or projectId == ''">
            AND mapping.project_id is null
        </if>
        ORDER BY role.create_time DESC
    </select>

    <select id="findAllRole" resultMap="resultMap" parameterType="java.lang.String">
        SELECT
        role.id,
        role.NAME,
        role.description,
        role.dept_id,
        role.type,
        role.create_time,
        role.modify_time
        FROM
        iam_role role
        <where>
            <if test="roleName  != null and roleName != ''" >
                role.name LIKE concat('%',#{roleName, jdbcType=VARCHAR},'%')
            </if>
        </where>
        ORDER BY role.create_time DESC
    </select>
</mapper>
