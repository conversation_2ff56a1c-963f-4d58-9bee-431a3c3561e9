server:
  port: 30570
ribbon:
  ReadTimeout: 28800000
  ConnectTimeout: 28800000

spring:
  main:
    allow-bean-definition-overriding: true
  application:
    name: sugoncloud-iam-api
  cloud:
    nacos:
      discovery:
        enabled: false
      config:
        enabled: false
  elasticsearch:
    rest:
      uris: ${es.rest-uris}
  redis:
    host: ***********
    port: 11379
    timeout: 1s
    password: Scloud@920redis
    jedis:
      pool:
        min-idle: 4
        max-idle: 16
        max-wait: 1000
        max-active: 32
        testOnBorrow: true
        testWhileIdle: true
        timeBettweenEvictionRunsMilllis: 30000
        numTestsPerEvictionRun: 10
        minEvictableIdleTimeMillis: 60000
  datasource:
    url: jdbc:mysql://***********:3306/iam?useUnicode=true&characterEncoding=UTF-8&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode=false&serverTimezone=GMT%2b8&useSSL=false
    username: root
    password: database_sugon
    # 使用druid数据源
    #    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 下面为连接池的补充设置，应用到上面所有数据源中
    # 初始化大小，最小，最大
    initialSize: 5
    minIdle: 5000
    maxActive: 20
    # 配置获取连接等待超时的时间
    maxWait: 60000
    # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
    timeBetweenEvictionRunsMillis: 60000
    # 配置一个连接在池中最小生存的时间，单位是毫秒
    minEvictableIdleTimeMillis: 300000
    validationQuery: SELECT 1 FROM DUAL
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
    # 打开PSCache，并且指定每个连接上PSCache的大小
    poolPreparedStatements: true
    maxPoolPreparedStatementPerConnectionSize: 20
    # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
    spring.datasource.filters: stat,wall,log4j
    # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
    connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
  http:
    multipart:
      enabled: true
      max-file-size: 204800MB     #单文件限制
      max-request-size: 512000MB  #总限制
  flyway:
    enabled: true
    # sql脚本位置
    locations: classpath:db/migration/mysql
    # 当迁移时发现目标schema非空，而且带有没有元数据的表时，是否自动执行基准迁移
    baseline-on-migrate: true
# mybatis_config
mybatis-plus:
  config-locations: classpath:mybatis/mybatis-config.xml
  mapper-locations: classpath:mybatis/mapper/*.xml
  type-aliases-package: com.sugon.cloud.iam.api.entity
  type-enums-package: com.sugon.cloud.iam.api.enums
## 日志配置路径
sugoncloud:
  swagger:
    enable: true
    title: sugoncloud-iam-api
    description: SugonCloud-IAM-Api 接口文档
    version: 2.0
    basePackage: com.sugon.cloud
    document-ignore-enabled: true
    contact:
      name: limd
      url: https://www.sugoncloud.com
      email: <EMAIL>
tools:
  ip: http://***********:30546
##xxl job executor desc,不写默认为default_desc
xxl:
  job:
    apptitle: 统一身份认证IAM
    appport: 18102
    addresses: http://***********:30507/xxl-job-admin
    admin:
      # xxl-job admin address list, such as "http://address" or "http://address01,http://address02"
      addresses: ${xxl.job.addresses}
    # xxl-job, access token
    accessToken:
    executor:
      # xxl-job executor appname
      appname: ${spring.application.name}
      # xxl-job groupTitle(0 < length <= 12)
      apptitle: ${xxl.job.apptitle:default_desc}
      # xxl-job executor server-info
      port: ${xxl.job.appport}
      # xxl-job executor log-retention-days
      logretentiondays: 10
iam:
  # 校验一个证书是否对应一个用户(true:校验 | false:不校验)
  userCertOne2One: true
  # 安全管理员ID,用户校验信息完整度又被篡改发邮件使用
  securityAdminUserId: 003f67adc2d4465da6e2f716a621307a
  # 定时任务cron表达式
  task:
    # 用户信息完整度校验
    userIntegrityCheck: 0 0 */1 * * ?
    userRole: 0 0/8 * * * ?
    ak-sk: 0 0/2 * * * ?
    strategy: 0 0/15 * * * ?
    userStrategy: 0 0/5 * * * ?
  ip: ***********
  port: 30510
  inner:
    user: inner
    passwd: Inner@fullstackOwner
    domain: default
    projectname: admin
    projectId: edcc24059c8b46969a37260dc9ab2329
    userId: 0d2bbb018e8b44b985a169647379f413
    in-userId: e84f98be630f475c9ee127a38e3eedac
    ak: inner-ak
    sk: 33BE8B87D58FA954C3CA73FBDB8D2C0BEA0A5B8A393B8057CEAD88F77AE84DB340D198F686555F236ED9FC8D86741564
    oss-ssl: true
vip:
  ip: ***********
  port: 30510
update-micro: true
## 日志配置路径
logging:
  config: classpath:log4j2-dev.xml
token:
  secret: "sugon-cloud-iam"
  expireTime: 600
  header: "Authorization"
jwt:
  skip:
    uri:
      - /error/extThrow
      - /v3/(.*)
      - /doc.html
      - /favicon.ico
      - /webjars/(.*)
      - /swagger-resources(.*)
      - /service-worker.js
      - /v2/api-docs
      - /v2/api-docs-ext
      - /precache-manifest(.*)
      - /sync-data/(.*)
      - /api/oauth/validate-token
      - /api/users
      - /manifest.json
      - /index.html
      - /robots.txt
      - /api/oauth/token
      - /api/v2/api-docs
      - /
      - /v3
      - /api/quotas/project/used
      - /api/quotas/project
      - /api/endpoint/(.*)
      - /api/globalsettings/info
      - /actuator/health
      - /micro-healthy
      - /api/micro-service-categories
      - /api/jenkins/job/84ef7d71-557d-4430-b58b-ddb9c8b412ae/ws
      - /api/globalsettings/publickey
      - /api/users/hash
      - /api/common/ram/policy/hash/(.*)
      - /api/sca4/(.*)
      - /api/third-party/(.*)
      - /api/messages/code
      - /api/messages/validate-code
      - /enc-dec/mfa-switch
      - /api/users/contact-info
      - /api/oauth/need-mfa-code
      - /api/seeds/upload

obs:
  certificate:
    number: 3

elasticsearch:
  index: sugoncloud-log
  type: operatelog
  alertindex: alert
  alerttype: alarm
  fields: ip,action,type,user,resource,project_id,region_id,sign,param,method,operate_api,user_id,log_date
  alertfields: alert_name,host_name,sum_mary,severity,description,alerm_name,vm_name,vm_status,vm_image_name,ends_at
  ip: ${es.ip}
  rest-uris: ${es.rest-uris}
  sniff: true
  port: ${es.http-port}
  pool: 5
  timeout: 20
  username: elastic
  password: admin1234@sugon
es:
  ip: ***********
  port: 9300
  rest-uris: ***********:39200
  http-port: 39200
  username: elastic
  password: admin1234@sugon
  resources-index: sugoncloud_index
  message-index: sugoncloud_message
  console-index: sugoncloud_console

redis-lock-registry:
  default-expire: 600000

deploy:
  nacosDataId: sugoncloud-license-common.yaml
  nacosGroup: DEFAULT_GROUP
license:
  licenseContent: 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
  publicCertKeystoreContent: /u3+7QAAAAIAAAABAAAAAgAKcHVibGljY2VydAAAAYk/J0VOAAVYLjUwOQAAAxMwggMPMIICy6ADAgECAgRqcmTsMA0GCWCGSAFlAwQDAgUAMFcxCzAJBgNVBAYTAkNOMQswCQYDVQQIEwJTSDELMAkGA1UEBxMCU0gxDjAMBgNVBAoTBXN1Z29uMQ4wDAYDVQQLEwVzdWdvbjEOMAwGA1UEAxMFc3Vnb24wHhcNMjMwNzEwMDkzNDQ1WhcNMzMwNzA3MDkzNDQ1WjBXMQswCQYDVQQGEwJDTjELMAkGA1UECBMCU0gxCzAJBgNVBAcTAlNIMQ4wDAYDVQQKEwVzdWdvbjEOMAwGA1UECxMFc3Vnb24xDjAMBgNVBAMTBXN1Z29uMIIBuDCCASwGByqGSM44BAEwggEfAoGBAP1/U4EddRIpUt9KnC7s5Of2EbdSPO9EAMMeP4C2USZpRV1AIlH7WT2NWPq/xfW6MPbLm1Vs14E7gB00b/JmYLdrmVClpJ+f6AR7ECLCT7up1/63xhv4O1fnxqimFQ8E+4P208UewwI1VBNaFpEy9nXzrith1yrv8iIDGZ3RSAHHAhUAl2BQjxUjC8yykrmCouuEC/BYHPUCgYEA9+GghdabPd7LvKtcNrhXuXmUr7v6OuqC+VdMCz0HgmdRWVeOutRZT+ZxBxCBgLRJFnEj6EwoFhO3zwkyjMim4TwWeotUfI0o4KOuHiuzpnWRbqN/C/ohNWLx+2J6ASQ7zKTxvqhRkImog9/hWuWfBpKLZl6Ae1UlZAFMO/7PSSoDgYUAAoGBAK8vTZuODt29/U4LM5TPy+7dhAQep8QEEE4l70Q8ZO8l1MiOcYYrDKM5QWw6FoxXp59GNcNGM/CcbAJi3jOvCSLB4NcH8/hQtd4zpH+uwLnKQd680tVPX+Tcm41bPF0ouMJzMCMwg9idlePuu6tmTaazxqqMwUtPfTa9DQJEkHLSoyEwHzAdBgNVHQ4EFgQUlWaFvBNegPrt89ZlPeesueHTpO4wDQYJYIZIAWUDBAMCBQADLwAwLAIUWI/RfWaoCjjhoMbXpuSIVLKIXDMCFG48ZgesKbTG0QuhQtBFFGdp1aEZWFfVQEiGHy4G4iB0petG4R9KnY0=

multiregion:
  multiEsEntityList:
    - name: RegionOne
      ip: ***********
      port: 9201
      username: elastic
      password: admin1234@sugon
  multiMonitorEntityList:
    - name: RegionOne
      ip: ***********
      port: 30502

user:
  authorize:
    mfa:
      #server: http://***********:18080/otpcenter
      server: https://otp.newsec.cn:40076/secauth/v2/verifyToken
# mfa类型 opt-server/ukey
mfa:
  enabled: true
  #双因子 签名服务器 opt-server/fisherman/swxa（三未信安）/infoSec（信安世纪）/sms短信认证/email邮箱认证
  type: ftng
  dean:
    host: *************
    port: 6006
    password: 12345678
  fisherman:
    host: **************
    port: 2020
  # iam中随机数接口获取 sign/fisherman-vmsgd/firsherman-ironic/dean
  swxa:
    host: https://otp.newsec.cn:40076/secauth/v2/verifyToken
    port: 0
    #算法类型
    strategy: SM3
    #口令计算方式
    wordSource: 1
    companyId: 100022
  infoSec:
    host: *************
    port: 4452
    minConnection: 10
    maxConnection: 20
    timeout: 5
    user: admin2
    password: 12345678

encryption:
  enabled: false
management:
  health:
    redis:
      enabled: false
    elasticsearch:
      enabled: false

#是否是从第三方登录过来
thirdPartySignOn: true


jasypt:
  encryptor:
    password: lTZKFlzkAN
    algorithm: PBEWithMD5AndDES
    bootstrap: false
sms:
  ak: cc99cd71fff54a67917e111f2148d9d3
  sk: 20579E18EA3D51F6031B0AC5138633D3F000EED2309716A2EB5E99D29AD5189140D198F686555F236ED9FC8D86741564