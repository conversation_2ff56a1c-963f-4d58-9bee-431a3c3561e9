ALTER TABLE micro_service ADD COLUMN `order` double NULL;
ALTER TABLE micro_service_category ADD COLUMN `order` double NULL;


UPDATE `micro_service_category` SET `order` = 0.0 WHERE `id` = '4afee060a36111eb8230f20ed01ddf38';
UPDATE `micro_service_category` SET `order` = 10.0 WHERE `id` = 'ea209553-6177-441b-981a-9c5e40c62ea8';
UPDATE `micro_service_category` SET `order` = 20.0 WHERE `id` = '13a38073a36111eb8230f20ed01ddf38';
UPDATE `micro_service_category` SET `order` = 30.0 WHERE `id` = 'd04f330d-7353-476d-afd8-72e7a8f4002e';
UPDATE `micro_service_category` SET `order` = 40.0 WHERE `id` = 'ab0377ca-1e5b-4b05-a970-5d79e9ce2015';
UPDATE `micro_service_category` SET `order` = 50.0 WHERE `id` = 'a3a0adae-c1e1-11eb-a5c2-fac676e41602';
UPDATE `micro_service_category` SET `order` = 60.0 WHERE `id` = '44c1f40ba59d11eb8230f20ed01ddf38';
UPDATE `micro_service_category` SET `order` = 70.0 WHERE `id` = '1ed139cd-0d6e-43e1-98c5-74b478d3f7f3';
UPDATE `micro_service_category` SET `order` = 80.0 WHERE `id` = '54df68e1a36111eb8230f20ed01ddf38';
UPDATE `micro_service_category` SET `order` = 90.0 WHERE `id` = 'dce92741-1d3b-43bc-9e51-ce21b60c9d4a';

ALTER TABLE `micro_service`
ADD COLUMN `third_part_access` tinyint(1) NULL DEFAULT 0 COMMENT '第三方接入' AFTER `version`;
