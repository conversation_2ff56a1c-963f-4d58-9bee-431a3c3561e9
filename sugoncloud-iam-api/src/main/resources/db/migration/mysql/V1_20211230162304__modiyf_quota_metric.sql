INSERT INTO `quota_metric` SELECT 'edr_module_pc', 'hss', 'PC总量(个)', 'PC使用量(个)' FROM DUAL WHERE NOT EXISTS(SELECT `name` FROM `quota_metric` WHERE `name` = 'edr_module_pc');
INSERT INTO `quota_metric` SELECT 'edr_module_server', 'hss', '服务器总量(个)', '服务器使用量(个)' FROM DUAL WHERE NOT EXISTS(SELECT `name` FROM `quota_metric` WHERE `name` = 'edr_module_server');
INSERT INTO `quota_metric` SELECT 'edr_module_server_web', 'hss', 'web服务器总量(个)', 'web服务器使用量(个)' FROM DUAL WHERE NOT EXISTS(SELECT `name` FROM `quota_metric` WHERE `name` = 'edr_module_server_web');

UPDATE micro_service_category SET `order` = 20  WHERE `id` = 'ea209553-6177-441b-981a-9c5e40c62ea8';
UPDATE micro_service_category SET `order` = 10  WHERE `id` = '13a38073a36111eb8230f20ed01ddf38';
UPDATE micro_service_category SET `order` = 30  WHERE `id` = '44c1f40ba59d11eb8230f20ed01ddf38';
UPDATE micro_service_category SET `order` = 31  WHERE `id` = 'd04f330d-7353-476d-afd8-72e7a8f4002e';
