
#########################################  policy sql  ##########################################
DELETE from  ram_policy where owner_id is null;
DELETE from ram_policy_role WHERE ram_role_id in ('22dc7fd7a67e11eb9db98257b6a5d0d7','5a5508808dc641bd83f2ca62ac641ee9','6d4372436aa34fd5a015550c22ba605c','94d25a5fa44b4e02b46f87fba31e1af1','c16c9944ca324aa693d3da6f7e7484f7');

INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '018c2c7192e42695c0306b5dc1756ec4', '修改用户状态', '组织管理-用户管理-操作-修改用户状态', '1', null, null, 'null', '1', '7d83120a78a67a76fda51b94adec4801', '', '', '', '7', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '018c2c7192e42695c0306b5dc1756ec4');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '0afb7e979297c8046dfd7e5839aad8ea', '修改', '策略管理-修改', '1', null, null, 'PUT', '1', 'bda8f353d61b50ae676b02fb62741ca1', 'com.sugon.cloud.policy.api.controller.CommonPolicyController', 'editPolicy', '/api/common/ram/policy/{id}', '2', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '0afb7e979297c8046dfd7e5839aad8ea');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '0bac8a03f16bcce0af6d58c161459e6c', '修改配额', '组织管理-项目管理-操作-修改配额', '2', null, null, 'PUT', '1', '2ad1a10cdd55777e0acc760e26fc4259', 'QuotaController', 'updateProjectQuota', '/api/quotas/project/{project_id}', '2', '', '2021-05-06 00:00:00', '/departmentManage/modifyQuota', '', '', '2', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '0bac8a03f16bcce0af6d58c161459e6c');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '1087f2f01567c61ac1baa20a10510c34', '新建', '组织管理-角色管理-新建', '1', null, null, 'POST', '1', 'fe3a21e5bffa710d207343632197ae40', 'com.sugon.cloud.iam.api.controller.IamRoleController', 'createRole', '/api/roles', '0', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '1087f2f01567c61ac1baa20a10510c34');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '170e6e421f685fd9ef1e517f363972fa', '新建', '组织管理-项目管理-新建', '1', null, null, 'POST', '1', '199e95b1f85d781cd4d04e2cfc08a2ea', 'com.sugon.cloud.iam.api.controller.ProjectController', 'create', '/api/projects', '0', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '170e6e421f685fd9ef1e517f363972fa');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '199e95b1f85d781cd4d04e2cfc08a2ea', '项目管理', '组织管理-项目管理-列表', '1', null, null, 'GET', '1', 'f79da1ccd2add999e2491cce36c89290', 'com.sugon.cloud.iam.api.controller.DepartmentController', 'pageList', '/api/departments/{id}/projects', '1', '', '2021-04-23 00:00:00', '', '', '', '2', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '199e95b1f85d781cd4d04e2cfc08a2ea');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '********************************', '角色管理', '组织管理-用户管理-操作-角色管理', '1', null, null, 'DELETE', '1', '7d83120a78a67a76fda51b94adec4801', 'com.sugon.cloud.iam.api.controller.UserController', 'batchAssignmentRoles', '/api/users/{user_id}/bind-roles', '2', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '********************************');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '252c150ee420f8ef83d349f3d293722d', 'IP控制', '组织管理-用户管理-操作-IP控制', '1', null, null, 'null', '1', '7d83120a78a67a76fda51b94adec4801', '', '', '', '6', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '252c150ee420f8ef83d349f3d293722d');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '2ad1a10cdd55777e0acc760e26fc4259', '操作', '组织管理-项目管理-操作', '1', null, null, 'null', '1', '199e95b1f85d781cd4d04e2cfc08a2ea', '', '', '', '2', '', '2021-04-23 00:00:00', '', '', '', '2', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '2ad1a10cdd55777e0acc760e26fc4259');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '2baf4e8462592ad4201df36edd6da1a4', '用户详情', '组织管理-用户列表-用户详情', 'null', null, null, 'null', '1', 'c1fd2cce952da1093f3aa062e8ca8d9e', '', '', '', '0', '', '2021-05-12 00:00:00', '/departmentManage/userDetail/:id', '', '', '2', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '2baf4e8462592ad4201df36edd6da1a4');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '2f563fdc6b3c716115a57fda77b06789', '修改角色', '组织管理-角色管理-操作-修改角色', '1', null, null, 'PUT', '1', 'd92db5112c6c67cf11a8e90d9ca79335', 'com.sugon.cloud.iam.api.controller.IamRoleController', 'update', '/api/roles', '1', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '2f563fdc6b3c716115a57fda77b06789');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '376b911d000734db8418a6f00e712d1a', '全局配置', '全局配置', 'null', null, null, 'null', '1', 'de5b8265a3f911eb8230f20ed01ddf38', '', '', '', '3', '', '2021-04-27 00:00:00', '/global', 'icon-quanjuguanli', '', '0', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '376b911d000734db8418a6f00e712d1a');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '3928a382a51cbe0b9dd96754709e3638', '删除', '策略管理-删除', '1', null, null, 'DELETE', '1', 'bda8f353d61b50ae676b02fb62741ca1', 'com.sugon.cloud.policy.api.controller.CommonPolicyController', 'deletePolicy', '/api/common/ram/policy/{id}', '3', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '3928a382a51cbe0b9dd96754709e3638');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '4b115b2d2e7f4b3c027164c2542f764c', '修改用户', '组织管理-用户管理-操作-修改用户', '1', null, null, 'PUT', '1', '7d83120a78a67a76fda51b94adec4801', 'com.sugon.cloud.iam.api.controller.UserController', 'update', '/api/users', '0', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '4b115b2d2e7f4b3c027164c2542f764c');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '5930d39ee1c3b19b0160723ff40c3779', '删除角色', '组织管理-角色管理-操作-删除角色', '1', null, null, 'DELETE', '1', 'd92db5112c6c67cf11a8e90d9ca79335', 'com.sugon.cloud.iam.api.controller.IamRoleController', 'deleteRole', '/api/roles/{role_id}', '0', '', '2021-04-23 00:00:00', '', '', 'a72680e8bc494c93715698a887740f78', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '5930d39ee1c3b19b0160723ff40c3779');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '61d125a65df54296d6ed381976585cab', '添加', '策略管理-添加', '1', null, null, 'POST', '1', 'bda8f353d61b50ae676b02fb62741ca1', 'com.sugon.cloud.policy.api.controller.CommonPolicyController', 'createPolicy', '/api/common/ram/policy', '1', '', '2021-04-23 00:00:00', '', 'iconziyuan121', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '61d125a65df54296d6ed381976585cab');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '630c2949797479f246573339f4218e57', '创建用户', '组织管理-用户管理-创建用户', '1', null, null, 'POST', '1', 'c1fd2cce952da1093f3aa062e8ca8d9e', 'com.sugon.cloud.iam.api.controller.UserController', 'createSub', '/api/users/sub', '1', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '630c2949797479f246573339f4218e57');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '6f5562a40b28f22788015a32d5e4e1e5', '编辑', '组织管理-项目管理-操作-编辑', '1', null, null, 'PUT', '1', '2ad1a10cdd55777e0acc760e26fc4259', 'com.sugon.cloud.iam.api.controller.ProjectController', 'update', '/api/projects/{id}', '0', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '6f5562a40b28f22788015a32d5e4e1e5');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '6f7a9d24602f6e528039800c1862e620', '设置用户过期时间', '组织管理-用户管理-操作-设置用户过期时间', '1', null, null, 'null', '1', '7d83120a78a67a76fda51b94adec4801', '', '', '', '5', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '6f7a9d24602f6e528039800c1862e620');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '70f15b48260a078ff3188591b1bbc9fc', '删除用户', '组织管理-用户管理-删除用户', '1', null, null, 'DELETE', '1', 'c1fd2cce952da1093f3aa062e8ca8d9e', 'com.sugon.cloud.iam.api.controller.UserController', 'delete', '/api/users/{user_id}', '1', '', '2021-04-23 00:00:00', '', '', '70f15b48260a078ff3188591b1bbc9fc', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '70f15b48260a078ff3188591b1bbc9fc');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '7d83120a78a67a76fda51b94adec4801', '操作', '组织管理-用户管理-操作', '1', null, null, 'null', '1', 'c1fd2cce952da1093f3aa062e8ca8d9e', '', '', '', '2', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '7d83120a78a67a76fda51b94adec4801');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '880d73ee0a2390331cee75bec070e9ca', '删除', '组织管理-项目管理-删除', '1', null, null, 'DELETE', '1', '199e95b1f85d781cd4d04e2cfc08a2ea', 'com.sugon.cloud.iam.api.controller.ProjectController', 'delete', '/api/projects/{id}', '1', '', '2021-04-23 00:00:00', '', '', '880d73ee0a2390331cee75bec070e9ca', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '880d73ee0a2390331cee75bec070e9ca');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '890826492a92e7db24d8865c778a8e38', '项目管理', '组织管理-用户管理-操作-项目管理', '1', null, null, 'POST', '1', '7d83120a78a67a76fda51b94adec4801', 'com.sugon.cloud.iam.api.controller.UserController', 'batchAssignmentProjects', '/api/users/{user_id}/bind-projects', '3', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '890826492a92e7db24d8865c778a8e38');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'a72680e8bc494c93715698a887740f78', '删除', '组织管理-角色管理-删除', '1', null, null, 'DELETE', '1', 'fe3a21e5bffa710d207343632197ae40', 'com.sugon.cloud.iam.api.controller.IamRoleController', 'deleteRole', '/api/roles/{role_id}', '1', '', '2021-04-23 00:00:00', '', '', 'a72680e8bc494c93715698a887740f78', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'a72680e8bc494c93715698a887740f78');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'a8cbf9ee2898e2b2979824d4d6098be2', '删除', '组织管理-用户管理-操作-删除', '1', null, null, 'DELETE', '1', '7d83120a78a67a76fda51b94adec4801', 'com.sugon.cloud.iam.api.controller.UserController', 'delete', '/api/users/{user_id}', '8', '', '2021-04-23 00:00:00', '', '', '70f15b48260a078ff3188591b1bbc9fc', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'a8cbf9ee2898e2b2979824d4d6098be2');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'aab2dc93c2c0a57e6efa0235b5cdc696', '概览', '用户管理IAM-概览', '1', null, null, 'GET', '1', 'de5b8265a3f911eb8230f20ed01ddf38', '', '', '', '0', '', '2021-04-23 00:00:00', '/overview', 'icon-gailan', '', '0', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'aab2dc93c2c0a57e6efa0235b5cdc696');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'b1ff673ebc7dee772da81fd3e68a5607', '列表', '组织管理-列表', 'null', null, null, 'GET', '1', 'f79da1ccd2add999e2491cce36c89290', 'com.sugon.cloud.iam.api.controller.DepartmentController', 'findByUserId', '/api/departments', '0', '', '2021-04-27 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'b1ff673ebc7dee772da81fd3e68a5607');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'bc0e2aa5b071a67599e4d3a8221750da', '列表', '策略管理-列表', '1', null, null, 'GET', '1', 'bda8f353d61b50ae676b02fb62741ca1', 'com.sugon.cloud.policy.api.controller.CommonPolicyController', 'getAllPolicyByTree', '/api/common/ram/policy/tree', '0', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'bc0e2aa5b071a67599e4d3a8221750da');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'bda8f353d61b50ae676b02fb62741ca1', '策略管理', '用户管理IAM-策略管理', '0', null, null, 'null', '1', 'de5b8265a3f911eb8230f20ed01ddf38', '', '', '', '2', '', '2021-04-23 00:00:00', '/strategy', 'icon-celueguanli', '', '0', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'bda8f353d61b50ae676b02fb62741ca1');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'c1fd2cce952da1093f3aa062e8ca8d9e', '用户管理', '组织管理-用户管理-列表', '1', null, null, 'GET', '1', 'f79da1ccd2add999e2491cce36c89290', 'com.sugon.cloud.iam.api.controller.UserController', 'userListByDeptId', '/api/users/dept/{dept_id}/list', '0', '', '2021-04-23 00:00:00', '', '', '', '2', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'c1fd2cce952da1093f3aa062e8ca8d9e');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'cfd7708b7d3f2b38ef67fdf187dcfdf1', '删除', '组织管理-项目管理-操作-删除', '1', null, null, 'DELETE', '1', '2ad1a10cdd55777e0acc760e26fc4259', 'com.sugon.cloud.iam.api.controller.ProjectController', 'delete', '/api/projects/{id}', '1', '', '2021-04-23 00:00:00', '', '', '880d73ee0a2390331cee75bec070e9ca', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'cfd7708b7d3f2b38ef67fdf187dcfdf1');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'd92db5112c6c67cf11a8e90d9ca79335', '操作', '组织管理-角色管理-操作', '1', null, null, 'null', '1', 'fe3a21e5bffa710d207343632197ae40', '', '', '', '2', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'd92db5112c6c67cf11a8e90d9ca79335');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'de5b8265a3f911eb8230f20ed01ddf38', '用户管理IAM', '用户管理IAM顶级菜单', '1', null, null, 'null', '1', '0000000000', '', '', '', '2', null, '2020-04-12 00:00:00', '/Vpc', 'icon-ico-33', '', '0', '0' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'de5b8265a3f911eb8230f20ed01ddf38');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'f78146c95201acef6690ecf30a19a03d', '重置密码', '组织管理-用户管理-操作-重置密码', '1', null, null, 'null', '1', '7d83120a78a67a76fda51b94adec4801', 'com.sugon.cloud.iam.api.controller.UserController', 'updatePassword', '/api/users/{user_id}/password/{password}/{old_password}', '4', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'f78146c95201acef6690ecf30a19a03d');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'f79da1ccd2add999e2491cce36c89290', '组织管理', '用户管理IAM-部门管理', '0', null, null, 'null', '1', 'de5b8265a3f911eb8230f20ed01ddf38', '', '', '', '0', '', '2021-04-23 00:00:00', '/departmentManage', 'icon-zuzhiguanli', '', '0', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'f79da1ccd2add999e2491cce36c89290');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'fcdfd962bc93512c1cdee056daad37ef', '编辑权限', '组织管理-角色管理-操作-编辑权限', '1', null, null, 'null', '1', 'd92db5112c6c67cf11a8e90d9ca79335', '', '', '', '2', '', '2021-04-23 00:00:00', '/departmentManage/modify', '', '', '2', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'fcdfd962bc93512c1cdee056daad37ef');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'fe3a21e5bffa710d207343632197ae40', '角色管理', '组织管理-角色管理-列表', '1', null, null, 'GET', '1', 'f79da1ccd2add999e2491cce36c89290', 'com.sugon.cloud.iam.api.controller.IamRoleController', 'listByDept', '/api/roles/dept/{dept_id}', '2', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'fe3a21e5bffa710d207343632197ae40');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '01fb064ab94fc7a400ea2f87f08769c7', 'f78146c95201acef6690ecf30a19a03d', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '01fb064ab94fc7a400ea2f87f08769c7');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '0389dc6dca623b1cc45a30985b81c602', '018c2c7192e42695c0306b5dc1756ec4', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '0389dc6dca623b1cc45a30985b81c602');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '05261adcf42d8f089682f30db7addf36', '0bac8a03f16bcce0af6d58c161459e6c', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '05261adcf42d8f089682f30db7addf36');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '06c419b547c1ae1e8754560383df110b', '252c150ee420f8ef83d349f3d293722d', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '06c419b547c1ae1e8754560383df110b');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '078df8b56244614b172c38f1631a02ed', '1087f2f01567c61ac1baa20a10510c34', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '078df8b56244614b172c38f1631a02ed');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '0c3f6c0b7dc4bb3e972fb1bab32362bd', 'b1ff673ebc7dee772da81fd3e68a5607', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '0c3f6c0b7dc4bb3e972fb1bab32362bd');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '0fd0a642e3c99b2df7d3c19ab3bb95e9', '2ad1a10cdd55777e0acc760e26fc4259', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '0fd0a642e3c99b2df7d3c19ab3bb95e9');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '11eb420a0d51ac01f5860bc5f52c7a47', '376b911d000734db8418a6f00e712d1a', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '11eb420a0d51ac01f5860bc5f52c7a47');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '13b16b81fd0966d6b30e49f7d928adc7', 'cfd7708b7d3f2b38ef67fdf187dcfdf1', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '13b16b81fd0966d6b30e49f7d928adc7');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '1ff0e0909dbee9d8ac618b56016076ee', 'd92db5112c6c67cf11a8e90d9ca79335', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '1ff0e0909dbee9d8ac618b56016076ee');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '21dd15441a799eff7d34b0c9855388cc', '70f15b48260a078ff3188591b1bbc9fc', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '21dd15441a799eff7d34b0c9855388cc');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '22821a8bf0269ea3ad4c014581dece37', '5930d39ee1c3b19b0160723ff40c3779', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '22821a8bf0269ea3ad4c014581dece37');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '228a242b3d74e2e5e7c2397dec3b30ab', 'b1ff673ebc7dee772da81fd3e68a5607', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '228a242b3d74e2e5e7c2397dec3b30ab');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '24f5252c902f227626889976e22048fa', '630c2949797479f246573339f4218e57', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '24f5252c902f227626889976e22048fa');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '2746aa1623e13fe3418577eec7951f63', '2f563fdc6b3c716115a57fda77b06789', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '2746aa1623e13fe3418577eec7951f63');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '27a5717d520e152558ad3998340fb064', '2baf4e8462592ad4201df36edd6da1a4', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '27a5717d520e152558ad3998340fb064');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '2ddfcc61cd74b5055ff10a48e53509ab', '252c150ee420f8ef83d349f3d293722d', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '2ddfcc61cd74b5055ff10a48e53509ab');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '313fe601e652184bf3da25a8ddfd51b2', 'c1fd2cce952da1093f3aa062e8ca8d9e', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '313fe601e652184bf3da25a8ddfd51b2');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '331ef9408c05dc755002aaafe00aff00', 'f79da1ccd2add999e2491cce36c89290', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '331ef9408c05dc755002aaafe00aff00');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '3456a98562ab4af0ac1aeeeec23498b4', '70f15b48260a078ff3188591b1bbc9fc', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '3456a98562ab4af0ac1aeeeec23498b4');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '37386f1b3d2380efd1a2c2c793fe5f75', '199e95b1f85d781cd4d04e2cfc08a2ea', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '37386f1b3d2380efd1a2c2c793fe5f75');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '3b689c606cc14b281fb90a6c92d5480f', '61d125a65df54296d6ed381976585cab', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '3b689c606cc14b281fb90a6c92d5480f');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '3d7c3ea860b7437a329a8e6e76286f4d', 'd92db5112c6c67cf11a8e90d9ca79335', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '3d7c3ea860b7437a329a8e6e76286f4d');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '3ee5d9c3d4e83f36e173e842300c4e84', 'aab2dc93c2c0a57e6efa0235b5cdc696', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '3ee5d9c3d4e83f36e173e842300c4e84');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '3f4bac2033e4fcc95d53072dc63ad762', '0afb7e979297c8046dfd7e5839aad8ea', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '3f4bac2033e4fcc95d53072dc63ad762');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '3ff7fa61a2823e5d9babb9fbe6b3ac58', '2baf4e8462592ad4201df36edd6da1a4', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '3ff7fa61a2823e5d9babb9fbe6b3ac58');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '42e2c9138591bfb8e8326f811c4a1341', '880d73ee0a2390331cee75bec070e9ca', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '42e2c9138591bfb8e8326f811c4a1341');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '444f3edb67e96f28db0c7cf0bf9af38f', '5930d39ee1c3b19b0160723ff40c3779', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '444f3edb67e96f28db0c7cf0bf9af38f');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '497f28b8d59df7b460abb11d9a7cf465', '880d73ee0a2390331cee75bec070e9ca', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '497f28b8d59df7b460abb11d9a7cf465');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '4ac088d92bde1f7ec87a12d29fece7be', '199e95b1f85d781cd4d04e2cfc08a2ea', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '4ac088d92bde1f7ec87a12d29fece7be');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '5420f943426ae39f0dcc20a7505c7dcf', '170e6e421f685fd9ef1e517f363972fa', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '5420f943426ae39f0dcc20a7505c7dcf');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '55cd0867568ba491cef3a1905aba4982', 'f79da1ccd2add999e2491cce36c89290', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '55cd0867568ba491cef3a1905aba4982');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '5784a150ed24bfd513d5a8945b97e10b', '5930d39ee1c3b19b0160723ff40c3779', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '5784a150ed24bfd513d5a8945b97e10b');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '5c86dca5343a9e8461c7cc76e91d89e8', '018c2c7192e42695c0306b5dc1756ec4', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '5c86dca5343a9e8461c7cc76e91d89e8');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '60f4e616a660582ae77fdb0fc0e01dac', '7d83120a78a67a76fda51b94adec4801', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '60f4e616a660582ae77fdb0fc0e01dac');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '6217df2e3c3ce118a7edddcfed714a29', '6f7a9d24602f6e528039800c1862e620', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '6217df2e3c3ce118a7edddcfed714a29');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '643db1e7ddaf810e0fa07fda97522c98', '252c150ee420f8ef83d349f3d293722d', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '643db1e7ddaf810e0fa07fda97522c98');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '69255795c9796d4bcb7e242f3229ef35', 'f78146c95201acef6690ecf30a19a03d', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '69255795c9796d4bcb7e242f3229ef35');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '6a4bd66e67e24a0ae2818a2fbcadd480', 'bc0e2aa5b071a67599e4d3a8221750da', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '6a4bd66e67e24a0ae2818a2fbcadd480');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '6c14543249a0f6da16d52b209862fc0c', '3928a382a51cbe0b9dd96754709e3638', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '6c14543249a0f6da16d52b209862fc0c');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '6d35d9a46894d2082bf476a3e217a698', '7d83120a78a67a76fda51b94adec4801', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '6d35d9a46894d2082bf476a3e217a698');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '76765d93d0f15fa9b96fce0176ec6851', 'c1fd2cce952da1093f3aa062e8ca8d9e', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '76765d93d0f15fa9b96fce0176ec6851');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '786f028100246e44971b67aecef7b5e0', 'a8cbf9ee2898e2b2979824d4d6098be2', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '786f028100246e44971b67aecef7b5e0');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '79145bb9c3b0616a9c77aa103cdd6bd5', 'a72680e8bc494c93715698a887740f78', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '79145bb9c3b0616a9c77aa103cdd6bd5');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '7933304bf734de12693e0cee574f56c0', 'de5b8265a3f911eb8230f20ed01ddf38', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '7933304bf734de12693e0cee574f56c0');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '7a6c865a11f7555878689a1346971880', '0bac8a03f16bcce0af6d58c161459e6c', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '7a6c865a11f7555878689a1346971880');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '7cd6e594bc17c8c07e33ea2f3ce69985', '2f563fdc6b3c716115a57fda77b06789', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '7cd6e594bc17c8c07e33ea2f3ce69985');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '7f9c18135ee01d6323d649684eea0b57', 'a8cbf9ee2898e2b2979824d4d6098be2', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '7f9c18135ee01d6323d649684eea0b57');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '847ece09ca50aa5b0105850888962cd9', 'cfd7708b7d3f2b38ef67fdf187dcfdf1', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '847ece09ca50aa5b0105850888962cd9');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '88e30b9928cb70fb52422d076e1b536a', '2ad1a10cdd55777e0acc760e26fc4259', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '88e30b9928cb70fb52422d076e1b536a');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '899fb06ce65f3e791f8b685a437ca744', '6f7a9d24602f6e528039800c1862e620', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '899fb06ce65f3e791f8b685a437ca744');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '8afd82a4d16e6142d298e3ef0f024b85', 'de5b8265a3f911eb8230f20ed01ddf38', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '8afd82a4d16e6142d298e3ef0f024b85');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '8b2c44bc12927d3ade522416d728165d', 'fcdfd962bc93512c1cdee056daad37ef', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '8b2c44bc12927d3ade522416d728165d');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '8eeff1d498016c36227eebffe204776c', 'aab2dc93c2c0a57e6efa0235b5cdc696', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '8eeff1d498016c36227eebffe204776c');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '8f7e51d8376d43232da5402223968a27', '4b115b2d2e7f4b3c027164c2542f764c', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '8f7e51d8376d43232da5402223968a27');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '91e46fb4513aa371f12ee482b3bca9dc', 'fe3a21e5bffa710d207343632197ae40', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '91e46fb4513aa371f12ee482b3bca9dc');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '9287c793d779470a38329bd477244a8b', '70f15b48260a078ff3188591b1bbc9fc', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '9287c793d779470a38329bd477244a8b');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '96bfec2885ad3690669c242a564c80ff', '********************************', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '96bfec2885ad3690669c242a564c80ff');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '99abdefbd43336124b3167c5bc6d9438', 'aab2dc93c2c0a57e6efa0235b5cdc696', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '99abdefbd43336124b3167c5bc6d9438');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '9ab4c94d8f0c12896e9459426678e497', 'f79da1ccd2add999e2491cce36c89290', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '9ab4c94d8f0c12896e9459426678e497');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '9ad879d7bcf70f596541a239be580332', '6f5562a40b28f22788015a32d5e4e1e5', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '9ad879d7bcf70f596541a239be580332');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '9cf36b73abd8d3b11fd788762c708715', 'bda8f353d61b50ae676b02fb62741ca1', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '9cf36b73abd8d3b11fd788762c708715');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'a0ae621f7d71c7c8b60748d212f361f2', 'a8cbf9ee2898e2b2979824d4d6098be2', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'a0ae621f7d71c7c8b60748d212f361f2');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'a1de0495968839c30da87dc79fe58311', '2f563fdc6b3c716115a57fda77b06789', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'a1de0495968839c30da87dc79fe58311');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'a2237ca3118dbd2b04ad831340f5b939', 'c1fd2cce952da1093f3aa062e8ca8d9e', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'a2237ca3118dbd2b04ad831340f5b939');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'a3f67739fc4ae01f40ebcd97122b38b9', '2ad1a10cdd55777e0acc760e26fc4259', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'a3f67739fc4ae01f40ebcd97122b38b9');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'a6c7eea427311939a9bf9912d2f2cb2b', '6f5562a40b28f22788015a32d5e4e1e5', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'a6c7eea427311939a9bf9912d2f2cb2b');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'aaf61f6c907b10a9dc60817c9101cf13', '880d73ee0a2390331cee75bec070e9ca', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'aaf61f6c907b10a9dc60817c9101cf13');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'af1b876f876d115a04cb60017d4f9281', 'b1ff673ebc7dee772da81fd3e68a5607', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'af1b876f876d115a04cb60017d4f9281');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'afa137937ac2a2ee2983f5ca94d5c90d', '6f5562a40b28f22788015a32d5e4e1e5', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'afa137937ac2a2ee2983f5ca94d5c90d');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'b23d70dc4245a8958554373e9d7a5001', '890826492a92e7db24d8865c778a8e38', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'b23d70dc4245a8958554373e9d7a5001');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'b48ae55deaf989bfbe5739c02719a31c', '630c2949797479f246573339f4218e57', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'b48ae55deaf989bfbe5739c02719a31c');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'b5ff22eda500f6726800ae73f566264e', 'fcdfd962bc93512c1cdee056daad37ef', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'b5ff22eda500f6726800ae73f566264e');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'c4fcb521525865b12402c55674a29a12', '1087f2f01567c61ac1baa20a10510c34', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'c4fcb521525865b12402c55674a29a12');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'c5200f370567de85cae5877bece5b03e', '170e6e421f685fd9ef1e517f363972fa', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'c5200f370567de85cae5877bece5b03e');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'cb12d44a740083151cb61d571367d50c', '018c2c7192e42695c0306b5dc1756ec4', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'cb12d44a740083151cb61d571367d50c');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'cd1616e3e4fec36d3b4ba71538ecf05f', 'f78146c95201acef6690ecf30a19a03d', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'cd1616e3e4fec36d3b4ba71538ecf05f');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'cea1b15a14bc2458be462d8fca6b9e1d', 'a72680e8bc494c93715698a887740f78', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'cea1b15a14bc2458be462d8fca6b9e1d');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'd505b2201192b56e722688881a59d3fe', 'a72680e8bc494c93715698a887740f78', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'd505b2201192b56e722688881a59d3fe');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'd6d04948fbe32e5471ac823e18f87495', '********************************', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'd6d04948fbe32e5471ac823e18f87495');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'd8508b87ce69f4962a40f4cc4369b2aa', '7d83120a78a67a76fda51b94adec4801', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'd8508b87ce69f4962a40f4cc4369b2aa');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'db0479f4693d16b4d4713de37de775e2', '890826492a92e7db24d8865c778a8e38', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'db0479f4693d16b4d4713de37de775e2');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'dd4a1524325424e873746701786664cb', '6f7a9d24602f6e528039800c1862e620', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'dd4a1524325424e873746701786664cb');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'ddcafd406324914beee2e8243aca400a', '630c2949797479f246573339f4218e57', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'ddcafd406324914beee2e8243aca400a');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'e2fd243ba143d3de3db3de516593f758', 'de5b8265a3f911eb8230f20ed01ddf38', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'e2fd243ba143d3de3db3de516593f758');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'e43d423545f095bce080d3bf67ecffdb', 'fe3a21e5bffa710d207343632197ae40', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'e43d423545f095bce080d3bf67ecffdb');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'e69c45abf361805f026c2a956671f9aa', '4b115b2d2e7f4b3c027164c2542f764c', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'e69c45abf361805f026c2a956671f9aa');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'e83bcc9be28379a2a433f009a198dad9', '199e95b1f85d781cd4d04e2cfc08a2ea', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'e83bcc9be28379a2a433f009a198dad9');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'e8a47d39a2087071b3cd009c4995e2f1', '170e6e421f685fd9ef1e517f363972fa', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'e8a47d39a2087071b3cd009c4995e2f1');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'f181a51e14f8e83053810f5b987b01c4', 'd92db5112c6c67cf11a8e90d9ca79335', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'f181a51e14f8e83053810f5b987b01c4');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'f2406b1cf6a74d0e18f546f7d8eda334', '2baf4e8462592ad4201df36edd6da1a4', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'f2406b1cf6a74d0e18f546f7d8eda334');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'f2473110c3dddca880f3756311d47ad0', '4b115b2d2e7f4b3c027164c2542f764c', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'f2473110c3dddca880f3756311d47ad0');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'f39ae4fd96eb23c21d5ae51ea8413003', '1087f2f01567c61ac1baa20a10510c34', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'f39ae4fd96eb23c21d5ae51ea8413003');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'f408b2d099bf52ddd33faa67d1e4ba27', 'fcdfd962bc93512c1cdee056daad37ef', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'f408b2d099bf52ddd33faa67d1e4ba27');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'fc238fa3ae0b9dd91ac6d3feca1dc1a4', 'fe3a21e5bffa710d207343632197ae40', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'fc238fa3ae0b9dd91ac6d3feca1dc1a4');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'ff3b8fc71bb3dc823eebd45eada49541', 'cfd7708b7d3f2b38ef67fdf187dcfdf1', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'ff3b8fc71bb3dc823eebd45eada49541');