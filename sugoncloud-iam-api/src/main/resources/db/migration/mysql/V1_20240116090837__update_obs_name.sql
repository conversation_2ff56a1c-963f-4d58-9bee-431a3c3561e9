REPLACE INTO `iam`.`micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`, `license_key`) VALUES ('97a5e050-30f7-40a3-bb10-361f4e0abd77', '对象存储(专业版) OBS', '对象存储(专业版) OBS', 'sugoncloud-obs-api', '2023-12-27 17:17:25', '2024-01-04 15:27:01', 'https://************:30000/obs', '13a38073a36111eb8230f20ed01ddf38', '0', NULL, '2023-12-27 17:17:38', NULL, '0', '41', 'https://***************:34433/obs', '1', '-1');
REPLACE INTO `iam`.`quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('obs', '对象存储(专业版) OBS', '38', '13a38073a36111eb8230f20ed01ddf38');
