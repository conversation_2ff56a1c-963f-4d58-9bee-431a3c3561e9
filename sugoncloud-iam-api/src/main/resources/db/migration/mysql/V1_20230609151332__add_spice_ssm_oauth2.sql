REPLACE INTO `iam`.`globalsettings` (`uuid`, `policy_name`, `policy_document`, `policy_type`, `retained_field`, `policy_display_name`) VALUES ('5abfa0b4-3b92-4524-a997-690f6c81ebdf', 'is_spice', 'true', 'is_spice', '是否启用spice', '是否启用spice');
REPLACE INTO `iam`.`micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`, `license_key`) VALUES ('587eju65-f8r5-55lo-74f5-36fr9r5lq2ssm', '服务治理 SSM', '服务治理 SSM', 'sugoncloud-ssm-api', '2023-03-01 10:42:36', '2023-03-18 10:42:36', 'https://************:30000/ssm', '44c1f40ba59d11eb8230f20ed01ddf38', 0, NULL, NULL, NULL, 0, NULL, 'https://***************:34433/ssm', 1, 'ssmAuthorizationInfo');
REPLACE INTO `iam`.`micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`, `license_key`) VALUES ('fbaa65c1-c88e-11ec-9bba-0894efae434f', '云数据库 DM', '云数据库 DM', 'sugoncloud-dm-api', '2021-04-15 15:45:00', '2022-06-22 16:39:58', 'https://************:30000/dm', 'a3a0adae-c1e1-11eb-a5c2-fac676e41602', 0, NULL, '2023-04-24 09:58:52', NULL, 0, 100, 'https://***************:34433/dm', 1, 'cloudDatabaseAuthorizationInfo');
UPDATE `iam`.`micro_service` SET `license_key` = 'vodAuthorizationInfo' WHERE `id` = 'f22db01f-4914-4bdf-9161-7cc5977edf7d';
REPLACE INTO `iam`.`quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('vod', '视频点播VOD', 34, 'esa09553-6177-441b-981a-9c5ehjhjc62ea8');
REPLACE INTO `iam`.`quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('vod_num', 'vod', '视频总量(个)', '视频使用量(个)');

CREATE TABLE IF NOT EXISTS `user_authorize_settings`
(
    `id`      varchar(64) NOT NULL COMMENT '主键Id',
    `user_id` varchar(64) NOT NULL COMMENT '用户ID',
    `type`    varchar(20) NOT NULL COMMENT '认证模式:UserAuthorizeType',
    `enabled` tinyint(1)  NOT NULL DEFAULT 0 COMMENT '是否启用',
    `scope`   varchar(20)          DEFAULT NULL COMMENT '访问途径：UserAuthorizeScope',
    `extra`   text                 DEFAULT NULL COMMENT '额外参数',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

REPLACE INTO `iam`.`quota_metric`(`name`, `type_name`, `total_name`, `used_name`) VALUES ('bms_nic_num', 'bms', '智能网卡服务器总量(个)', '智能网卡服务器使用量(个)');
REPLACE INTO `iam`.`quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('ssm', '微服务治理SSM', 33, '44c1f40ba59d11eb8230f20ed01ddf38');
REPLACE INTO `iam`.`quota_metric`(`name`, `type_name`, `total_name`, `used_name`) VALUES ('ssm_instance', 'ssm', '网格实例总量(个)', '网格实例使用量(个)');
REPLACE INTO `iam`.`quota_type`(`name`, `description`, `sequence`, `category_id`) VALUES ('sig', '服务网关SIG', 30, '4546d40a-2d8a-11ed-99d5-0894efae434a');
REPLACE INTO `iam`.`quota_metric`(`name`, `type_name`, `total_name`, `used_name`) VALUES ('sig_instance', 'sig', '网关实例总量(个)', '网关实例使用量(个)');
REPLACE INTO `iam`.`quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('dbs', '数据备份DBS', 29, '79369c85-b08d-11ec-bde1-083a88902c92');
REPLACE INTO `iam`.`quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('dbs_disk', 'dbs', '硬盘存储总量(GiB)', '硬盘存储使用量(GiB)');
REPLACE INTO `iam`.`quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('dbs_oss', 'dbs', '对象存储总量(GiB)', '对象存储使用量(GiB)');
REPLACE INTO `iam`.`quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('dm', '云数据库DM', 32, 'a3a0adae-c1e1-11eb-a5c2-fac676e41602');
REPLACE INTO `iam`.`quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('dm_cpu', 'dm', 'cpu总量(个)', 'cpu使用量(个)');
REPLACE INTO `iam`.`quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('dm_ram', 'dm', '内存总量(GiB)', '内存使用量(GiB)');
REPLACE INTO `iam`.`quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('dm_vm', 'dm', '虚拟机总量(个)', '虚拟机使用量(个)');
REPLACE INTO `iam`.`quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('dm_volume', 'dm', '云硬盘总量(GiB)', '云硬盘使用总量(GiB)');
REPLACE INTO `iam`.`quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('ecbs', '实例备份ECBS', 29, '79369c85-b08d-11ec-bde1-083a88902c92');
REPLACE INTO `iam`.`quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('ecbs_data', 'ecbs', '实例备份总量(GiB)', '实例备份使用量(GiB)');

INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '2b1cba372811c79206d3b9f88744198d', '认证管理', '管理员管理-管理员用户-操作-认证管理', 'null', '', null, 'null', '1', '6386e7bf8979fa0461fd8ef6c76ae756', '', '', '', '9', '', '2023-05-23 00:00:00', '/oauth-manage-admin', '', '', '2', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '2b1cba372811c79206d3b9f88744198d');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '90bcd29ac2e29f63bf5071e8e4797dc0', '认证管理', '用户管理-操作-认证管理', 'null', '', null, 'null', '1', 'f1bad45c67da1ae1817ddbc5f0f80215', '', '', '', '9', '', '2023-05-17 10:44:58', '/oauth-manage', '', '', '2', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '90bcd29ac2e29f63bf5071e8e4797dc0');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'a341c8b659ac17587123e4a75773c408', '认证管理', '组织管理-用户管理-操作-认证管理', 'null', '', null, 'null', '1', '7d83120a78a67a76fda51b94adec4801', '', '', '', '9', '', '2023-05-17 16:38:04', '/oauth-manage-depart', '', '', '2', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'a341c8b659ac17587123e4a75773c408');

INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '7bd5d6dba84a76e471cd62ce82b7a7ff', '2b1cba372811c79206d3b9f88744198d', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '7bd5d6dba84a76e471cd62ce82b7a7ff');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'aba7c43f8c5e13d2f99670634c97806a', '90bcd29ac2e29f63bf5071e8e4797dc0', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'aba7c43f8c5e13d2f99670634c97806a');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '215a0875f90a81b5bc8e65fdba149ef6', '90bcd29ac2e29f63bf5071e8e4797dc0', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '215a0875f90a81b5bc8e65fdba149ef6');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '53d35b752063807ab4e6b69c78778a6b', '90bcd29ac2e29f63bf5071e8e4797dc0', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '53d35b752063807ab4e6b69c78778a6b');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '0a2e6028ff6888e1507bb321b8849748', 'a341c8b659ac17587123e4a75773c408', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '0a2e6028ff6888e1507bb321b8849748');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'fdcf5710c8b46b9a94d728bacd8e3a1f', 'a341c8b659ac17587123e4a75773c408', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'fdcf5710c8b46b9a94d728bacd8e3a1f');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '0c2e136d2451ea640ae2d18e590c10b2', 'a341c8b659ac17587123e4a75773c408', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '0c2e136d2451ea640ae2d18e590c10b2');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '0d106ddb6be47754893dcddae244602d', 'a341c8b659ac17587123e4a75773c408', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '0d106ddb6be47754893dcddae244602d');

REPLACE INTO `iam`.`secret_key` (`id`, `access_key`, `secret_key`, `enabled`, `description`, `create_at`, `user_id`) VALUES ('25605370-ef07-11ed-b3ae-0894efae434a', 'inner-ak', '33BE8B87D58FA954C3CA73FBDB8D2C0BEA0A5B8A393B8057CEAD88F77AE84DB340D198F686555F236ED9FC8D86741564', 1, '内置inner', '2023-05-10 15:49:08', 'e84f98be630f475c9ee127a38e3eedac');
