CREATE TABLE IF NOT EXISTS `seeds`  (
                          `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                          `private_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                          `seed_len` int NULL DEFAULT NULL,
                          `seed` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                          `code_len` int NULL DEFAULT NULL,
                          `code_time` int NULL DEFAULT NULL,
                          PRIMARY KEY (`code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;