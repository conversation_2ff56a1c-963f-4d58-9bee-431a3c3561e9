REPLACE INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('29e3d052-2487-4e14-be59-40116b7a0bf5', '云堡垒机基础版 CBH', '云堡垒机基础版 CBH', 'sugoncloud-cbh-api', '2021-08-12 16:35:34', '2022-06-22 16:39:57', 'https://************:30000/cbh/#/cbh-manage', 'd04f330d-7353-476d-afd8-72e7a8f4002e', '0', NULL, '2021-08-12 16:36:03', NULL, '0', '50', 'https://***************:34433/cbh/#/cbh-manage', '1');
REPLACE INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('908e46de-8e5f-43fd-bdf9-4054627237e1', '云堡垒机高级版 USM', '云堡垒机高级版 USM', 'sugoncloud-cbh-api', '2021-08-12 16:35:34', '2022-06-22 16:39:57', 'https://************:30000/cbh/#/cbh-advanced-manage', 'd04f330d-7353-476d-afd8-72e7a8f4002e', '0', NULL, '2021-08-12 16:36:03', NULL, '0', '50', 'https://***************:34433/cbh/#/cbh-advanced-manage', '1');
UPDATE `micro_service` SET `name`='网络ACL ACL' WHERE `id`='a9f37912-fd01-11ec-b241-0894efae434a';
UPDATE quota_type SET description='云堡垒机配额' WHERE `name`='cbh';
REPLACE INTO `quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('cbh_volume', 'cbh', '云硬盘总量(GiB)', '云硬盘使用总量(GiB)');
