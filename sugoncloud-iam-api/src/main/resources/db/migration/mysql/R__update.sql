# 黑名单微服务sql
CREATE TABLE IF NOT EXISTS `blacklist_micro_service` (
       `id` varchar(64) NOT NULL COMMENT '主键，菜单主键，或者微服务micro主键，微服务类型category主键',
       `type` varchar(64) DEFAULT NULL COMMENT '菜单代表menu，微服务micro，微服务类型category',
       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DELIMITER $$
DROP PROCEDURE IF EXISTS `add_pxetime_column` $$
CREATE PROCEDURE add_pxetime_column()
BEGIN
	IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema='iam' AND table_name='quota_type' AND column_name='micro_id')
	THEN
ALTER TABLE `quota_type` ADD COLUMN `micro_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属微服务id' AFTER `category_id`;
END IF;
END $$
DELIMITER ;
CALL add_pxetime_column;

# 黑名单微服务sql

DELIMITER $$
DROP PROCEDURE IF EXISTS `add_pxetime_column` $$
CREATE PROCEDURE add_pxetime_column()
BEGIN
	IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema='iam' AND table_name='blacklist_micro_service' AND column_name='alias')
	THEN
ALTER TABLE `blacklist_micro_service` ADD COLUMN `alias` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称' AFTER `type`;
END IF;
	IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema='iam' AND table_name='blacklist_micro_service' AND column_name='extra')
	THEN
ALTER TABLE `blacklist_micro_service` ADD COLUMN `extra` varchar(255) NULL COMMENT '扩展字段' AFTER `alias`;
END IF;
END $$
DELIMITER ;
CALL add_pxetime_column;

CREATE TABLE IF NOT EXISTS `user_access` (
    `id` varchar(64) NOT NULL COMMENT '主键ID',
    `user_id` VARCHAR(64) NOT NULL COMMENT '用户id',
    `start_date` VARCHAR(64) DEFAULT NULL COMMENT '开始时间',
    `end_date` VARCHAR(64) DEFAULT NULL COMMENT '结束时间',
    `limit_flag` tinyint(1) DEFAULT NULL COMMENT '限制时间点标识',
    `limit_time` VARCHAR(2000) DEFAULT NULL COMMENT '限制时间点',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
    PRIMARY KEY (`id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT;


DELIMITER $$
DROP PROCEDURE IF EXISTS `add_pxetime_column` $$
CREATE PROCEDURE add_pxetime_column()
BEGIN
	IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema='iam' AND table_name='project' AND column_name='type')
	THEN
ALTER TABLE `project` ADD COLUMN `type` VARCHAR(40) NULL DEFAULT NULL COMMENT '项目类型：com.sugon.cloud.iam.common.constants.ProjectType' AFTER `modify_time`;
END IF;
	IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema='iam' AND table_name='project' AND column_name='start_time')
	THEN
ALTER TABLE `project` ADD COLUMN `start_time` DATE NULL DEFAULT NULL COMMENT '试用开始时间' AFTER `type`;
END IF;
	IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema='iam' AND table_name='project' AND column_name='end_time')
	THEN
ALTER TABLE `project` ADD COLUMN `end_time`DATE NULL DEFAULT NULL COMMENT '试用结束时间' AFTER `start_time`;
END IF;
	IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema='iam' AND table_name='project' AND column_name='meter_types')
	THEN
ALTER TABLE `project` ADD COLUMN `meter_types` VARCHAR(255) NULL DEFAULT NULL COMMENT '计费方式计费类型（统一计费：contract_meter_unification | 合同计费：contract_meter_contract）' AFTER `end_time`;
END IF;
	IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema='iam' AND table_name='project' AND column_name='task_status')
	THEN
ALTER TABLE `project` ADD COLUMN `task_status` VARCHAR(40) NULL DEFAULT NULL COMMENT '任务状态（转正申请中:FORMAL_APPLY|延期申请中:EXTENSION_APPLY）' AFTER `meter_types`;
END IF;
	IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema='iam' AND table_name='project' AND column_name='operation_approve_type')
	THEN
ALTER TABLE `project` ADD COLUMN `operation_approve_type` VARCHAR(255) NULL DEFAULT NULL COMMENT '自服务审批类型，REQUIRE：审批，NO_REQUIRE：不审批';
END IF;
END $$
DELIMITER ;
CALL add_pxetime_column;

DELIMITER $$
DROP PROCEDURE IF EXISTS `add_pxetime_column` $$
CREATE PROCEDURE add_pxetime_column()
BEGIN
	IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema='iam' AND table_name='region' AND column_name='type')
	THEN
ALTER TABLE `region` ADD COLUMN `type` VARCHAR(40) NULL DEFAULT 0 COMMENT '类型' AFTER `extra`;
END IF;
END $$
DELIMITER ;
CALL add_pxetime_column;

REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('007d91f24d9078db636ba7f7c5e7e9ed', 'MENU', '计费管理-申请审批-资源补录-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('0a33bf76d21f347681e7774a72c80ddc', 'MENU', '计费管理-申请审批-资源申请-配置-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('1307eb18c8075b77e30cf090dbc4c81b', 'MENU', '计费管理-申请审批-资源补录-详情-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('1a9e8cf86ab54f0ad05d8165fff19877', 'MENU', '计费管理-计费报表-云服务报表-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('1bf33cf8c4f470ac2c3cc8d00d7683f0', 'MENU', '计费管理-服务目录-服务目录绑定-绑定-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('26a34f1e2e3251b22f58be654c4c4615', 'MENU', '计费管理-服务目录管理-新建-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('27e4b2bf6da03c79d528e1cc7707f199', 'MENU', '计费管理-服务目录-服务目录管理-详情-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('28e6b5850aef4cac3dbbecd191ebdfc5', 'MENU', '计费管理-配置管理-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('2983817f45895b9c8318383e49850677', 'MENU', '计费管理-申请审批-资源申请-审批-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('4889c113a71df8bb546e5d947ae8e7bc', 'MENU', '计费管理-申请审批-资源配置-列表-操作-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('4c82ef2cac35610f616471fcb6987176', 'MENU', '计费管理-合同管理-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('4ed71936d5a82a4e8b95083582970bc8', 'MENU', '计费管理-申请审批-资源申请-详情-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('5010894919303c330403deb121c103fa', 'MENU', '计费管理-申请审批-资源变更-编辑-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('5eaf1df8473eff7be01a3a99df5d3550', 'MENU', '计费管理-申请审批-资源回收-审批-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('601c1be9622d46adfedf6b95861e464a', 'MENU', '计费管理-服务目录-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('6fd0da013b547aac8c228990c235654f', 'MENU', '计费管理-申请审批-资源回收-配置-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('72548d30be3a25ca7722f9119776f0cd', 'MENU', '计费管理-申请审批-资源配置-详情-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('7c7e4d541e3f1cd88bd16ef99115db8b', 'MENU', '计费管理-申请审批-资源申请-编辑-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('7d5b94f10f8ec9a1f4193b202817d54e', 'MENU', '计费管理-配置管理-计费配置-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('7d73c6c7a6b817348578e118c4163521', 'MENU', '计费管理-服务目录管理-年度备份-删除-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('93ba9ce7dc65e116c973730ada1c7647', 'MENU', '计费管理-配置管理-流程配置-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('9ab82d3c596b84112dce00d592324419', 'MENU', '计费管理-申请审批-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('9bc9a931516925660e9723905a66e108', 'MENU', '计费管理-申请审批-资源补录-编辑-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('9c02af47152ee5f5e65e7fa45c18918d', 'MENU', '计费管理-申请审批-资源申请-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('9d84b1cf29094e6144be72c5ddee0c8d', 'MENU', '计费管理-申请审批-资源变更-配置-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('a2dce0a4854d97a55064150c06a57748', 'MENU', '计费管理-配置管理-审批组-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('a644e306db7ec6bbd4fa47a7849bc77e', 'MENU', '计费管理-配置管理-流程配置-详情-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('aed00fbecd66446fd942b67f059e04d6', 'MENU', '计费管理-配置管理-审批组-详情-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('b8aab1ba80c1d671db7a57ff6ee48b1d', 'MENU', '计费管理-申请审批-资源变更-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('c1a6a9529a2d7d3f36930d76a56d74f7', 'MENU', '计费管理-服务目录-服务目录绑定-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('c1e5606961bb19ec239ed01899a75875', 'MENU', '计费管理-申请审批-资源补录-审批-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('c2cde9e595c0fc3526714961c5e649bf', 'MENU', '计费管理-申请审批-资源回收-详情-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('c4dc21e2e6af549320e75dbebf1f9a77', 'MENU', '计费管理-申请审批-资源配置-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('c52dfb51562a721fade6b8fe5b2d9d9e', 'MENU', '计费管理-服务目录管理-备份-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('cd07a6fca98079e74bcb2afbd00641b6', 'MENU', '计费管理-申请审批-资源变更-审批-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('ce594fccca436de73ea8e8880d1dca61', 'MENU', '计费管理-计费报表-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('cec4cc935ea209daee6ef01f6b015f92', 'MENU', '计费管理-合同管理-详情-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('cf521183054c5edfa25b0dc01d27dbe9', 'MENU', '计费管理-申请审批-资源变更-详情-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('d44665e33eb7b58ddf83e88e681f016f', 'MENU', '计费管理-服务目录管理-配置-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('dc70d6b89027cb3ab1a7724b13fc7832', 'MENU', '计费管理-服务目录-服务目录池管理-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('e727fa97d6f87a665da6f2e14e64188a', 'MENU', '计费管理-申请审批-资源回收-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('f0ddcf85ad3124dfd921743df34b84ba', 'MENU', '计费管理-申请审批-资源回收-编辑-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('f599401d811407f8afaa60d74bddf6a0', 'MENU', '计费管理-计费报表-计费明细-operation', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('f7925f0a03005b471a45f1c129f62a7c', 'MENU', '计费管理-服务目录-服务目录管理-operation', 'operations');

REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('a22fcec448c5e36ed51f77b42a4c127f', 'MENU', '项目管理-操作-开启/关闭审批-IAM', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('dd9682a6c4a0706f92cf1cf0687e1b8f', 'MENU', '项目管理-详情-合同列表tab-IAM', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('6ec1546bd903149787aa15ccacb8204d', 'MENU', '项目管理-详情-合同管理-绑定合同-IAM', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('1f9578c5a13349dbffc4d4e16b9fa819', 'MENU', '项目管理-详情-合同管理-批量取消绑定-IAM', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('fdebe8b235e538445050bbf9d7e00a3b', 'MENU', '全局菜单-运营-计费', 'operations');
REPLACE INTO `blacklist_micro_service` (`id`, `type`, `alias`, `extra`) VALUES ('52f2f86b-8423-11ee-9090-02420a43c727', 'MICRO', '计费管理', 'operations');
DELETE FROM `blacklist_micro_service` where `id` = 'd3a89727-26a7-11ee-9065-0242bd4205e4';

-- 默认策略修改创建人
UPDATE `strategy` SET `creat_by` = 'inner' WHERE `id` = '3c1a26485af17a0f39a269f205a67c82';
UPDATE `strategy` SET `creat_by` = 'inner' WHERE `id` = 'bcbef30d017e65ed5a5c20c19bce98e8';

UPDATE `iam_role` SET `name` = '默认角色', `dept_id` = NULL, `description` = '系统默认角色,所有用户均可关联', `type` = 'default', `create_time` = NULL, `modify_time` = NULL WHERE `id` = '23546c7ab11e11ecbde1083a88902c92';

REPLACE INTO `project` (`id`, `name`, `extra`, `description`, `enabled`, `domain_id`, `parent_id`, `is_domain`, `dept_id`, `alias`, `create_time`, `modify_time`, `type`, `start_time`, `end_time`, `meter_types`, `task_status`, `operation_approve_type`) VALUES ('admin-inner-project', '默认项目', NULL, '', 1, 'default', 'default', 0, 'admin', '默认项目', '2024-07-10 01:47:47', '2024-07-10 01:47:47', 'formal', NULL, NULL, 'contract_meter_unification', NULL, NULL);

REPLACE INTO `department` (`id`, `name`, `description`, `create_time`, `modify_time`, `parent_id`, `domain_id`, `level`) VALUES ('admin', '默认组织', '默认组织', '2024-07-12 02:59:46', '2024-07-12 02:59:46', NULL, 'admin', 0);

UPDATE `user` SET dept_id = 'admin' WHERE name = 'admin';

-- 保密默认用户、角色
INSERT INTO `user` (`id`, `name`, `alias`, `extra`, `enabled`, `default_project_id`, `created_at`, `modify_time`, `last_active_at`, `domain_id`, `password_expires_at`, `password`, `email`, `phone`, `type`, `dept_id`, `expired`, `allow_ip`, `last_login_ip`, `iv`, `hash`, `hash_role`, `last_password_time`) VALUES ('66d31a77fa004dc3bfb2d808157b4976', 'secauditor', '审计管理员', '{\"passwordEncode\":false,\"description\":\"审计管理员\"}', 1, '9fd3e5e1090d4bc4b5a42e0538c1ea79', '2024-07-05 11:03:45', '2024-07-05 11:03:45', NULL, '9fd3e5e1090d4bc4b5a42e0538c1ea79', NULL, '$2a$10$SI7GESgRKz8NUGXjpexEiebFM4FkNdZ5bbnQ5r10CF4HSz4aY8pLS', '<EMAIL>', '17700000000', 'sec_auditor', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-07-05 11:03:45') ON DUPLICATE KEY UPDATE id=id;
INSERT INTO `user` (`id`, `name`, `alias`, `extra`, `enabled`, `default_project_id`, `created_at`, `modify_time`, `last_active_at`, `domain_id`, `password_expires_at`, `password`, `email`, `phone`, `type`, `dept_id`, `expired`, `allow_ip`, `last_login_ip`, `iv`, `hash`, `hash_role`, `last_password_time`) VALUES ('35b6a369aefc48099a4151f55963e8ed', 'secadmin', '安全管理员', '{\"passwordEncode\":false,\"description\":\"安全管理员\"}', 1, 'b5a2519194454eb7b8f0b5730b740f43', '2024-07-05 11:02:43', '2024-07-05 11:02:43', NULL, 'b5a2519194454eb7b8f0b5730b740f43', NULL, '$2a$10$gZ29wC4RTrFTdxL0zTosAu1EkJ.invPhf7SMRi4lLwyCPF8F77nFi', '<EMAIL>', '19900000000', 'sec_admin', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-07-05 11:02:43') ON DUPLICATE KEY UPDATE id=id;
INSERT INTO `user` (`id`, `name`, `alias`, `extra`, `enabled`, `default_project_id`, `created_at`, `modify_time`, `last_active_at`, `domain_id`, `password_expires_at`, `password`, `email`, `phone`, `type`, `dept_id`, `expired`, `allow_ip`, `last_login_ip`, `iv`, `hash`, `hash_role`, `last_password_time`) VALUES ('00c65f4ee32f441095ce595e7fc9bb3c', 'sysadmin', '系统管理员', '{\"passwordEncode\":false,\"description\":\"系统管理员\"}', 1, '7966c91322784ea089cfe22849405ac5', '2024-07-05 11:01:40', '2024-07-05 11:01:40', NULL, '7966c91322784ea089cfe22849405ac5', NULL, '$2a$10$angvXHI8vLYtBDy4h.IOw.OKq8WlOV8ok9kbL7Qr.GB0g7GlZW3Iu', '<EMAIL>', '18899999999', 'sys_admin', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-07-05 11:01:40') ON DUPLICATE KEY UPDATE id=id;
INSERT INTO `project` (`id`, `name`, `extra`, `description`, `enabled`, `domain_id`, `parent_id`, `is_domain`, `dept_id`, `alias`, `create_time`, `modify_time`, `type`, `start_time`, `end_time`, `meter_types`, `task_status`, `operation_approve_type`) VALUES ('9fd3e5e1090d4bc4b5a42e0538c1ea79', 'secauditor_project', NULL, NULL, 1, '<<keystone.domain.root>>', NULL, 1, NULL, 'secauditor_project', '2024-07-05 11:03:45', '2024-07-05 11:03:45', NULL, NULL, NULL, NULL, NULL, NULL) ON DUPLICATE KEY UPDATE id=id;
INSERT INTO `project` (`id`, `name`, `extra`, `description`, `enabled`, `domain_id`, `parent_id`, `is_domain`, `dept_id`, `alias`, `create_time`, `modify_time`, `type`, `start_time`, `end_time`, `meter_types`, `task_status`, `operation_approve_type`) VALUES ('b5a2519194454eb7b8f0b5730b740f43', 'secadmin_project', NULL, NULL, 1, '<<keystone.domain.root>>', NULL, 1, NULL, 'secadmin_project', '2024-07-05 11:02:43', '2024-07-05 11:02:43', NULL, NULL, NULL, NULL, NULL, NULL) ON DUPLICATE KEY UPDATE id=id;
INSERT INTO `project` (`id`, `name`, `extra`, `description`, `enabled`, `domain_id`, `parent_id`, `is_domain`, `dept_id`, `alias`, `create_time`, `modify_time`, `type`, `start_time`, `end_time`, `meter_types`, `task_status`, `operation_approve_type`) VALUES ('7966c91322784ea089cfe22849405ac5', 'sysadmin_project', NULL, NULL, 1, '<<keystone.domain.root>>', NULL, 1, NULL, 'sysadmin_project', '2024-07-05 11:01:40', '2024-07-05 11:01:40', NULL, NULL, NULL, NULL, NULL, NULL) ON DUPLICATE KEY UPDATE id=id;
INSERT INTO `iam_role` (`id`, `name`, `dept_id`, `description`, `type`, `create_time`, `modify_time`) VALUES ('cef0a66db29141ee90cc8dacb51279a4', 'secauditor', NULL, '系统审计管理员', 'sec_auditor', '2024-07-02 09:55:19', '2024-07-02 09:55:19') ON DUPLICATE KEY UPDATE id=id;
INSERT INTO `iam_role` (`id`, `name`, `dept_id`, `description`, `type`, `create_time`, `modify_time`) VALUES ('d2cc060a9f53422aa7160c0d516a7292', 'secadmin', NULL, '系统安全管理员', 'sec_admin', '2024-07-02 09:37:30', '2024-07-02 09:37:30') ON DUPLICATE KEY UPDATE id=id;
INSERT INTO `iam_role` (`id`, `name`, `dept_id`, `description`, `type`, `create_time`, `modify_time`) VALUES ('53ce26c3a0704d0994e006b53a92ffbf', 'sysadmin', NULL, '系统管理员', 'sys_admin', '2024-07-02 09:29:41', '2024-07-02 09:29:41') ON DUPLICATE KEY UPDATE id=id;
INSERT INTO `iam_role` (`id`, `name`, `dept_id`, `description`, `type`, `create_time`, `modify_time`) VALUES ('cd00062036704277b6c2fc7573d8d596', 'orgsecauditor', NULL, '组织审计管理员', 'org_secauditor', '2024-07-01 10:46:23', '2024-07-01 10:46:23') ON DUPLICATE KEY UPDATE id=id;
INSERT INTO `iam_role` (`id`, `name`, `dept_id`, `description`, `type`, `create_time`, `modify_time`) VALUES ('1c404380721a4b6eb1a7dc7cb26cc49e', 'orgsecadmin', NULL, '组织安全管理员', 'org_secadmin', '2024-07-01 10:45:37', '2024-07-01 10:45:37') ON DUPLICATE KEY UPDATE id=id;
INSERT INTO `iam_role` (`id`, `name`, `dept_id`, `description`, `type`, `create_time`, `modify_time`) VALUES ('c5afe573c18f4ee4a5c7a5976bbce5f1', 'orgsysadmin', NULL, '组织系统管理员', 'org_sysadmin', '2024-07-01 10:45:16', '2024-07-01 10:45:16') ON DUPLICATE KEY UPDATE id=id;
INSERT INTO `iam_role` (`id`, `name`, `dept_id`, `description`, `type`, `create_time`, `modify_time`) VALUES ('11bd27a621044873a2eca5efb7220a11', '普通用户角色', NULL, '普通用户角色', 'common', '2024-07-01 10:45:16', '2024-07-01 10:45:16') ON DUPLICATE KEY UPDATE id=id;
INSERT INTO `user_role_project_mapping` (`id`, `user_id`, `role_id`, `dept_id`, `project_id`) VALUES ('35a1ee1a4d6421c89761994212358344', '66d31a77fa004dc3bfb2d808157b4976', 'cef0a66db29141ee90cc8dacb51279a4', NULL, NULL) ON DUPLICATE KEY UPDATE id=id;
INSERT INTO `user_role_project_mapping` (`id`, `user_id`, `role_id`, `dept_id`, `project_id`) VALUES ('efd1cd807cc61bef2a2bd35c3c5b5ade', '35b6a369aefc48099a4151f55963e8ed', 'd2cc060a9f53422aa7160c0d516a7292', NULL, NULL) ON DUPLICATE KEY UPDATE id=id;
INSERT INTO `user_role_project_mapping` (`id`, `user_id`, `role_id`, `dept_id`, `project_id`) VALUES ('2a8e008101b31e28b841bcbf31fa0f78', '00c65f4ee32f441095ce595e7fc9bb3c', '53ce26c3a0704d0994e006b53a92ffbf', NULL, NULL) ON DUPLICATE KEY UPDATE id=id;
REPLACE INTO `assignment` (`type`, `actor_id`, `target_id`, `role_id`, `inherited`) VALUES ('UserProject', '00c65f4ee32f441095ce595e7fc9bb3c', '7966c91322784ea089cfe22849405ac5', '8e5bd5e03d2243319887d650976200b9', 0);
REPLACE INTO `assignment` (`type`, `actor_id`, `target_id`, `role_id`, `inherited`) VALUES ('UserProject', '35b6a369aefc48099a4151f55963e8ed', 'b5a2519194454eb7b8f0b5730b740f43', '8e5bd5e03d2243319887d650976200b9', 0);
