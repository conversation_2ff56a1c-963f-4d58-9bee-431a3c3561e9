INSERT INTO micro_service_category (`id`, `name`, `description`, `create_time`, `modify_time`) VALUES ('dce92741-1d3b-43bc-9e51-ce21b60c9d4a', '大数据治理与开发', '大数据治理与开发', '2021-09-24 15:50:34', '2021-09-24 15:50:37');

INSERT INTO micro_service_category (`id`, `name`, `description`, `create_time`, `modify_time`) VALUES ('1ed139cd-0d6e-43e1-98c5-74b478d3f7f3', '大数据应用', '大数据应用', '2021-09-24 15:50:34', '2021-09-24 15:50:37');

UPDATE micro_service_category SET `name` = '大数据计算', `description` = '大数据计算', `create_time` = '2021-07-10 11:51:30', `modify_time` = '2021-09-24 15:51:30' WHERE `id` = '54df68e1a36111eb8230f20ed01ddf38';


UPDATE micro_service set name='云堡垒机 CBH' where id='29e3d052-2487-4e14-be59-40116b7a0bf5';
UPDATE micro_service set name='云数据库 PostgreSQL' where id='31069c74-0477-11ec-aedb-1a9678e015b1';
UPDATE micro_service set name='云数据库 TiDB' where id='375744d6-0477-11ec-aedb-1a9678e015b1';
UPDATE micro_service set name='主机安全 HSS' where id='75ae3ab8-355f-466b-ab54-2bacce193ae5';
UPDATE micro_service set name='云数据库 MySQL' where id='b4387ecd-c1e1-11eb-a5c2-fac676e41602';


INSERT INTO micro_service (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`) VALUES ('e3bb9321-cd6e-42d0-8940-d5f41da2e686', '数据开发 DDS', '数据开发 DDS', 'sugoncloud-dds-api', '2021-09-24 10:24:56', '2021-09-24 10:40:41', 'http://***********:9082/', 'dce92741-1d3b-43bc-9e51-ce21b60c9d4a', 0, NULL, NULL, NULL);


INSERT INTO micro_service (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`) VALUES ('cdcefce3-a9f8-4f71-8e4d-c2513c2ad915', '数据治理中心 DGS', '数据治理中心 DGS', 'sugoncloud-dds-api', '2021-09-24 10:24:56', '2021-09-24 10:40:41', 'http://***********:9085/', 'dce92741-1d3b-43bc-9e51-ce21b60c9d4a', 0, NULL, NULL, NULL);

INSERT INTO micro_service (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`) VALUES ('a0010200-1ab9-4320-bac6-83110fcf8876', '数据资源目录 DRDS', '数据资源目录 DRDS', 'sugoncloud-dds-api', '2021-09-24 10:24:56', '2021-09-24 10:40:41', 'http://***********:9084/', 'dce92741-1d3b-43bc-9e51-ce21b60c9d4a', 0, NULL, NULL, NULL);


INSERT INTO micro_service (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`) VALUES ('20b3becc-ebd3-4f78-b628-55238ba8e883', '数据服务 DSS', '数据服务 DSS', 'sugoncloud-dds-api', '2021-09-24 10:24:56', '2021-09-24 10:40:41', 'http://***********:9086/', 'dce92741-1d3b-43bc-9e51-ce21b60c9d4a', 0, NULL, NULL, NULL);

INSERT INTO micro_service (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`) VALUES ('22a84290-97bb-4378-882e-4bb869a8de9d', '数据标签 DLS', '数据标签 DLS', 'sugoncloud-dds-api', '2021-09-24 10:24:56', '2021-09-24 10:40:41', 'http://***********:9088/', '1ed139cd-0d6e-43e1-98c5-74b478d3f7f3', 0, NULL, NULL, NULL);

INSERT INTO micro_service (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`) VALUES ('e87926e9-e56b-4854-9694-ce671a27ed99', '数据可视化 DAV （待上线）', '数据可视化 DAV （待上线）', 'sugoncloud-dds-api', '2021-09-24 10:24:56', '2021-09-24 10:40:41', '', '1ed139cd-0d6e-43e1-98c5-74b478d3f7f3', 0, NULL, NULL, NULL);
-- 安全
INSERT INTO micro_service (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`) VALUES ('1ec90e83-206d-4a1d-afe0-d58c8f637390', '病毒查杀 VKS', '病毒查杀 VKS', 'sugoncloud-security-api', '2021-08-12 16:35:34', '2021-09-23 10:40:41', 'http://************:30533/#/virus-kill', 'd04f330d-7353-476d-afd8-72e7a8f4002e', 0, NULL, '2021-08-12 16:36:03', NULL);

INSERT INTO micro_service (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`) VALUES ('f0865f60-d82e-4ba8-9dc6-713025c06761', '网马查杀 TKS', '网马查杀 TKS', 'sugoncloud-security-api', '2021-08-12 16:35:34', '2021-09-23 10:40:41', 'http://************:30533/#/network-trojan-killing', 'd04f330d-7353-476d-afd8-72e7a8f4002e', 0, NULL, '2021-08-12 16:36:03', NULL);

INSERT INTO micro_service (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`) VALUES ('4ef88d6c-0ed9-48ed-b64e-befdb8329b8b', '微隔离 MIS', '微隔离 MIS', 'sugoncloud-security-api', '2021-08-12 16:35:34', '2021-09-23 10:40:41', 'http://************:30533/#/net-filter', 'd04f330d-7353-476d-afd8-72e7a8f4002e', 0, NULL, '2021-08-12 16:36:03', NULL);

-- 调整类别顺序
UPDATE micro_service_category SET `modify_time` = '2021-07-05 11:51:30' WHERE `id` = '4afee060a36111eb8230f20ed01ddf38';
UPDATE micro_service_category SET  `modify_time` = '2021-07-05 11:51:37' WHERE `id` = 'ea209553-6177-441b-981a-9c5e40c62ea8';
UPDATE micro_service_category SET `modify_time` = '2021-07-05 11:51:38' WHERE `id` = '13a38073a36111eb8230f20ed01ddf38';