ALTER TABLE micro_service_category ADD COLUMN type VARCHAR(255) DEFAULT 'inner' COMMENT '类型 inner:内部 costom:自定义';

ALTER TABLE micro_service ADD COLUMN type VARCHAR(50) DEFAULT 'inner' COMMENT '类型inner:内部,costom:自定义';

ALTER TABLE micro_service ADD COLUMN open_mode VARCHAR(50) COMMENT '打开方式Internal:内部加载,external:外部跳转';

REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('apt_cpu', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('apt_ram', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('apt_volume', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('bms_nic_num', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('bms_num', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('cbh_cpu', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('cbh_ram', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('cbh_vm', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('cce_cpu', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('cce_ram', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('cce_volume', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('cfw_cpu', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('cfw_ram', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('dbs_disk', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('dbs_oss', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('dc_v2_vm', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('dc_vm', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('dm_cpu', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('dm_ram', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('dm_volume', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('ecbs_data', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('ecs_cpu', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('ecs_dcu', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('ecs_ram', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('ecs_system_disk', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('emr_cpu', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('emr_ram', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('emr_vm', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('emr_volume', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('er_vm', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('es_cpu', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('es_ram', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('es_volume', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('evs_capacity', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('evs_snapshot', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('floatip_num', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('hss_cwpp_ag_workload', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('hss_cwpp_ag_workstation', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('hss_pc', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('hss_server', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('hss_server_web', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('kafka_cpu', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('kafka_ram', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('kafka_volume', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('mongodb_cpu', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('mongodb_ram', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('mongodb_volume', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('mysql_cpu', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('mysql_ram', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('mysql_volume', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('nat_v2', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('pgsql_cpu', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('pgsql_ram', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('pgsql_volume', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('prom_cpu', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('prom_ram', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('prom_volume', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('ras_cpu', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('ras_ram', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('rbs_cpu', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('rbs_ram', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('rbs_volume', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('rcce_cpu', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('rcce_ram', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('rcce_volume', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('redis_cpu', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('redis_ram', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('redis_volume', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('secs_num', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('sfs_cpu', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('sfs_ram', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('sfs_vm', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('sfs_volume', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('sig_instance', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('ssm_instance', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('usm_cpu', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('usm_ram', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('usm_volume', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('vdb_cpu', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('vdb_ram', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('vdb_volume', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('ver_cpu', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('ver_ram', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('ver_volume', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('vod_num', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('vpn_cpu', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('vpn_ram', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('vpn_vm', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('waf_cpu', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('waf_ram', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('waf_volume', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('wpt_cpu', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('wpt_ram', 'admin', 999999999,999999999, 'RegionOne');


REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('apt_cpu', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('apt_ram', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('apt_volume', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('bms_nic_num', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('bms_num', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('cbh_cpu', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('cbh_ram', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('cbh_vm', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('cce_cpu', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('cce_ram', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('cce_volume', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('cfw_cpu', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('cfw_ram', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('dbs_disk', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('dbs_oss', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('dc_v2_vm', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('dc_vm', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('dm_cpu', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('dm_ram', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('dm_volume', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('ecbs_data', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('ecs_cpu', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('ecs_dcu', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('ecs_ram', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('ecs_system_disk', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('emr_cpu', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('emr_ram', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('emr_vm', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('emr_volume', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('er_vm', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('es_cpu', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('es_ram', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('es_volume', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('evs_capacity', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('evs_snapshot', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('floatip_num', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('hss_cwpp_ag_workload', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('hss_cwpp_ag_workstation', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('hss_pc', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('hss_server', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('hss_server_web', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('kafka_cpu', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('kafka_ram', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('kafka_volume', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('mongodb_cpu', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('mongodb_ram', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('mongodb_volume', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('mysql_cpu', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('mysql_ram', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('mysql_volume', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('nat_v2', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('pgsql_cpu', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('pgsql_ram', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('pgsql_volume', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('prom_cpu', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('prom_ram', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('prom_volume', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('ras_cpu', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('ras_ram', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('rbs_cpu', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('rbs_ram', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('rbs_volume', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('rcce_cpu', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('rcce_ram', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('rcce_volume', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('redis_cpu', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('redis_ram', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('redis_volume', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('secs_num', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('sfs_cpu', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('sfs_ram', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('sfs_vm', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('sfs_volume', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('sig_instance', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('ssm_instance', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('usm_cpu', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('usm_ram', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('usm_volume', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('vdb_cpu', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('vdb_ram', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('vdb_volume', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('ver_cpu', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('ver_ram', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('ver_volume', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('vod_num', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('vpn_cpu', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('vpn_ram', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('vpn_vm', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('waf_cpu', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('waf_ram', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('waf_volume', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('wpt_cpu', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('wpt_ram', 'admin-inner-project', 999999999,0, 'RegionOne');