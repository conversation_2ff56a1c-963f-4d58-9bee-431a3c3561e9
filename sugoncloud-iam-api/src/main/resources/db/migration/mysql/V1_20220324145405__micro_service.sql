ALTER TABLE `micro_service`
    ADD COLUMN `need_policy` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否需要配置策略' AFTER `public_link`;
DELETE FROM micro_service_category WHERE `id` IN ('670ea69d-ab1c-11ec-9548-0894efae434a', 'a2423457-ab1c-11ec-9548-0894efae434a');
INSERT INTO `micro_service_category` (`id`, `name`, `description`, `create_time`, `modify_time`, `order`, `nav_hidden`) VALUES ('670ea69d-ab1c-11ec-9548-0894efae434a', '运维管理', '运维管理', '2022-03-24 10:46:34', '2022-03-24 10:46:34', 21, 1);
INSERT INTO `micro_service_category` (`id`, `name`, `description`, `create_time`, `modify_time`, `order`, `nav_hidden`) VALUES ('a2423457-ab1c-11ec-9548-0894efae434a', '企业', '企业', '2022-03-24 10:46:54', '2022-03-24 10:46:54', 22, 1);


DELETE FROM micro_service WHERE `id` IN ('f56e82b6-ab1c-11ec-9548-0894efae434a');
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `category_id`,
                             `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`,
                             `order`, `link`, `public_link`)
VALUES ('f56e82b6-ab1c-11ec-9548-0894efae434a', '运维管理', '运维管理', 'sugoncloud-ops-api', '2021-05-03 10:24:56',
        '2022-03-24 00:02:02', '670ea69d-ab1c-11ec-9548-0894efae434a', 1, NULL, '2021-05-17 14:33:25', NULL, 0, 0,
        'https://************:30000/ops', NULL);

update micro_service set category_id = 'a2423457-ab1c-11ec-9548-0894efae434a',nav_hidden=1 WHERE `name` = '统一身份认证IAM';
