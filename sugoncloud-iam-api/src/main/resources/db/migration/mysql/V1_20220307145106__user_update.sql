ALTER TABLE `user`
    MODIFY COLUMN `email` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL AFTER `password`,
    MODIFY COLUMN `phone` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL AFTER `email`,
    ADD COLUMN `iv` varchar(128) NULL COMMENT '电话号、邮箱加密所用的随机码' AFTER `last_login_ip`,
    ADD COLUMN `hash` varchar(255) NULL COMMENT '用户完整性' AFTER `iv`,
    ADD COLUMN `hash_role` varchar(255) NULL AFTER `hash`;

update `user` u set u.email='<EMAIL>' where u.email is null or u.email='';
update `user` u set u.phone='18888888888' where u.phone is null or u.email='';
