INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '0cf0f1f1c39e109bd279a630054e7771', '同步配额', '组织管理-项目管理-操作-同步配额', 'null', null, null, 'null', '1', '2ad1a10cdd55777e0acc760e26fc4259', '', '', '', '3', '', '2022-04-18 14:30:09', '', '', '', '1', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '0cf0f1f1c39e109bd279a630054e7771');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '987d71c11d370c7cc5a3a40289150e15', '区域管理', '用户管理IAM-区域管理', '', null, null, 'null', '1', 'de5b8265a3f911eb8230f20ed01ddf38', '', '', '', '7', '', '2022-04-19 00:00:00', '/region-manage', 'icon-quyuguanli', '', '0', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '987d71c11d370c7cc5a3a40289150e15');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'ee98e52983ed0bccd0b794a6979a1b8f', '0cf0f1f1c39e109bd279a630054e7771', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'ee98e52983ed0bccd0b794a6979a1b8f');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'bb80c007b98c15a229f748086fe73bf3', '0cf0f1f1c39e109bd279a630054e7771', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'bb80c007b98c15a229f748086fe73bf3');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '9120a16a2e1640fc27bf08358f1e5ddd', '0cf0f1f1c39e109bd279a630054e7771', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '9120a16a2e1640fc27bf08358f1e5ddd');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '4650d197a5a9d785c44e658f7e07ab1c', '0cf0f1f1c39e109bd279a630054e7771', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '4650d197a5a9d785c44e658f7e07ab1c');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '86c4637386cb12ff09224423e71f6bb2', '987d71c11d370c7cc5a3a40289150e15', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '86c4637386cb12ff09224423e71f6bb2');
