ALTER TABLE `iam`.`user` MODIFY COLUMN `last_login_ip` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL AFTER `allow_ip`;

DELETE FROM `iam`.`globalsettings` WHERE uuid = '23dfc1-2645-4aaa-bfa6-044a4c73301';

INSERT INTO `iam`.`globalsettings` ( `uuid`, `policy_name`, `policy_document`, `policy_type`, `retained_field`, `policy_display_name` ) VALUES ( '23dfc1-2645-4aaa-bfa6-044a4c73301', 'vnc_ip', 'https://************:30000', 'vnc_public_ip', 'vnc公网IP', 'vnc公网IP' );

DELETE FROM `iam`.`globalsettings` WHERE uuid = '78228459-ab09-11ec-bde1-083a88902c92';

INSERT INTO `iam`.`globalsettings`(`uuid`, `policy_name`, `policy_document`, `policy_type`, `retained_field`, `policy_display_name`) VALUES ('78228459-ab09-11ec-bde1-083a88902c92', 'real_ip_internet', 'https://console.cloud.sugon.com/getRealIp', 'real_ip', '浏览器互联网客户端ip', '浏览互联网器客户端ip');

