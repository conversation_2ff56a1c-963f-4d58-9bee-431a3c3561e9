REPLACE INTO `micro_service_category` (`id`, `name`, `description`, `create_time`, `modify_time`, `order`, `nav_hidden`) VALUES ('4546d40a-2d8a-11ed-99d5-0894efae434a', '应用中间件', '应用中间件', '2021-7-9 10:33:34', '2021-7-5 11:51:37', 31, 0);
UPDATE `micro_service_category` SET `order`=32 WHERE `id`='dce92741-1d3b-43bc-9e51-ce21b60c9d4a';
UPDATE `micro_service_category` SET `order`=81 WHERE `id`='d04f330d-7353-476d-afd8-72e7a8f4002e';
DELETE FROM `micro_service` WHERE `id`='375744d6-0477-11ec-aedb-1a9678e015b2';

UPDATE `micro_service` SET `name`='云备份 CBR',`description`='云备份 CBR' WHERE `id`='276d2b49-ef06-c4db-2d77-be8ad8612589';
REPLACE INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('4b19e803-2d8b-11ed-99d5-0894efae434a', '分布式缓存服务 Redis', '分布式缓存服务 Redis', 'sugoncloud-redis-api', '2021-11-1 10:24:56', '2022-6-22 16:39:57', 'https://************:30000/redis', '4546d40a-2d8a-11ed-99d5-0894efae434a', 0, NULL, NULL, NULL, 0, 10, 'https://***************:34433/redis', 1);
REPLACE INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('5848c8a0-ccac-4ff9-b4bf-ee42c9545c77', '云数据库 MongoDB', '云数据库 MongoDB', 'sugoncloud-mongodb-api', '2022-9-6 10:24:56', '2022-9-6 10:24:56', 'https://************:30000/mongodb', 'a3a0adae-c1e1-11eb-a5c2-fac676e41602', 0, NULL, '2022-9-6 10:24:56', NULL, 0, 10, 'https://***************:34433/mongodb', 1);
REPLACE INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('6e5464b6-2d91-11ed-99d5-0894efae434a', '云搜索服务 CSS', '云搜索服务 CSS', 'sugoncloud-es-api', '2021-4-15 15:45:00', '2022-6-22 16:39:58', 'https://************:30000/es', '4546d40a-2d8a-11ed-99d5-0894efae434a', 0, NULL, NULL, NULL, 0, 40, 'https://***************:34433/es', 1);
REPLACE INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('b8e463c5-2d8a-11ed-99d5-0894efae434a', '分布式消息服务 Kafka', '分布式消息服务 Kafka', 'sugoncloud-kafka-api', '2021-11-1 10:24:56', '2022-6-22 16:39:57', 'https://************:30000/kafka', '4546d40a-2d8a-11ed-99d5-0894efae434a', 0, NULL, NULL, NULL, 0, 30, 'https://***************:34433/kafka', 1);

REPLACE INTO `quota_metric` VALUES ('es_cpu', 'es', 'cpu总量(个)', 'cpu使用量(个)');
REPLACE INTO `quota_metric` VALUES ('es_ram', 'es', '内存总量(GiB)', '内存使用量(GiB)');
REPLACE INTO `quota_metric` VALUES ('es_volume', 'es', '云硬盘总量(GiB)', '云硬盘使用总量(GiB)');
REPLACE INTO `quota_metric` VALUES ('kafka_cpu', 'kafka', 'cpu总量(个)', 'cpu使用量(个)');
REPLACE INTO `quota_metric` VALUES ('kafka_ram', 'kafka', '内存总量(GiB)', '内存使用量(GiB)');
REPLACE INTO `quota_metric` VALUES ('kafka_volume', 'kafka', '云硬盘总量(GiB)', '云硬盘使用总量(GiB)');
REPLACE INTO `quota_metric` VALUES ('mongodb_cpu', 'mongodb', 'cpu总量(个)', 'cpu使用量(个)');
REPLACE INTO `quota_metric` VALUES ('mongodb_ram', 'mongodb', '内存总量(GiB)', '内存使用量(GiB)');
REPLACE INTO `quota_metric` VALUES ('mongodb_volume', 'mongodb', '云硬盘总量(GiB)', '云硬盘使用总量(GiB)');
REPLACE INTO `quota_metric` VALUES ('redis_cpu', 'redis', 'cpu总量(个)', 'cpu使用量(个)');
REPLACE INTO `quota_metric` VALUES ('redis_ram', 'redis', '内存总量(GiB)', '内存使用量(GiB)');
REPLACE INTO `quota_metric` VALUES ('redis_volume', 'redis', '云硬盘总量(GiB)', '云硬盘使用总量(GiB)');

REPLACE INTO `quota_type` VALUES ('es', '云搜索服务CSS配额', '18');
REPLACE INTO `quota_type` VALUES ('kafka', '分布式消息服务Kafka配额', '19');
REPLACE INTO `quota_type` VALUES ('mongodb', '云数据库MongoDB配额', '16');
REPLACE INTO `quota_type` VALUES ('redis', '分布式缓存服务Redis配额', '17');

