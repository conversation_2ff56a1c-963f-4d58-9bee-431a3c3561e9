REPLACE INTO `iam`.`micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('6a0c0e2f-5f34-11ed-aebb-0894efae434a', '云防火墙 CFW', '云防火墙 CFW', 'sugoncloud-cfw-api', '2022-06-06 17:32:49', '2022-06-23 11:26:36', 'https://************:30000/cfw', 'ea209553-6177-441b-981a-9c5e40c62ea8', '0', NULL, NULL, NULL, '0', '47', 'https://***************:34433/cfw', '1');
REPLACE INTO `iam`.`micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('25aj48dh-loay-q23t-25lj-84mj21vm48iq', '虚拟专用网络 VPN', '虚拟专用网络 VPN', 'sugoncloud-vpn-api', '2022-11-11 14:31:57', '2022-11-11 14:31:57', 'https://************:30000/vpn', 'ea209553-6177-441b-981a-9c5e40c62ea8', '0', NULL, NULL, NULL, '0', '75', 'https://***************:34433/vpn', '1');
REPLACE INTO `iam`.`quota_type` (`name`, `description`, `sequence`) VALUES ('cfw', '云防火墙CFW配额', '25');
REPLACE INTO `iam`.`quota_type` (`name`, `description`, `sequence`) VALUES ('vpn', '虚拟专用网络VPN配额', '24');
REPLACE INTO `iam`.`quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('cfw_cpu', 'cfw', 'cpu总量(个)', 'cpu使用量(个)');
REPLACE INTO `iam`.`quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('cfw_ram', 'cfw', '内存总量(GiB)', '内存使用量(GiB)');
REPLACE INTO `iam`.`quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('vpn_cpu', 'vpn', 'cpu总量(个)', 'cpu使用量(个)');
REPLACE INTO `iam`.`quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('vpn_ram', 'vpn', '内存总量(GiB)', '内存使用量(GiB)');
