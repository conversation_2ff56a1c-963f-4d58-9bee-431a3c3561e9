CREATE TABLE IF NOT EXISTS `secret_key` (
  `id` varchar(64) NOT NULL,
  `access_key` varchar(64) NOT NULL,
  `secret_key` varchar(128) NOT NULL,
  `enabled` tinyint(1) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `create_at` datetime DEFAULT NULL,
  `user_id` varchar(64) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for user_strategy
-- ----------------------------
CREATE TABLE IF NOT EXISTS `user_strategy` (
 `strategy_id` varchar(64) NOT NULL,
 `user_id` varchar(64) NOT NULL,
 PRIMARY KEY (`strategy_id`,`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `strategy` (
 `id` varchar(64) NOT NULL,
 `policy` text NOT NULL,
 `name` varchar(64) NOT NULL,
 `description` varchar(128) DEFAULT NULL,
 `create_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `catalog` varchar(16) NOT NULL COMMENT '策略类型(系统策略、用户自定义策略)',
 `creat_by` varchar(64) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `service_action` (
  `id` varchar(64) NOT NULL,
  `action` varchar(64) NOT NULL COMMENT '操作（服务名:类名:方法名）',
  `action_type` varchar(8) NOT NULL COMMENT '操作类型（读、写、列表、权限）',
  `service_id` varchar(64) NOT NULL COMMENT '服务ID',
  `description` varchar(32) NOT NULL COMMENT '描述',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
