-- iam quota department value
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('image', 'admin', 999999999,999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('vpc', 'admin', 999999999, 999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('lb', 'admin', 999999999, 999999999, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('lb_v2', 'admin', 999999999, 999999999, 'RegionOne');

-- iam quota project value
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('image', 'admin-inner-project', 999999999,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('vpc', 'admin-inner-project', 999999999, 0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('lb', 'admin-inner-project', 999999999, 0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('lb_v2', 'admin-inner-project', 999999999, 0, 'RegionOne');