REPLACE INTO `iam`.`micro_service`(`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`, `license_key`) VALUES ('f72db00f-4914-4bdf-9161-7cc5977edfsd', '工单', '工单', 'sugoncloud-log-api', '2021-05-03 10:24:56', '2022-06-22 16:39:58', 'https://************:30000/workorder', '670ea69d-ab1c-11ec-9548-0894efae434b', 0, NULL, NULL, NULL, 0, NULL, 'https://***************:34433/workorder', 1, '-1');
REPLACE INTO `iam`.`micro_service_category`(`id`, `name`, `description`, `create_time`, `modify_time`, `order`, `nav_hidden`) VALUES ('670ea69d-ab1c-11ec-9548-0894efae434b', '工单', '工单', '2022-03-24 10:46:34', '2022-03-24 10:46:34', 23, 1);
UPDATE `micro_service` SET `license_key` = 'swrAuthorizationInfo' WHERE `id` = 'a87cbc5b-9e49-4026-95e3-6eb10a12bac5';
UPDATE `micro_service` SET `service_id` = 'sugoncloud-vpc-api' WHERE `id` = '5e6015af-0e6f-4cf6-8bab-25d513772468';
REPLACE INTO `iam`.`micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`, `license_key`) VALUES ('afa751d5-7973-4cb1-9a66-16ddf19295021', '态势感知 SOC', '态势感知 SOC', 'sugoncloud-soc-api', '2023-02-27 11:28:24', '2023-02-27 11:28:26', 'https://************:30000/soc', 'd04f330d-7353-476d-afd8-72e7a8f4002e', 0, NULL, '2023-02-27 11:28:52', NULL, 0, 105, 'https://***************:34433/soc', 1, '-1');
REPLACE INTO `iam`.`micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`, `license_key`) VALUES ('fbaa65c1-c88e-11ed-9bbb-0894efae434a', '企业路由器 ER', '网络', 'sugoncloud-vpc-api', '2021-04-15 15:45:00', '2022-06-22 16:39:58', 'https://************:30000/vpc/#/vpc-er-list', 'ea209553-6177-441b-981a-9c5e40c62ea8', 0, NULL, NULL, NULL, 0, 100, 'https://***************:34433/vpc/#/vpc-er-list', 1, 'erAuthorizationInfo');
REPLACE INTO `iam`.`quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('er', '企业路由器ER', 30, 'ea209553-6177-441b-981a-9c5e40c62ea8');
REPLACE INTO `iam`.`quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('er_vm', 'er', '企业路由器总量(个)', '企业路由器使用量(个)');





