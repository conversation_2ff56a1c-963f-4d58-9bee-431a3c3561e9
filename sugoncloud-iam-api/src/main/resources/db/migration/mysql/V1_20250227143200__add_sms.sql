CREATE TABLE IF NOT EXISTS `danger_operation_url`  (
    `id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
    `method` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接口方式',
    `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接口地址',
    `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接口描述',
    `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `modify_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
    `expired` tinyint(1) NULL DEFAULT NULL COMMENT '是否使验证码失效',
    `enabled` tinyint(1) DEFAULT NULL COMMENT '是否启用 1表示启用，0表示不启用',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('0082a903c1d54808a13e36b4245d4d07', 'PUT', '/sugoncloud-mongodb-api/api/users/:instance_id/root', 'MongoDB-实例管理-修改root密码', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('00e8a6ceb34b4d789f82b0091320539c', 'PUT', '/sugoncloud-ecs-v2-api/api/ecs/servers/:server_id/soft-delete-volumes', '弹性云服务器ECS-常规操作-删除/批量删除移入回收站(数据盘移入回收站)', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('00e8a6ceb34b4d789f82b0091320539d', 'PUT', '/sugoncloud-ecs-v2-api/api/ecs/servers/:server_id/soft-delete', '弹性云服务器ECS-常规操作-删除/批量删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('025e6259580644d5a311300430b751ab', 'DELETE', '/sugoncloud-ecs-v2-api/api/ecs/servers/:server_id', '云服务器-回收站-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('025e6259580644d5a311300430b751ac', 'DELETE', '/sugoncloud-ecs-v2-api/api/ecs/servers/:server_id/with-resources', '云服务器-回收站-删除（实例及关联资源）', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('07eaaebadfaa4c7abe3747aaee1fa14b', 'DELETE', '/sugoncloud-ops-api/api/ops/eip/poolBind', '网络-资源池-解绑项目', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('0a02df1cc1764a8bb503c063ebd20696', 'DELETE', '/sugoncloud-vpc-api/api/vpc/dns-zone/records/:record_id', '内网解析DNS-详情-解析记录-删除/批量删除记录集', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('0af9b1e917d74a2986668dc2cd959ee5', 'DELETE', '/sugoncloud-vpc-api/api/vpc/v2/lbaas/pools/:pool_id/members', '负载均衡-负载均衡（基础版）-删除资源', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('0c991a23c92440f19892c5ff21055e19', 'DELETE', '/sugoncloud-vpc-api/api/vpc/nat/v3/fip/:nat_gw_id', 'NAT网关v2-详情-公网IP-删除/批量删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('1594119ff932493388d0f401838cc4b7', 'DELETE', '/sugoncloud-oss-api/api/bucket/policy', '对象存储OSS-桶策略-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('165671a0cea24701911782795d2aff78', 'PUT', '/sugoncloud-vpc-api/api/eip/floatips/:ip_id/disassociate-for-network', '网络配置-解绑公网IP', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('1970e83b93344713b97fbc7a5316cb09', 'DELETE', '/sugoncloud-iam-api/api/users/:user_id', '租户-用户管理-删除IAM用户', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('1c8bb8fd58be455c8c05538eae58cac4', 'DELETE', '/sugoncloud-ops-api/api/ops/evs/volume-types/:volume_type_id', '存储-云硬盘类型-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('1e30b15370c24774a88602fd0b1be6a1', 'PUT', '/sugoncloud-ecs-v2-api/api/ecs/servers/:server_id/migrate', '弹性云服务器ECS-迁移-冷迁移', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('********************************', 'PUT', '/sugoncloud-ecs-v2-api/api/secs/servers/ports', '安全云服务器SECS-网络配置-修改端口IP', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('1e9edc2b948c45b7b140dabb6bb1eead', 'POST', '/sugoncloud-mongodb-api/api/backups/recovery', 'AnhanDB(for MongoDB)-备份管理-恢复', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('20a246beae73483e907f8c3012b895f1', 'POST', '/sugoncloud-redis-api/api/instance/flushAll', 'Redis-实例管理-清空实例', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('219d464311ac4416b345cde57fecac34', 'DELETE', '/sugoncloud-backup-api/api/backup/task/by-instance/:instance_id', '实例备份-备份数据-删除备份数据', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('22302dfe869d4ca998dc41e239274c02', 'DELETE', '/sugoncloud-vpc-api/api/eip/floatips/release_batch', '弹性公网IP-弹性公网IPv4-释放/批量释放公网IP', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('24cafbaf60504966b8c357e1632618f4', 'PUT', '/sugoncloud-vpc-api/api/vpc/v3/lbaas/loadbalances/:loadbalancer_id/status', '负载均衡（性能版）-禁用负载均衡', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('2605478d903246aba04eb56de6eba460', 'PUT', '/sugoncloud-pgsql-api/api/users/:instance_id/root', 'PostgreSQL-实例管理-修改root密码', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('26b86fd0f6684c8ea9e021b4d5894692', 'PUT', '/sugoncloud-ecs-v2-api/api/secs/servers/:server_id/rebuild', '安全云服务器SECS-常规操作-重建云主机', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('297fa32d0ab74874a21c3efc6a94c7b1', 'DELETE', '/sugoncloud-redis-api/api/redis/:instance_id', 'Redis-实例管理-删除实例', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('2c9da0b9d2af424db0f9b29780ec5629', 'PUT', '/sugoncloud-obs-api/api/buckets/:uuid/oscps', '对象存储OBS-桶策略-编辑', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('2c9da0b9d2af424db0f9b29780ec5630', 'POST', '/sugoncloud-obs-api/api/buckets/:uuid/oscps', '对象存储OBS-桶策略-创建', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('2ce5d20d4a8b49a1b8c9edc27b7a706b', 'PUT', '/sugoncloud-iam-api/api/users', '修改用户', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('2de64963325443e9b6981e56f331060b', 'PUT', '/sugoncloud-ops-api/api/edge-cluster/:cluster_id', '网络-edge集群管理-修改', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('2f477ebb5e26473ba675c9d66351ac30', 'DELETE', '/sugoncloud-mysql-api/api/node/:node_id', 'AnhanDB(for MySQL)-实例详情-节点删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('305c368780a84fb9a0609d916759dee2', 'DELETE', '/sugoncloud-kafka-api/api/kafka/:instance_id', '分布式消息服务kafka-实例管理-删除集群', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('309b975a27f245f3bccbf812c9c16b47', 'DELETE', '/sugoncloud-mongodb-api/api/node/:node_id', 'AnhanDB(for MongoDB)-实例详情-节点删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('315b6be58ff04d83b8f5d1515ae1aee4', 'DELETE', '/sugoncloud-oss-api/api/bucket/:bucketName', '对象存储OSS-桶操作-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('31c3fe8bbf464fb7a5e0e43ecf5b4841', 'DELETE', '/sugoncloud-vpc-api/api/vpc/v2/lbaas/listeners/:listener_id', '负载均衡-负载均衡（基础版）-删除监听器', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('35029012ebef48d4ac9eadff26d0a186', 'DELETE', '/sugoncloud-sfs-api/api/sfs/servers/:instance_id', '文件存储SFS-实例管理-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('********************************', 'DELETE', '/sugoncloud-ecs-v2-api/api/ecs/servers/:server_id/detachCinder/:volume_id', '弹性云服务器ECS-关联资源-卸载云硬盘', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('3870b00750994b5980eb84ac0198028b', 'PUT', '/sugoncloud-vpn-api/api/vpn/channel', '虚拟专用网络VPN-vpn通道-修改', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('38f125f0a2db40e99fb27cf36fe60211', 'DELETE', '/sugoncloud-vpc-api/api/ops/vpc/ipv6_eip/:id', '弹性公网IP-弹性公网IPv6-释放/批量释放公网IP', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('3a2cdaa8dd2c4a74b28235d5d5320feb', 'PUT', '/sugoncloud-evs-api/api/evs/volumes/:cinder_id/garbage', '云硬盘-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('3aebeec0b8a8464fa91bac0835cfc7f6', 'DELETE', '/sugoncloud-iam-api/api/projects/:id', '租户-项目管理-删除项目', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('3be9b233570346898181a9536facd488', 'POST', '/sugoncloud-obs-api/api/buckets/', '对象存储OBS-桶策略-创建', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('3f025cefd46a4a8991ee5e3214542f77', 'PUT', '/sugoncloud-ecs-v2-api/api/secs/servers/:server_id/set-vnc-password', '安全云服务器SECS-高级配置-修改VNC密码', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('3ff5367542174422b2843810ad021d1e', 'DELETE', '/sugoncloud-obs-api/api/buckets/:buckets_id/acls', '对象存储OBS-桶ACLS-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('42ae1e0dc94344938a3af1fc744dfc94', 'PUT', '/sugoncloud-vpc-api/api/vpc/networks/ports/:port_id/vip', '虚拟私有云VPC-详情-虚拟ip管理-解绑/绑定实例', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('42bb4a8e98c24de9bece73d3ed051119', 'PUT', '/sugoncloud-bms-api/api/instances/:instance_id/password', '裸金属实例-修改密码', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('43bb301da98e4f3bb216822276afd85e', 'DELETE', '/sugoncloud-vpc-api/api/vpc/nat/v2/dnat_rule/:rule_id', 'NAT网关-详情- DNAT规则-删除/批量删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('445a553e5ed6431f95f6ba66e6d10040', 'DELETE', '/sugoncloud-vpc-api/api/vpc/v2/lbaas/pools/:pool_id/members/:member_id', '负载均衡-负载均衡（基础版）-删除资源', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('45cfc1fa3cd4422bb09532b0444929e9', 'DELETE', '/sugoncloud-kafka-api/api/node/:node_id', '分布式消息服务kafka-实例详情-节点删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('4d1ace946223470c9300bc0a758784f8', 'PUT', '/sugoncloud-vpc-api/api/vpc/dns-zone/records/:record_id', '内网解析DNS-详情-解析记录-修改记录集', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('4d70265695d94203bf20efc16828751c', 'DELETE', '/sugoncloud-ecs-v2-api/api/secs/servers/:server_id/cdrom/:volume_id', '安全云服务器SECS-关联资源-卸载CD-ROM-分离cdrom-volume', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('4d70265695d94203bf20efc16828751d', 'DELETE', '/sugoncloud-ecs-v2-api/api/secs/servers/:server_id/cdrom', '安全云服务器SECS-关联资源-卸载CD-ROM', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('501839b8d599484fa027ef34f8571c6f', 'DELETE', '/sugoncloud-prom-api/api/instance/:instance_id', '监控服务Prometheus-实例管理-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('50f982d5ae65464c90cc5acbb17d0dbc', 'DELETE', '/sugoncloud-pgsql-api/api/backups/:id', 'PostgreSQL-备份管理-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('5181d21ffeb24d6ebcd5e779036a44a9', 'DELETE', '/sugoncloud-backup-api/api/backup/task/delete/:id', '实例备份-备份任务-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('554ef49ed9c2466b8a518bd3f56c067e', 'PUT', '/sugoncloud-obs-api/api/buckets/:buckets_id/referers', '对象存储OBS-防盗链-编辑白名单黑名单', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('555b5cdbc06f4eebaf22486edaa168c1', 'PUT', '/sugoncloud-ecs-v2-api/api/ecs/servers/:server_id/set-vnc-password', '弹性云服务器ECS-高级配置-修改VNC密码', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('5684dd5b45c8414483c1afdabdbc8d63', 'DELETE', '/sugoncloud-vpc-api/api/vpc/certificates/:id', '负载均衡-证书管理-删除/批量删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('594262e67d894267807bde3e27f0790c', 'DELETE', '/sugoncloud-vpc-api/api/vpc/v3/lbaas/pools/:pool_id/members', '负载均衡-负载均衡（性能版）-删除实例资源', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('59cee21cd0eb4ff8842e9afe28ad5b51', 'PUT', '/sugoncloud-oss-api/api/bucket/policy', '对象存储OSS-桶策略-增加、编辑、删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('59fa434b41844f4da5b5e08f93fb8c1c', 'PUT', '/sugoncloud-ecs-v2-api/api/secs/servers/:server_id/unbind-host-dev', '安全云服务器SECS-关联资源-卸载裸磁盘', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('5acaa83c86854803ac7fa773289f24b0', 'DELETE', '/sugoncloud-evs-api/api/evs/volumes/safe-delete/:cinder_id', '云硬盘EVS-回收站-安全删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('5d64b0eb0cad4e8198c94cc70ca19272', 'DELETE', '/sugoncloud-vpc-api/api/dc_virtual_interface/v2/:id', '云专线v2-删除虚拟接口', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('5fa9caec8e344657beac3fa7f72ea48c', 'DELETE', '/sugoncloud-ops-api/api/priority_server/:id', '网络-外部业务互联-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('5fa9caec8e344657beac3fa7f72ea48d', 'PUT', '/sugoncloud-ops-api/api/priority_server/:id', '网络-外部业务互联-关闭', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('6158e979a3ea4fca86ddc5beaa8d20b8', 'PUT', '/sugoncloud-ops-api/api/ops/ecs/services/disable', '计算-物理机-禁用服务', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('6430235edc68424d8dc39a94477af838', 'PUT', '/sugoncloud-bms-api/api/bare-metal/power/:id/:power', '裸金属实例-关机/硬重启', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('6622ddf37de34a678ddbaf3f79b1cac9', 'DELETE', '/sugoncloud-obs-api/api/buckets', '对象存储OBS-桶策略-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('679cc6bf7950484b90f0b5c896e9b858', 'PUT', '/sugoncloud-obs-api/api/buckets/:buckets_id/acls', '对象存储OBS-桶ACLS-编辑', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('6ee3f8321fd94f1c8ec0433e8c86b526', 'DELETE', '/sugoncloud-ecs-v2-api/api/ecs/servers/:server_id/usb', '弹性云服务器ECS-关联资源-卸载USB', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('718ae949ce034ddeb77766b0882da05f', 'DELETE', '/sugoncloud-obs-api/api/buckets/', '对象存储OBS-桶策略-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('76781d9b43854cc38948969224863827', 'DELETE', '/sugoncloud-vpn-api/api/vpn/:instance_id', '虚拟专用网络VPN-vpn网关-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('769e0092ccc547fc8adaa210254b680f', 'PUT', '/sugoncloud-ecs-v2-api/api/ecs/servers/:server_id/password', '弹性云服务器ECS-高级配置-修改密码', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('76d8de7da035432e9832395b11901939', 'DELETE', '/sugoncloud-vpc-api/api/vpc/v3/lbaas/loadbalances/:loadbalancer_id', '负载均衡（性能版）-删除/批量删除负载均衡', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('79b760515cf540fd97518018b3d43d91', 'DELETE', '/sugoncloud-ops-api/api/ops/vpc/networks/vpc/:network_id', '网络-公有网络-删除/批量删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('7a00965acf2a4d73aa064a4fca0bf908', 'PUT', '/sugoncloud-ecs-v2-api/api/ecs/servers/:server_id/live-migration', '弹性云服务器ECS-迁移-热迁移', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('7cff675469ef45b1995fbc1938e72797', 'PUT', '/sugoncloud-ecs-v2-api/api/secs/servers/:server_id/password', '安全云服务器SECS-高级配置-修改密码', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('7e1738d65c294990918aeb7aebe02f62', 'DELETE', '/sugoncloud-vpn-api/api/ssl/client/:id', '虚拟专用网络VPN-SSL客户端-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('845b5f0a611b4780bba88eb1f7d02bfe', 'PUT', '/sugoncloud-iam-api/api/users/:user_id/update-password', '租户-个人信息-修改登录密码', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('887a67b40d05462388a7c0b1628d7813', 'PUT', '/sugoncloud-ops-api/api/drs/hostaggregate/:host_aggregate_id/drs/state', '计算-集群-动态资源调度策略启用和停用', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('8e9aa35ccbe24da594271b5f1c83ec99', 'PUT', '/sugoncloud-ops-api/api/ha/single_node_status/:node_name', '网络-服务状态-剔除迁移备选节点', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('8f5486cd3abf4c7c95912caeb37d1498', 'PUT', '/sugoncloud-ops-api/api/ops/localfs/disable/:pool_id', '存储-本地存储-禁用', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('8fd50596a1694ab593d3188acb7f1acf', 'PUT', '/sugoncloud-ecs-v2-api/api/ecs/servers/ports', '弹性云服务器ECS-网络配置-修改端口IP', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('906bd57b5c3c4f8b8b7f3e8610aa2708', 'PUT', '/sugoncloud-ecs-v2-api/api/secs/servers/:server_id/action/:action_name', '安全云服务器SECS-电源操作', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('92db8025a2714f14b37723dde68e618c', 'DELETE', '/sugoncloud-vpc-api/api/dc_virtual_interface/:id', '云专线-删除虚拟接口', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('9584b8d8d772412d8434b8505713ba02', 'PUT', '/sugoncloud-ecs-v2-api/api/secs/servers/:server_id/soft-delete', '安全云服务器SECS-常规操作-删除/批量删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('9ae3af551c0b445b9d5d89e2d1e3c31c', 'DELETE', '/sugoncloud-cce-api/api/cluster/:cluster_id', '云容器引擎CCE-集群管理-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('9bb94c6537ce4c8db8dd9d23ac6632b5', 'DELETE', '/sugoncloud-ecs-v2-api/api/ecs/servers/:server_id/ports/:port_id', '弹性云服务器ECS-网络配置-卸载网卡', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('a09ab00c64df4efab7ca43bd1e3b30a5', 'PUT', '/sugoncloud-redis-api/api/users/:instance_id/default', 'Redis-实例管理-修改默认用户密码', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('a6794ff35af549a5b4f23c010640a75b', 'DELETE', '/sugoncloud-pgsql-api/api/pgsql/:instance_id', 'PostgreSQL-实例管理-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('a936f85917374bc4bbc435ec2493f87b', 'PUT', '/sugoncloud-ops-api/api/l4lb-cluster/:cluster_name', '网络-负载均衡集群管理-修改', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('aa1e06f81e0b47a2a6d605bfeeb18c51', 'DELETE', '/sugoncloud-ecs-v2-api/api/secs/servers/:server_id/usb', '安全云服务器SECS-关联资源-卸载USB', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('ada4a4deac9644b9bf598f8b0166426e', 'DELETE', '/sugoncloud-redis-api/api/node/:node_id', 'AnhanDB for Redis-实例详情-节点删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('b02f659993fe416995510937c69d9dba', 'DELETE', '/sugoncloud-ops-api/api/ops/localfs/:pool_id', '存储-本地存储-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('b12946ac44494238a1e6e7e624555ce3', 'PUT', '/sugoncloud-obs-api/api/buckets/', '对象存储OBS-桶策略-编辑', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('b21b6695051e47b0b5d909e15da6a5d8', 'DELETE', '/sugoncloud-mongodb-api/api/backups/:id', 'MongoDB-备份管理-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('b2ccc832bea84638b4081f6e4c94eba7', 'POST', '/sugoncloud-mysql-api/api/node/:node_id', 'AnhanDB(for MySQL)-备份管理-恢复', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('b60f69f8d4fa4299b1c4e3ee73d387ea', 'DELETE', '/sugoncloud-ops-api/api/ops/sharefs/:pool_id', '存储-共享文件存储-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('b6c4c181f66c4610b43769c59911b358', 'DELETE', '/sugoncloud-vpc-api/api/vpc/security-groups/:security_group_id', '网络安全-安全组SG-网络安全', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('b7e408302b9547988dd6eee78fa59fcd', 'POST', '/sugoncloud-obs-api/api/buckets/:buckets_id/acls', '对象存储OBS-桶ACLS-增加', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('b863af66e7b04dccaf271f26c7fee8df', 'DELETE', '/sugoncloud-ops-api/api/l4lb-cluster/:cluster_name', '网络-负载均衡集群管理-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('b863af66e7b04dccaf271f26c7feqeq', 'DELETE', '/sugoncloud-vpc-api/api/vpc/v2/lbaas/loadbalances/:loadbalancer_id', '负载均衡-负载均衡（基础版）-删除/批量删除负载均衡', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('b896f9e765294ccaa61c658cf5bc9075', 'POST', '/sugoncloud-ops-api/api/ha/migrate', '网络-服务状态-资源迁移', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('b966c03a93a548ff84dc03f923d270a5', 'DELETE', '/sugoncloud-mysql-api/api/mysql/:instance_id', 'MySQL-实例管理-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('bc0be03b78f64f7f8fa9de4599559afd', 'DELETE', '/sugoncloud-vpc-api/api/vpc/v3/lbaas/pools/:pool_id/members/:member_id', '负载均衡-负载均衡（性能版）-删除实例资源', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('bdb810bf07ec444788d3fdd959ed03a6', 'PUT', '/sugoncloud-ecs-v2-api/api/ecs/servers/live-migration/:host', '计算-物理机-一键热迁移', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('bdb810bf07ec444788d3fdd959ed03a7', 'PUT', '/sugoncloud-ecs-v2-api/api/ecs/servers/migrate/:host', '计算-物理机-一键冷迁移', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('bedc7805baa1420f87e03c19f3e4d641', 'PUT', '/sugoncloud-mysql-api/api/users/:instance_id/root', 'MySQL-实例管理-修改root密码', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('c035183ac9324c68b451179c65a239b7', 'DELETE', '/sugoncloud-rbs-api/api/rbs/:instance_id', '分布式消息服务rabbitmq-实例管理-删除集群', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('c1a43bfb31024cf7ad001994a9ba1758', 'DELETE', '/sugoncloud-es-api/api/es/:instance_id', '云搜索服务CSS-实例管理-删除集群', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('c26657110332485ca381687a0d7c1512', 'POST', '/sugoncloud-pgsql-api/api/backups/recovery', 'AnhanDB(for PostgreSQL)-备份管理-恢复', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('c6bcad72dc33460ebd0e36a6458730e4', 'PUT', '/sugoncloud-oss-api/api/bucket/acl', '对象存储OSS-桶ACLS-新增、编辑、删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('c7a0a8e2edfd4994bd3b8d1a9298bddf', 'DELETE', '/sugoncloud-vpc-api/api/vpc/nat/v2/snat_rule/:rule_id', 'NAT网关-详情- SNAT规则-删除/批量删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('c8331451c0aa42c18f9bd277b87c50a5', 'DELETE', '/sugoncloud-ops-api/api/ops/vpc/SDN/mfip/delete/:id', '网络-MFIP-删除/批量删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('c8afa05fa6c14c4d8439b3ee4f99e936', 'POST', '/sugoncloud-ecs-v2-api/api/ecs/servers/:server_id/detach-vgpu', '弹性云服务器ECS-关联资源-卸载vGPU', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('c8be9eaa57cb47b6a93bbcdf93c6afc8', 'DELETE', '/sugoncloud-vpc-api/api/vpc/nat/v3/snat_rule/:rule_id', 'NAT网关v2-详情- SNAT规则-删除/批量删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('caaf1b35afb24553a73459e271ab5ca9', 'DELETE', '/sugoncloud-bms-api/api/smart-nic/instances/:instance_id/volume/:volume_id', '裸金属实例-卸载云硬盘', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('cc1d38cc24ed4a41b0fac723a11bacf5', 'PUT', '/sugoncloud-ecs-v2-api/api/ecs/servers/:server_id/rebuild', '弹性云服务器ECS-常规操作-重建云主机', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('cf65e0af6b7a41a08c7ef93956d0f18c', 'DELETE', '/sugoncloud-ecs-v2-api/api/secs/servers/:server_id/ports/:port_id', '安全云服务器SECS-网络配置-卸载网卡', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('d009363373014cbb848ae9ccc72adf50', 'DELETE', '/sugoncloud-iam-api/api/strategy/:user_id/:id', '租户-策略管理-删除策略', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('d064e783121f4a6ba851360fee20deec', 'POST', '/sugoncloud-redis-api/api/users/:instance_id/open-no-pass', 'Redis-实例管理-开启免密', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('d1ec8d5f30f442b2b4e787227ebce4fe', 'DELETE', '/sugoncloud-ops-api/api/ops/usans/:usan_id', '存储-集中式存储-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('d2951f4205b14d7b97524499632103d9', 'DELETE', '/sugoncloud-mysql-api/api/backups/:id', 'MySQL-备份管理-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('d4e998528f0c4722be106e4e89ec1f44', 'DELETE', '/sugoncloud-vpn-api/api/vpn/channel/:id', '虚拟专用网络VPN-vpn通道-删除/批量删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('d71ec2ead9464c5082e00e93142c42ce', 'PUT', '/sugoncloud-ops-api/api/ops/sharefs/disable/:pool_id', '存储-共享文件存储-禁用', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('d82b81360b734effa4e203796efb0cf0', 'DELETE', '/sugoncloud-vpc-api/api/cit/vpc-peer-connect/:connect_router_id', '对等连接PC-列表-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('d95ad99eb9f34d0f87dd93d7de6ab520', 'PUT', '/sugoncloud-ecs-v2-api/api/ecs/servers/:server_id/action/:action_name', '弹性云服务器ECS-电源操作', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('dc56869c762e4d568438375a61baa584', 'PUT', '/sugoncloud-ops-api/api/ops/vpc/SDN/chassis', '网络-主机SNAT-修改/修改IPv6', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('e063ea175af5464ebc8ce266b3c307ce', 'PUT', '/sugoncloud-ops-api/api/ops/ecs/host-dev/:host_dev_id/disable', '计算-物理机设备-裸磁盘禁用/DCU禁用/GPU禁用/USB禁用', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('e07d293c356742c58d3369ad585912c0', 'DELETE', '/sugoncloud-mongodb-api/api/mongodb/:instance_id', 'MongoDB-实例管理-删除实例', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('e0d1d255a23d4eb1ba46359919593a5b', 'DELETE', '/sugoncloud-ecs-v2-api/api/secs/servers/:server_id/detachCinder/:volume_id', '安全云服务器SECS-关联资源-卸载云硬盘', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('e2a61184af1346e5b5c3d8db7dffde20', 'DELETE', '/sugoncloud-obs-api/api/buckets/:uuid/oscps', '对象存储OBS-桶策略-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('e2a61184af1346e5b5c3d8db7dffde21', 'DELETE', '/sugoncloud-obs-api/api/buckets/:uuid', '对象存储OBS-桶操作-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('e2bc14d4b57948b3b1f7b68295617f62', 'DELETE', '/sugoncloud-ops-api/api/edge-cluster/:cluster_id', '网络-edge集群管理-删除/批量删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('e44bb27710cb419db3cf0786bcde8101', 'DELETE', '/sugoncloud-ops-api/api/ops/storage-pool/:id', '存储-存储池-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('e518ff985b5d4f35810f3ee26dce4a23', 'PUT', '/sugoncloud-ecs-v2-api/api/ecs/servers/:server_id/unbind-host-dev', '弹性云服务器ECS-关联资源-卸载DCU/GPU/卸载裸磁盘', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('e76539ce0cdb470c867b22428cea6fbb', 'DELETE', '/sugoncloud-ecs-v2-api/api/ecs/servers/:server_id/cdrom', '弹性云服务器ECS-关联资源-卸载CD-ROM', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('e76539ce0cdb470c867b22428cea6fbc', 'DELETE', '/sugoncloud-ecs-v2-api/api/ecs/servers/:server_id/cdrom/:volume_id', '弹性云服务器ECS-关联资源-卸载CD-ROM-分离cdrom-volume', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('eb7a4abcea934414b3d3dcb0e65bf36a', 'DELETE', '/sugoncloud-pgsql-api/api/node/:node_id', 'AnhanDB(for PostgreSQL)-实例详情-节点删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('ebb992e9ab6f4d0cb6b9368f3306abd8', 'DELETE', '/sugoncloud-bms-api/api/instances/:id', '裸金属实例-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('ef40d3bb66884aa7b874f86e5a203b5c', 'PUT', '/sugoncloud-iam-api/api/users', '租户-个人信息-修改用户信息', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('f17a72555afa4bdca0d600f23c1026c1', 'DELETE', '/sugoncloud-evs-api/api/evs/volumes/:cinder_id', '云硬盘EVS-回收站-删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('f282d36abb3842519e6f8725ddf74b97', 'DELETE', '/sugoncloud-vpc-api/api/vpc/nat/v3/dnat_rule/:rule_id', 'NAT网关v2-详情- DNAT规则-删除/批量删除', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('f3ee58bd47ee4c4ca0021cc4b974623a', 'DELETE', '/sugoncloud-backup-api/api/backup/policy/:id', '实例备份-策略管理-删除策略', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('f3ee58bd47ee4c4ca0021cc4b974623c', 'DELETE', '/sugoncloud-vpc-api/api/vpc/v3/lbaas/listeners/:listener_id', '负载均衡-负载均衡（性能版）-删除监听器', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('48b3423b995c4ba3ae1d53df278dd180', 'PUT', '/sugoncloud-ops-api/api/ops/physical/machine/power/off', '区域管理-物理机节点-关机', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('7bafa046f9b540f1ba73c10967ced212', 'PUT', '/sugoncloud-ops-api/api/ops/physical/machine/power/reset', '区域管理-物理机节点-重启', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('335d4e4d72d54580ae1b8a4e4640861d', 'DELETE', '/sugoncloud-ops-api/api/ops/physical/machine/node/:node_uuid', '区域管理-物理机节点-下线', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 0, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('bfb66ba90c614b3cb89fa650cdf14a3c', 'PUT', '/sugoncloud-ops-api/api/ops/physical/machine/ipmi/set-password', '区域管理-物理机节点-设置IPMI用户名和密码', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);
REPLACE INTO `danger_operation_url`(`id`, `method`, `url`, `description`, `create_time`, `modify_time`, `expired`, `enabled`) VALUES ('0082a903c1d54808a13e36b4245d4d08', 'PUT', '/sugoncloud-mongodb-api/api/users/:instance_id', 'MongoDB-实例管理-修改用户密码', '2025-02-09 15:12:51', '2025-02-09 15:12:51', 1, 1);

CREATE TABLE IF NOT EXISTS `business_message_relation` (
    `id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
    `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `modify_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
    `business_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务类型标识',
    `business_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务类型描述',
    `message_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息类型 sms:短信 email:邮箱',
    `template_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板id',
    `template_type` int(0) NULL DEFAULT NULL COMMENT '模板类型 2表示通知邮件，3表示验证码邮件',
    `enabled` tinyint(1) DEFAULT NULL COMMENT '是否启用 1表示启用，0表示不启用',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

REPLACE INTO `business_message_relation`(`id`, `create_time`, `modify_time`, `business_type`, `business_desc`, `message_type`, `template_id`, `template_type`, `enabled`) VALUES ('3da1c64bae3c4de185a68eac1cfe22d0', '2025-02-10 14:19:40', '2025-02-10 14:19:42', 'danger', '敏感操作', 'email', '18345ba6afa2487db34c47b994666bba', 3, 1);
REPLACE INTO `business_message_relation`(`id`, `create_time`, `modify_time`, `business_type`, `business_desc`, `message_type`, `template_id`, `template_type`, `enabled`) VALUES ('8a147c3edc754290b4edf9cbeb06444b', '2025-02-10 14:19:40', '2025-02-10 14:19:42', 'login', '登录', 'sms', '2352231', 3, 1);
REPLACE INTO `business_message_relation`(`id`, `create_time`, `modify_time`, `business_type`, `business_desc`, `message_type`, `template_id`, `template_type`, `enabled`) VALUES ('979c45c5adf64feab5654b11cc0efb16', '2025-02-10 14:19:40', '2025-02-10 14:19:42', 'danger', '敏感操作', 'sms', '2360135', 3, 1);
REPLACE INTO `business_message_relation`(`id`, `create_time`, `modify_time`, `business_type`, `business_desc`, `message_type`, `template_id`, `template_type`, `enabled`) VALUES ('eb957feab7cf4bc58fca0425f651a667', '2025-02-10 14:19:40', '2025-02-10 14:19:42', 'login', '登录', 'email', '18345ba6afa2487db34c47b994666bbf', 3, 1);
REPLACE INTO `business_message_relation`(`id`, `create_time`, `modify_time`, `business_type`, `business_desc`, `message_type`, `template_id`, `template_type`, `enabled`) VALUES ('eb957feab7cf4bc58fca0425f651a668', '2025-02-10 14:19:40', '2025-02-10 14:19:42', 'alarm', '告警', 'email', '18345ba6afa2487db34c47b994666bbb', 2, 1);
REPLACE INTO `business_message_relation`(`id`, `create_time`, `modify_time`, `business_type`, `business_desc`, `message_type`, `template_id`, `template_type`, `enabled`) VALUES ('eb957feab7cf4bc58fca0425f651a669', '2025-02-10 14:19:40', '2025-02-10 14:19:42', 'alarm', '告警', 'sms', '2371238', 2, 1);

CREATE TABLE IF NOT EXISTS `email_templates`  (
    `id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
    `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `modify_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
    `template_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板名称',
    `template_type` int(0) NULL DEFAULT NULL COMMENT '模板类型 2表示通知邮件，3表示验证码邮件',
    `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '模板内容',
    `subject` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮件主题',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

REPLACE INTO `email_templates`(`id`, `create_time`, `modify_time`, `template_name`, `template_type`, `content`, `subject`) VALUES ('18345ba6afa2487db34c47b994666bba', '2025-02-10 16:20:03', '2025-02-10 16:20:05', '操作保护验证码邮件', 3, '<!DOCTYPE html><html lang=\"zh\"><head><meta charset=\"UTF-8\"><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"><title>登录验证码邮件</title><style>body {font-family: Arial, sans-serif;line-height: 1.6;}.container {max-width: 600px;margin: 20px auto;padding: 20px;border: 1px solid #ddd;border-radius: 5px;background-color: #f9f9f9;}.highlight {font-weight: bold;color: #d9534f;}.footer {margin-top: 20px;font-size: 0.9em;color: #666;}</style></head><body><div class=\"container\"><p>尊敬的曙光云用户：</p><p>您好！</p><p>您的帐号正在执行敏感操作，验证码：<span class=\"highlight\">[[${code}]]</span>，10分钟内有效。如非本人操作，请忽略此信息。</p><p class=\"footer\">该邮件为系统邮件，请勿直接回复。祝您使用愉快！</p><p class=\"footer\">曙光云</p></div></body></html>', '敏感操作验证码');
REPLACE INTO `email_templates`(`id`, `create_time`, `modify_time`, `template_name`, `template_type`, `content`, `subject`) VALUES ('18345ba6afa2487db34c47b994666bbb', '2025-02-10 16:20:03', '2025-02-10 16:20:05', '告警邮件模板', 2, '<!DOCTYPE html><html lang=\"zh\"><head><meta charset=\"UTF-8\"><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"><title>登录验证码邮件</title><style>body {font-family: Arial, sans-serif;line-height: 1.6;}.container {max-width: 600px;margin: 20px auto;padding: 20px;border: 1px solid #ddd;border-radius: 5px;background-color: #f9f9f9;}.highlight {font-weight: bold;color: #d9534f;}.footer {margin-top: 20px;font-size: 0.9em;color: #666;}</style></head><body><div class=\"container\"><p>尊敬的曙光云用户：</p><p>您好！</p><p>我们监控到：<span class=\"highlight\">[[${content}]]</span>，请您尽快安排相关人员进行处理，以确保系统的正常运行。若您已了解该情况并正在处理，请忽略此信息。</p><p class=\"footer\">该邮件为系统邮件，请勿直接回复。祝您使用愉快！</p><p class=\"footer\">曙光云</p></div></body></html>', '云平台告警通知');
REPLACE INTO `email_templates`(`id`, `create_time`, `modify_time`, `template_name`, `template_type`, `content`, `subject`) VALUES ('18345ba6afa2487db34c47b994666bbf', '2025-02-10 16:20:03', '2025-02-10 16:20:05', '登录验证码邮件', 3, '<!DOCTYPE html><html lang=\"zh\"><head><meta charset=\"UTF-8\"><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"><title>登录验证码邮件</title><style>body {font-family: Arial, sans-serif;line-height: 1.6;}.container {max-width: 600px;margin: 20px auto;padding: 20px;border: 1px solid #ddd;border-radius: 5px;background-color: #f9f9f9;}.highlight {font-weight: bold;color: #d9534f;}.footer {margin-top: 20px;font-size: 0.9em;color: #666;}</style></head><body><div class=\"container\"><p>尊敬的曙光云用户：</p><p>您好！</p><p>您正在登陆系统，验证码：<span class=\"highlight\">[[${code}]]</span>，10分钟内有效。如非本人操作，请忽略此信息。</p><p class=\"footer\">该邮件为系统邮件，请勿直接回复。祝您使用愉快！</p><p class=\"footer\">曙光云</p></div></body></html>', '登录验证码');

