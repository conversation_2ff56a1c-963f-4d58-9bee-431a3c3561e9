REPLACE INTO `iam`.`quota_metric`(`name`, `type_name`, `total_name`, `used_name`) VALUES ('hss_cwpp_ag_workstation', 'hss', 'workstation总量(个)', 'workstation使用量(个)');
REPLACE INTO `iam`.`quota_metric`(`name`, `type_name`, `total_name`, `used_name`) VALUES ('hss_cwpp_ag_workload', 'hss', 'workload总量(个)', 'workload使用量(个)');

REPLACE INTO `iam`.`ram_policy` (`uuid`, `policy_name`, `policy_document`, `action`, `class_name`, `method_name`, `policy_type`, `comments`, `owner_id`, `effect`, `parent_id`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_link_id`, `resource_policy_flag`, `can_delete`) VALUES ('38f418f01d56c9bad09e4b0560b6d590', '管理员角色', '管理员管理-管理员角色', 'null', '', '', NULL, '', NULL, '1', 'caca86da7a85b3381d329cc3f9a27eba', '', 0, '', '2022-12-06 00:00:00', '', '', '', NULL, '', '1');
REPLACE INTO `iam`.`ram_policy` (`uuid`, `policy_name`, `policy_document`, `action`, `class_name`, `method_name`, `policy_type`, `comments`, `owner_id`, `effect`, `parent_id`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_link_id`, `resource_policy_flag`, `can_delete`) VALUES ('3aa3ba785b4375b78e371ccc40e9cc6a', '管理员管理', '管理员管理-管理员用户', 'null', '', '', NULL, '', NULL, '1', 'caca86da7a85b3381d329cc3f9a27eba', '', 0, '', '2022-12-06 00:00:00', '', '', '', NULL, '', '1');
REPLACE INTO `iam`.`ram_policy` (`uuid`, `policy_name`, `policy_document`, `action`, `class_name`, `method_name`, `policy_type`, `comments`, `owner_id`, `effect`, `parent_id`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_link_id`, `resource_policy_flag`, `can_delete`) VALUES ('755389a3a03064ab4841db638f002c28', '列表', '管理员管理-管理员角色-列表', 'null', '', '', NULL, '', NULL, '1', '38f418f01d56c9bad09e4b0560b6d590', '', 0, '', '2022-12-06 09:31:48', '', '', '', NULL, '', '1');
REPLACE INTO `iam`.`ram_policy` (`uuid`, `policy_name`, `policy_document`, `action`, `class_name`, `method_name`, `policy_type`, `comments`, `owner_id`, `effect`, `parent_id`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_link_id`, `resource_policy_flag`, `can_delete`) VALUES ('8d0b6bed7c25a11dd0e17834deb37612', '详情', '管理员管理-管理员用户-详情', 'null', '', '', NULL, '', NULL, '1', '3aa3ba785b4375b78e371ccc40e9cc6a', '', 0, '', '2022-12-07 00:00:00', '/administrator-management/userDetail/:id', '', '', NULL, '2', '1');
REPLACE INTO `iam`.`ram_policy` (`uuid`, `policy_name`, `policy_document`, `action`, `class_name`, `method_name`, `policy_type`, `comments`, `owner_id`, `effect`, `parent_id`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_link_id`, `resource_policy_flag`, `can_delete`) VALUES ('caca86da7a85b3381d329cc3f9a27eba', '管理员管理', '用户管理IAM-管理员管理', 'null', '', '', NULL, '', NULL, '1', 'de5b8265a3f911eb8230f20ed01ddf38', '', 4, '', '2022-12-06 00:00:00', '/administrator-management', 'icon-a-ziyuan78', '', NULL, '0', '1');
REPLACE INTO `iam`.`ram_policy` (`uuid`, `policy_name`, `policy_document`, `action`, `class_name`, `method_name`, `policy_type`, `comments`, `owner_id`, `effect`, `parent_id`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_link_id`, `resource_policy_flag`, `can_delete`) VALUES ('e50f0db767049c0728c6a40434f986c6', '列表', '管理员管理-管理员用户-列表', 'null', '', '', NULL, '', NULL, '1', '3aa3ba785b4375b78e371ccc40e9cc6a', '', 0, '', '2022-12-06 09:32:21', '', '', '', NULL, '', '1');
REPLACE INTO `iam`.`ram_policy` (`uuid`, `policy_name`, `policy_document`, `action`, `class_name`, `method_name`, `policy_type`, `comments`, `owner_id`, `effect`, `parent_id`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_link_id`, `resource_policy_flag`, `can_delete`) VALUES ('ef81af8e62c0cd54c7e0d28434006437', '新建', '管理员管理-管理员用户-新建', 'null', '', '', NULL, '', NULL, '1', '3aa3ba785b4375b78e371ccc40e9cc6a', '', 0, '', '2022-12-06 09:32:59', '', '', '', NULL, '1', '1');
REPLACE INTO `iam`.`ram_policy` (`uuid`, `policy_name`, `policy_document`, `action`, `class_name`, `method_name`, `policy_type`, `comments`, `owner_id`, `effect`, `parent_id`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_link_id`, `resource_policy_flag`, `can_delete`) VALUES ('f70b5175fe64ecac8a4232a3a64e1054', '删除', '管理员管理-管理员用户-删除', 'null', '', '', '1', '', NULL, '1', '3aa3ba785b4375b78e371ccc40e9cc6a', '', 0, '', '2022-12-06 00:00:00', '', '', '', NULL, '1', '1');

REPLACE INTO `iam`.`ram_policy_role` (`uuid`, `policy_id`, `ram_role_id`) VALUES ('23b8b959d4e6cafaf62c17eae7ebdeca', 'caca86da7a85b3381d329cc3f9a27eba', 'c16c9944ca324aa693d3da6f7e7484f7');
REPLACE INTO `iam`.`ram_policy_role` (`uuid`, `policy_id`, `ram_role_id`) VALUES ('43f459a6fbc8fe464946d5ce3ae5a60a', '3aa3ba785b4375b78e371ccc40e9cc6a', 'c16c9944ca324aa693d3da6f7e7484f7');
REPLACE INTO `iam`.`ram_policy_role` (`uuid`, `policy_id`, `ram_role_id`) VALUES ('7e6cdead85a8bb1fc2759803e3dfad8f', '8d0b6bed7c25a11dd0e17834deb37612', 'c16c9944ca324aa693d3da6f7e7484f7');
REPLACE INTO `iam`.`ram_policy_role` (`uuid`, `policy_id`, `ram_role_id`) VALUES ('8753d9f919e8339f4058536ee226b4b4', 'f70b5175fe64ecac8a4232a3a64e1054', 'c16c9944ca324aa693d3da6f7e7484f7');
REPLACE INTO `iam`.`ram_policy_role` (`uuid`, `policy_id`, `ram_role_id`) VALUES ('9d671a6081f24e4066bf23c26c6d5318', 'ef81af8e62c0cd54c7e0d28434006437', 'c16c9944ca324aa693d3da6f7e7484f7');
REPLACE INTO `iam`.`ram_policy_role` (`uuid`, `policy_id`, `ram_role_id`) VALUES ('a0b430b16350091114c9b4da91eca434', '38f418f01d56c9bad09e4b0560b6d590', 'c16c9944ca324aa693d3da6f7e7484f7');
REPLACE INTO `iam`.`ram_policy_role` (`uuid`, `policy_id`, `ram_role_id`) VALUES ('ba62df273177747e490e0cc4c4cc0ef2', '755389a3a03064ab4841db638f002c28', 'c16c9944ca324aa693d3da6f7e7484f7');
REPLACE INTO `iam`.`ram_policy_role` (`uuid`, `policy_id`, `ram_role_id`) VALUES ('e6b3e96c43fffedd8c2e3f4e67f4f47a', 'e50f0db767049c0728c6a40434f986c6', 'c16c9944ca324aa693d3da6f7e7484f7');
UPDATE `iam`.`micro_service` SET `name` = '容器镜像服务 SWR', `description` = '容器镜像与实例', `service_id` = 'sugoncloud-swr-api', `create_time` = '2023-01-06 16:09:18', `modify_time` = '2023-01-06 16:39:58', `link` = 'https://************:30000/swr', `category_id` = '44c1f40ba59d11eb8230f20ed01ddf38', `nav_hidden` = 0, `latest_request_time` = NULL, `collect_time` = '2021-05-17 14:40:20', `version` = NULL, `third_part_access` = 0, `order` = NULL, `public_link` = 'https://***************:34433/swr', `need_policy` = 1 WHERE `id` = 'a87cbc5b-9e49-4026-95e3-6eb10a12bac5';

