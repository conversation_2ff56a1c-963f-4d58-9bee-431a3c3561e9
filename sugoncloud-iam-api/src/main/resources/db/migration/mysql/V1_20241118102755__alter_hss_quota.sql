REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('hss_cwpp_ag_workload', 'admin', 999999999,0, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('hss_cwpp_ag_workstation', 'admin', 999999999,0, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('hss_pc', 'admin', 999999999,0, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('hss_server', 'admin', 999999999,0, 'RegionOne');
REPLACE INTO `quota_department_value` (`metric_name`, `department_id`, `total_value`, `used_value`, `region_id`) VALUES ('hss_server_web', 'admin', 999999999,0, 'RegionOne');

REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('hss_cwpp_ag_workload', 'admin-inner-project', 0,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('hss_cwpp_ag_workstation', 'admin-inner-project', 0,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('hss_pc', 'admin-inner-project', 0,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('hss_server', 'admin-inner-project', 0,0, 'RegionOne');
REPLACE INTO `quota_project_value` (`metric_name`, `project_id`, `total_value`, `used_value`, `region_id`) VALUES ('hss_server_web', 'admin-inner-project', 0,0, 'RegionOne');