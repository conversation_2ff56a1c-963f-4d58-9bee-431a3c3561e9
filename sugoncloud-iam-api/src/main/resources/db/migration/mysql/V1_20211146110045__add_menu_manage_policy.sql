INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '********************************', '菜单管理', '用户管理IAM-菜单管理', 'null', null, null, 'null', '1', 'de5b8265a3f911eb8230f20ed01ddf38', '', '', '', '4', '', '2021-11-04 00:00:00', '/menu-manage', 'icon-cdgl', '', '0', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '********************************');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'ca3eb5a9c430cc84b08e7f7dc210ed1b', '********************************', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'ca3eb5a9c430cc84b08e7f7dc210ed1b');

