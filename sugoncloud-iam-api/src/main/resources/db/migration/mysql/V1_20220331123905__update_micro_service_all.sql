DELETE FROM `micro_service`;
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('07d93831-05a1-4d8b-bee2-14184b13eee0', '云监控CMS', '云监控CMS', 'sugoncloud-monitor-api', '2021-11-1 10:24:56', '2022-3-21 03:00:23', 'http://************:30000/cms', '3deae720-1824-4fb3-acb1-ae1765b07a1b', 0, NULL, NULL, NULL, 0, NULL, 'https://************:34433/cms', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('161f2333cb454c32a745778df2566bcb', '弹性云服务器 ECS', '云服务器', 'sugoncloud-ecs-api', '2021-4-15 15:41:16', '2022-3-21 03:00:23', 'http://************:30000/ecs/#/ecs-instance-list', '4afee060a36111eb8230f20ed01ddf38', 0, NULL, '2021-5-24 14:42:47', NULL, 0, NULL, 'https://************:34433/ecs/#/ecs-instance-list', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('1dd58db4-dc74-918c-1836-7425ec0cc73d', '对等连接 CIT', '网络', 'sugoncloud-vpc-api', '2021-4-15 15:45:00', '2022-3-21 03:00:23', 'http://************:30000/vpc/#/vpc-peer-connect-list', 'ea209553-6177-441b-981a-9c5e40c62ea8', 0, NULL, NULL, NULL, 0, NULL, 'https://************:34433/vpc/#/vpc-peer-connect-list', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('1ec90e83-206d-4a1d-afe0-d58c8f637390', '病毒查杀 VKS', '病毒查杀 VKS', 'sugoncloud-security-api', '2021-8-12 16:35:34', '2022-3-21 03:00:23', 'http://************:30000/hss/#/virus-kill', 'd04f330d-7353-476d-afd8-72e7a8f4002e', 0, NULL, '2021-8-12 16:36:03', NULL, 0, 10, 'https://************:34433/hss/#/virus-kill', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('20b3becc-ebd3-4f78-b628-55238ba8e883', '数据服务 DSS', '数据服务 DSS', 'sugoncloud-dds-api', '2021-9-24 10:24:56', '2021-9-24 10:40:41', 'http://************:30000/xy-data-service', 'dce92741-1d3b-43bc-9e51-ce21b60c9d4a', 0, NULL, NULL, NULL, 1, 30, 'https://************:34433/xy-data-service', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('2112bb3f-d180-4ac4-8514-8c5ec09c1c24', '云硬盘备份 EVBS', '云硬盘', 'sugoncloud-evs-api', '2021-4-15 15:43:44', '2022-3-21 03:00:23', 'http://************:30000/evs/#/ecs-volume-backup-list', '13a38073a36111eb8230f20ed01ddf38', 0, NULL, '2021-5-24 15:59:06', NULL, 0, 20, 'https://************:34433/evs/#/ecs-volume-backup-list', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('21365f9f42b2494296c5fcc74b5ae6c9', '虚拟私有云 VPC', '网络', 'sugoncloud-vpc-api', '2021-4-15 15:45:00', '2022-3-21 03:00:23', 'http://************:30000/vpc/#/vpc-network-list', 'ea209553-6177-441b-981a-9c5e40c62ea8', 0, NULL, NULL, NULL, 0, NULL, 'https://************:34433/vpc/#/vpc-network-list', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('22a84290-97bb-4378-882e-4bb869a8de9d', '数据标签 DLS', '数据标签 DLS', 'sugoncloud-dds-api', '2021-9-24 10:24:56', '2021-9-24 10:40:41', 'http://************:30000/xy-data-label', '1ed139cd-0d6e-43e1-98c5-74b478d3f7f3', 0, NULL, NULL, NULL, 1, NULL, 'https://************:34433/xy-data-label', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('276d2b49-ef06-c4db-2d77-be8ad8612589', '数据保护 BACKUP', '数据保护 BACKUP', 'sugoncloud-backup-api', '2021-5-3 10:24:56', '2022-3-21 03:00:23', 'http://************:30000/backup', '4afee060a36111eb8230f20ed01ddf38', 0, NULL, NULL, NULL, 0, NULL, 'https://************:34433/backup', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('29e3d052-2487-4e14-be59-40116b7a0bf5', '云堡垒机 CBH', '云堡垒机 CBH', 'sugoncloud-cbh-api', '2021-8-12 16:35:34', '2022-3-21 03:00:23', 'http://************:30000/cbh', 'd04f330d-7353-476d-afd8-72e7a8f4002e', 0, NULL, '2021-8-12 16:36:03', NULL, 0, 40, 'https://************:34433/cbh', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('2b7777aa-525f-432a-ac66-8f1ebfd44064', '镜像服务 IMS', '云服务器控制台', 'sugoncloud-ecs-api', '2021-4-15 15:41:16', '2022-3-21 03:00:23', 'http://************:30000/ecs/#/ecs-mirror-image', '4afee060a36111eb8230f20ed01ddf38', 0, NULL, '2021-5-24 14:42:47', NULL, 0, NULL, 'https://************:34433/ecs/#/ecs-mirror-image', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('31069c74-0477-11ec-aedb-1a9678e015b1', '云数据库 PostgreSQL', '云数据库', 'sugoncloud-pgsql-api', '2021-8-24 10:24:56', '2022-3-21 03:00:23', 'http://************:30000/pg', 'a3a0adae-c1e1-11eb-a5c2-fac676e41602', 0, NULL, '2021-8-24 10:24:56', NULL, 0, 10, 'https://************:34433/pg', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('375744d6-0477-11ec-aedb-1a9678e015b1', '云数据库 TiDB', '云数据库', 'sugoncloud-tidb-api', '2021-8-24 17:24:56', '2022-3-21 03:00:23', 'http://************:30000/tidb', 'a3a0adae-c1e1-11eb-a5c2-fac676e41602', 0, NULL, '2021-8-24 17:24:56', NULL, 0, 20, 'https://************:34433/tidb', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('375744d6-0477-11ec-aedb-1a9678e015b2', '云数据库 Redis', '云数据库', 'sugoncloud-redis-api', '2021-8-24 17:24:56', '2022-3-30 11:28:02', '', 'a3a0adae-c1e1-11eb-a5c2-fac676e41602', 0, NULL, '2021-8-24 17:24:56', NULL, 0, 30, NULL, 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('4e9dfd85fdd84b10a8e269dd09e4b535', '裸金属 BMS', '裸金属', 'sugoncloud-bms-api', '2021-4-15 14:25:51', '2022-3-21 03:00:23', 'http://************:30000/bms', '4afee060a36111eb8230f20ed01ddf38', 0, NULL, '2021-5-17 14:33:25', NULL, 0, NULL, 'https://************:34433/bms', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('4ef88d6c-0ed9-48ed-b64e-befdb8329b8b', '微隔离 MIS', '微隔离 MIS', 'sugoncloud-security-api', '2021-8-12 16:35:34', '2022-3-21 03:00:23', 'http://************:30000/hss/#/net-filter', 'd04f330d-7353-476d-afd8-72e7a8f4002e', 0, NULL, '2021-8-12 16:36:03', NULL, 0, 30, 'https://************:34433/hss/#/net-filter', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('5e6015af-0e6f-4cf6-8bab-25d513772468', '安全组 SG', '网络', 'sugoncloud-ecs-api', '2021-4-15 15:45:00', '2022-3-21 03:00:23', 'http://************:30000/ecs/#/security-group-list', 'ea209553-6177-441b-981a-9c5e40c62ea8', 0, NULL, NULL, NULL, 0, NULL, 'https://************:34433/ecs/#/security-group-list', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('6672dcff-bcad-48b8-a140-496e2158a94d', '负载均衡 LB', '网络', 'sugoncloud-vpc-api', '2021-4-15 15:45:00', '2022-3-21 03:00:23', 'http://************:30000/vpc/#/vpc-load-balance-list', 'ea209553-6177-441b-981a-9c5e40c62ea8', 0, NULL, NULL, NULL, 0, NULL, 'https://************:34433/vpc/#/vpc-load-balance-list', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('68ef1d25-d97a-4712-af7a-f3249c2ece6a', '代码仓库 CodeHub', '开发服务DevOps', 'sugoncloud-devops-api', '2021-5-3 10:24:56', '2022-3-21 03:00:23', 'http://************:30000/cicd/#/devops-project', 'ab0377ca-1e5b-4b05-a970-5d79e9ce2015', 0, NULL, NULL, NULL, 0, NULL, 'https://************:34433/cicd/#/devops-project', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('75ae3ab8-355f-466b-ab54-2bacce193ae5', '主机安全 HSS', '主机安全HSS', 'sugoncloud-security-api', '2021-8-12 16:35:34', '2022-3-21 03:00:23', 'http://************:30000/hss', 'd04f330d-7353-476d-afd8-72e7a8f4002e', 0, NULL, '2021-8-12 16:36:03', NULL, 0, 0, 'https://************:34433/hss', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('76b08502a59d11eb8230f20ed01ddf38', '云容器实例 CCI', '容器镜像与实例', 'sugoncloud-cci-api', '2021-4-25 16:09:18', '2022-3-21 03:00:23', 'http://************:30000/cci', '44c1f40ba59d11eb8230f20ed01ddf38', 0, NULL, '2021-5-17 14:40:20', NULL, 0, NULL, 'https://************:34433/cci', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('794efc43-4608-4b3c-8d1d-954c6dccb599', '内网解析 DNS', '网络', 'sugoncloud-vpc-api', '2021-4-15 15:45:00', '2022-3-21 03:00:23', 'http://************:30000/vpc/#/vpc-dns-list', 'ea209553-6177-441b-981a-9c5e40c62ea8', 0, NULL, NULL, NULL, 0, NULL, 'https://************:34433/vpc/#/vpc-dns-list', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('95680aff-8ad4-4e1a-8e90-ef1ee5e45b68', '云服务器快照 ECSS', '云服务器', 'sugoncloud-ecs-api', '2021-4-15 15:41:16', '2022-3-21 03:00:23', 'http://************:30000/ecs/#/ecs-instance-snapshot-list', '4afee060a36111eb8230f20ed01ddf38', 0, NULL, '2021-5-24 14:42:47', NULL, 0, NULL, 'https://************:34433/ecs/#/ecs-instance-snapshot-list', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('9ef1e498-cbaa-4068-9aa4-5e2c210b7c33', '应用市场 AM', '容器镜像与实例', 'sugoncloud-cci-api', '2021-4-25 16:09:18', '2022-3-21 03:00:23', 'http://************:30000/cci/#/applicationMarket', '44c1f40ba59d11eb8230f20ed01ddf38', 0, NULL, '2021-5-17 14:40:20', NULL, 0, NULL, 'https://************:34433/cci/#/applicationMarket', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('a0010200-1ab9-4320-bac6-83110fcf8876', '数据资源目录 DRDS', '数据资源目录 DRDS', 'sugoncloud-dds-api', '2021-9-24 10:24:56', '2021-9-24 10:40:41', 'http://************:30000/xy-data-map', 'dce92741-1d3b-43bc-9e51-ce21b60c9d4a', 0, NULL, NULL, NULL, 1, 20, 'https://************:34433/xy-data-map', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('a24baa59-3458-413d-89b9-c41d785134f7', '弹性公网IP EIP', '网络', 'sugoncloud-vpc-api', '2021-4-15 15:45:00', '2022-3-21 03:00:23', 'http://************:30000/vpc/#/vpc-floating-ip-list', 'ea209553-6177-441b-981a-9c5e40c62ea8', 0, NULL, NULL, NULL, 0, NULL, 'https://************:34433/vpc/#/vpc-floating-ip-list', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('a5cdd2ca-ed13-4794-99a5-a59eb4d4c133', '私有依赖库 PDL', '开发服务DevOps', 'sugoncloud-devops-api', '2021-5-3 10:24:56', '2022-3-21 03:00:23', 'http://************:30000/cicd/#/devops-repository', 'ab0377ca-1e5b-4b05-a970-5d79e9ce2015', 0, NULL, NULL, NULL, 0, NULL, 'https://************:34433/cicd/#/devops-repository', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('a87cbc5b-9e49-4026-95e3-6eb10a12bac5', '容器镜像服务 SWR', '容器镜像与实例', 'sugoncloud-cci-api', '2021-4-25 16:09:18', '2022-3-21 03:00:23', 'http://************:30000/cci/#/namespace', '44c1f40ba59d11eb8230f20ed01ddf38', 0, NULL, '2021-5-17 14:40:20', NULL, 0, NULL, 'https://************:34433/cci/#/namespace', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('ac988dbe28ec428893304640df20087f', '统一身份认证IAM', '统一身份认证', 'sugoncloud-iam-api', '2021-4-15 14:11:27', '2022-3-21 03:00:23', 'http://************:30000/iam', 'a2423457-ab1c-11ec-9548-0894efae434a', 1, NULL, NULL, NULL, 0, NULL, 'https://************:34433/iam', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('ad489cce-90bb-4b5d-8dc2-cc58bfd9094e', '云服务器备份 ECBS', '云服务器', 'sugoncloud-ecs-api', '2021-4-15 15:41:16', '2022-3-21 03:00:23', 'http://************:30000/ecs/#/ecs-instance-backup-list', '4afee060a36111eb8230f20ed01ddf38', 0, NULL, '2021-5-24 14:42:47', NULL, 0, NULL, 'https://************:34433/ecs/#/ecs-instance-backup-list', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('afa751d5-7973-4cb1-9a66-16ddf19295019', '代码扫描 CS', '开发服务DevOps', 'sugoncloud-devops-api', '2021-5-3 10:24:56', '2022-3-21 03:00:23', 'http://************:30000/cicd/#/scan-task-list', 'ab0377ca-1e5b-4b05-a970-5d79e9ce2015', 0, NULL, NULL, NULL, 0, NULL, 'https://************:34433/cicd/#/scan-task-list', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('afa751d5-7973-4cb1-9a66-16ddf19295020', '云上IDE CIDE', '开发服务DevOps', 'sugoncloud-devops-api', '2021-5-3 10:24:56', '2021-9-27 11:31:09', NULL, 'ab0377ca-1e5b-4b05-a970-5d79e9ce2015', 1, NULL, NULL, NULL, 0, NULL, NULL, 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('afa751d5-7973-4cb1-9a66-16ddf1929509', '持续集成 CICD', '开发服务DevOps', 'sugoncloud-devops-api', '2021-5-3 10:24:56', '2022-3-21 03:00:23', 'http://************:30000/cicd/#/devops-assemblyLine-list', 'ab0377ca-1e5b-4b05-a970-5d79e9ce2015', 0, NULL, NULL, NULL, 0, NULL, 'https://************:34433/cicd/#/devops-assemblyLine-list', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('b3a790ae-44c3-4e20-a239-f257234b2d07', '云硬盘快照 EVSS', '云硬盘', 'sugoncloud-evs-api', '2021-4-15 15:43:44', '2022-3-21 03:00:23', 'http://************:30000/evs/#/ecs-volume-snapshot-list', '13a38073a36111eb8230f20ed01ddf38', 0, NULL, '2021-5-24 15:59:06', NULL, 0, 10, 'https://************:34433/evs/#/ecs-volume-snapshot-list', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('b4387ecd-c1e1-11eb-a5c2-fac676e41602', '云数据库 MySQL', '云数据库', 'sugoncloud-mysql-api', '2021-5-3 10:24:56', '2022-3-21 03:00:23', 'http://************:30000/mysql', 'a3a0adae-c1e1-11eb-a5c2-fac676e41602', 0, NULL, '2021-5-17 14:33:25', NULL, 0, 0, 'https://************:34433/mysql', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('cdcefce3-a9f8-4f71-8e4d-c2513c2ad915', '数据治理中心 DGS', '数据治理中心 DGS', 'sugoncloud-dds-api', '2021-9-24 10:24:56', '2021-9-24 10:40:41', 'http://************:30000/xy-meta-data', 'dce92741-1d3b-43bc-9e51-ce21b60c9d4a', 0, NULL, NULL, NULL, 1, 10, 'https://************:34433/xy-meta-data', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('de123898-986c-11ec-8cb4-0894efae434a', '文字识别', '文字识别', 'sugoncloud-smartengine-api', '2022-2-28 17:24:56', '2022-3-24 01:47:56', 'http://************:30000/smartengine', 'ace23085-986c-11ec-8cb4-0894efae434a', 0, NULL, '2022-2-28 17:24:56', NULL, 0, 1, 'https://************:34433/smartengine', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('e3bb9321-cd6e-42d0-8940-d5f41da2e686', '数据开发 DDS', '数据开发 DDS', 'sugoncloud-dds-api', '2021-9-24 10:24:56', '2021-9-24 10:40:41', 'http://************:30000/xy-data-develop', 'dce92741-1d3b-43bc-9e51-ce21b60c9d4a', 0, NULL, NULL, NULL, 1, 0, 'https://************:34433/xy-data-develop', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('e455f304d2e34f2eb448d1d3182c3c67', '云硬盘 EVS', '云硬盘', 'sugoncloud-evs-api', '2021-4-15 15:43:44', '2022-3-21 03:00:23', 'http://************:30000/evs/#/ecs-volume-list', '13a38073a36111eb8230f20ed01ddf38', 0, NULL, '2021-5-24 15:59:06', NULL, 0, 0, 'https://************:34433/evs/#/ecs-volume-list', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('e455f304d2e34f2eb448d1d3182c3c68', '对象存储 OSS', '对象存储', 'sugoncloud-oss-api', '2021-4-15 15:43:44', '2022-3-30 11:28:14', '', '13a38073a36111eb8230f20ed01ddf38', 0, NULL, '2021-5-24 15:59:06', NULL, 0, 40, NULL, 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('e5cbe0cf-8698-4b3d-a20a-691a118cb4ce', '全栈云文档 Doc', '全栈云文档', 'sugoncloud-document-api', '2021-3-1 16:34:35', '2022-3-21 03:00:23', 'http://************:30000', '4afee060a36111eb8230f20ed01ddf38', 0, NULL, '2021-7-16 16:35:33', NULL, 0, NULL, 'https://************:34433', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('e87926e9-e56b-4854-9694-ce671a27ed99', '数据可视化 DAV ', '数据可视化 DAV', 'sugoncloud-dds-api', '2021-9-24 10:24:56', '2021-9-24 10:40:41', 'http://**************:31088/starter.html?isEdit&id=1481088844572004352/noBlank', '1ed139cd-0d6e-43e1-98c5-74b478d3f7f3', 0, NULL, NULL, NULL, 1, NULL, 'https://**************:31088/starter.html?isEdit&id=1481088844572004352/noBlank', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('f0865f60-d82e-4ba8-9dc6-713025c06761', '网马查杀 TKS', '网马查杀 TKS', 'sugoncloud-security-api', '2021-8-12 16:35:34', '2022-3-21 03:00:23', 'http://************:30000/hss/#/network-trojan-killing', 'd04f330d-7353-476d-afd8-72e7a8f4002e', 0, NULL, '2021-8-12 16:36:03', NULL, 0, 20, 'https://************:34433/hss/#/network-trojan-killing', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('f56e82b6-ab1c-11ec-9548-0894efae434a', '运维管理', '运维管理', 'sugoncloud-ops-api', '2021-5-3 10:24:56', '2022-3-24 18:37:41', 'http://************:30000/ops', '670ea69d-ab1c-11ec-9548-0894efae434a', 1, NULL, '2021-5-17 14:33:25', NULL, 0, 0, 'https://************:34433/ops', 0);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('f72db00f-4914-4bdf-9161-7cc5977edf7d', 'E-MapReduce', 'E-MapReduce', 'sugoncloud-emr-api', '2021-5-3 10:24:56', '2022-3-21 03:00:23', 'http://************:30000/emr', '54df68e1a36111eb8230f20ed01ddf38', 0, NULL, NULL, NULL, 0, NULL, 'https://************:34433/emr', 1);
