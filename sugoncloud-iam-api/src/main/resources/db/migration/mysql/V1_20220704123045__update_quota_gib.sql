DELETE FROM `micro_service`;
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('07d93831-05a1-4d8b-bee2-14184b13eee0', '云监控 CMS', '云监控 CMS', 'sugoncloud-monitor-api', '2021-11-1 10:24:56', '2022-6-22 16:39:57', 'https://************:30000/cms', '3deae720-1824-4fb3-acb1-ae1765b07a1b', 0, NULL, NULL, NULL, 0, 10, 'https://***************:34433/cms', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('161f2333cb454c32a745778df2566bcb', '弹性云服务器 ECS', '云服务器', 'sugoncloud-ecs-api', '2021-4-15 15:41:16', '2022-6-22 16:39:57', 'https://************:30000/ecs/#/ecs-instance-list', '4afee060a36111eb8230f20ed01ddf38', 0, NULL, '2021-5-24 14:42:47', NULL, 0, NULL, 'https://***************:34433/ecs/#/ecs-instance-list', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('1aa568c5-f2c5-11ec-bf0b-b8cef6ef31e7', '网页防篡改 WPT', '网页防篡改 WPT', 'sugoncloud-wpt-api', '2022-6-1 15:30:21', '2022-5-6 17:24:39', 'https://************:30000/wpt', 'd04f330d-7353-476d-afd8-72e7a8f4002e', 0, NULL, NULL, NULL, 0, 80, 'https://***************:34433/wpt', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('1dd58db4-dc74-918c-1836-7425ec0cc73d', '对等连接 CIT', '网络', 'sugoncloud-vpc-api', '2021-4-15 15:45:00', '2022-6-22 16:39:57', 'https://************:30000/vpc/#/vpc-peer-connect-list', 'ea209553-6177-441b-981a-9c5e40c62ea8', 0, NULL, NULL, NULL, 0, 50, 'https://***************:34433/vpc/#/vpc-peer-connect-list', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('1ec90e83-206d-4a1d-afe0-d58c8f637390', '病毒查杀 VKS', '云主机安全', 'sugoncloud-security-api', '2021-8-12 16:35:34', '2022-6-22 16:39:57', 'https://************:30000/hss/#/virus-kill', 'd04f330d-7353-476d-afd8-72e7a8f4002e', 0, NULL, '2021-8-12 16:36:03', NULL, 0, 10, 'https://***************:34433/hss/#/virus-kill', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('20b3becc-ebd3-4f78-b628-55238ba8e883', '数据服务 DSS', '数据服务 DSS', 'sugoncloud-dds-api', '2021-9-24 10:24:56', '2021-9-24 10:40:41', 'https://************:30006/xy-data-service', 'dce92741-1d3b-43bc-9e51-ce21b60c9d4a', 0, NULL, NULL, NULL, 1, 30, 'https://***************:34435/xy-data-service', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('2112bb3f-d180-4ac4-8514-8c5ec09c1c24', '云硬盘备份 EVBS', '云硬盘', 'sugoncloud-evs-api', '2021-4-15 15:43:44', '2022-6-22 16:39:57', 'https://************:30000/evs/#/ecs-volume-backup-list', '13a38073a36111eb8230f20ed01ddf38', 0, NULL, '2021-5-24 15:59:06', NULL, 0, 20, 'https://***************:34433/evs/#/ecs-volume-backup-list', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('21365f9f42b2494296c5fcc74b5ae6c9', '虚拟私有云 VPC', '网络', 'sugoncloud-vpc-api', '2021-4-15 15:45:00', '2022-6-22 16:39:57', 'https://************:30000/vpc/#/vpc-network-list', 'ea209553-6177-441b-981a-9c5e40c62ea8', 0, NULL, NULL, NULL, 0, 10, 'https://***************:34433/vpc/#/vpc-network-list', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('22a84290-97bb-4378-882e-4bb869a8de9d', '数据标签 DLS', '数据标签 DLS', 'sugoncloud-dds-api', '2021-9-24 10:24:56', '2021-9-24 10:40:41', 'https://************:30006/xy-data-label', '1ed139cd-0d6e-43e1-98c5-74b478d3f7f3', 0, NULL, NULL, NULL, 1, NULL, 'https://***************:34435/xy-data-label', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('2327ca4e-57f2-407b-9754-b341266c5b73', '数据运维 DOS', '数据运维 DOS', 'sugoncloud-dds-api', '2021-9-24 10:24:56', '2021-9-24 10:40:41', 'https://************:30006/xy-operation', 'dce92741-1d3b-43bc-9e51-ce21b60c9d4a', 0, NULL, NULL, NULL, 1, 70, 'https://***************:34435/xy-operation', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('276d2b49-ef06-c4db-2d77-be8ad8612589', '数据保护 BACKUP', '数据保护 BACKUP', 'sugoncloud-backup-api', '2021-5-3 10:24:56', '2022-6-22 16:39:57', 'https://************:30000/backup', '79369c85-b08d-11ec-bde1-083a88902c92', 0, NULL, NULL, NULL, 0, NULL, '', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('29e3d052-2487-4e14-be59-40116b7a0bf5', '云堡垒机 CBH', '云堡垒机 CBH', 'sugoncloud-cbh-api', '2021-8-12 16:35:34', '2022-6-22 16:39:57', 'https://************:30000/cbh', 'd04f330d-7353-476d-afd8-72e7a8f4002e', 0, NULL, '2021-8-12 16:36:03', NULL, 0, 50, 'https://***************:34433/cbh', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('2b7777aa-525f-432a-ac66-8f1ebfd44064', '镜像服务 IMS', '云服务器', 'sugoncloud-ecs-api', '2021-4-15 15:41:16', '2022-6-22 16:39:57', 'https://************:30000/ecs/#/ecs-mirror-image', '4afee060a36111eb8230f20ed01ddf38', 0, NULL, '2021-5-24 14:42:47', NULL, 0, NULL, 'https://***************:34433/ecs/#/ecs-mirror-image', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('2f3c9486-da36-5e83-a567-bf4cd93608ea', '日志审计 VER', '日志审计 VER', 'sugoncloud-ver-api', '2022-6-22 15:32:49', '2022-6-22 15:32:49', 'https://************:30000/ver', 'd04f330d-7353-476d-afd8-72e7a8f4002e', 0, NULL, NULL, NULL, 0, 60, 'https://***************:34433/ver', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('31069c74-0477-11ec-aedb-1a9678e015b1', '云数据库 PostgreSQL', '云数据库 PostgreSQL', 'sugoncloud-pgsql-api', '2021-8-24 10:24:56', '2022-6-22 16:39:57', 'https://************:30000/pg', 'a3a0adae-c1e1-11eb-a5c2-fac676e41602', 0, NULL, '2021-8-24 10:24:56', NULL, 0, 10, 'https://***************:34433/pg', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('375744d6-0477-11ec-aedb-1a9678e015b1', '云数据库 TiDB', '云数据库 TiDB', 'sugoncloud-tidb-api', '2021-8-24 17:24:56', '2022-6-22 16:39:57', 'https://************:30000/tidb', 'a3a0adae-c1e1-11eb-a5c2-fac676e41602', 0, NULL, '2021-8-24 17:24:56', NULL, 0, 20, 'https://***************:34433/tidb', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('375744d6-0477-11ec-aedb-1a9678e015b2', '云数据库 Redis', '云数据库 Redis', 'sugoncloud-redis-api', '2021-8-24 17:24:56', '2022-3-30 11:28:02', '', 'a3a0adae-c1e1-11eb-a5c2-fac676e41602', 1, NULL, '2021-8-24 17:24:56', NULL, 1, 30, NULL, 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('421cedc2-d9b9-4e6b-a64f-e029614afc7c', '数据库审计 VDB', '数据库审计 VDB', 'sugoncloud-vdb-api', '2022-6-6 17:32:49', '2022-6-6 17:32:49', 'https://************:30000/vdb', 'd04f330d-7353-476d-afd8-72e7a8f4002e', 0, NULL, NULL, NULL, 0, 70, 'https://***************:34433/vdb', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('4e9dfd85fdd84b10a8e269dd09e4b535', '裸金属 BMS', '裸金属', 'sugoncloud-bms-api', '2021-4-15 14:25:51', '2022-6-22 16:39:57', 'https://************:30000/bms', '4afee060a36111eb8230f20ed01ddf38', 0, NULL, '2021-5-17 14:33:25', NULL, 0, NULL, 'https://***************:34433/bms', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('4ef88d6c-0ed9-48ed-b64e-befdb8329b8b', '微隔离 MIS', '云主机安全', 'sugoncloud-security-api', '2021-8-12 16:35:34', '2022-6-22 16:39:57', 'https://************:30000/hss/#/net-filter', 'd04f330d-7353-476d-afd8-72e7a8f4002e', 0, NULL, '2021-8-12 16:36:03', NULL, 0, 30, 'https://***************:34433/hss/#/net-filter', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('5e6015af-0e6f-4cf6-8bab-25d513772468', '安全组 SG', '云服务器', 'sugoncloud-ecs-api', '2021-4-15 15:45:00', '2022-6-22 16:39:57', 'https://************:30000/ecs/#/security-group-list', 'ea209553-6177-441b-981a-9c5e40c62ea8', 0, NULL, NULL, NULL, 0, 30, 'https://***************:34433/ecs/#/security-group-list', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('6672dcff-bcad-48b8-a140-496e2158a94d', '负载均衡 LB', '网络', 'sugoncloud-vpc-api', '2021-4-15 15:45:00', '2022-6-22 16:39:57', 'https://************:30000/vpc/#/vpc-load-balance-list', 'ea209553-6177-441b-981a-9c5e40c62ea8', 0, NULL, NULL, NULL, 0, 40, 'https://***************:34433/vpc/#/vpc-load-balance-list', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('68ef1d25-d97a-4712-af7a-f3249c2ece6a', '代码仓库 CodeHub', '开发服务DevOps', 'sugoncloud-devops-api', '2021-5-3 10:24:56', '2022-6-22 16:39:57', 'https://************:30000/cicd/#/devops-project', 'ab0377ca-1e5b-4b05-a970-5d79e9ce2015', 0, NULL, NULL, NULL, 0, NULL, 'https://***************:34433/cicd/#/devops-project', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('75ae3ab8-355f-466b-ab54-2bacce193ae5', '主机安全 HSS', '云主机安全', 'sugoncloud-security-api', '2021-8-12 16:35:34', '2022-6-22 16:39:57', 'https://************:30000/hss', 'd04f330d-7353-476d-afd8-72e7a8f4002e', 0, NULL, '2021-8-12 16:36:03', NULL, 0, 0, 'https://***************:34433/hss', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('76b08502a59d11eb8230f20ed01ddf38', '云容器实例 CCI', '容器镜像与实例', 'sugoncloud-cci-api', '2021-4-25 16:09:18', '2022-6-22 16:39:57', 'https://************:30000/cci', '44c1f40ba59d11eb8230f20ed01ddf38', 0, NULL, '2021-5-17 14:40:20', NULL, 0, NULL, 'https://***************:34433/cci', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('794efc43-4608-4b3c-8d1d-954c6dccb599', '内网解析 DNS', '网络', 'sugoncloud-vpc-api', '2021-4-15 15:45:00', '2022-6-22 16:39:58', 'https://************:30000/vpc/#/vpc-dns-list', 'ea209553-6177-441b-981a-9c5e40c62ea8', 0, NULL, NULL, NULL, 0, 60, 'https://***************:34433/vpc/#/vpc-dns-list', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('876a51f8-f1cf-2619-6f92-47f35424c232', '漏洞扫描 RAS', '漏洞扫描 RAS', 'sugoncloud-ras-api', '2022-5-5 17:32:49', '2022-6-22 16:39:58', 'https://************:30000/ras', 'd04f330d-7353-476d-afd8-72e7a8f4002e', 0, NULL, NULL, NULL, 0, 40, 'https://***************:34433/ras', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('95680aff-8ad4-4e1a-8e90-ef1ee5e45b68', '云服务器快照 ECSS', '云服务器', 'sugoncloud-ecs-api', '2021-4-15 15:41:16', '2022-6-22 16:39:58', 'https://************:30000/ecs/#/ecs-instance-snapshot-list', '4afee060a36111eb8230f20ed01ddf38', 0, NULL, '2021-5-24 14:42:47', NULL, 0, NULL, 'https://***************:34433/ecs/#/ecs-instance-snapshot-list', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('9ef1e498-cbaa-4068-9aa4-5e2c210b7c33', '应用市场 AM', '容器镜像与实例', 'sugoncloud-cci-api', '2021-4-25 16:09:18', '2022-6-22 16:39:58', 'https://************:30000/cci/#/applicationMarket', '44c1f40ba59d11eb8230f20ed01ddf38', 0, NULL, '2021-5-17 14:40:20', NULL, 0, NULL, 'https://***************:34433/cci/#/applicationMarket', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('a0010200-1ab9-4320-bac6-83110fcf8876', '数据资源目录 DRDS', '数据资源目录 DRDS', 'sugoncloud-dds-api', '2021-9-24 10:24:56', '2021-9-24 10:40:41', 'https://************:30006/xy-data-map', 'dce92741-1d3b-43bc-9e51-ce21b60c9d4a', 0, NULL, NULL, NULL, 1, 20, 'https://***************:34435/xy-data-map', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('a24baa59-3458-413d-89b9-c41d785134f7', '弹性公网IP EIP', '网络', 'sugoncloud-vpc-api', '2021-4-15 15:45:00', '2022-6-22 16:39:58', 'https://************:30000/vpc/#/vpc-floating-ip-list', 'ea209553-6177-441b-981a-9c5e40c62ea8', 0, NULL, NULL, NULL, 0, 20, 'https://***************:34433/vpc/#/vpc-floating-ip-list', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('a5cdd2ca-ed13-4794-99a5-a59eb4d4c133', '私有依赖库 PDL', '开发服务DevOps', 'sugoncloud-devops-api', '2021-5-3 10:24:56', '2022-6-22 16:39:58', 'https://************:30000/cicd/#/devops-repository', 'ab0377ca-1e5b-4b05-a970-5d79e9ce2015', 0, NULL, NULL, NULL, 0, NULL, 'https://***************:34433/cicd/#/devops-repository', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('a87cbc5b-9e49-4026-95e3-6eb10a12bac5', '容器镜像服务 SWR', '容器镜像与实例', 'sugoncloud-cci-api', '2021-4-25 16:09:18', '2022-6-22 16:39:58', 'https://************:30000/cci/#/namespace', '44c1f40ba59d11eb8230f20ed01ddf38', 0, NULL, '2021-5-17 14:40:20', NULL, 0, NULL, 'https://***************:34433/cci/#/namespace', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('aa956f70-913a-420f-9da9-e64a31a3d700', '数据质量 DQS', '数据质量 DQS', 'sugoncloud-dds-api', '2021-9-24 10:24:56', '2021-9-24 10:40:41', 'https://************:30006/xy-data-quality', 'dce92741-1d3b-43bc-9e51-ce21b60c9d4a', 0, NULL, NULL, NULL, 1, 60, 'https://***************:34435/xy-data-quality', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('ac988dbe28ec428893304640df20087f', '统一身份认证IAM', '统一身份认证', 'sugoncloud-iam-api', '2021-4-15 14:11:27', '2022-6-22 16:39:58', 'https://************:30000/iam', 'a2423457-ab1c-11ec-9548-0894efae434a', 1, NULL, NULL, NULL, 0, NULL, 'https://***************:34433/iam', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('ad489cce-90bb-4b5d-8dc2-cc58bfd9094e', '云服务器备份 ECBS', '云服务器', 'sugoncloud-ecs-api', '2021-4-15 15:41:16', '2022-6-22 16:39:58', 'https://************:30000/ecs/#/ecs-instance-backup-list', '4afee060a36111eb8230f20ed01ddf38', 0, NULL, '2021-5-24 14:42:47', NULL, 0, NULL, 'https://***************:34433/ecs/#/ecs-instance-backup-list', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('afa751d5-7973-4cb1-9a66-16ddf19295019', '代码扫描 CS', '开发服务DevOps', 'sugoncloud-devops-api', '2021-5-3 10:24:56', '2022-6-22 16:39:58', 'https://************:30000/cicd/#/scan-task-list', 'ab0377ca-1e5b-4b05-a970-5d79e9ce2015', 0, NULL, NULL, NULL, 0, NULL, 'https://***************:34433/cicd/#/scan-task-list', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('afa751d5-7973-4cb1-9a66-16ddf19295020', '云上IDE CIDE', '开发服务DevOps', 'sugoncloud-devops-api', '2021-5-3 10:24:56', '2021-9-27 11:31:09', NULL, 'ab0377ca-1e5b-4b05-a970-5d79e9ce2015', 1, NULL, NULL, NULL, 0, NULL, NULL, 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('afa751d5-7973-4cb1-9a66-16ddf1929509', '持续集成 CICD', '开发服务DevOps', 'sugoncloud-devops-api', '2021-5-3 10:24:56', '2022-6-22 16:39:58', 'https://************:30000/cicd/#/devops-assemblyLine-list', 'ab0377ca-1e5b-4b05-a970-5d79e9ce2015', 0, NULL, NULL, NULL, 0, NULL, 'https://***************:34433/cicd/#/devops-assemblyLine-list', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('b3a790ae-44c3-4e20-a239-f257234b2d07', '云硬盘快照 EVSS', '云硬盘', 'sugoncloud-evs-api', '2021-4-15 15:43:44', '2022-6-22 16:39:58', 'https://************:30000/evs/#/ecs-volume-snapshot-list', '13a38073a36111eb8230f20ed01ddf38', 0, NULL, '2021-5-24 15:59:06', NULL, 0, 10, 'https://***************:34433/evs/#/ecs-volume-snapshot-list', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('b4387ecd-c1e1-11eb-a5c2-fac676e41602', '云数据库 MySQL', '云数据库 MySQL', 'sugoncloud-mysql-api', '2021-5-3 10:24:56', '2022-6-22 16:39:58', 'https://************:30000/mysql', 'a3a0adae-c1e1-11eb-a5c2-fac676e41602', 0, NULL, '2021-5-17 14:33:25', NULL, 0, 0, 'https://***************:34433/mysql', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('b86b9cc4-53bd-43b7-9fd8-97521e2f99b9', '数据审计 DAS', '数据审计 DAS', 'sugoncloud-dds-api', '2021-9-24 10:24:56', '2021-9-24 10:40:41', 'https://************:30006/xy-audit', 'dce92741-1d3b-43bc-9e51-ce21b60c9d4a', 0, NULL, NULL, NULL, 1, 50, 'https://***************:34435/xy-audit', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('c8f9ea8c-9fd3-434c-91b9-134a5effd826', '智能运维 SmartOps', '智能运维 SmartOps', 'sugoncloud-retrieval-api', '2021-11-1 10:24:56', '2022-6-22 16:39:58', 'https://************:30000/ses', '3deae720-1824-4fb3-acb1-ae1765b07a1b', 0, NULL, NULL, NULL, 0, 20, 'https://***************:34433/ses', 0);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('cdcefce3-a9f8-4f71-8e4d-c2513c2ad915', '数据治理中心 DGS', '数据治理中心 DGS', 'sugoncloud-dds-api', '2021-9-24 10:24:56', '2021-9-24 10:40:41', 'https://************:30006/xy-meta-data', 'dce92741-1d3b-43bc-9e51-ce21b60c9d4a', 0, NULL, NULL, NULL, 1, 10, 'https://***************:34435/xy-meta-data', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('de123898-986c-11ec-8cb4-0894efae434a', '文字识别', '文字识别', 'sugoncloud-smartengine-api', '2022-2-28 17:24:56', '2022-6-22 16:39:58', 'https://************:30000/smartengine', 'ace23085-986c-11ec-8cb4-0894efae434a', 0, NULL, '2022-2-28 17:24:56', NULL, 0, 1, 'https://***************:34433/smartengine', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('e35a7bf8-4a75-34ce-3666-0b944f7f8ab6', 'WEB应用防火墙 WAF', 'WEB应用防火墙 WAF', 'sugoncloud-waf-api', '2022-6-6 17:32:49', '2022-6-23 11:26:36', 'https://************:30000/waf', 'd04f330d-7353-476d-afd8-72e7a8f4002e', 0, NULL, NULL, NULL, 0, 100, 'https://***************:34433/waf', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('e3bb9321-cd6e-42d0-8940-d5f41da2e686', '数据开发 DDS', '数据开发 DDS', 'sugoncloud-dds-api', '2021-9-24 10:24:56', '2021-9-24 10:40:41', 'https://************:30006/xy-data-develop', 'dce92741-1d3b-43bc-9e51-ce21b60c9d4a', 0, NULL, NULL, NULL, 1, 0, 'https://***************:34435/xy-data-develop', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('e455f304d2e34f2eb448d1d3182c3c67', '云硬盘 EVS', '云硬盘', 'sugoncloud-evs-api', '2021-4-15 15:43:44', '2022-6-22 16:39:58', 'https://************:30000/evs/#/ecs-volume-list', '13a38073a36111eb8230f20ed01ddf38', 0, NULL, '2021-5-24 15:59:06', NULL, 0, 0, 'https://***************:34433/evs/#/ecs-volume-list', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('e455f304d2e34f2eb448d1d3182c3c68', '对象存储 OSS', '对象存储', 'sugoncloud-oss-api', '2021-4-15 15:43:44', '2022-3-30 11:28:14', '', '13a38073a36111eb8230f20ed01ddf38', 1, NULL, '2021-5-24 15:59:06', NULL, 1, 40, NULL, 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('e5cbe0cf-8698-4b3d-a20a-691a118cb4ce', '全栈云文档 Doc', '全栈云文档', 'sugoncloud-document-api', '2021-3-1 16:34:35', '2022-6-22 16:39:58', 'https://************:30000', '4afee060a36111eb8230f20ed01ddf38', 0, NULL, '2021-7-16 16:35:33', NULL, 0, NULL, 'https://***************:34433', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('e87926e9-e56b-4854-9694-ce671a27ed99', '数据可视化 DAV ', '数据可视化 DAV', 'sugoncloud-dds-api', '2021-9-24 10:24:56', '2022-4-2 21:53:01', 'https://************:30010/dav-web/load.html/otherType', '1ed139cd-0d6e-43e1-98c5-74b478d3f7f3', 0, NULL, NULL, NULL, 1, NULL, 'https://***************:34436/dav-web/load.html/otherType', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('f0865f60-d82e-4ba8-9dc6-713025c06761', '网马查杀 TKS', '云主机安全', 'sugoncloud-security-api', '2021-8-12 16:35:34', '2022-6-22 16:39:58', 'https://************:30000/hss/#/network-trojan-killing', 'd04f330d-7353-476d-afd8-72e7a8f4002e', 0, NULL, '2021-8-12 16:36:03', NULL, 0, 20, 'https://***************:34433/hss/#/network-trojan-killing', 1);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('f56e82b6-ab1c-11ec-9548-0894efae434a', '运维管理', '运维管理', 'sugoncloud-ops-api', '2021-5-3 10:24:56', '2022-6-22 16:39:58', 'https://************:30000/ops', '670ea69d-ab1c-11ec-9548-0894efae434a', 1, NULL, '2021-5-17 14:33:25', NULL, 0, 0, 'https://************:34433/ops', 0);
INSERT INTO `micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('f72db00f-4914-4bdf-9161-7cc5977edf7d', 'E-MapReduce', 'E-MapReduce', 'sugoncloud-emr-api', '2021-5-3 10:24:56', '2022-6-22 16:39:58', 'https://************:30000/emr', '54df68e1a36111eb8230f20ed01ddf38', 0, NULL, NULL, NULL, 0, NULL, 'https://***************:34433/emr', 1);

DELETE FROM `quota_metric`;
INSERT INTO `quota_metric` VALUES ('cbh_cpu', 'cbh', 'cpu总量(个)', 'cpu使用量(个)');
INSERT INTO `quota_metric` VALUES ('cbh_ram', 'cbh', '内存总量(GiB)', '内存使用量(GiB)');
INSERT INTO `quota_metric` VALUES ('cce_cpu', 'cce', 'cpu总量(个)', 'cpu使用量(个)');
INSERT INTO `quota_metric` VALUES ('cce_ram', 'cce', '内存总量(GiB)', '内存使用量(GiB)');
INSERT INTO `quota_metric` VALUES ('cce_volume', 'cce', '容器卷总量(GiB)', '容器卷使用量(GiB)');
INSERT INTO `quota_metric` VALUES ('ecs_cpu', 'ecs', 'cpu总量(个)', 'cpu使用量(个)');
INSERT INTO `quota_metric` VALUES ('ecs_ram', 'ecs', '内存总量(GiB)', '内存使用量(GiB)');
INSERT INTO `quota_metric` VALUES ('emr_cpu', 'emr', 'cpu总量(个)', 'cpu使用量(个)');
INSERT INTO `quota_metric` VALUES ('emr_ram', 'emr', '内存总量(GiB)', '内存使用量(GiB)');
INSERT INTO `quota_metric` VALUES ('emr_volume', 'emr', '云硬盘总量(GiB)', '云硬盘使用总量(GiB)');
INSERT INTO `quota_metric` VALUES ('evs_capacity', 'evs', '容量总量(GiB)', '容量使用量(GiB)');
INSERT INTO `quota_metric` VALUES ('evs_snapshot', 'evs', '快照总量(GiB)', '快照使用量(GiB)');
INSERT INTO `quota_metric` VALUES ('floatip_num', 'floatip', '弹性公网IP总量(个)', '弹性公网IP使用量(个)');
INSERT INTO `quota_metric` VALUES ('hss_pc', 'hss', 'PC总量(个)', 'PC使用量(个)');
INSERT INTO `quota_metric` VALUES ('hss_server', 'hss', '服务器总量(个)', '服务器使用量(个)');
INSERT INTO `quota_metric` VALUES ('hss_server_web', 'hss', 'web服务器总量(个)', 'web服务器使用量(个)');
INSERT INTO `quota_metric` VALUES ('mysql_cpu', 'mysql', 'cpu总量(个)', 'cpu使用量(个)');
INSERT INTO `quota_metric` VALUES ('mysql_ram', 'mysql', '内存总量(GiB)', '内存使用量(GiB)');
INSERT INTO `quota_metric` VALUES ('mysql_volume', 'mysql', '云硬盘总量(GiB)', '云硬盘使用总量(GiB)');
INSERT INTO `quota_metric` VALUES ('pgsql_cpu', 'pgsql', 'cpu总量(个)', 'cpu使用量(个)');
INSERT INTO `quota_metric` VALUES ('pgsql_ram', 'pgsql', '内存总量(GiB)', '内存使用量(GiB)');
INSERT INTO `quota_metric` VALUES ('pgsql_volume', 'pgsql', '云硬盘总量(GiB)', '云硬盘使用总量(GiB)');
INSERT INTO `quota_metric` VALUES ('ras_cpu', 'ras', 'cpu总量(个)', 'cpu使用量(个)');
INSERT INTO `quota_metric` VALUES ('ras_ram', 'ras', '内存总量(GiB)', '内存使用量(GiB)');
INSERT INTO `quota_metric` VALUES ('tidb_cpu', 'tidb', 'cpu总量(个)', 'cpu使用量(个)');
INSERT INTO `quota_metric` VALUES ('tidb_ram', 'tidb', '内存总量(GiB)', '内存使用量(GiB)');
INSERT INTO `quota_metric` VALUES ('tidb_volume', 'tidb', '云硬盘总量(GiB)', '云硬盘使用总量(GiB)');
INSERT INTO `quota_metric` VALUES ('vdb_cpu', 'vdb', 'cpu总量(个)', 'cpu使用量(个)');
INSERT INTO `quota_metric` VALUES ('vdb_ram', 'vdb', '内存总量(GiB)', '内存使用量(GiB)');
INSERT INTO `quota_metric` VALUES ('vdb_volume', 'vdb', '云硬盘总量(GiB)', '云硬盘使用总量(GiB)');
INSERT INTO `quota_metric` VALUES ('ver_cpu', 'ver', 'cpu总量(个)', 'cpu使用量(个)');
INSERT INTO `quota_metric` VALUES ('ver_ram', 'ver', '内存总量(GiB)', '内存使用量(GiB)');
INSERT INTO `quota_metric` VALUES ('ver_volume', 'ver', '云硬盘总量(GiB)', '云硬盘使用总量(GiB)');
INSERT INTO `quota_metric` VALUES ('waf_cpu', 'waf', 'cpu总量(个)', 'cpu使用量(个)');
INSERT INTO `quota_metric` VALUES ('waf_ram', 'waf', '内存总量(GiB)', '内存使用量(GiB)');
INSERT INTO `quota_metric` VALUES ('waf_volume', 'waf', '云硬盘总量(GiB)', '云硬盘使用总量(GiB)');
INSERT INTO `quota_metric` VALUES ('wpt_cpu', 'wpt', 'cpu总量(个)', 'cpu使用量(个)');
INSERT INTO `quota_metric` VALUES ('wpt_ram', 'wpt', '内存总量(GiB)', '内存使用量(GiB)');

DELETE FROM `quota_type`;
INSERT INTO `quota_type` VALUES ('cbh', '云堡垒机CBH配额', '8');
INSERT INTO `quota_type` VALUES ('cce', '云容器实例CCI配额', '9');
INSERT INTO `quota_type` VALUES ('ecs', '云服务器ECS配额', '1');
INSERT INTO `quota_type` VALUES ('emr', 'E-MapReduce配额', '6');
INSERT INTO `quota_type` VALUES ('evs', '云硬盘EVS配额', '2');
INSERT INTO `quota_type` VALUES ('floatip', '弹性公网IP配额', '2');
INSERT INTO `quota_type` VALUES ('hss', '主机安全HSS配额', '7');
INSERT INTO `quota_type` VALUES ('mysql', '云数据库MySQL配额', '3');
INSERT INTO `quota_type` VALUES ('pgsql', '云数据库PostgreSQL配额', '4');
INSERT INTO `quota_type` VALUES ('ras', '云漏洞扫描RAS配额', '10');
INSERT INTO `quota_type` VALUES ('tidb', '云数据库TiDB配额', '5');
INSERT INTO `quota_type` VALUES ('vdb', '数据库审计VDB配额', '13');
INSERT INTO `quota_type` VALUES ('ver', '日志审计VER配额', '11');
INSERT INTO `quota_type` VALUES ('waf', 'WEB应用防火墙WAF配额', '14');
INSERT INTO `quota_type` VALUES ('wpt', '网页防篡改WPT配额', '12');



