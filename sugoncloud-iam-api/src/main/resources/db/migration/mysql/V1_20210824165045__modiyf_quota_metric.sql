DELETE FROM `quota_metric`;
INSERT INTO `quota_metric` VALUES ('cce_cpu', 'cce', 'cpu总量(个)', 'cpu使用量(个)');
INSERT INTO `quota_metric` VALUES ('cce_ram', 'cce', '内存总量(MB)', '内存使用量(MB)');
INSERT INTO `quota_metric` VALUES ('ecs_cpu', 'ecs', 'cpu总量(个)', 'cpu使用量(个)');
INSERT INTO `quota_metric` VALUES ('ecs_ram', 'ecs', '内存总量(MB)', '内存使用量(MB)');
INSERT INTO `quota_metric` VALUES ('emr_cpu', 'emr', 'cpu总量(个)', 'cpu使用量(个)');
INSERT INTO `quota_metric` VALUES ('emr_ram', 'emr', '内存总量(MB)', '内存使用量(MB)');
INSERT INTO `quota_metric` VALUES ('evs_capacity', 'evs', '容量总量(GB)', '容量使用量(GB)');
INSERT INTO `quota_metric` VALUES ('evs_snapshot', 'evs', '快照总量(GB)', '快照使用量(GB)');
INSERT INTO `quota_metric` VALUES ('mysql_cpu', 'mysql', 'cpu总量(个)', 'cpu使用量(个)');
INSERT INTO `quota_metric` VALUES ('mysql_ram', 'mysql', '内存总量(MB)', '内存使用量(MB)');
INSERT INTO `quota_metric` VALUES ('mysql_volume', 'mysql', '云硬盘总量(GB)', '云硬盘使用总量(GB)');
INSERT INTO `quota_metric` VALUES ('pgsql_cpu', 'pgsql', 'cpu总量(个)', 'cpu使用量(个)');
INSERT INTO `quota_metric` VALUES ('pgsql_ram', 'pgsql', '内存总量(MB)', '内存使用量(MB)');
INSERT INTO `quota_metric` VALUES ('pgsql_volume', 'pgsql', '云硬盘总量(GB)', '云硬盘使用总量(GB)');
INSERT INTO `quota_metric` VALUES ('tidb_cpu', 'tidb', 'cpu总量(个)', 'cpu使用量(个)');
INSERT INTO `quota_metric` VALUES ('tidb_ram', 'tidb', '内存总量(MB)', '内存使用量(MB)');
INSERT INTO `quota_metric` VALUES ('tidb_volume', 'tidb', '云硬盘总量(GB)', '云硬盘使用总量(GB)');
INSERT INTO `quota_metric` VALUES ('cbh_cpu', 'cbh', 'cpu总量(个)', 'cpu使用量(个)');
INSERT INTO `quota_metric` VALUES ('cbh_ram', 'cbh', '内存总量(MB)', '内存使用量(MB)');

DELETE FROM `quota_type`;
INSERT INTO `quota_type` VALUES ('cbh', '云堡垒机CBH配额', '8');
INSERT INTO `quota_type` VALUES ('cce', '云容器引擎CCE配额', '9');
INSERT INTO `quota_type` VALUES ('ecs', '云服务器ECS配额', '1');
INSERT INTO `quota_type` VALUES ('emr', 'E-MapReduce配额', '6');
INSERT INTO `quota_type` VALUES ('evs', '云硬盘EVS配额', '2');
INSERT INTO `quota_type` VALUES ('hss', '主机安全HSS配额', '7');
INSERT INTO `quota_type` VALUES ('mysql', '云数据库MySQL配额', '3');
INSERT INTO `quota_type` VALUES ('pgsql', '云数据库PostgreSQL配额', '4');
INSERT INTO `quota_type` VALUES ('tidb', '云数据库TiDB配额', '5');
