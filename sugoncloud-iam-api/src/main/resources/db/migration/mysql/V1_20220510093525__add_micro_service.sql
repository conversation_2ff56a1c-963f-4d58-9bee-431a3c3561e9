INSERT INTO `iam`.`micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('876a51f8-f1cf-2619-6f92-47f35424c232', '漏洞扫描 RAS', '漏洞扫描 RAS', 'sugoncloud-ras-api', '2022-05-05 17:32:49', '2022-05-06 17:24:39', 'https://************:30000/ras', 'd04f330d-7353-476d-afd8-72e7a8f4002e', 0, NULL, NULL, NULL, 0, NULL, 'https://dev.console.shuguangcloud.com:34433/ras', 1);

INSERT INTO `iam`.`quota_type` (`name`, `description`, `sequence`) VALUES ('ras', '云漏洞扫描RAS配额', 10);
INSERT INTO `iam`.`quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('ras_cpu', 'ras', 'cpu总量(个)', 'cpu使用量(个)');
INSERT INTO `iam`.`quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('ras_ram', 'ras', '内存总量(GB)', '内存使用量(GB)');
