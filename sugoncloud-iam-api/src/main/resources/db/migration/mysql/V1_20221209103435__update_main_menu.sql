REPLACE INTO `iam`.`micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('4d989133-aa0e-45b3-83f7-fa3a47c68faf', '网络 QoS', '网络', 'sugoncloud-vpc-api', '2021-04-15 15:45:00', '2022-06-22 16:39:58', 'https://************:30000/vpc/#/vpc-network-qos', 'ea209553-6177-441b-981a-9c5e40c62ea8', 0, NULL, NULL, NULL, 0, 80, 'https://***************:34433/vpc/#/vpc-network-qos', 1);
UPDATE `iam`.globalsettings SET policy_document='720',retained_field='强制修改密码的时间间隔（天），最长720' WHERE uuid='1231235';
UPDATE `iam`.micro_service SET `name`='负载均衡 SLB' WHERE id='6672dcff-bcad-48b8-a140-496e2158a94d';
UPDATE `iam`.micro_service SET `name`='对等连接 PC' WHERE id='1dd58db4-dc74-918c-1836-7425ec0cc73d';
UPDATE `iam`.micro_service SET `name`='网络 ACL' WHERE id='a9f37912-fd01-11ec-b241-0894efae434a';
UPDATE `iam`.micro_service_category SET `name`='数据库' WHERE id='a3a0adae-c1e1-11eb-a5c2-fac676e41602';
UPDATE `iam`.micro_service_category SET `name`='中间件' WHERE id='4546d40a-2d8a-11ed-99d5-0894efae434a';
UPDATE `iam`.micro_service_category SET `name`='安全合规' WHERE id='d04f330d-7353-476d-afd8-72e7a8f4002e';
