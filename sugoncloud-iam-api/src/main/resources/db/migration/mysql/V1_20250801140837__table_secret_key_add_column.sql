--表secret key添加inner_key字段
DELIMITER //
DROP PROCEDURE IF EXISTS `AddInnerColumnIfNotExists` //
CREATE PROCEDURE AddInnerColumnIfNotExists()
BEGIN
    DECLARE column_exists INT;

    -- 检查列是否存在
SELECT COUNT(*)
INTO column_exists
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'secret_key'
  AND COLUMN_NAME = 'inner_key';

-- 如果列不存在，则添加列
IF column_exists = 0 THEN
ALTER TABLE secret_key ADD COLUMN `inner_key` tinyint(1)  NOT NULL DEFAULT 0 COMMENT '内置key' AFTER `user_id`;
END IF;
END //

DELIMITER ;
CALL AddInnerColumnIfNotExists();