ALTER TABLE `quota_department_value`
ADD COLUMN `region_id` varchar(64) NOT NULL DEFAULT 'RegionOne' AFTER `used_value` ,
DROP PRIMARY KEY,
ADD PRIMARY KEY (`metric_name`, `department_id`, `region_id`) USING BTREE;

ALTER TABLE `quota_project_value`
ADD COLUMN `region_id` varchar(64) NOT NULL DEFAULT 'RegionOne' AFTER `used_value`,
DROP PRIMARY KEY,
ADD PRIMARY KEY (`metric_name`,`project_id`,`region_id`) USING BTREE;

DELETE  FROM `quota_metric` where `name` = 'dbs_disk';
REPLACE INTO `quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('dbs_disk', 'dbs', '磁盘存储总量(GiB)', '磁盘存储使用量(GiB)');

ALTER TABLE `user`
ADD COLUMN `modify_time` datetime DEFAULT NULL AFTER `created_at`;

ALTER TABLE `endpoint`
DROP PRIMARY KEY,
ADD PRIMARY KEY (`id`, `region_id`) USING BTREE;

UPDATE globalsettings set policy_document='false' WHERE policy_name='is_spice';

INSERT INTO `endpoint` (`id`, `legacy_endpoint_id`, `interface`, `service_id`, `url`, `extra`, `enabled`, `region_id`) VALUES ('8e6184d171544d21ba112773e4deffcd', NULL, 'public', '83b0c027a1b4cde14390e868269ef43f', 'http://gova-api.openstack.svc.cluster.local/v2', '{}', 1, 'RegionOne');
INSERT INTO `service` (`id`, `type`, `enabled`, `extra`, `description`, `name`) VALUES ('83b0c027a1b4cde14390e868269ef43f', 'compute', 1, '{\"name\": \"gova\", \"description\": \"RegionOne: gova (compute) service\"}', NULL, 'gova');
UPDATE `micro_service` SET `service_id` = 'sugoncloud-ecs-v2-api' WHERE `id` = '161f2333cb454c32a745778df2566bcb';
UPDATE `micro_service` SET `name` = 'AnhanDB(for PostgreSQL)',`description` = 'AnhanDB(for PostgreSQL)' WHERE `id` = '31069c74-0477-11ec-aedb-1a9678e015b1';
UPDATE `micro_service` SET `name` = 'PingCAP TiDB',`description` = 'PingCAP TiDB' WHERE	`id` = '375744d6-0477-11ec-aedb-1a9678e015b1';
UPDATE `micro_service` SET `name` = 'AnhanDB(for Redis)',`description` = 'AnhanDB(for Redis)' WHERE	`id` = '4b19e803-2d8b-11ed-99d5-0894efae434a';
UPDATE `micro_service` SET `name` = 'AnhanDB(for MongoDB)',`description` = 'AnhanDB(for MongoDB)' WHERE	`id` = '5848c8a0-ccac-4ff9-b4bf-ee42c9545c77';
UPDATE `micro_service` SET `name` = 'AnhanDB(for MySQL)',`description` = 'AnhanDB(for MySQL)' WHERE `id` = 'b4387ecd-c1e1-11eb-a5c2-fac676e41602';
UPDATE `micro_service` SET `name` = '达梦数据库 DM',`description` = '达梦数据库 DM' WHERE	`id` = 'fbaa65c1-c88e-11ec-9bba-0894efae434f';

UPDATE `quota_type` SET description = 'AnhanDB(for MySQL)' WHERE name = 'mysql';
UPDATE `quota_type` SET description = 'AnhanDB(for PostgreSQL)' WHERE name = 'pgsql';
UPDATE `quota_type` SET description = 'PingCAP TiDB' WHERE name = 'tidb';
UPDATE `quota_type` SET description = 'AnhanDB(for Redis)' WHERE name = 'redis';
UPDATE `quota_type` SET description = 'AnhanDB(for MongoDB)' WHERE name = 'mongodb';
UPDATE `quota_type` SET description = '达梦数据库 DM' WHERE name = 'dm';

UPDATE `micro_service` SET `public_link` = 'https://***************:34433/backup' WHERE id = '276d2b49-ef06-c4db-2d77-be8ad8612589';

REPLACE INTO `micro_service_category` (`id`, `name`, `description`, `create_time`, `modify_time`, `order`, `nav_hidden`) VALUES ('2c2e1bed-26a7-11ee-9065-0242bd4206e3', '运营管理', '运营管理', '2023-07-20 10:43:13', '2023-07-20 10:43:13', 22, 1);
REPLACE INTO `iam`.`micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`, `license_key`) VALUES ('c8071164-1f89-11ee-9065-0242bd4206e3', '计费管理', '计费管理', 'sugoncloud-meterage-api', '2023-07-11 09:25:12', '2023-09-14 16:27:32', 'https://************:30000/meterage', '2c2e1bed-26a7-11ee-9065-0242bd4206e3', 1, NULL, NULL, NULL, 0, 100, 'https://***************:34433/meterage', 1, '-1');
REPLACE INTO `iam`.`micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`, `license_key`) VALUES ('d3a89727-26a7-11ee-9065-0242bd4205e4', '资源管理', '资源管理', 'sugoncloud-meterage-api', '2023-07-20 10:47:54', '2023-07-20 10:47:54', 'https://************:30000/meterage/#/resource-report', '2c2e1bed-26a7-11ee-9065-0242bd4206e3', 0, NULL, NULL, NULL, 0, 100, 'https://***************:34433/meterage/#/price-calculator', 1, '-1');
REPLACE INTO `iam`.`micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`, `license_key`) VALUES ('d3a89727-26a7-11ee-9065-0242bd4206e3', '计价', '计价', 'sugoncloud-meterage-api', '2023-07-20 10:47:54', '2023-07-20 10:47:54', 'https://************:30000/meterage/#/price-calculator', '2c2e1bed-26a7-11ee-9065-0242bd4206e3', 0, NULL, NULL, NULL, 0, 100, 'https://***************:34433/meterage/#/price-calculator', 1, '-1');


REPLACE INTO `strategy` VALUES ('bcbef30d017e65ed5a5c20c19bce98e8', '{\"Statement\":[{\"Effect\":\"Allow\",\"action\":[\"oss:CreateBucket\",\"oss:HeadBucket\",\"oss:ListAllMyBuckets\",\"oss:ListBucket\"],\"Resource\":[\"*\"]}],\"version\":\"2012-10-17\"}', '对象存储初始策略', '对象存储用户绑定本策略', '2022-11-04 13:56:34', 'SYSTEM', '0d2bbb018e8b44b985a169647379f413');
REPLACE INTO `strategy` VALUES ('3c1a26485af17a0f39a269f205a67c82', '{\"Statement\":[{\"Effect\":\"Allow\",\"action\":[\"oss:CreateBucket\"]}],\"version\":\"2012-10-17\"}', '创建桶', '创建桶', '2022-10-16 10:53:33', 'SYSTEM', '0d2bbb018e8b44b985a169647379f413');
REPLACE INTO `user_strategy`(`strategy_id`, `user_id`) VALUES ('bcbef30d017e65ed5a5c20c19bce98e8', '0d2bbb018e8b44b985a169647379f413');
REPLACE INTO `user_strategy`(`strategy_id`, `user_id`) VALUES ('3c1a26485af17a0f39a269f205a67c82', '0d2bbb018e8b44b985a169647379f413');

REPLACE INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) VALUES (null, '0f21c4d08dd89db53ca32e1740a9f9b3', '修改配额', '用户管理IAM-部门管理-修改配额', 'null', null, null, 'null', '1', 'f79da1ccd2add999e2491cce36c89290', '', '', '', '0', '', '2021-09-10 00:00:00', '/departmentManage/departModifyQuota', '', '', '2', '1');
REPLACE INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) VALUES (null, '0bac8a03f16bcce0af6d58c161459e6c', '修改配额', '组织管理-项目管理-操作-修改配额', '2', null, null, 'PUT', '1', '2ad1a10cdd55777e0acc760e26fc4259', 'QuotaController', 'updateProjectQuota', '/api/quotas/project/{project_id}', '2', '', '2021-05-06 00:00:00', '/departmentManage/projectModifyQuota', '', '', '2', '1');
REPLACE INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) VALUES (null, 'f4f40669037ccf5911887d43e1ea1fc6', '修改配额', '项目管理-操作-修改配额', 'null', null, null, 'null', '1', '8ca0051adc7bcea83b7a2ee5b6e00d8d', '', '', '', '1', '', '2023-02-15 00:00:00', '/modifyQuota', '', '', '2', '1');

REPLACE INTO `quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('ecs_system_disk', 'ecs', '系统盘总量(GiB)', '系统盘使用量(GiB)');

REPLACE INTO `iam_role` (`id`, `name`, `dept_id`, `description`, `type`, `create_time`, `modify_time`) VALUES ('bfb8a1f64bdd42c8bbbb75c791713f53', '运营管理员', 'bfb8a1f64bdd42c8bbbb75c791713f53', '运营管理员除了运维管理部分功能没有之外，其他服务的权限全有', 'sub_admin', '2023-08-31 11:41:25', '2023-10-08 11:43:59');
REPLACE INTO `iam_role` (`id`, `name`, `dept_id`, `description`, `type`, `create_time`, `modify_time`) VALUES ('e767c7adeb21487a8d01fab25fdce14b', '只读管理员', 'e767c7adeb21487a8d01fab25fdce14b', '具备所有服务的只读权限的管理员', 'sub_admin', '2023-08-31 11:42:53', '2023-10-08 11:43:10');

REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('fd8e2b469eb538ae7398d8387a529b48', '2f563fdc6b3c716115a57fda77b06789', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('bcf53d8cad18cfab1fa4521ac24ed04d', '1b48776def1fb454bfc8523d4f983f4f', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('bbda7b6faf232d35c1e5cb173ddad7f7', 'b36fb35927f92f790a5264922bdcd6bc', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('bac1c1bf97127461d9f6238b908626ef', '8bfd9785ffe9af45a057805815f24f02', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('b6b5246768f76d5c1691b64e97064e4a', '70f15b48260a078ff3188591b1bbc9fc', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('b4ebaa9f30c68db8b50f2992a6552592', '6f7a9d24602f6e528039800c1862e620', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('ac032f3727dc377ce6d641efc0ee9b53', 'a7c0e3e72c7d8d56c4204221124de606', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('ab8e6d80b999c37f34b24273790c313d', '2be99f704134f944189bb5bb1e950332', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('a3d143aecdf70986901daeda7f5e557a', '6c6dafc2f8127287e43dee8f3c9ec412', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('a01eef281879223c76452ca3aa2706bc', '16bde97bcdf589f2fc9c65b770f33fd5', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('9ffc88f86a9a9a8797c10779670b3548', 'efabbf0b22a8bd676af52211590296d2', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('9ee340569a34b0b367332b0d2b7186c7', 'a341c8b659ac17587123e4a75773c408', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('9de08ffd7207a691f545359e2499e4a2', '2129966ac3e8d9082b544ac6537e7c2a', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('b54f8d71d162495773bc2db72f6c53d2', '875f8a852262c0ecb969ee6838851dd9', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('92bdddd126f2898deee4b82b954ff5eb', '90bcd29ac2e29f63bf5071e8e4797dc0', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('807ac56e0fabef67e1ac1e6a088ec7be', '5930d39ee1c3b19b0160723ff40c3779', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('7e85b4ed8754caf4dd0a3c2bd9c84935', 'fc80c7d126c295baadd78d2bc0564633', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('7db85adf91b45e6386bacb2f8cba934f', '5ca4a36fd7ad5eae7c252efb7b78e867', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('bfbfb0c831eb0438d3207dd8812516df', '593cb35d7143e50c3bc5fa2fb14e7ce2', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('c097d52b15f78b36941a089606f021c7', 'd92db5112c6c67cf11a8e90d9ca79335', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('c3ef80762022d70789bc544dda60cfdb', '0cf0f1f1c39e109bd279a630054e7771', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('f5824a1a7dadc1b0ca79b02f208dbafc', '5a18ed094c25f31d320ec808a5e81147', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('f49157183ac3ba61e7db17972f24ebe4', 'bed3b57be44cc68792cc7d14bef16606', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('f47fe6813290e1abe440142ef12669d2', '6efa108027f034daa4b71ed137db251f', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('f3a5a5cabd859f297f07b0bea3e0b8a8', 'f1bad45c67da1ae1817ddbc5f0f80215', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('f0a0f7ea02c86aeb6189b4afde44a50a', '2baf4e8462592ad4201df36edd6da1a4', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('ed2e46c9b6a747418fbb945f0de9ec5a', 'a8cbf9ee2898e2b2979824d4d6098be2', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('e62e9326ff50e13019c991ad7fbff914', '199e95b1f85d781cd4d04e2cfc08a2ea', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('e4ffec7bf3f8c74c0fb51aa04da022b5', 'b983998df86c396ff290927b5045c5f9', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('e3385f1b30d797fb3fb27b9e963620a0', '880d73ee0a2390331cee75bec070e9ca', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('e13a5861280e2d5c4b05afcfc53683a5', 'de5b8265a3f911eb8230f20ed01ddf38', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('dc9f625ecbc298bed964b9fa189e8675', 'cfd7708b7d3f2b38ef67fdf187dcfdf1', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('dabed8f30d0528bd1dfd5bc4f967fa33', '0bac8a03f16bcce0af6d58c161459e6c', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('d8c107eec283c67dd5aa9256973c172b', '08b95decde36ae016fbce02ad9b7c69b', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('cf00e9d0ab6b3e6474d47b2e30caef6f', 'e87f3a8553c2792982e1ea15d2a0a54c', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('cb2a51d81d1b0516d446d14759616a9c', 'a72680e8bc494c93715698a887740f78', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('c64167d01a5171d5ad198bc4f2af9405', 'e8b3952b04d0ff66b215d4577255444e', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('c44aeb0b3fe2f5a4d69d31ef34f0ff15', '0f5133ab84a30dac9023b834ca747cec', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('7c6397452c67b9500bafbf3621aa9451', '10cda0983f13804014ca186b9b612c0a', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('7542a6bcbec065cd96e71dd95eddb62e', 'e31a58f105e490c9b057166f5a81b696', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('53acb419a9481810ef800f4ad4f0cc34', '2ad1a10cdd55777e0acc760e26fc4259', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('4edf71300e4145d06242a623e6730893', '1087f2f01567c61ac1baa20a10510c34', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('446f4e1791dd2f1c3dfa21f6855ed8b0', '9a32ff48615e87c1718d5073c59c8162', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('105ccacf7061189863b14adea603206b', 'd0a0296989d0b46f0a7399568df05064', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('3dfa965a174b61adef8b71d15f7e1503', '0f21c4d08dd89db53ca32e1740a9f9b3', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('3d8c9461032215451ff247f5f7be7a1b', '8ca0051adc7bcea83b7a2ee5b6e00d8d', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('3b3646b245f99980580f5193e669920a', '3d03c2717ab93cfe9205740c60455bd9', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('1a087d078c4b642a00409b2492dda4dc', '4b115b2d2e7f4b3c027164c2542f764c', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('344614761eeef97953d2713a37e999d8', '170e6e421f685fd9ef1e517f363972fa', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('1b63c74b04282ee5d9e627e457a3a2a1', '7d83120a78a67a76fda51b94adec4801', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('3430485c1408abcd934d4fbeb89411f8', 'fcdfd962bc93512c1cdee056daad37ef', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('30639f841049e08b8137de6040f047b9', 'c1fd2cce952da1093f3aa062e8ca8d9e', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('2fbea34044ff6e3cd3ae74472cee4fcf', 'aab2dc93c2c0a57e6efa0235b5cdc696', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('24a15c036251b1be383b24d215e5749e', 'f79da1ccd2add999e2491cce36c89290', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('279b03225b8a5b8c56e363d7a37df590', '890826492a92e7db24d8865c778a8e38', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('27ee265296bb2011ab41c063d580bc2b', '2faec1b0f66c7ceaa3fb383ddc4d5096', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('5293854aa4a374fc1dbf223ecd7ae69c', '018c2c7192e42695c0306b5dc1756ec4', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('0fe26ace91f03d4958e95a864218fbc4', '81016b1ef5a8f06e89115a5f4c9f214b', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('73c3640aaad32eee23bf597ac97d3f7a', 'fe3a21e5bffa710d207343632197ae40', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('6f6e68176827f39b6c12e432b8cf27ad', '6f5562a40b28f22788015a32d5e4e1e5', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('678e7d646865ca0b60878139e01fd52f', '630c2949797479f246573339f4218e57', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('61beadb514f7f94e8345827ef4abcc08', 'f78146c95201acef6690ecf30a19a03d', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('07a4140333b2f61f2da63c1b1860853e', '5104bdb87121783837dd275036a629d9', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('5aaa23187221fdbe20937edaf4e1375d', '494fdb8f68c26eb3404f5f7b7ae37ed3', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('59ae825da11c889cee0beb363e10c6c9', '3e678ae67389cce29b1df5b14a0ad40a', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('593abd57edeed20ee5a060e7c2e74855', 'f4f40669037ccf5911887d43e1ea1fc6', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('27e7592c5dc312204a399cee0f5cedb3', 'ef25f0a4bd4b8221f81636047d1e2351', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('0bba58e8dcf60a637df983686d87d899', '2f29666b441d9f10b193ae5db1687345', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('58174c3a36cc72b369b1339606a9c02f', 'b1ff673ebc7dee772da81fd3e68a5607', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('55782c993dd0fef2a4f3618b5774806c', '67e696772d38e38fd6f423253b41ab8c', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('0a4f3c9562a92bda6f005d828604d805', '8a878e0d9a6622c3313e726b61085ea9', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('086632614908968bc189f9fe904c12bf', '252c150ee420f8ef83d349f3d293722d', 'bfb8a1f64bdd42c8bbbb75c791713f53');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('dbf0e9f0e2295fe79878af9a8edb9fd2', 'd0a0296989d0b46f0a7399568df05064', 'e767c7adeb21487a8d01fab25fdce14b');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('14185aeb3675af390bae0f7861025a0a', '199e95b1f85d781cd4d04e2cfc08a2ea', 'e767c7adeb21487a8d01fab25fdce14b');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('fc800b22e52acebddcaf1de7427b37b4', '6c6dafc2f8127287e43dee8f3c9ec412', 'e767c7adeb21487a8d01fab25fdce14b');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('9b72d2b35e812cf3bb94e0479f1aa96e', 'b36fb35927f92f790a5264922bdcd6bc', 'e767c7adeb21487a8d01fab25fdce14b');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('ea17bd24658bcd2f7111d862437f941c', '8bfd9785ffe9af45a057805815f24f02', 'e767c7adeb21487a8d01fab25fdce14b');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('17f8678b79fa8afde1c6079da80d2b6c', '8a878e0d9a6622c3313e726b61085ea9', 'e767c7adeb21487a8d01fab25fdce14b');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('e8e6aa236a0bc3a2fbeefe3c54b5f9fc', '2baf4e8462592ad4201df36edd6da1a4', 'e767c7adeb21487a8d01fab25fdce14b');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('db2d295ea41a16f863683bf39425385b', '2f563fdc6b3c716115a57fda77b06789', 'e767c7adeb21487a8d01fab25fdce14b');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('d592cd00f6cf106d93c0efe523eeac4e', 'fc80c7d126c295baadd78d2bc0564633', 'e767c7adeb21487a8d01fab25fdce14b');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('8d11acb039fa28a10c42ab08265fc773', 'efabbf0b22a8bd676af52211590296d2', 'e767c7adeb21487a8d01fab25fdce14b');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('a1d5daced9d6cc3c5f5eddb8571516e9', 'de5b8265a3f911eb8230f20ed01ddf38', 'e767c7adeb21487a8d01fab25fdce14b');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('76a567a1dd64760342665e1cf8c52578', 'd92db5112c6c67cf11a8e90d9ca79335', 'e767c7adeb21487a8d01fab25fdce14b');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('a3e4236df7658f1f4224021ecf2696ac', 'b1ff673ebc7dee772da81fd3e68a5607', 'e767c7adeb21487a8d01fab25fdce14b');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('aacbea563b1de5f4b3e3747ab6df1bc7', '08b95decde36ae016fbce02ad9b7c69b', 'e767c7adeb21487a8d01fab25fdce14b');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('9ba64622ee3e2d403b2abcef04c9a3c1', '3d03c2717ab93cfe9205740c60455bd9', 'e767c7adeb21487a8d01fab25fdce14b');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('b99377f604e53646e56b4421cb92a79b', '3e678ae67389cce29b1df5b14a0ad40a', 'e767c7adeb21487a8d01fab25fdce14b');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('ba7242a6abbca1daf1249db470796784', 'bed3b57be44cc68792cc7d14bef16606', 'e767c7adeb21487a8d01fab25fdce14b');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('3694ff1567ed017894e8184983033e00', 'fe3a21e5bffa710d207343632197ae40', 'e767c7adeb21487a8d01fab25fdce14b');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('be32f5f3030ab62bfa8ee7d9a044b67c', 'aab2dc93c2c0a57e6efa0235b5cdc696', 'e767c7adeb21487a8d01fab25fdce14b');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('3667b81e77911c42604d31034916d2c7', '593cb35d7143e50c3bc5fa2fb14e7ce2', 'e767c7adeb21487a8d01fab25fdce14b');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('c2824de0e713769d50e65e245c9153ad', 'c1fd2cce952da1093f3aa062e8ca8d9e', 'e767c7adeb21487a8d01fab25fdce14b');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('28fdb59dcc132abbba387e583504d406', '5104bdb87121783837dd275036a629d9', 'e767c7adeb21487a8d01fab25fdce14b');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('28bfa40095c02cef7b24dba856015a89', 'f79da1ccd2add999e2491cce36c89290', 'e767c7adeb21487a8d01fab25fdce14b');
REPLACE INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) VALUES ('723aee4da1fe3cee0b659b5053b05d67', '10cda0983f13804014ca186b9b612c0a', 'e767c7adeb21487a8d01fab25fdce14b');

UPDATE micro_service_category SET `order`='99' WHERE id='dce92741-1d3b-43bc-9e51-ce21b60c9d4a';
UPDATE micro_service_category SET `order`='32' WHERE id='esa09553-6177-441b-981a-9c5ehjhjc62ea8';
