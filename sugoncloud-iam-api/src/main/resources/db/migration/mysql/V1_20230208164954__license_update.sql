ALTER TABLE `quota_type` ADD COLUMN `category_id` varchar(64) NULL COMMENT '所属分类' AFTER `sequence`;

REPLACE INTO `quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('bms', '裸金属BMS', 15, '4afee060a36111eb8230f20ed01ddf38');
REPLACE INTO `quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('cbh', '云堡垒机', 8, 'd04f330d-7353-476d-afd8-72e7a8f4002e');
REPLACE INTO `quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('cce', '云容器实例CCI', 9, '44c1f40ba59d11eb8230f20ed01ddf38');
REPLACE INTO `quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('cfw', '云防火墙CFW', 25, 'ea209553-6177-441b-981a-9c5e40c62ea8');
R<PERSON>LACE INTO `quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('dc', '云专线DC', 27, 'ea209553-6177-441b-981a-9c5e40c62ea8');
REPLACE INTO `quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('ecs', '云服务器ECS', 1, '4afee060a36111eb8230f20ed01ddf38');
REPLACE INTO `quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('emr', 'E-MapReduce', 6, '54df68e1a36111eb8230f20ed01ddf38');
REPLACE INTO `quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('es', '云搜索服务CSS', 18, '4546d40a-2d8a-11ed-99d5-0894efae434a');
REPLACE INTO `quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('evs', '云硬盘EVS', 2, '13a38073a36111eb8230f20ed01ddf38');
REPLACE INTO `quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('floatip', '弹性公网IP', 2, 'ea209553-6177-441b-981a-9c5e40c62ea8');
REPLACE INTO `quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('hss', '主机安全HSS', 7, 'd04f330d-7353-476d-afd8-72e7a8f4002e');
REPLACE INTO `quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('kafka', '分布式消息服务Kafka', 19, '4546d40a-2d8a-11ed-99d5-0894efae434a');
REPLACE INTO `quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('mongodb', '云数据库MongoDB', 16, 'a3a0adae-c1e1-11eb-a5c2-fac676e41602');
REPLACE INTO `quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('mysql', '云数据库MySQL', 3, 'a3a0adae-c1e1-11eb-a5c2-fac676e41602');
REPLACE INTO `quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('oss', '对象存储OSS', 28, '13a38073a36111eb8230f20ed01ddf38');
REPLACE INTO `quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('pgsql', '云数据库PostgreSQL', 4, 'a3a0adae-c1e1-11eb-a5c2-fac676e41602');
REPLACE INTO `quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('ras', '云漏洞扫描RAS', 10, 'd04f330d-7353-476d-afd8-72e7a8f4002e');
REPLACE INTO `quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('rcce', '云容器引擎CCE', 22, '44c1f40ba59d11eb8230f20ed01ddf38');
REPLACE INTO `quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('redis', '分布式缓存服务Redis', 17, '4546d40a-2d8a-11ed-99d5-0894efae434a');
REPLACE INTO `quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('sfs', '文件存储SFS', 21, '13a38073a36111eb8230f20ed01ddf38');
REPLACE INTO `quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('tidb', '云数据库TiDB', 5, 'a3a0adae-c1e1-11eb-a5c2-fac676e41602');
REPLACE INTO `quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('vdb', '数据库审计VDB', 13, 'd04f330d-7353-476d-afd8-72e7a8f4002e');
REPLACE INTO `quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('ver', '日志审计VER', 11, 'd04f330d-7353-476d-afd8-72e7a8f4002e');
REPLACE INTO `quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('vpc', '虚拟私有云VPC', 26, 'ea209553-6177-441b-981a-9c5e40c62ea8');
REPLACE INTO `quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('vpn', '虚拟专用网络VPN', 24, 'ea209553-6177-441b-981a-9c5e40c62ea8');
REPLACE INTO `quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('waf', 'WEB应用防火墙WAF', 14, 'd04f330d-7353-476d-afd8-72e7a8f4002e');
REPLACE INTO `quota_type` (`name`, `description`, `sequence`, `category_id`) VALUES ('wpt', '网页防篡改WPT', 12, 'd04f330d-7353-476d-afd8-72e7a8f4002e');

REPLACE INTO `quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('cbh_vm', 'cbh', '虚拟机总量(个)', '虚拟机使用量(个)');
REPLACE INTO `quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('cce_vm', 'cce', '虚拟机总量(个)', '虚拟机使用量(个)');
REPLACE INTO `quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('cfw_vm', 'cfw', '虚拟机总量(个)', '虚拟机使用量(个)');
REPLACE INTO `quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('dc_vm', 'dc', '虚拟机总量(个)', '虚拟机使用量(个)');
REPLACE INTO `quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('es_vm', 'es', '虚拟机总量(个)', '虚拟机使用量(个)');
REPLACE INTO `quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('kafka_vm', 'kafka', '虚拟机总量(个)', '虚拟机使用量(个)');
REPLACE INTO `quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('mongodb_vm', 'mongodb', '虚拟机总量(个)', '虚拟机使用量(个)');
REPLACE INTO `quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('mysql_vm', 'mysql', '虚拟机总量(个)', '虚拟机使用量(个)');
REPLACE INTO `quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('pgsql_vm', 'pgsql', '虚拟机总量(个)', '虚拟机使用量(个)');
REPLACE INTO `quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('rcce_vm', 'rcce', '虚拟机总量(个)', '虚拟机使用量(个)');
REPLACE INTO `quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('redis_vm', 'redis', '虚拟机总量(个)', '虚拟机使用量(个)');
REPLACE INTO `quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('sfs_vm', 'sfs', '虚拟机总量(个)', '虚拟机使用量(个)');
REPLACE INTO `quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('tidb_vm', 'tidb', '虚拟机总量(个)', '虚拟机使用量(个)');
REPLACE INTO `quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('vpn_vm', 'vpn', '虚拟机总量(个)', '虚拟机使用量(个)');
UPDATE `micro_service` set `need_policy` = 1 WHERE `id` = 'f56e82b6-ab1c-11ec-9548-0894efae434a';


ALTER TABLE `micro_service` ADD COLUMN `license_key` varchar(255) NULL DEFAULT '-1' COMMENT 'license_key' AFTER `need_policy`;
UPDATE `micro_service` SET `license_key` = 'cloudManageAndMonitorAuthorizationInfo' WHERE `id` = '07d93831-05a1-4d8b-bee2-14184b13eee0';
UPDATE `micro_service` SET `license_key` = 'vmVpnAuthorizationInfo' WHERE `id` = '25aj48dh-loay-q23t-25lj-84mj21vm48iq';
UPDATE `micro_service` SET `license_key` = 'cloudDisasterBackupAuthorizationInfo' WHERE `id` = '276d2b49-ef06-c4db-2d77-be8ad8612589';
UPDATE `micro_service` SET `license_key` = 'cloudBastionMachineAuthorizationInfo' WHERE `id` = '29e3d052-2487-4e14-be59-40116b7a0bf5';
UPDATE `micro_service` SET `license_key` = 'cloudDatabaseAuthorizationInfo' WHERE `id` = '31069c74-0477-11ec-aedb-1a9678e015b1';
UPDATE `micro_service` SET `license_key` = 'cloudDatabaseAuthorizationInfo' WHERE `id` = '375744d6-0477-11ec-aedb-1a9678e015b1';
UPDATE `micro_service` SET `license_key` = 'cloudDcAuthorizationInfo' WHERE `id` = '45eb624e-7f70-11ed-9cdf-0242a63e3c80';
UPDATE `micro_service` SET `license_key` = 'middlewareAuthorizationInfo' WHERE `id` = '4b19e803-2d8b-11ed-99d5-0894efae434a';
UPDATE `micro_service` SET `license_key` = 'bmsAuthorizationInfo' WHERE `id` = '4e9dfd85fdd84b10a8e269dd09e4b535';
UPDATE `micro_service` SET `license_key` = 'cloudDatabaseAuthorizationInfo' WHERE `id` = '5848c8a0-ccac-4ff9-b4bf-ee42c9545c77';
UPDATE `micro_service` SET `license_key` = 'cceAuthorizationInfo' WHERE `id` = '587eju65-f8r5-55lo-74f5-36fr9r5lq2o5';
UPDATE `micro_service` SET `license_key` = 'cloudNativeDevOpsAuthorizationInfo' WHERE `id` = '68ef1d25-d97a-4712-af7a-f3249c2ece6a';
UPDATE `micro_service` SET `license_key` = 'ocrAuthorizationInfo' WHERE `id` = '69f85996-ee5e-452e-9407-507a8d871133';
UPDATE `micro_service` SET `license_key` = 'cfwAuthorizationInfo' WHERE `id` = '6a0c0e2f-5f34-11ed-aebb-0894efae434a';
UPDATE `micro_service` SET `license_key` = 'middlewareAuthorizationInfo' WHERE `id` = '6e5464b6-2d91-11ed-99d5-0894efae434a';
UPDATE `micro_service` SET `license_key` = 'sigAuthorizationInfo' WHERE `id` = '9bd08d2e-d361-403e-b42d-0f1be6595259';
UPDATE `micro_service` SET `license_key` = 'cloudNativeDevOpsAuthorizationInfo' WHERE `id` = 'a5cdd2ca-ed13-4794-99a5-a59eb4d4c133';
UPDATE `micro_service` SET `license_key` = 'cloudNativeDevOpsAuthorizationInfo' WHERE `id` = 'afa751d5-7973-4cb1-9a66-16ddf19295019';
UPDATE `micro_service` SET `license_key` = 'cloudNativeDevOpsAuthorizationInfo' WHERE `id` = 'afa751d5-7973-4cb1-9a66-16ddf19295020';
UPDATE `micro_service` SET `license_key` = 'cloudNativeDevOpsAuthorizationInfo' WHERE `id` = 'afa751d5-7973-4cb1-9a66-16ddf1929509';
UPDATE `micro_service` SET `license_key` = 'cloudDatabaseAuthorizationInfo' WHERE `id` = 'b4387ecd-c1e1-11eb-a5c2-fac676e41602';
UPDATE `micro_service` SET `license_key` = 'middlewareAuthorizationInfo' WHERE `id` = 'b8e463c5-2d8a-11ed-99d5-0894efae434a';
UPDATE `micro_service` SET `license_key` = 'smartOpsAuthorizationInfo' WHERE `id` = 'c8f9ea8c-9fd3-434c-91b9-134a5effd826';
UPDATE `micro_service` SET `license_key` = 'ocrAuthorizationInfo' WHERE `id` = 'de123898-986c-11ec-8cb4-0894efae434a';
UPDATE `micro_service` SET `license_key` = 'ossAuthorizationInfo' WHERE `id` = 'e455f304d2e34f2eb448d1d3182c3c68';
UPDATE `micro_service` SET `license_key` = 'sfsAuthorizationInfo' WHERE `id` = 'f1f95ca3-ed59-4c00-8564-64ee14aaad5d';
UPDATE `micro_service` SET `license_key` = 'emrAuthorizationInfo' WHERE `id` = 'f72db00f-4914-4bdf-9161-7cc5977edf7d';
