UPDATE `ram_policy` SET `action` = 'PUT', `class_name` = 'UserController', `method_name` = 'updateUserType' WHERE `uuid` = '018c2c7192e42695c0306b5dc1756ec4';
UPDATE `ram_policy` SET `action` = 'PUT', `class_name` = 'CommonPolicyController', `method_name` = 'editPolicy' WHERE `uuid` = '0afb7e979297c8046dfd7e5839aad8ea';
UPDATE `ram_policy` SET `action` = 'PUT', `class_name` = 'QuotaController', `method_name` = 'updateProjectQuota' WHERE `uuid` = '0bac8a03f16bcce0af6d58c161459e6c';
UPDATE `ram_policy` SET `action` = 'DELETE', `class_name` = 'AdminUserController', `method_name` = 'deleteAdmin' WHERE `uuid` = '0bdf5d506e7b4ebddb7ee1911c7fbf2f';
UPDATE `ram_policy` SET `action` = 'PUT', `class_name` = 'QuotaController', `method_name` = 'synchronizeProjectQuota' WHERE `uuid` = '0cf0f1f1c39e109bd279a630054e7771';
UPDATE `ram_policy` SET `action` = 'PUT', `class_name` = 'ServiceActionController', `method_name` = 'update' WHERE `uuid` = '0da1267b51dc88e1db34ab41890f7ea0';
UPDATE `ram_policy` SET `action` = 'PUT', `class_name` = 'QuotaController', `method_name` = 'updateDepartmentQuota' WHERE `uuid` = '0f21c4d08dd89db53ca32e1740a9f9b3';
UPDATE `ram_policy` SET `action` = 'PUT', `class_name` = 'UserController', `method_name` = 'updateUserType' WHERE `uuid` = '0f5133ab84a30dac9023b834ca747cec';
UPDATE `ram_policy` SET `action` = 'POST', `class_name` = 'IamRoleController', `method_name` = 'createRole' WHERE `uuid` = '1087f2f01567c61ac1baa20a10510c34';
UPDATE `ram_policy` SET `action` = 'DELETE', `class_name` = 'UserController', `method_name` = 'delete' WHERE `uuid` = '16bde97bcdf589f2fc9c65b770f33fd5';
UPDATE `ram_policy` SET `action` = 'POST', `class_name` = 'ProjectController', `method_name` = 'create' WHERE `uuid` = '170e6e421f685fd9ef1e517f363972fa';
UPDATE `ram_policy` SET `action` = 'POST', `class_name` = 'UserController', `method_name` = 'batchAssignmentRoles' WHERE `uuid` = '********************************';
UPDATE `ram_policy` SET `action` = 'POST', `class_name` = 'UserController', `method_name` = 'userAccessControl' WHERE `uuid` = '2129966ac3e8d9082b544ac6537e7c2a';
UPDATE `ram_policy` SET `action` = 'POST', `class_name` = 'UserController', `method_name` = 'userAccessControl' WHERE `uuid` = '252c150ee420f8ef83d349f3d293722d';
UPDATE `ram_policy` SET `action` = 'POST', `class_name` = 'StrategyController', `method_name` = 'userBindStrategy' WHERE `uuid` = '2be99f704134f944189bb5bb1e950332';
UPDATE `ram_policy` SET `action` = 'POST', `class_name` = 'StrategyController', `method_name` = 'createStrategy' WHERE `uuid` = '2f29666b441d9f10b193ae5db1687345';
UPDATE `ram_policy` SET `action` = 'PUT', `class_name` = 'IamRoleController', `method_name` = 'update' WHERE `uuid` = '2f563fdc6b3c716115a57fda77b06789';
UPDATE `ram_policy` SET `action` = 'PUT', `class_name` = 'ProjectController', `method_name` = 'update' WHERE `uuid` = '2faec1b0f66c7ceaa3fb383ddc4d5096';
UPDATE `ram_policy` SET `action` = 'POST', `class_name` = 'UserController', `method_name` = 'userAccessControl' WHERE `uuid` = '3238366d508438608cf1037311fdf2cd';
UPDATE `ram_policy` SET `action` = 'GET', `class_name` = 'GlobalsettingsController', `method_name` = 'getAllGlobalsettingsByParam' WHERE `uuid` = '376b911d000734db8418a6f00e712d1a';
UPDATE `ram_policy` SET `action` = 'DELETE', `class_name` = 'CommonPolicyController', `method_name` = 'deletePolicy' WHERE `uuid` = '3928a382a51cbe0b9dd96754709e3638';
UPDATE `ram_policy` SET `action` = 'POST', `class_name` = 'UserController', `method_name` = 'userExpired' WHERE `uuid` = '404e39ddd5ee876798c176af7acd5627';
UPDATE `ram_policy` SET `action` = 'DELETE', `class_name` = 'AdminUserController', `method_name` = 'deleteAdminRole' WHERE `uuid` = '48c020d50153e5b0df4ca2a607d85579';
UPDATE `ram_policy` SET `action` = 'POST', `class_name` = 'UserController', `method_name` = 'userExpired' WHERE `uuid` = '494fdb8f68c26eb3404f5f7b7ae37ed3';
UPDATE `ram_policy` SET `action` = 'DELETE', `class_name` = 'ServiceActionController', `method_name` = 'delete' WHERE `uuid` = '566c57598457f8ac3aa20b4fd971e178';
UPDATE `ram_policy` SET `action` = 'GET', `class_name` = 'SecurityUserController', `method_name` = 'innerRoleList' WHERE `uuid` = '58aea5c77a80a55d293af6e32ba14aa5';
UPDATE `ram_policy` SET `action` = 'DELETE', `class_name` = 'IamRoleController', `method_name` = 'deleteRole' WHERE `uuid` = '5930d39ee1c3b19b0160723ff40c3779';
UPDATE `ram_policy` SET `action` = 'DELETE', `class_name` = 'StrategyController', `method_name` = 'deleteStrategy' WHERE `uuid` = '5a18ed094c25f31d320ec808a5e81147';
UPDATE `ram_policy` SET `action` = 'POST', `class_name` = 'UserController', `method_name` = 'batchAssignmentRoles' WHERE `uuid` = '5ba06d21b1e1ef7e1dae5fb42f4f39ca';
UPDATE `ram_policy` SET `action` = 'POST', `class_name` = 'UserController', `method_name` = 'batchAssignmentProjects' WHERE `uuid` = '5ca4a36fd7ad5eae7c252efb7b78e867';
UPDATE `ram_policy` SET `action` = 'POST', `class_name` = 'CommonPolicyController', `method_name` = 'createPolicy' WHERE `uuid` = '61d125a65df54296d6ed381976585cab';
UPDATE `ram_policy` SET `action` = 'POST', `class_name` = 'UserController', `method_name` = 'createSub' WHERE `uuid` = '630c2949797479f246573339f4218e57';
UPDATE `ram_policy` SET `action` = 'DELETE', `class_name` = 'ProjectController', `method_name` = 'delete' WHERE `uuid` = '67e696772d38e38fd6f423253b41ab8c';
UPDATE `ram_policy` SET `action` = 'PUT', `class_name` = 'ProjectController', `method_name` = 'update' WHERE `uuid` = '6f5562a40b28f22788015a32d5e4e1e5';
UPDATE `ram_policy` SET `action` = 'POST', `class_name` = 'UserController', `method_name` = 'userExpired' WHERE `uuid` = '6f7a9d24602f6e528039800c1862e620';
UPDATE `ram_policy` SET `action` = 'DELETE', `class_name` = 'UserController', `method_name` = 'delete' WHERE `uuid` = '70f15b48260a078ff3188591b1bbc9fc';
UPDATE `ram_policy` SET `action` = 'GET', `class_name` = 'AdminUserController', `method_name` = 'listAdminRole' WHERE `uuid` = '755389a3a03064ab4841db638f002c28';
UPDATE `ram_policy` SET `action` = 'POST', `class_name` = 'ServiceActionController', `method_name` = 'saveBatch' WHERE `uuid` = '7f1e37708c4bf9ab43f2f600a06dadce';
UPDATE `ram_policy` SET `action` = 'DELETE', `class_name` = 'IamRoleController', `method_name` = 'deleteRole' WHERE `uuid` = '81016b1ef5a8f06e89115a5f4c9f214b';
UPDATE `ram_policy` SET `action` = 'PUT', `class_name` = 'QuotaController', `method_name` = 'synchronizeProjectQuota' WHERE `uuid` = '875f8a852262c0ecb969ee6838851dd9';
UPDATE `ram_policy` SET `action` = 'DELETE', `class_name` = 'ProjectController', `method_name` = 'delete' WHERE `uuid` = '880d73ee0a2390331cee75bec070e9ca';
UPDATE `ram_policy` SET `action` = 'POST', `class_name` = 'UserController', `method_name` = 'batchAssignmentProjects' WHERE `uuid` = '890826492a92e7db24d8865c778a8e38';
UPDATE `ram_policy` SET `action` = 'GET', `class_name` = 'StrategyController', `method_name` = 'getStrategy' WHERE `uuid` = '8bfd9785ffe9af45a057805815f24f02';
UPDATE `ram_policy` SET `action` = 'POST', `class_name` = 'StrategyController', `method_name` = 'userUnbindStrategy' WHERE `uuid` = '9a32ff48615e87c1718d5073c59c8162';
UPDATE `ram_policy` SET `action` = 'DELETE', `class_name` = 'IamRoleController', `method_name` = 'deleteRole' WHERE `uuid` = 'a72680e8bc494c93715698a887740f78';
UPDATE `ram_policy` SET `action` = 'PUT', `class_name` = 'CommonPolicyController', `method_name` = 'editRoleWithPolicy' WHERE `uuid` = 'a7c0e3e72c7d8d56c4204221124de606';
UPDATE `ram_policy` SET `action` = 'DELETE', `class_name` = 'UserController', `method_name` = 'delete' WHERE `uuid` = 'a8cbf9ee2898e2b2979824d4d6098be2';
UPDATE `ram_policy` SET `action` = 'GET', `class_name` = 'CommonPolicyController', `method_name` = 'getAllPolicyByTree' WHERE `uuid` = 'bc0e2aa5b071a67599e4d3a8221750da';
UPDATE `ram_policy` SET `action` = 'PUT', `class_name` = 'UserController', `method_name` = 'updateUserType' WHERE `uuid` = 'bfdd1b63cf9dca5799c17d23950ac9c6';
UPDATE `ram_policy` SET `action` = 'GET', `class_name` = 'UserController', `method_name` = 'userListByDeptId' WHERE `uuid` = 'c1fd2cce952da1093f3aa062e8ca8d9e';
UPDATE `ram_policy` SET `action` = 'DELETE', `class_name` = 'ProjectController', `method_name` = 'delete' WHERE `uuid` = 'cfd7708b7d3f2b38ef67fdf187dcfdf1';
UPDATE `ram_policy` SET `action` = 'DELETE', `class_name` = 'AdminUserController', `method_name` = 'deleteAdminRole' WHERE `uuid` = 'd3aacce5b3ac3c67d8e2be8d077334c0';
UPDATE `ram_policy` SET `action` = 'PUT', `class_name` = 'CommonPolicyController', `method_name` = 'editRoleWithPolicy' WHERE `uuid` = 'dd772ed9568880e38a2182a87eb8e389';
UPDATE `ram_policy` SET `action` = 'PUT', `class_name` = 'UserController', `method_name` = 'resetPassword' WHERE `uuid` = 'e31a58f105e490c9b057166f5a81b696';
UPDATE `ram_policy` SET `action` = 'PUT', `class_name` = 'CommonPolicyController', `method_name` = 'editRoleWithPolicy' WHERE `uuid` = 'e39c495ad1e9202e23af5a7359fcfdf2';
UPDATE `ram_policy` SET `action` = 'GET', `class_name` = 'AdminUserController', `method_name` = 'adminUserList' WHERE `uuid` = 'e50f0db767049c0728c6a40434f986c6';
UPDATE `ram_policy` SET `action` = 'PUT', `class_name` = 'IamRoleController', `method_name` = 'update' WHERE `uuid` = 'e7ca99db2bf8aec460f36f4e7fbac07b';
UPDATE `ram_policy` SET `action` = 'PUT', `class_name` = 'StrategyController', `method_name` = 'updateStrategy' WHERE `uuid` = 'e87f3a8553c2792982e1ea15d2a0a54c';
UPDATE `ram_policy` SET `action` = 'POST', `class_name` = 'StrategyController', `method_name` = 'userUnbindStrategy' WHERE `uuid` = 'e8b3952b04d0ff66b215d4577255444e';
UPDATE `ram_policy` SET `action` = 'POST', `class_name` = 'UserController', `method_name` = 'userUnbindStrategy' WHERE `uuid` = 'e998c266e8aec1c87f1c80b22b1b56e5';
UPDATE `ram_policy` SET `action` = 'GET', `class_name` = 'StrategyController', `method_name` = 'getStrategyList' WHERE `uuid` = 'ebe00065e976fc76a134edf0cdd4e002';
UPDATE `ram_policy` SET `action` = 'POST', `class_name` = 'StrategyController', `method_name` = 'userBindStrategy' WHERE `uuid` = 'eddb1507ca9a72f242ebd91b4adfa290';
UPDATE `ram_policy` SET `action` = 'POST', `class_name` = 'StrategyController', `method_name` = 'userBindStrategy' WHERE `uuid` = 'ef25f0a4bd4b8221f81636047d1e2351';
UPDATE `ram_policy` SET `action` = 'POST', `class_name` = 'AdminUserController', `method_name` = 'createAdmin' WHERE `uuid` = 'ef81af8e62c0cd54c7e0d28434006437';
UPDATE `ram_policy` SET `action` = 'PUT', `class_name` = 'IamRoleController', `method_name` = 'update' WHERE `uuid` = 'efabbf0b22a8bd676af52211590296d2';
UPDATE `ram_policy` SET `action` = 'PUT', `class_name` = 'QuotaController', `method_name` = 'updateProjectQuota' WHERE `uuid` = 'f4f40669037ccf5911887d43e1ea1fc6';
UPDATE `ram_policy` SET `action` = 'PUT', `class_name` = 'UserController', `method_name` = 'resetPassword' WHERE `uuid` = 'f78146c95201acef6690ecf30a19a03d';
UPDATE `ram_policy` SET `action` = 'PUT', `class_name` = 'CommonPolicyController', `method_name` = 'editRoleWithPolicy' WHERE `uuid` = 'fcdfd962bc93512c1cdee056daad37ef';
UPDATE `ram_policy` SET `action` = 'PUT', `class_name` = 'UserController', `method_name` = 'resetPassword' WHERE `uuid` = 'fe946da97b8fe7190789bde08f32ca6a';
UPDATE `ram_policy` SET `action` = 'DELETE', `class_name` = 'AdminUserController', `method_name` = 'deleteAdmin' WHERE `uuid` = 'ffe951ee60115715501ba395956c14c7';