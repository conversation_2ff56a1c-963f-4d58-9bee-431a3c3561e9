REPLACE INTO `iam`.`service` (`id`, `type`, `enabled`, `extra`, `description`, `name`) VALUES ('7caace0daaa142b7ax10b6bf2e7fa227', 'oss', '1', '{\"name\":\"oss\",\"description\":\"service\"}', 'oss service', 'oss');
REPLACE INTO `iam`.`endpoint` (`id`, `legacy_endpoint_id`, `interface`, `service_id`, `url`, `extra`, `enabled`, `region_id`) VALUES ('01e1e416342a43279d3993bd4f453efb', NULL, 'public', '7caace0daaa142b7ax10b6bf2e7fa227', 'http://**********:7333', '{}', '1', 'RegionOne');

REPLACE INTO `iam`.`micro_service_category` (`id`, `name`, `description`, `create_time`, `modify_time`, `order`, `nav_hidden`) VALUES ('93e587fc-a3ec-4c95-989c-798bb698a541', '大数据工具与服务', '大数据工具与服务', '2021-09-24 15:50:34', '2022-03-31 17:14:49', '110', '0');
REPLACE INTO `iam`.`micro_service_category` (`id`, `name`, `description`, `create_time`, `modify_time`, `order`, `nav_hidden`) VALUES ('esa09553-6177-441b-981a-9c5ehjhjc62ea8', '视频', '视频', '2021-07-09 10:33:34', '2021-07-05 11:51:37', '99', '0');

REPLACE INTO `iam`.`micro_service`(`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('e455f304d2e34f2eb448d1d3182c3c68', '对象存储 OSS', '对象存储', 'sugoncloud-oss-api', '2021-04-15 15:43:44', '2022-03-30 11:28:14', 'https://************:30000/oss', '13a38073a36111eb8230f20ed01ddf38', 0, NULL, '2021-05-24 15:59:06', NULL, 0, 40, 'https://***************:34433/oss', 1);
REPLACE INTO `iam`.`micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('587eju65-f8r5-55lo-74f5-36fr9r5lq2o5', '云容器引擎 CCE', '云容器引擎 CCE', 'sugoncloud-cce-api', '2022-10-08 10:42:36', '2022-10-08 10:42:36', 'https://************:30000/cce', '44c1f40ba59d11eb8230f20ed01ddf38', '0', NULL, NULL, NULL, '0', NULL, 'https://***************:34433/cce', '1');
REPLACE INTO `iam`.`micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('9bd08d2e-d361-403e-b42d-0f1be6595259', '服务网关 SIG', '服务网关 SIG', 'sugoncloud-gateway-api', '2021-11-01 10:24:56', '2022-06-22 16:39:57', 'https://************:30000/gateway', '4546d40a-2d8a-11ed-99d5-0894efae434a', '0', NULL, NULL, NULL, '0', '50', 'https://***************:34433/gateway', '1');
REPLACE INTO `iam`.`micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('f1f95ca3-ed59-4c00-8564-64ee14aaad5d', '文件存储 SFS', '文件存储 SFS', 'sugoncloud-sfs-api', '2022-10-13 09:19:43', '2022-10-13 09:19:49', 'https://************:30000/sfs', '13a38073a36111eb8230f20ed01ddf38', '0', NULL, NULL, NULL, '0', '50', 'https://***************:34433/sfs', '1');
REPLACE INTO `iam`.`micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('3b1ae212-36b1-413f-9906-164149e1dcc6', '数据集成 Data Integration', '数据集成 Data Integration', 'sugoncloud-dds-api', '2021-09-24 10:24:56', '2021-09-24 10:40:41', 'https://************:30006/xy-data-integration', '93e587fc-a3ec-4c95-989c-798bb698a541', '0', NULL, NULL, NULL, '1', '80', 'https://***************:34435/xy-data-integration', '1');
REPLACE INTO `iam`.`micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('88f1b115-5a60-11ed-aebb-0894efae434a', '视频直播 LIVE', '视频直播 LIVE', 'sugoncloud-live-api', '2022-11-02 11:45:18', '2022-11-02 11:45:18', 'https://************:30000/live', 'esa09553-6177-441b-981a-9c5ehjhjc62ea8', '0', NULL, NULL, NULL, '0', NULL, 'https://************:30000/live', '1');
REPLACE INTO `iam`.`micro_service` (`id`, `name`, `description`, `service_id`, `create_time`, `modify_time`, `link`, `category_id`, `nav_hidden`, `latest_request_time`, `collect_time`, `version`, `third_part_access`, `order`, `public_link`, `need_policy`) VALUES ('f22db01f-4914-4bdf-9161-7cc5977edf7d', '视频点播 VOD', '视频点播 VOD', 'sugoncloud-vod-api', '2021-05-03 10:24:56', '2022-06-22 16:39:58', 'https://************:30000/vod', 'esa09553-6177-441b-981a-9c5ehjhjc62ea8', '0', NULL, NULL, NULL, '0', NULL, 'https://************:30000/vod', '1');


REPLACE INTO `iam`.`quota_type` (`name`, `description`, `sequence`) VALUES ('sfs', '文件存储SFS配额', '21');
REPLACE INTO `iam`.`quota_type` (`name`, `description`, `sequence`) VALUES ('rcce', '云容器引擎CCE配额', '22');
REPLACE INTO `iam`.`quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('sfs_cpu', 'sfs', 'cpu总量(个)', 'cpu使用量(个)');
REPLACE INTO `iam`.`quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('sfs_ram', 'sfs', '内存总量(GiB)', '内存使用量(GiB)');
REPLACE INTO `iam`.`quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('sfs_volume', 'sfs', '云硬盘总量(GiB)', '云硬盘使用总量(GiB)');
REPLACE INTO `iam`.`quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('rcce_cpu', 'rcce', 'cpu总量(个)', 'cpu使用量(个)');
REPLACE INTO `iam`.`quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('rcce_ram', 'rcce', '内存总量(GiB)', '内存使用量(GiB)');
REPLACE INTO `iam`.`quota_metric` (`name`, `type_name`, `total_name`, `used_name`) VALUES ('rcce_volume', 'rcce', '云硬盘总量(GiB)', '云硬盘使用总量(GiB)');


-- ----------------------------
-- Records of service_action
-- ----------------------------
REPLACE INTO `service_action` VALUES ('014c8d91cdbf0df506a870b2b29213e5', 'oss:PutBucketNotification', 'write', 'e455f304d2e34f2eb448d1d3182c3c68', '设置桶的消息通知配置');
REPLACE INTO `service_action` VALUES ('03f476fca54a7cf31f8df44beec7e56e', 'oss:HeadBucket', 'list', 'e455f304d2e34f2eb448d1d3182c3c68', '获取桶元数据');
REPLACE INTO `service_action` VALUES ('0741366e19ded662a4996234b252645b', 'oss:DeleteObject', 'write', 'e455f304d2e34f2eb448d1d3182c3c68', '删除对象、批量删除对象');
REPLACE INTO `service_action` VALUES ('0defa3f572de942a51c69c710aa94d51', 'oss:CreateBucket', 'write', 'e455f304d2e34f2eb448d1d3182c3c68', '创建桶');
REPLACE INTO `service_action` VALUES ('0ff3fddceda5868d6475cd4627eb93af', 'oss:DeleteBucketInventoryConfiguration', 'write', 'e455f304d2e34f2eb448d1d3182c3c68', '删除桶清单');
REPLACE INTO `service_action` VALUES ('115b207993b0f6b86b59989669a06e88', 'oss:PutObjectTagging', 'write', 'e455f304d2e34f2eb448d1d3182c3c68', '设置对象标签	');
REPLACE INTO `service_action` VALUES ('15a794ca673ce78f2baaf4184b297337', 'oss:ReplicateObject', 'write', 'e455f304d2e34f2eb448d1d3182c3c68', '复制对象');
REPLACE INTO `service_action` VALUES ('1e5650aacec693dd86b2bb9028a7184e', 'oss:GetObjectRetention', 'read', 'e455f304d2e34f2eb448d1d3182c3c68', '获取对象保留周期');
REPLACE INTO `service_action` VALUES ('1ed22add6f329b8907b77edd314de1a2', 'oss:GetReplicationConfiguration', 'read', 'e455f304d2e34f2eb448d1d3182c3c68', '获取桶的跨区域复制配置');
REPLACE INTO `service_action` VALUES ('2e1b929e435e35d25c0da0a3d52dab5a', 'oss:GetBucketTagging', 'read', 'e455f304d2e34f2eb448d1d3182c3c68', '获取桶标签');
REPLACE INTO `service_action` VALUES ('3751587d9c7fce950579f9fe7e20a16e', 'oss:GetObjectTagging', 'read', 'e455f304d2e34f2eb448d1d3182c3c68', '获取对象标签');
REPLACE INTO `service_action` VALUES ('3fae1b6151337f5c55b4d24ca890bfb0', 'oss:ListenBucketNotification', 'read', 'e455f304d2e34f2eb448d1d3182c3c68', '桶事件通知 ');
REPLACE INTO `service_action` VALUES ('411d23cfe02d8761e906de17738c0a37', 'oss:PutBucketTagging', 'write', 'e455f304d2e34f2eb448d1d3182c3c68', '设置桶标签');
REPLACE INTO `service_action` VALUES ('4662a66cc9a735f1204c45178afa2752', 'oss:GetBucketPolicy', 'read', 'e455f304d2e34f2eb448d1d3182c3c68', 'GetBucketPolicy');
REPLACE INTO `service_action` VALUES ('51046119c6aee847575c18837b6ad80b', 'oss:ListBucketMultipartUploads', 'list', 'e455f304d2e34f2eb448d1d3182c3c68', '多段上传列表');
REPLACE INTO `service_action` VALUES ('65f34febbb4865c23618407f6519cb1f', 'oss:ForceDeleteBucket', 'write', 'e455f304d2e34f2eb448d1d3182c3c68', '强制删除桶');
REPLACE INTO `service_action` VALUES ('6db18ac94da96f3a27e781e9d7eac013', 'oss:ListMultipartUploadParts', 'read', 'e455f304d2e34f2eb448d1d3182c3c68', '列举已上传的段');
REPLACE INTO `service_action` VALUES ('6e1a92c9986f6ccc8a9eed5582762486', 'oss:GetObjectVersion', 'read', 'e455f304d2e34f2eb448d1d3182c3c68', '获取对象内容、获取对象元数据');
REPLACE INTO `service_action` VALUES ('72f4b25c770ca03ea8cfcd422cae3a85', 'oss:ReplicateTags', 'write', 'e455f304d2e34f2eb448d1d3182c3c68', '复制标签');
REPLACE INTO `service_action` VALUES ('733f4fd0bb71e7a068fbc1e3ffbddc56', 'oss:PutBucketObjectLockConfiguration', 'write', 'e455f304d2e34f2eb448d1d3182c3c68', '设置桶对象锁定配置');
REPLACE INTO `service_action` VALUES ('74d628700485f189b8cfbe66adc77639', 'oss:ListAllMyBuckets', 'list', 'e455f304d2e34f2eb448d1d3182c3c68', '获取桶列表');
REPLACE INTO `service_action` VALUES ('79e2b2ad00868baf923e2177684fe22d', 'oss:GetBucketVersioning', 'read', 'e455f304d2e34f2eb448d1d3182c3c68', '获取桶的多版本状态');
REPLACE INTO `service_action` VALUES ('85b0fe5031c8e091b50b553cd4edb2cd', 'oss:BypassGovernanceRetention', 'write', 'e455f304d2e34f2eb448d1d3182c3c68', '控管模式保留');
REPLACE INTO `service_action` VALUES ('a0538a77883efb36ad094324e681d54f', 'oss:DeleteObjectVersionTagging', 'write', 'e455f304d2e34f2eb448d1d3182c3c68', '删除对象版本标记');
REPLACE INTO `service_action` VALUES ('a0928c8dfd319630200a7f323db2271c', 'oss:GetObjectVersionTagging', 'read', 'e455f304d2e34f2eb448d1d3182c3c68', '获取对象版本标记');
REPLACE INTO `service_action` VALUES ('a6e76a3bc1abc8fd33b9e8d6fdb92dab', 'oss:PutObject', 'write', 'e455f304d2e34f2eb448d1d3182c3c68', 'PUT上传、POST上传、复制对象、追加写对象、初始化上传段任务');
REPLACE INTO `service_action` VALUES ('a7e3e89e8f664a061be7201f333a32cb', 'oss:GetBucketPolicyStatus', 'read', 'e455f304d2e34f2eb448d1d3182c3c68', '获取桶策略状态');
REPLACE INTO `service_action` VALUES ('ab3dcea13b4a215f80b550ad61082558', 'oss:GetBucketLocation', 'read', 'e455f304d2e34f2eb448d1d3182c3c68', '获取桶区域位置');
REPLACE INTO `service_action` VALUES ('b35820381c9f719e86f6a704c6785b06', 'oss:PutObjectRetention', 'write', 'e455f304d2e34f2eb448d1d3182c3c68', '设置对象保留周期');
REPLACE INTO `service_action` VALUES ('b5734ec0af0dce96cafd0eeb6856f27e', 'oss:DeleteObjectVersion', 'write', 'e455f304d2e34f2eb448d1d3182c3c68', '删除对象、批量删除对象');
REPLACE INTO `service_action` VALUES ('b79434f6e866919ec7d8caca441e027b', 'oss:ListBucket', 'list', 'e455f304d2e34f2eb448d1d3182c3c68', '列举桶内对象');
REPLACE INTO `service_action` VALUES ('b9c1890e1d8b1592f246c8d19aafc86c', 'oss:DeleteBucketPolicy', 'auth', 'e455f304d2e34f2eb448d1d3182c3c68', '删除桶策略');
REPLACE INTO `service_action` VALUES ('be1e01e996d8af04ffa144ef5b3c84c0', 'oss:PutReplicationConfiguration', 'write', 'e455f304d2e34f2eb448d1d3182c3c68', '设置桶的跨区域复制配置');
REPLACE INTO `service_action` VALUES ('bfef53d55dbf6b484e3fbcb8b73060ba', 'oss:DeleteObjectTagging', 'write', 'e455f304d2e34f2eb448d1d3182c3c68', '删除对象标签	');
REPLACE INTO `service_action` VALUES ('c003c7220a964bd77d2c4f4f4d437ef6', 'oss:PutObjectVersionTagging', 'write', 'e455f304d2e34f2eb448d1d3182c3c68', '设置对象版本标记');
REPLACE INTO `service_action` VALUES ('c0160172e4ff98c6447d113f8d75ebff', 'oss:PutBucketVersioning', 'write', 'e455f304d2e34f2eb448d1d3182c3c68', '设置桶的多版本状态');
REPLACE INTO `service_action` VALUES ('c466fc51257aedbca2d52ca48b06c252', 'oss:PutBucketPolicy', 'auth', 'e455f304d2e34f2eb448d1d3182c3c68', '设置桶策略');
REPLACE INTO `service_action` VALUES ('d28258e130c4a3ef1df8888a5fe0db37', 'oss:ReplicateDelete', 'write', 'e455f304d2e34f2eb448d1d3182c3c68', '删除复制	');
REPLACE INTO `service_action` VALUES ('d34e0851c09fe1942d488d55a28186a4', 'oss:GetBucketNotification', 'read', 'e455f304d2e34f2eb448d1d3182c3c68', '获取桶的消息通知配置');
REPLACE INTO `service_action` VALUES ('d6e40a4c0e77bcdea70872013bdcd12c', 'oss:GetObjectLegalHold', 'read', 'e455f304d2e34f2eb448d1d3182c3c68', '获取对象合法保留');
REPLACE INTO `service_action` VALUES ('d75ccee8db22b8d2642fd28118514178', 'oss:ListBucketVersions', 'read', 'e455f304d2e34f2eb448d1d3182c3c68', '列举桶内多版本对象');
REPLACE INTO `service_action` VALUES ('db2f9c18319dbfe00f3305af6cc54446', 'oss:AbortMultipartUpload', 'write', 'e455f304d2e34f2eb448d1d3182c3c68', '取消多段上传任务');
REPLACE INTO `service_action` VALUES ('dcaaf3e0051bc262a7a01e2027ab44cf', 'oss:GetEncryptionConfiguration', 'read', 'e455f304d2e34f2eb448d1d3182c3c68', '获取桶的加密配置');
REPLACE INTO `service_action` VALUES ('df2562c0f362de0de66d97364b6a34b7', 'oss:ListenNotification', 'read', 'e455f304d2e34f2eb448d1d3182c3c68', '事件通知');
REPLACE INTO `service_action` VALUES ('e7aca754293a14fffef85164d9e2046c', 'oss:PutEncryptionConfiguration', 'write', 'e455f304d2e34f2eb448d1d3182c3c68', '设置桶的加密配置');
REPLACE INTO `service_action` VALUES ('ea28d6496a4049bb2dbe2c4789b1be97', 'oss:GetObjectVersionForReplication', 'read', 'e455f304d2e34f2eb448d1d3182c3c68', '获取对象版本');
REPLACE INTO `service_action` VALUES ('ed0fd8a81405ad35bf728cef2dfba0c4', 'oss:GetObject', 'read', 'e455f304d2e34f2eb448d1d3182c3c68', '获取对象内容、获取对象元数据');
REPLACE INTO `service_action` VALUES ('f40c46118ac042b44f0df693c6f274ae', 'oss:PutLifecycleConfiguration', 'write', 'e455f304d2e34f2eb448d1d3182c3c68', '设置桶的生命周期配置、删除桶的生命周期配置');
REPLACE INTO `service_action` VALUES ('f42d859c81ecd45b05ef6a3c5ce14a9f', 'oss:GetLifecycleConfiguration', 'read', 'e455f304d2e34f2eb448d1d3182c3c68', '获取桶的生命周期配置');
REPLACE INTO `service_action` VALUES ('f69a5686c8a3376d45f2eb04ea47e34b', 'oss:GetBucketObjectLockConfiguration', 'read', 'e455f304d2e34f2eb448d1d3182c3c68', '获取桶对象锁定配置');
REPLACE INTO `service_action` VALUES ('f7aabdf405fdc3be88b09719987c98c3', 'oss:PutObjectLegalHold', 'write', 'e455f304d2e34f2eb448d1d3182c3c68', '设置对象合法保留');
REPLACE INTO `service_action` VALUES ('f98ff66fc33f9ba05d023d1515bca3ca', 'oss:DeleteBucket', 'write', 'e455f304d2e34f2eb448d1d3182c3c68', '删除桶');

 -- ----------------------------
-- Records of strategy
-- ----------------------------
REPLACE INTO `strategy` VALUES ('3c1a26485af17a0f39a269f205a67c82', '{\"Statement\":[{\"Effect\":\"Allow\",\"action\":[\"oss:CreateBucket\"]}],\"version\":\"2012-10-17\"}', '创建桶', '创建桶', '2022-10-16 10:53:33', 'SYSTEM', '0d2bbb018e8b44b985a169647379f413');
REPLACE INTO `strategy` VALUES ('bcbef30d017e65ed5a5c20c19bce98e8', '{\"Statement\":[{\"Effect\":\"Allow\",\"action\":[\"oss:CreateBucket\",\"oss:HeadBucket\",\"oss:ListAllMyBuckets\"],\"Resource\":[\"*\"]}],\"version\":\"2012-10-17\"}', '对象存储初始策略', '对象存储用户绑定本策略', '2022-11-04 13:56:34', 'SYSTEM', '0d2bbb018e8b44b985a169647379f413');


#########################################  policy sql  ##########################################
DELETE from  ram_policy where owner_id is null;
DELETE from ram_policy_role WHERE ram_role_id in ('23546c7ab11e11ecbde1083a88902c92','22dc7fd7a67e11eb9db98257b6a5d0d7','5a5508808dc641bd83f2ca62ac641ee9','6d4372436aa34fd5a015550c22ba605c','94d25a5fa44b4e02b46f87fba31e1af1','c16c9944ca324aa693d3da6f7e7484f7');

INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '018c2c7192e42695c0306b5dc1756ec4', '修改用户状态', '组织管理-用户管理-操作-修改用户状态', '1', null, null, 'null', '1', '7d83120a78a67a76fda51b94adec4801', '', '', '', '7', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '018c2c7192e42695c0306b5dc1756ec4');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '02ec01c22b07f4a0d9812078d7fcddfe', '编辑权限', '角色管理-内置角色-编辑权限', 'null', null, null, 'null', '1', '44bdc7b1abd201a8490f36b246cf630c', '', '', '', '0', '', '2022-02-18 15:18:39', '/roleManage/modify', '', '', '2', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '02ec01c22b07f4a0d9812078d7fcddfe');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '08b95decde36ae016fbce02ad9b7c69b', '项目管理', '用户管理IAM-项目管理', 'null', null, null, 'null', '1', 'de5b8265a3f911eb8230f20ed01ddf38', '', '', '', '3', '', '2022-02-18 00:00:00', '/project-manage', 'icon-xmgl0218', '', '0', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '08b95decde36ae016fbce02ad9b7c69b');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '0afb7e979297c8046dfd7e5839aad8ea', '修改', '权限管理-修改', '1', null, null, 'PUT', '1', 'bda8f353d61b50ae676b02fb62741ca1', 'com.sugon.cloud.policy.api.controller.CommonPolicyController', 'editPolicy', '/api/common/ram/policy/{id}', '2', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '0afb7e979297c8046dfd7e5839aad8ea');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '0bac8a03f16bcce0af6d58c161459e6c', '修改配额', '组织管理-项目管理-操作-修改配额', '2', null, null, 'PUT', '1', '2ad1a10cdd55777e0acc760e26fc4259', 'QuotaController', 'updateProjectQuota', '/api/quotas/project/{project_id}', '2', '', '2021-05-06 00:00:00', '/departmentManage/modifyQuota', '', '', '2', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '0bac8a03f16bcce0af6d58c161459e6c');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '0cf0f1f1c39e109bd279a630054e7771', '同步配额', '组织管理-项目管理-操作-同步配额', 'null', null, null, 'null', '1', '2ad1a10cdd55777e0acc760e26fc4259', '', '', '', '3', '', '2022-04-18 14:30:09', '', '', '', '1', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '0cf0f1f1c39e109bd279a630054e7771');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '0da1267b51dc88e1db34ab41890f7ea0', '修改', '权限管理-特殊权限管理-修改', 'null', null, null, 'null', '1', '639ce03ce131b9388ff33be216b3fd72', '', '', '', '1', '', '2022-09-14 12:07:47', '', '', '', '1', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '0da1267b51dc88e1db34ab41890f7ea0');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '0f21c4d08dd89db53ca32e1740a9f9b3', '修改配额', '用户管理IAM-部门管理-修改配额', 'null', null, null, 'null', '1', 'f79da1ccd2add999e2491cce36c89290', '', '', '', '0', '', '2021-09-10 15:04:31', '/departmentManage/modifyQuota_two', '', '', '2', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '0f21c4d08dd89db53ca32e1740a9f9b3');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '1087f2f01567c61ac1baa20a10510c34', '新建', '组织管理-角色管理-新建', '1', null, null, 'POST', '1', 'fe3a21e5bffa710d207343632197ae40', 'com.sugon.cloud.iam.api.controller.IamRoleController', 'createRole', '/api/roles', '0', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '1087f2f01567c61ac1baa20a10510c34');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '10cda0983f13804014ca186b9b612c0a', '详情', '组织管理-项目管理-列表-详情', 'null', null, null, 'null', '1', '199e95b1f85d781cd4d04e2cfc08a2ea', '', '', '', '2', '', '2021-09-10 00:00:00', '/departmentManage/projectDetail/:id', '', '', '2', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '10cda0983f13804014ca186b9b612c0a');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '********************************', '菜单管理', '用户管理IAM-菜单管理', 'null', null, null, 'null', '1', 'de5b8265a3f911eb8230f20ed01ddf38', '', '', '', '7', '', '2021-11-04 00:00:00', '/menu-manage', 'icon-cdgl', '', '0', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '********************************');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '170e6e421f685fd9ef1e517f363972fa', '新建', '组织管理-项目管理-新建', '1', null, null, 'POST', '1', '199e95b1f85d781cd4d04e2cfc08a2ea', 'com.sugon.cloud.iam.api.controller.ProjectController', 'create', '/api/projects', '0', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '170e6e421f685fd9ef1e517f363972fa');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '199e95b1f85d781cd4d04e2cfc08a2ea', '项目管理', '组织管理-项目管理-列表', '1', null, null, 'GET', '1', 'f79da1ccd2add999e2491cce36c89290', 'com.sugon.cloud.iam.api.controller.DepartmentController', 'pageList', '/api/departments/{id}/projects', '1', '', '2021-04-23 00:00:00', '', '', '', '2', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '199e95b1f85d781cd4d04e2cfc08a2ea');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '********************************', '角色管理', '组织管理-用户管理-操作-角色管理', '1', null, null, 'DELETE', '1', '7d83120a78a67a76fda51b94adec4801', 'com.sugon.cloud.iam.api.controller.UserController', 'batchAssignmentRoles', '/api/users/{user_id}/bind-roles', '2', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '********************************');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '252c150ee420f8ef83d349f3d293722d', 'IP控制', '组织管理-用户管理-操作-IP控制', '1', null, null, 'null', '1', '7d83120a78a67a76fda51b94adec4801', '', '', '', '6', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '252c150ee420f8ef83d349f3d293722d');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '2ad1a10cdd55777e0acc760e26fc4259', '操作', '组织管理-项目管理-操作', '1', null, null, 'null', '1', '199e95b1f85d781cd4d04e2cfc08a2ea', '', '', '', '2', '', '2021-04-23 00:00:00', '', '', '', '2', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '2ad1a10cdd55777e0acc760e26fc4259');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '2baf4e8462592ad4201df36edd6da1a4', '用户详情', '组织管理-用户列表-用户详情', 'null', null, null, 'null', '1', 'c1fd2cce952da1093f3aa062e8ca8d9e', '', '', '', '0', '', '2021-05-12 00:00:00', '/departmentManage/userDetail/:id', '', '', '2', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '2baf4e8462592ad4201df36edd6da1a4');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '2f29666b441d9f10b193ae5db1687345', '创建自定义策略', '策略管理-创建自定义策略', 'null', null, null, 'null', '1', '6c6dafc2f8127287e43dee8f3c9ec412', '', '', '', '0', '', '2022-09-09 15:19:44', '/create-strategy', '', '', '2', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '2f29666b441d9f10b193ae5db1687345');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '2f563fdc6b3c716115a57fda77b06789', '修改角色', '组织管理-角色管理-操作-修改角色', '1', null, null, 'PUT', '1', 'd92db5112c6c67cf11a8e90d9ca79335', 'com.sugon.cloud.iam.api.controller.IamRoleController', 'update', '/api/roles', '1', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '2f563fdc6b3c716115a57fda77b06789');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '376b911d000734db8418a6f00e712d1a', '全局配置', '全局配置', 'null', null, null, 'null', '1', 'de5b8265a3f911eb8230f20ed01ddf38', '', '', '', '6', '', '2021-04-27 00:00:00', '/global', 'icon-quanjuguanli', '', '0', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '376b911d000734db8418a6f00e712d1a');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '3928a382a51cbe0b9dd96754709e3638', '删除', '权限管理-删除', '1', null, null, 'DELETE', '1', 'bda8f353d61b50ae676b02fb62741ca1', 'com.sugon.cloud.policy.api.controller.CommonPolicyController', 'deletePolicy', '/api/common/ram/policy/{id}', '3', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '3928a382a51cbe0b9dd96754709e3638');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '3d03c2717ab93cfe9205740c60455bd9', '详情', '项目管理-详情', 'null', null, null, 'null', '1', '08b95decde36ae016fbce02ad9b7c69b', '', '', '', '0', '', '2022-02-18 15:15:05', '/projectManage/projectDetail/:id', '', '', '2', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '3d03c2717ab93cfe9205740c60455bd9');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '44bdc7b1abd201a8490f36b246cf630c', '内置角色', '角色管理-内置角色', 'null', null, null, 'null', '1', 'd0a0296989d0b46f0a7399568df05064', '', '', '', '1', '', '2022-02-18 15:17:09', '', '', '', '1', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '44bdc7b1abd201a8490f36b246cf630c');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '4b115b2d2e7f4b3c027164c2542f764c', '修改用户', '组织管理-用户管理-操作-修改用户', '1', null, null, 'PUT', '1', '7d83120a78a67a76fda51b94adec4801', 'com.sugon.cloud.iam.api.controller.UserController', 'update', '/api/users', '0', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '4b115b2d2e7f4b3c027164c2542f764c');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '5104bdb87121783837dd275036a629d9', '详情', '用户管理-详情', 'null', null, null, 'null', '1', '593cb35d7143e50c3bc5fa2fb14e7ce2', '', '', '', '0', '', '2022-02-18 15:12:34', '/userManage/userDetail', '', '', '2', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '5104bdb87121783837dd275036a629d9');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '566c57598457f8ac3aa20b4fd971e178', '删除', '权限管理-特殊权限管理-删除', 'null', null, null, 'null', '1', '639ce03ce131b9388ff33be216b3fd72', '', '', '', '2', '', '2022-09-14 12:08:16', '', '', '', '1', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '566c57598457f8ac3aa20b4fd971e178');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '5930d39ee1c3b19b0160723ff40c3779', '删除角色', '组织管理-角色管理-操作-删除角色', '1', null, null, 'DELETE', '1', 'd92db5112c6c67cf11a8e90d9ca79335', 'com.sugon.cloud.iam.api.controller.IamRoleController', 'deleteRole', '/api/roles/{role_id}', '0', '', '2021-04-23 00:00:00', '', '', 'a72680e8bc494c93715698a887740f78', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '5930d39ee1c3b19b0160723ff40c3779');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '593cb35d7143e50c3bc5fa2fb14e7ce2', '用户管理', '用户管理IAM-用户管理', 'null', null, null, 'null', '1', 'de5b8265a3f911eb8230f20ed01ddf38', '', '', '', '2', '', '2022-02-18 00:00:00', '/user-manage', 'icon-yhgl0218', '', '0', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '593cb35d7143e50c3bc5fa2fb14e7ce2');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '5a18ed094c25f31d320ec808a5e81147', '删除', '策略管理-删除', 'null', null, null, 'null', '1', '6c6dafc2f8127287e43dee8f3c9ec412', '', '', '', '2', '', '2022-09-14 12:03:33', '', '', '', '1', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '5a18ed094c25f31d320ec808a5e81147');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '61d125a65df54296d6ed381976585cab', '添加', '权限管理-添加', '1', null, null, 'POST', '1', 'bda8f353d61b50ae676b02fb62741ca1', 'com.sugon.cloud.policy.api.controller.CommonPolicyController', 'createPolicy', '/api/common/ram/policy', '1', '', '2021-04-23 00:00:00', '', 'iconziyuan121', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '61d125a65df54296d6ed381976585cab');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '630c2949797479f246573339f4218e57', '创建用户', '组织管理-用户管理-创建用户', '1', null, null, 'POST', '1', 'c1fd2cce952da1093f3aa062e8ca8d9e', 'com.sugon.cloud.iam.api.controller.UserController', 'createSub', '/api/users/sub', '1', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '630c2949797479f246573339f4218e57');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '639ce03ce131b9388ff33be216b3fd72', '特殊权限管理', '权限管理-特殊权限管理', 'null', null, null, 'null', '1', 'bda8f353d61b50ae676b02fb62741ca1', '', '', '', '4', '', '2022-09-14 12:06:46', '', '', '', '1', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '639ce03ce131b9388ff33be216b3fd72');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '6c6dafc2f8127287e43dee8f3c9ec412', '策略管理', '用户管理IAM-策略管理', 'null', null, null, 'null', '1', 'de5b8265a3f911eb8230f20ed01ddf38', '', '', '', '5', '', '2022-09-09 00:00:00', '/strategy-manage', 'icon-celveguanli', '', '0', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '6c6dafc2f8127287e43dee8f3c9ec412');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '6f5562a40b28f22788015a32d5e4e1e5', '编辑', '组织管理-项目管理-操作-编辑', '1', null, null, 'PUT', '1', '2ad1a10cdd55777e0acc760e26fc4259', 'com.sugon.cloud.iam.api.controller.ProjectController', 'update', '/api/projects/{id}', '0', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '6f5562a40b28f22788015a32d5e4e1e5');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '6f7a9d24602f6e528039800c1862e620', '设置用户过期时间', '组织管理-用户管理-操作-设置用户过期时间', '1', null, null, 'null', '1', '7d83120a78a67a76fda51b94adec4801', '', '', '', '5', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '6f7a9d24602f6e528039800c1862e620');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '70f15b48260a078ff3188591b1bbc9fc', '删除用户', '组织管理-用户管理-删除用户', '1', null, null, 'DELETE', '1', 'c1fd2cce952da1093f3aa062e8ca8d9e', 'com.sugon.cloud.iam.api.controller.UserController', 'delete', '/api/users/{user_id}', '1', '', '2021-04-23 00:00:00', '', '', '70f15b48260a078ff3188591b1bbc9fc', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '70f15b48260a078ff3188591b1bbc9fc');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '7d83120a78a67a76fda51b94adec4801', '操作', '组织管理-用户管理-操作', '1', null, null, 'null', '1', 'c1fd2cce952da1093f3aa062e8ca8d9e', '', '', '', '2', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '7d83120a78a67a76fda51b94adec4801');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '7f1e37708c4bf9ab43f2f600a06dadce', '新建操作', '权限管理-特殊权限管理-新建操作', 'null', null, null, 'null', '1', '639ce03ce131b9388ff33be216b3fd72', '', '', '', '0', '', '2022-09-14 12:07:25', '', '', '', '1', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '7f1e37708c4bf9ab43f2f600a06dadce');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '880d73ee0a2390331cee75bec070e9ca', '删除', '组织管理-项目管理-删除', '1', null, null, 'DELETE', '1', '199e95b1f85d781cd4d04e2cfc08a2ea', 'com.sugon.cloud.iam.api.controller.ProjectController', 'delete', '/api/projects/{id}', '1', '', '2021-04-23 00:00:00', '', '', '880d73ee0a2390331cee75bec070e9ca', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '880d73ee0a2390331cee75bec070e9ca');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '890826492a92e7db24d8865c778a8e38', '项目管理', '组织管理-用户管理-操作-项目管理', '1', null, null, 'POST', '1', '7d83120a78a67a76fda51b94adec4801', 'com.sugon.cloud.iam.api.controller.UserController', 'batchAssignmentProjects', '/api/users/{user_id}/bind-projects', '3', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '890826492a92e7db24d8865c778a8e38');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '8bfd9785ffe9af45a057805815f24f02', '详情', '策略管理-详情', 'null', null, null, 'null', '1', '6c6dafc2f8127287e43dee8f3c9ec412', '', '', '', '0', '', '2022-09-26 00:00:00', '/strategy-manage/detail/:id', '', '', '2', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '8bfd9785ffe9af45a057805815f24f02');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '987d71c11d370c7cc5a3a40289150e15', '区域管理', '用户管理IAM-区域管理', '', null, null, 'null', '1', 'de5b8265a3f911eb8230f20ed01ddf38', '', '', '', '8', '', '2022-04-19 00:00:00', '/region-manage', 'icon-quyuguanli', '', '0', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '987d71c11d370c7cc5a3a40289150e15');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '99312f37b4cfd58db349a7704cf4695a', '编辑角色', '角色管理-自定义角色-编辑角色', 'null', null, null, 'null', '1', 'b36fb35927f92f790a5264922bdcd6bc', '', '', '', '0', '', '2022-02-18 15:26:19', '', '', '', '1', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '99312f37b4cfd58db349a7704cf4695a');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, '9a32ff48615e87c1718d5073c59c8162', '解绑策略', '组织管理-用户管理-操作-策略管理-解绑', 'null', null, null, 'null', '1', '7d83120a78a67a76fda51b94adec4801', '', '', '', '4', '', '2022-09-19 00:00:00', '', '', '', '1', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = '9a32ff48615e87c1718d5073c59c8162');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'a72680e8bc494c93715698a887740f78', '删除', '组织管理-角色管理-删除', '1', null, null, 'DELETE', '1', 'fe3a21e5bffa710d207343632197ae40', 'com.sugon.cloud.iam.api.controller.IamRoleController', 'deleteRole', '/api/roles/{role_id}', '1', '', '2021-04-23 00:00:00', '', '', 'a72680e8bc494c93715698a887740f78', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'a72680e8bc494c93715698a887740f78');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'a8cbf9ee2898e2b2979824d4d6098be2', '删除', '组织管理-用户管理-操作-删除', '1', null, null, 'DELETE', '1', '7d83120a78a67a76fda51b94adec4801', 'com.sugon.cloud.iam.api.controller.UserController', 'delete', '/api/users/{user_id}', '8', '', '2021-04-23 00:00:00', '', '', '70f15b48260a078ff3188591b1bbc9fc', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'a8cbf9ee2898e2b2979824d4d6098be2');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'aab2dc93c2c0a57e6efa0235b5cdc696', '概览', '用户管理IAM-概览', '1', null, null, 'GET', '1', 'de5b8265a3f911eb8230f20ed01ddf38', '', '', '', '0', '', '2021-04-23 00:00:00', '/overview', 'icon-gailan', '', '0', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'aab2dc93c2c0a57e6efa0235b5cdc696');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'b1ff673ebc7dee772da81fd3e68a5607', '列表', '组织管理-列表', 'null', null, null, 'GET', '1', 'f79da1ccd2add999e2491cce36c89290', 'com.sugon.cloud.iam.api.controller.DepartmentController', 'findByUserId', '/api/departments', '0', '', '2021-04-27 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'b1ff673ebc7dee772da81fd3e68a5607');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'b36fb35927f92f790a5264922bdcd6bc', '自定义角色', '角色管理-自定义角色', 'null', null, null, 'null', '1', 'd0a0296989d0b46f0a7399568df05064', '', '', '', '0', '', '2022-02-18 15:19:53', '', '', '', '1', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'b36fb35927f92f790a5264922bdcd6bc');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'bc0e2aa5b071a67599e4d3a8221750da', '列表', '权限管理-列表', '1', null, null, 'GET', '1', 'bda8f353d61b50ae676b02fb62741ca1', 'com.sugon.cloud.policy.api.controller.CommonPolicyController', 'getAllPolicyByTree', '/api/common/ram/policy/tree', '0', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'bc0e2aa5b071a67599e4d3a8221750da');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'bc7711c210c864be42f0c43af05a1803', '编辑权限', '角色管理-自定义角色-编辑权限', 'null', null, null, 'null', '1', 'b36fb35927f92f790a5264922bdcd6bc', '', '', '', '2', '', '2022-02-18 00:00:00', '/roleManage/modify', '', '', '2', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'bc7711c210c864be42f0c43af05a1803');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'bda8f353d61b50ae676b02fb62741ca1', '权限管理', '用户管理IAM-权限管理', '0', null, null, 'null', '1', 'de5b8265a3f911eb8230f20ed01ddf38', '', '', '', '4', '', '2021-04-23 00:00:00', '/strategy', 'icon-celueguanli', '', '0', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'bda8f353d61b50ae676b02fb62741ca1');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'c1fd2cce952da1093f3aa062e8ca8d9e', '用户管理', '组织管理-用户管理-列表', '1', null, null, 'GET', '1', 'f79da1ccd2add999e2491cce36c89290', 'com.sugon.cloud.iam.api.controller.UserController', 'userListByDeptId', '/api/users/dept/{dept_id}/list', '0', '', '2021-04-23 00:00:00', '', '', '', '2', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'c1fd2cce952da1093f3aa062e8ca8d9e');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'cfd7708b7d3f2b38ef67fdf187dcfdf1', '删除', '组织管理-项目管理-操作-删除', '1', null, null, 'DELETE', '1', '2ad1a10cdd55777e0acc760e26fc4259', 'com.sugon.cloud.iam.api.controller.ProjectController', 'delete', '/api/projects/{id}', '1', '', '2021-04-23 00:00:00', '', '', '880d73ee0a2390331cee75bec070e9ca', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'cfd7708b7d3f2b38ef67fdf187dcfdf1');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'd0a0296989d0b46f0a7399568df05064', '角色管理', '用户管理IAM-角色管理', 'null', null, null, 'null', '1', 'de5b8265a3f911eb8230f20ed01ddf38', '', '', '', '1', '', '2022-02-17 00:00:00', '/role-manage', 'icon-jiaoseguanli', '', '0', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'd0a0296989d0b46f0a7399568df05064');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'd92db5112c6c67cf11a8e90d9ca79335', '操作', '组织管理-角色管理-操作', '1', null, null, 'null', '1', 'fe3a21e5bffa710d207343632197ae40', '', '', '', '2', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'd92db5112c6c67cf11a8e90d9ca79335');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'de5b8265a3f911eb8230f20ed01ddf38', '用户管理IAM', '用户管理IAM顶级菜单', '1', null, null, 'null', '1', '0000000000', '', '', '', '2', null, '2020-04-12 00:00:00', '/Vpc', 'icon-ico-33', '', '0', '0' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'de5b8265a3f911eb8230f20ed01ddf38');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'e87f3a8553c2792982e1ea15d2a0a54c', '编辑策略', '策略管理-编辑策略', 'null', null, null, 'null', '1', '6c6dafc2f8127287e43dee8f3c9ec412', '', '', '', '1', '', '2022-09-14 00:00:00', '/edit-strategy', '', '', '2', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'e87f3a8553c2792982e1ea15d2a0a54c');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'ef25f0a4bd4b8221f81636047d1e2351', '绑定策略', '组织管理-用户管理-操作-策略管理-绑定', 'null', null, null, 'null', '1', '7d83120a78a67a76fda51b94adec4801', '', '', '', '4', '', '2022-09-19 14:43:29', '', '', '', '1', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'ef25f0a4bd4b8221f81636047d1e2351');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'f78146c95201acef6690ecf30a19a03d', '重置密码', '组织管理-用户管理-操作-重置密码', '1', null, null, 'null', '1', '7d83120a78a67a76fda51b94adec4801', 'com.sugon.cloud.iam.api.controller.UserController', 'updatePassword', '/api/users/{user_id}/password/{password}/{old_password}', '4', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'f78146c95201acef6690ecf30a19a03d');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'f79da1ccd2add999e2491cce36c89290', '组织管理', '用户管理IAM-部门管理', '0', null, null, 'null', '1', 'de5b8265a3f911eb8230f20ed01ddf38', '', '', '', '0', '', '2021-04-23 00:00:00', '/departmentManage', 'icon-zuzhiguanli', '', '0', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'f79da1ccd2add999e2491cce36c89290');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'fcdfd962bc93512c1cdee056daad37ef', '编辑权限', '组织管理-角色管理-操作-编辑权限', '1', null, null, 'null', '1', 'd92db5112c6c67cf11a8e90d9ca79335', '', '', '', '2', '', '2021-04-23 00:00:00', '/departmentManage/modify', '', '', '2', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'fcdfd962bc93512c1cdee056daad37ef');
INSERT INTO ram_policy (`resource_link_id`,`uuid`, `policy_name`, `policy_document`, `policy_type`, `comments`, `owner_id`, `action`, `effect`, `parent_id`, `class_name`, `method_name`, `method_url`, `sort`, `create_by`, `create_at`, `index`, `icon`, `link_id`, `resource_policy_flag`, `can_delete`) SELECT null, 'fe3a21e5bffa710d207343632197ae40', '角色管理', '组织管理-角色管理-列表', '1', null, null, 'GET', '1', 'f79da1ccd2add999e2491cce36c89290', 'com.sugon.cloud.iam.api.controller.IamRoleController', 'listByDept', '/api/roles/dept/{dept_id}', '2', '', '2021-04-23 00:00:00', '', '', '', '', '1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy WHERE uuid = 'fe3a21e5bffa710d207343632197ae40');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '00744a9caa6cbae907d01bf42d33fb0e', '0f21c4d08dd89db53ca32e1740a9f9b3', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '00744a9caa6cbae907d01bf42d33fb0e');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '00de0a8134050c37846a6cd1a4ccce93', '199e95b1f85d781cd4d04e2cfc08a2ea', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '00de0a8134050c37846a6cd1a4ccce93');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '0471660cdf68b5f2d16f5d58fcd11b2e', '99312f37b4cfd58db349a7704cf4695a', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '0471660cdf68b5f2d16f5d58fcd11b2e');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '058475e093160a7130f3bc6700fc3720', '880d73ee0a2390331cee75bec070e9ca', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '058475e093160a7130f3bc6700fc3720');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '0710decf20f85df7dec8d81e54bed9a0', 'bda8f353d61b50ae676b02fb62741ca1', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '0710decf20f85df7dec8d81e54bed9a0');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '0764127e69847e04909b1d24ce9e6aa9', '5104bdb87121783837dd275036a629d9', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '0764127e69847e04909b1d24ce9e6aa9');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '076f54fdd1781fa7de3a079524f0c58e', '880d73ee0a2390331cee75bec070e9ca', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '076f54fdd1781fa7de3a079524f0c58e');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '086acff2dee41d18402d6b6a2aafedea', '2f563fdc6b3c716115a57fda77b06789', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '086acff2dee41d18402d6b6a2aafedea');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '087f77e78b49520b3ce4738fceaa93a7', '593cb35d7143e50c3bc5fa2fb14e7ce2', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '087f77e78b49520b3ce4738fceaa93a7');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '0905b196326be3c55b7d62f3d881eb97', 'd92db5112c6c67cf11a8e90d9ca79335', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '0905b196326be3c55b7d62f3d881eb97');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '0acacaf02429b4f41865c29821ff1b1f', 'c1fd2cce952da1093f3aa062e8ca8d9e', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '0acacaf02429b4f41865c29821ff1b1f');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '0afde7798bb867c91ecdcb471ce0df66', '5930d39ee1c3b19b0160723ff40c3779', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '0afde7798bb867c91ecdcb471ce0df66');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '0b6150b29c1f42807129588510bbc5f0', '99312f37b4cfd58db349a7704cf4695a', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '0b6150b29c1f42807129588510bbc5f0');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '0b8ed9c99b6b6d18eac5a7da14d4882f', '170e6e421f685fd9ef1e517f363972fa', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '0b8ed9c99b6b6d18eac5a7da14d4882f');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '0bdef7b8c911701186ce0ccac3a50b3b', '08b95decde36ae016fbce02ad9b7c69b', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '0bdef7b8c911701186ce0ccac3a50b3b');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '0bf091efb9ba32579967265bb6381b0e', '566c57598457f8ac3aa20b4fd971e178', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '0bf091efb9ba32579967265bb6381b0e');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '0c0a5d310833b0084eee5046213f5959', '018c2c7192e42695c0306b5dc1756ec4', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '0c0a5d310833b0084eee5046213f5959');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '0f1f04972899da54eed7812e96f684ce', 'fe3a21e5bffa710d207343632197ae40', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '0f1f04972899da54eed7812e96f684ce');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '100b66733566c15a220dc49293a2cfd6', '4b115b2d2e7f4b3c027164c2542f764c', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '100b66733566c15a220dc49293a2cfd6');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '1190fa16f48a8bd2b2be7146aabee2bb', 'f79da1ccd2add999e2491cce36c89290', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '1190fa16f48a8bd2b2be7146aabee2bb');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '13966066b4279fcbb7505c9877230a02', 'fcdfd962bc93512c1cdee056daad37ef', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '13966066b4279fcbb7505c9877230a02');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '1500d59cd4e8dcf62869124ae8ace71a', '6c6dafc2f8127287e43dee8f3c9ec412', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '1500d59cd4e8dcf62869124ae8ace71a');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '160edbd8ccf9a0a001c1bedc44171356', 'f79da1ccd2add999e2491cce36c89290', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '160edbd8ccf9a0a001c1bedc44171356');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '16115d55b0c00c38263495f2445ccb93', '5a18ed094c25f31d320ec808a5e81147', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '16115d55b0c00c38263495f2445ccb93');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '161a317a7b2373852bef3abb44ff23ce', '10cda0983f13804014ca186b9b612c0a', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '161a317a7b2373852bef3abb44ff23ce');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '163b4d5f9271cd6a0e2a92cda5596e70', 'c1fd2cce952da1093f3aa062e8ca8d9e', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '163b4d5f9271cd6a0e2a92cda5596e70');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '17159a03ed076083afd3e5c2421d6b41', 'b36fb35927f92f790a5264922bdcd6bc', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '17159a03ed076083afd3e5c2421d6b41');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '1afaa3a3ccc787ca244267c3ec6cf2c2', '0bac8a03f16bcce0af6d58c161459e6c', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '1afaa3a3ccc787ca244267c3ec6cf2c2');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '1c79efa4106a305e8dd7598fe284c855', '018c2c7192e42695c0306b5dc1756ec4', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '1c79efa4106a305e8dd7598fe284c855');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '1da157e815d79c0c3ca412286329665a', '890826492a92e7db24d8865c778a8e38', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '1da157e815d79c0c3ca412286329665a');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '1f0a9aa0390ed5d8b34332e1cab4a183', 'b1ff673ebc7dee772da81fd3e68a5607', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '1f0a9aa0390ed5d8b34332e1cab4a183');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '1f8f844e4dfd3243ca17f6e0f05cc1b8', '018c2c7192e42695c0306b5dc1756ec4', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '1f8f844e4dfd3243ca17f6e0f05cc1b8');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '2033e536d25020222a0ec77f7c7b0e9d', 'd92db5112c6c67cf11a8e90d9ca79335', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '2033e536d25020222a0ec77f7c7b0e9d');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '221fbb0b849301b6bdc556afcf554b6c', '08b95decde36ae016fbce02ad9b7c69b', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '221fbb0b849301b6bdc556afcf554b6c');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '2239275512ef9f3096ea5c9a93f4fd87', '1087f2f01567c61ac1baa20a10510c34', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '2239275512ef9f3096ea5c9a93f4fd87');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '2402c1b67d4c39b47a0fd92126a54be3', 'bc0e2aa5b071a67599e4d3a8221750da', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '2402c1b67d4c39b47a0fd92126a54be3');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '25a0bea773ac60deff5ae54b4f33b10e', '7d83120a78a67a76fda51b94adec4801', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '25a0bea773ac60deff5ae54b4f33b10e');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '25bff1d3c3532b1ccaa6bc332b0f1960', '70f15b48260a078ff3188591b1bbc9fc', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '25bff1d3c3532b1ccaa6bc332b0f1960');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '265df79caeba9730d66f1a31d6146adf', '2baf4e8462592ad4201df36edd6da1a4', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '265df79caeba9730d66f1a31d6146adf');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '29a6f1b235d4226ced5efbc266a26bb9', '2ad1a10cdd55777e0acc760e26fc4259', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '29a6f1b235d4226ced5efbc266a26bb9');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '2a062d6177417481c5732058066d30ff', 'f79da1ccd2add999e2491cce36c89290', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '2a062d6177417481c5732058066d30ff');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '2a5573a77c90f9fe277bb4f1af941cf7', '99312f37b4cfd58db349a7704cf4695a', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '2a5573a77c90f9fe277bb4f1af941cf7');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '2b09fac0339e387c3747f3fcd7a45b7a', '0cf0f1f1c39e109bd279a630054e7771', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '2b09fac0339e387c3747f3fcd7a45b7a');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '2c03a24ae9ebffd005ad3a18f1bf5dd0', 'f78146c95201acef6690ecf30a19a03d', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '2c03a24ae9ebffd005ad3a18f1bf5dd0');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '320ae994db8cd2f0507123ab99906ee7', '02ec01c22b07f4a0d9812078d7fcddfe', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '320ae994db8cd2f0507123ab99906ee7');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '324a22062c5df4f0cf08dc921a3e4351', '199e95b1f85d781cd4d04e2cfc08a2ea', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '324a22062c5df4f0cf08dc921a3e4351');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '36558f12320b02cc5839de38f44fae04', '8bfd9785ffe9af45a057805815f24f02', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '36558f12320b02cc5839de38f44fae04');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '36dd9572d968b8538cf1cda41900d745', '2f563fdc6b3c716115a57fda77b06789', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '36dd9572d968b8538cf1cda41900d745');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '39a3b6504e57774bd3d3659bad15ec77', '018c2c7192e42695c0306b5dc1756ec4', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '39a3b6504e57774bd3d3659bad15ec77');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '39eb21cc9180f378920d7a0426f46c6d', '02ec01c22b07f4a0d9812078d7fcddfe', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '39eb21cc9180f378920d7a0426f46c6d');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '3a018e5b79aa2d31a730136523182bb0', 'b36fb35927f92f790a5264922bdcd6bc', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '3a018e5b79aa2d31a730136523182bb0');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '3a10557b418f2de848afca287bd21140', 'cfd7708b7d3f2b38ef67fdf187dcfdf1', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '3a10557b418f2de848afca287bd21140');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '3d8b1562059012bb93789d6fc6e2deec', '8bfd9785ffe9af45a057805815f24f02', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '3d8b1562059012bb93789d6fc6e2deec');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '4161411a1e164578ef8f09b20458f5ba', 'fcdfd962bc93512c1cdee056daad37ef', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '4161411a1e164578ef8f09b20458f5ba');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '416ddb1edc6ef8e5aa243fe5cc1ad647', 'f78146c95201acef6690ecf30a19a03d', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '416ddb1edc6ef8e5aa243fe5cc1ad647');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '427282efbe7ca25346e0b2ac977cb5f7', 'a72680e8bc494c93715698a887740f78', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '427282efbe7ca25346e0b2ac977cb5f7');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '42e75c4df4eec886604ced14bff740b6', '0da1267b51dc88e1db34ab41890f7ea0', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '42e75c4df4eec886604ced14bff740b6');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '432cbc7de81831437409551a0765068e', 'bc7711c210c864be42f0c43af05a1803', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '432cbc7de81831437409551a0765068e');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '439177388ca13dc34b19ab874e9bb1a6', '5a18ed094c25f31d320ec808a5e81147', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '439177388ca13dc34b19ab874e9bb1a6');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '458429a97c71e7bdbc1d2195ef43e2cd', '5a18ed094c25f31d320ec808a5e81147', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '458429a97c71e7bdbc1d2195ef43e2cd');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '46ac36e41d616a41879a389a94359efd', 'c1fd2cce952da1093f3aa062e8ca8d9e', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '46ac36e41d616a41879a389a94359efd');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '47f2d5739bf76d1312310e478f292931', 'a72680e8bc494c93715698a887740f78', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '47f2d5739bf76d1312310e478f292931');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '4802395aa0fee8b3468262cbb3af1212', 'b36fb35927f92f790a5264922bdcd6bc', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '4802395aa0fee8b3468262cbb3af1212');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '49fb838bb541676f42729cfc7468ea3a', '08b95decde36ae016fbce02ad9b7c69b', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '49fb838bb541676f42729cfc7468ea3a');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '4c3073010e15cda86f4d77dab9181a77', '593cb35d7143e50c3bc5fa2fb14e7ce2', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '4c3073010e15cda86f4d77dab9181a77');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '4cf9a0f63b249d2bfd7b2e454360dea7', '44bdc7b1abd201a8490f36b246cf630c', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '4cf9a0f63b249d2bfd7b2e454360dea7');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '4e7f2b5688927fe343ce8ad510cc9ab0', 'aab2dc93c2c0a57e6efa0235b5cdc696', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '4e7f2b5688927fe343ce8ad510cc9ab0');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '4ed59060ae9cea93a114aff6b39650e4', '0cf0f1f1c39e109bd279a630054e7771', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '4ed59060ae9cea93a114aff6b39650e4');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '515895f6f043d1a9449407855b3d49e2', '6c6dafc2f8127287e43dee8f3c9ec412', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '515895f6f043d1a9449407855b3d49e2');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '520a060faf5b3e88e72c32dd585c1cb5', 'd92db5112c6c67cf11a8e90d9ca79335', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '520a060faf5b3e88e72c32dd585c1cb5');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '523af5d3edba32942b1cd3f32c22d29d', '252c150ee420f8ef83d349f3d293722d', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '523af5d3edba32942b1cd3f32c22d29d');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '52938c5679fe385120b050c43505c5d1', '5930d39ee1c3b19b0160723ff40c3779', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '52938c5679fe385120b050c43505c5d1');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '5297644ffd81e7e5ff52a564c09f8af6', 'd0a0296989d0b46f0a7399568df05064', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '5297644ffd81e7e5ff52a564c09f8af6');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '5426448d9df4a83e63382cf43fc8ac65', '8bfd9785ffe9af45a057805815f24f02', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '5426448d9df4a83e63382cf43fc8ac65');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '546794f67847beba12740c7e695c8e21', '1087f2f01567c61ac1baa20a10510c34', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '546794f67847beba12740c7e695c8e21');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '54adea10f22e01947bff2e340177c45f', 'f78146c95201acef6690ecf30a19a03d', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '54adea10f22e01947bff2e340177c45f');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '5628d8479bbe6b8eb49e7fa9edbc89f8', '199e95b1f85d781cd4d04e2cfc08a2ea', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '5628d8479bbe6b8eb49e7fa9edbc89f8');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '56a893666bfc01f4ae1b4a2774b1060c', '376b911d000734db8418a6f00e712d1a', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '56a893666bfc01f4ae1b4a2774b1060c');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '56c1f9ca3ed04c21ba139c18cde481cf', '6f7a9d24602f6e528039800c1862e620', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '56c1f9ca3ed04c21ba139c18cde481cf');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '57e3749e07a2cb758d65c78dc9296f5e', 'fe3a21e5bffa710d207343632197ae40', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '57e3749e07a2cb758d65c78dc9296f5e');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '58095840378cf427d9129dcb4ee912b5', 'de5b8265a3f911eb8230f20ed01ddf38', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '58095840378cf427d9129dcb4ee912b5');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '580fa1d08d9ec619a9e313e2cfd59c4c', 'fcdfd962bc93512c1cdee056daad37ef', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '580fa1d08d9ec619a9e313e2cfd59c4c');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '584c0c316fdf94e7998455320ee93c7b', 'e87f3a8553c2792982e1ea15d2a0a54c', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '584c0c316fdf94e7998455320ee93c7b');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '595df77f4762bdd6424da8ac5c4fe2f2', '0bac8a03f16bcce0af6d58c161459e6c', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '595df77f4762bdd6424da8ac5c4fe2f2');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '59ae21c4d2676928b64c68d1123e0490', '5a18ed094c25f31d320ec808a5e81147', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '59ae21c4d2676928b64c68d1123e0490');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '5a021771304dde708b8529881957f4f5', '5930d39ee1c3b19b0160723ff40c3779', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '5a021771304dde708b8529881957f4f5');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '5a067ba5d48f4211c7adb6d7e8abc21d', '4b115b2d2e7f4b3c027164c2542f764c', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '5a067ba5d48f4211c7adb6d7e8abc21d');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '5aa9f662b6a8d04b7a7ca439dc4c649d', '0bac8a03f16bcce0af6d58c161459e6c', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '5aa9f662b6a8d04b7a7ca439dc4c649d');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '5b960f4a788397357421ac0223ab6fbb', '6f7a9d24602f6e528039800c1862e620', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '5b960f4a788397357421ac0223ab6fbb');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '5d7c0aa034a54814a23474f29caa2c1a', '593cb35d7143e50c3bc5fa2fb14e7ce2', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '5d7c0aa034a54814a23474f29caa2c1a');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '5d977c082bb07c5d5a35521c4662da38', 'ef25f0a4bd4b8221f81636047d1e2351', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '5d977c082bb07c5d5a35521c4662da38');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '5ed99693372c03951b67df8478235f48', '70f15b48260a078ff3188591b1bbc9fc', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '5ed99693372c03951b67df8478235f48');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '5f47d5b01266afd404603740da41dcaf', '********************************', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '5f47d5b01266afd404603740da41dcaf');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '60d7e9fbcb2ca71da0ba76ac897fdb14', '0afb7e979297c8046dfd7e5839aad8ea', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '60d7e9fbcb2ca71da0ba76ac897fdb14');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '64ba5d0942e9791e12498b67966e67dc', '890826492a92e7db24d8865c778a8e38', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '64ba5d0942e9791e12498b67966e67dc');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '64ca5e0a0c4394d7475128f45a4a84ea', '0f21c4d08dd89db53ca32e1740a9f9b3', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '64ca5e0a0c4394d7475128f45a4a84ea');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '664bac19613cce9de2ed282bc3fc93df', 'a72680e8bc494c93715698a887740f78', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '664bac19613cce9de2ed282bc3fc93df');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '68f00c7deea302e5be381b16d148aad3', '170e6e421f685fd9ef1e517f363972fa', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '68f00c7deea302e5be381b16d148aad3');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '69207fe53ad896b7ff54c73afa276f4d', '6c6dafc2f8127287e43dee8f3c9ec412', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '69207fe53ad896b7ff54c73afa276f4d');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '6a9d4f7ec68a70ac295827685188de71', '********************************', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '6a9d4f7ec68a70ac295827685188de71');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '6ad341d085a8d358f68b6950600f0996', 'de5b8265a3f911eb8230f20ed01ddf38', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '6ad341d085a8d358f68b6950600f0996');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '6b1be8b51a5a691de20be2cbec1f9415', 'aab2dc93c2c0a57e6efa0235b5cdc696', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '6b1be8b51a5a691de20be2cbec1f9415');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '6ce674dd3ddd16aad5d69bbe9a1b3308', 'a8cbf9ee2898e2b2979824d4d6098be2', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '6ce674dd3ddd16aad5d69bbe9a1b3308');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '6db491369f7f6af2f9cd5e1859681086', '44bdc7b1abd201a8490f36b246cf630c', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '6db491369f7f6af2f9cd5e1859681086');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '6e4ed611aa4c62ac31023467609f3335', '630c2949797479f246573339f4218e57', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '6e4ed611aa4c62ac31023467609f3335');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '6f20e91d210b47c2aa1fc58f45761aff', '630c2949797479f246573339f4218e57', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '6f20e91d210b47c2aa1fc58f45761aff');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '700b6de90ed1eb14a9610f96b0c9d366', '170e6e421f685fd9ef1e517f363972fa', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '700b6de90ed1eb14a9610f96b0c9d366');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '7044d0afc264b2b06afe0eb33ddf4e57', 'e87f3a8553c2792982e1ea15d2a0a54c', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '7044d0afc264b2b06afe0eb33ddf4e57');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '713b2fa2e001171cdb2f0fe36bc63f74', '3d03c2717ab93cfe9205740c60455bd9', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '713b2fa2e001171cdb2f0fe36bc63f74');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '7185bd28e8d3db574a8c2eefe9339da0', 'f79da1ccd2add999e2491cce36c89290', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '7185bd28e8d3db574a8c2eefe9339da0');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '725fd6196e47e1e2cfb34423ae2bc8d1', 'ef25f0a4bd4b8221f81636047d1e2351', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '725fd6196e47e1e2cfb34423ae2bc8d1');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '7337e6c27a16bd20dc5bed42133ba7f9', '630c2949797479f246573339f4218e57', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '7337e6c27a16bd20dc5bed42133ba7f9');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '737c95d474bb6573d73e0156b6ca3145', '********************************', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '737c95d474bb6573d73e0156b6ca3145');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '73c005a431f92161ca0e217d0ae5270e', '639ce03ce131b9388ff33be216b3fd72', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '73c005a431f92161ca0e217d0ae5270e');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '7478ed5127a5d010d630b099bcf9e3fc', 'b1ff673ebc7dee772da81fd3e68a5607', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '7478ed5127a5d010d630b099bcf9e3fc');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '753d8daf082d5d1fbb777e87e0334db3', '2f563fdc6b3c716115a57fda77b06789', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '753d8daf082d5d1fbb777e87e0334db3');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '758bed4ea71ee69409d30b8ccb49039b', 'b36fb35927f92f790a5264922bdcd6bc', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '758bed4ea71ee69409d30b8ccb49039b');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '77bf4a512538eac4580af1d4adcd90bb', '5104bdb87121783837dd275036a629d9', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '77bf4a512538eac4580af1d4adcd90bb');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '7817b9552dff929ce102e3678cbbe6d1', '02ec01c22b07f4a0d9812078d7fcddfe', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '7817b9552dff929ce102e3678cbbe6d1');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '7980845b6bd5c553e885c9d6a1ed0165', '44bdc7b1abd201a8490f36b246cf630c', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '7980845b6bd5c553e885c9d6a1ed0165');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '7bcf731f7679fd8daa98e1576ed70c9c', 'aab2dc93c2c0a57e6efa0235b5cdc696', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '7bcf731f7679fd8daa98e1576ed70c9c');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '7bdefbaaa5a147d18067a5952c6386c5', 'fcdfd962bc93512c1cdee056daad37ef', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '7bdefbaaa5a147d18067a5952c6386c5');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '7c83d90a2559087c32bd9f945d2acb62', '630c2949797479f246573339f4218e57', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '7c83d90a2559087c32bd9f945d2acb62');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '7ee1e817bf5141251c8762cb5220c78d', '02ec01c22b07f4a0d9812078d7fcddfe', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '7ee1e817bf5141251c8762cb5220c78d');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '7f8e8e2c6ad02bd58275301522ffdd14', '70f15b48260a078ff3188591b1bbc9fc', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '7f8e8e2c6ad02bd58275301522ffdd14');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '80702fcd1f06bade127a5f9d4aa3bf8f', '99312f37b4cfd58db349a7704cf4695a', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '80702fcd1f06bade127a5f9d4aa3bf8f');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '80b8c3b43ff31f7676c1ffa196532b64', '6f5562a40b28f22788015a32d5e4e1e5', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '80b8c3b43ff31f7676c1ffa196532b64');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '816c885ac2382e5f80b0eaf03ee43c8f', '880d73ee0a2390331cee75bec070e9ca', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '816c885ac2382e5f80b0eaf03ee43c8f');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '86240f594dd8140dda4db8c1a8dc0f0b', '2ad1a10cdd55777e0acc760e26fc4259', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '86240f594dd8140dda4db8c1a8dc0f0b');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '87636ffbcade352a515f07157fc79343', '0bac8a03f16bcce0af6d58c161459e6c', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '87636ffbcade352a515f07157fc79343');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '8783a706c9f518a35be5b81be058f3dd', 'b1ff673ebc7dee772da81fd3e68a5607', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '8783a706c9f518a35be5b81be058f3dd');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '87e4dabd0d23c64de130ee836e7f3716', '8bfd9785ffe9af45a057805815f24f02', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '87e4dabd0d23c64de130ee836e7f3716');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '89d43e79802452e62458303d268e7857', '7d83120a78a67a76fda51b94adec4801', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '89d43e79802452e62458303d268e7857');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '8a9770030c02725fa5ef4681ebd00740', '6f5562a40b28f22788015a32d5e4e1e5', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '8a9770030c02725fa5ef4681ebd00740');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '8ba5fb8ff3464d03bbf5d934da0fe10e', '2baf4e8462592ad4201df36edd6da1a4', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '8ba5fb8ff3464d03bbf5d934da0fe10e');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '8c6ba9461a9cd84ac1f8d0969551365f', 'b1ff673ebc7dee772da81fd3e68a5607', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '8c6ba9461a9cd84ac1f8d0969551365f');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '8cc2c0c657f8759fc7a05461fefdac58', 'bc7711c210c864be42f0c43af05a1803', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '8cc2c0c657f8759fc7a05461fefdac58');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '8d461c3d7b98ce9ba0faca197e7ee2f3', 'a8cbf9ee2898e2b2979824d4d6098be2', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '8d461c3d7b98ce9ba0faca197e7ee2f3');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '8e255b3143f3896e0b420a48dd9ac769', '252c150ee420f8ef83d349f3d293722d', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '8e255b3143f3896e0b420a48dd9ac769');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '93581ebdb26ba5d7f782a52bfb9f3b36', 'a72680e8bc494c93715698a887740f78', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '93581ebdb26ba5d7f782a52bfb9f3b36');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '94c3f5ab8fd5f90bd3c5061529ba78a8', '890826492a92e7db24d8865c778a8e38', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '94c3f5ab8fd5f90bd3c5061529ba78a8');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '9546f84a160a03ac1cb2310ba1958aa8', '6f7a9d24602f6e528039800c1862e620', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '9546f84a160a03ac1cb2310ba1958aa8');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '95849d95835139c747eaf694e11ebdb7', '6f5562a40b28f22788015a32d5e4e1e5', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '95849d95835139c747eaf694e11ebdb7');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '96402cf29026cb37f10ae80912e32112', '6f7a9d24602f6e528039800c1862e620', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '96402cf29026cb37f10ae80912e32112');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '96ac816461e7c3897c23c0896b8330be', 'cfd7708b7d3f2b38ef67fdf187dcfdf1', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '96ac816461e7c3897c23c0896b8330be');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '96ca45b0b7e419f4166694f694fefc3c', '252c150ee420f8ef83d349f3d293722d', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '96ca45b0b7e419f4166694f694fefc3c');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '97269a8941cd4dd11683dcdc188c2238', '2f29666b441d9f10b193ae5db1687345', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '97269a8941cd4dd11683dcdc188c2238');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '9790ca8f48e1a3b97e521e019196ed06', 'cfd7708b7d3f2b38ef67fdf187dcfdf1', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '9790ca8f48e1a3b97e521e019196ed06');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '97f62e5322ff8c11bac6bae1aa1d5fc8', 'c1fd2cce952da1093f3aa062e8ca8d9e', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '97f62e5322ff8c11bac6bae1aa1d5fc8');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '97fc80f509b78b806991d8b05ff16309', '7f1e37708c4bf9ab43f2f600a06dadce', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '97fc80f509b78b806991d8b05ff16309');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '991b3f270e27142d1640842cc2d1e7e5', '2ad1a10cdd55777e0acc760e26fc4259', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '991b3f270e27142d1640842cc2d1e7e5');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '99e0fe83cdb5d5115ab3fba72b4a79e0', '630c2949797479f246573339f4218e57', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '99e0fe83cdb5d5115ab3fba72b4a79e0');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '99f0eaa326577c684ed0fb8ba560a8ff', '2f563fdc6b3c716115a57fda77b06789', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '99f0eaa326577c684ed0fb8ba560a8ff');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '9bbebe18e1f20e6e89d672509db58236', '3d03c2717ab93cfe9205740c60455bd9', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '9bbebe18e1f20e6e89d672509db58236');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '9be06030a70504cd7b701338d0d0944f', '6f7a9d24602f6e528039800c1862e620', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '9be06030a70504cd7b701338d0d0944f');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '9ce23ccccfe049c3350eda384bcbfe7a', 'fe3a21e5bffa710d207343632197ae40', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '9ce23ccccfe049c3350eda384bcbfe7a');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '9d58e4eac179e688fe8da523a88e9c27', '170e6e421f685fd9ef1e517f363972fa', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '9d58e4eac179e688fe8da523a88e9c27');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '9dd242814fc04971facf5b84b1c95298', 'd0a0296989d0b46f0a7399568df05064', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '9dd242814fc04971facf5b84b1c95298');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '9dfbe4e2e0d5b810dfd476d685bc1cb2', '10cda0983f13804014ca186b9b612c0a', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '9dfbe4e2e0d5b810dfd476d685bc1cb2');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '9e199c407372ffe255b5d2518cc5eb12', '2ad1a10cdd55777e0acc760e26fc4259', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '9e199c407372ffe255b5d2518cc5eb12');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '9e29e830a65db1928b12d4e446928855', '2f29666b441d9f10b193ae5db1687345', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '9e29e830a65db1928b12d4e446928855');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '9e53f6f55c4f4ce0501e3e9a7c67db44', '6c6dafc2f8127287e43dee8f3c9ec412', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '9e53f6f55c4f4ce0501e3e9a7c67db44');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '9e8bc10ac73bed867351618672bc766f', 'b1ff673ebc7dee772da81fd3e68a5607', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '9e8bc10ac73bed867351618672bc766f');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '9eba1c41694f93ea6377df02bbe22562', '3928a382a51cbe0b9dd96754709e3638', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '9eba1c41694f93ea6377df02bbe22562');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '9ec6ec6fd7673f9ec6065aece90ff434', '170e6e421f685fd9ef1e517f363972fa', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '9ec6ec6fd7673f9ec6065aece90ff434');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT '9f749eb161c25c23bc9a80d16394c0af', '880d73ee0a2390331cee75bec070e9ca', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = '9f749eb161c25c23bc9a80d16394c0af');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'a0157ac3a4f2438b7a7839c4a28a1134', 'f78146c95201acef6690ecf30a19a03d', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'a0157ac3a4f2438b7a7839c4a28a1134');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'a028e36c06ba98901dfbc0d89275a83d', 'a8cbf9ee2898e2b2979824d4d6098be2', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'a028e36c06ba98901dfbc0d89275a83d');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'a04e041c13029fa47eb88145ee48d4c3', 'e87f3a8553c2792982e1ea15d2a0a54c', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'a04e041c13029fa47eb88145ee48d4c3');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'a1bec1e1cb7fccf83a07b097c5753627', 'fe3a21e5bffa710d207343632197ae40', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'a1bec1e1cb7fccf83a07b097c5753627');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'a1d921a8fd526559831d7639eaca52dc', 'aab2dc93c2c0a57e6efa0235b5cdc696', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'a1d921a8fd526559831d7639eaca52dc');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'a676f8cb253753719ccf270f34fbada4', 'f78146c95201acef6690ecf30a19a03d', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'a676f8cb253753719ccf270f34fbada4');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'a6c646cb77a4581c72bf7e913be6c0c2', '70f15b48260a078ff3188591b1bbc9fc', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'a6c646cb77a4581c72bf7e913be6c0c2');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'a7597affadd781cadb37422b92ac6a24', '7d83120a78a67a76fda51b94adec4801', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'a7597affadd781cadb37422b92ac6a24');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'a9534cde0fc5adcdcaee25705b798928', '9a32ff48615e87c1718d5073c59c8162', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'a9534cde0fc5adcdcaee25705b798928');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'a9dfcfaf9f90b887744f0e6f89e649fa', '593cb35d7143e50c3bc5fa2fb14e7ce2', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'a9dfcfaf9f90b887744f0e6f89e649fa');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'abafb04db3b0851d60120db04e7cd81f', '593cb35d7143e50c3bc5fa2fb14e7ce2', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'abafb04db3b0851d60120db04e7cd81f');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'ac151f7a04a06d3bbce1bccddf18afc6', '10cda0983f13804014ca186b9b612c0a', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'ac151f7a04a06d3bbce1bccddf18afc6');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'aca951d1017d3d9d232aeb7e8f2ff38c', 'bc7711c210c864be42f0c43af05a1803', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'aca951d1017d3d9d232aeb7e8f2ff38c');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'ad8fd5fdc5d2c63509cedb285209d2f0', '5a18ed094c25f31d320ec808a5e81147', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'ad8fd5fdc5d2c63509cedb285209d2f0');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'b0778e118d480a27ce9740890c0a7de1', '9a32ff48615e87c1718d5073c59c8162', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'b0778e118d480a27ce9740890c0a7de1');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'b37396c2518a15fb27cefb73a917b151', '99312f37b4cfd58db349a7704cf4695a', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'b37396c2518a15fb27cefb73a917b151');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'b3ce3e55134a12353549e8859aa230aa', '0cf0f1f1c39e109bd279a630054e7771', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'b3ce3e55134a12353549e8859aa230aa');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'b45b1ff6d749d2179a126de45bd805ba', '5104bdb87121783837dd275036a629d9', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'b45b1ff6d749d2179a126de45bd805ba');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'b47986eca9452caddd4bf6bcf6b281b6', 'f79da1ccd2add999e2491cce36c89290', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'b47986eca9452caddd4bf6bcf6b281b6');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'b557a4d539da0d2029e39429c979db55', 'fe3a21e5bffa710d207343632197ae40', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'b557a4d539da0d2029e39429c979db55');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'b724fbc5da14ce3af1df9704bd1cd460', '7d83120a78a67a76fda51b94adec4801', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'b724fbc5da14ce3af1df9704bd1cd460');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'b779b986b4f2a808e596be2224571dce', 'de5b8265a3f911eb8230f20ed01ddf38', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'b779b986b4f2a808e596be2224571dce');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'b7f30c872dfba7a98e8d18f59c2851b8', '********************************', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'b7f30c872dfba7a98e8d18f59c2851b8');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'b8aa944bcab9317c58f26e2f9b3e3828', 'a8cbf9ee2898e2b2979824d4d6098be2', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'b8aa944bcab9317c58f26e2f9b3e3828');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'b991756f9655dabf802ec7fa10a3c33a', '9a32ff48615e87c1718d5073c59c8162', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'b991756f9655dabf802ec7fa10a3c33a');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'b9fb6097a64c4381b9718f02edab6a02', '1087f2f01567c61ac1baa20a10510c34', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'b9fb6097a64c4381b9718f02edab6a02');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'ba7c0dfc403a4554695ce5b4d8f67beb', '5104bdb87121783837dd275036a629d9', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'ba7c0dfc403a4554695ce5b4d8f67beb');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'ba92352dbbcda9f4188e219463099861', '2f29666b441d9f10b193ae5db1687345', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'ba92352dbbcda9f4188e219463099861');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'bd8d2b167051aa5818eef989b3946575', '5104bdb87121783837dd275036a629d9', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'bd8d2b167051aa5818eef989b3946575');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'bde10f24250619f87441c9d8aeaafec8', 'bc7711c210c864be42f0c43af05a1803', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'bde10f24250619f87441c9d8aeaafec8');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'be6aee2a1b0288b84fd65b081361965f', 'cfd7708b7d3f2b38ef67fdf187dcfdf1', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'be6aee2a1b0288b84fd65b081361965f');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'bf2fa7f55da50b01c830259476d2642b', '252c150ee420f8ef83d349f3d293722d', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'bf2fa7f55da50b01c830259476d2642b');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'bf967df324ffd149b12b836db2bb2ae9', '1087f2f01567c61ac1baa20a10510c34', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'bf967df324ffd149b12b836db2bb2ae9');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'c0d63f1ac31705931f542c206962276d', '6f5562a40b28f22788015a32d5e4e1e5', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'c0d63f1ac31705931f542c206962276d');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'c0fff55a36f0ed6ac8a9e9226b6526ac', 'c1fd2cce952da1093f3aa062e8ca8d9e', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'c0fff55a36f0ed6ac8a9e9226b6526ac');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'c20ca5ef1326d5c96574a55f4429702d', '6c6dafc2f8127287e43dee8f3c9ec412', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'c20ca5ef1326d5c96574a55f4429702d');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'c2b30259b7a4404c3e7735c12689693c', '9a32ff48615e87c1718d5073c59c8162', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'c2b30259b7a4404c3e7735c12689693c');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'c2f7938f06fee04f24c69f178556d12b', 'e87f3a8553c2792982e1ea15d2a0a54c', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'c2f7938f06fee04f24c69f178556d12b');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'c4f99d8314d1dab144295896cccb6c77', '0cf0f1f1c39e109bd279a630054e7771', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'c4f99d8314d1dab144295896cccb6c77');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'c69e9bb74acb4ffc54147af4232564a9', '70f15b48260a078ff3188591b1bbc9fc', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'c69e9bb74acb4ffc54147af4232564a9');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'c6ce5794e944db3e91ebf5473db2fab2', '4b115b2d2e7f4b3c027164c2542f764c', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'c6ce5794e944db3e91ebf5473db2fab2');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'c7449a3bcd68a144ab3c1fb4892f720d', 'de5b8265a3f911eb8230f20ed01ddf38', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'c7449a3bcd68a144ab3c1fb4892f720d');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'c7bb2b55abce7b21b8bedb287d163861', '2baf4e8462592ad4201df36edd6da1a4', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'c7bb2b55abce7b21b8bedb287d163861');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'c8c41e59385b1bd87d02a1949644beb4', '********************************', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'c8c41e59385b1bd87d02a1949644beb4');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'cac792ac8fa909d37f14fc7c7636ee24', '2baf4e8462592ad4201df36edd6da1a4', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'cac792ac8fa909d37f14fc7c7636ee24');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'cbc75c2e65f36022590540e9dc80579e', '199e95b1f85d781cd4d04e2cfc08a2ea', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'cbc75c2e65f36022590540e9dc80579e');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'cd33b6fcae762d835846f6accb0ec952', '880d73ee0a2390331cee75bec070e9ca', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'cd33b6fcae762d835846f6accb0ec952');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'd09c4e587914489670b1c5036b8ca6e9', '4b115b2d2e7f4b3c027164c2542f764c', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'd09c4e587914489670b1c5036b8ca6e9');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'd15e5369e7b498afcb6f26682fbbc08b', '6f5562a40b28f22788015a32d5e4e1e5', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'd15e5369e7b498afcb6f26682fbbc08b');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'd196ca34aa115adcd00e134558fe24a1', '3d03c2717ab93cfe9205740c60455bd9', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'd196ca34aa115adcd00e134558fe24a1');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'd2436775a4e32e47a4a053cb1c104d6f', '02ec01c22b07f4a0d9812078d7fcddfe', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'd2436775a4e32e47a4a053cb1c104d6f');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'd2e91ba0527bcbd88ff0a36847f027ff', 'de5b8265a3f911eb8230f20ed01ddf38', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'd2e91ba0527bcbd88ff0a36847f027ff');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'd3ae7dd317524ed4224e67e1b77df017', '08b95decde36ae016fbce02ad9b7c69b', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'd3ae7dd317524ed4224e67e1b77df017');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'd3cfada308416b7be446675c90a3d869', '10cda0983f13804014ca186b9b612c0a', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'd3cfada308416b7be446675c90a3d869');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'd505360d46c9735eab06569a65899c26', 'd92db5112c6c67cf11a8e90d9ca79335', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'd505360d46c9735eab06569a65899c26');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'd505b711b708ab3b7df96210e3ce1786', '5930d39ee1c3b19b0160723ff40c3779', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'd505b711b708ab3b7df96210e3ce1786');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'd55c5b03db18c0ea7aa76b0c799f1d36', 'ef25f0a4bd4b8221f81636047d1e2351', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'd55c5b03db18c0ea7aa76b0c799f1d36');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'd5aa0a932fbf808387e98e7a845190a2', '8bfd9785ffe9af45a057805815f24f02', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'd5aa0a932fbf808387e98e7a845190a2');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'd60963b47347badacc9f7f39c8dc41db', 'a72680e8bc494c93715698a887740f78', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'd60963b47347badacc9f7f39c8dc41db');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'd611654fd87dcdaef92cc0da51d2a75e', '2f29666b441d9f10b193ae5db1687345', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'd611654fd87dcdaef92cc0da51d2a75e');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'd64a2dc5cd267814170ca8d18fe41084', 'd92db5112c6c67cf11a8e90d9ca79335', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'd64a2dc5cd267814170ca8d18fe41084');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'd9d69fdc1d8ababace33face55a54ee6', '08b95decde36ae016fbce02ad9b7c69b', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'd9d69fdc1d8ababace33face55a54ee6');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'da174f84dc90055d5409f696cd9b1497', 'b36fb35927f92f790a5264922bdcd6bc', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'da174f84dc90055d5409f696cd9b1497');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'dab816804c7d3b1089d746a9b8d5f588', '2ad1a10cdd55777e0acc760e26fc4259', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'dab816804c7d3b1089d746a9b8d5f588');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'db623fbe13adefe4e66bba6af71a4e08', '0f21c4d08dd89db53ca32e1740a9f9b3', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'db623fbe13adefe4e66bba6af71a4e08');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'dc2727e383513f4503f7a8bb308ddf47', '3d03c2717ab93cfe9205740c60455bd9', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'dc2727e383513f4503f7a8bb308ddf47');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'dd124e47c970435e6063eb2eaac137d8', '2f563fdc6b3c716115a57fda77b06789', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'dd124e47c970435e6063eb2eaac137d8');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'dfc167cff8901b36cfda71f15116690a', 'd0a0296989d0b46f0a7399568df05064', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'dfc167cff8901b36cfda71f15116690a');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'e0d9e72cdec2dfb89852465bfec527d3', '1087f2f01567c61ac1baa20a10510c34', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'e0d9e72cdec2dfb89852465bfec527d3');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'e1f215f21722c0d14a94d5fdba524179', '0f21c4d08dd89db53ca32e1740a9f9b3', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'e1f215f21722c0d14a94d5fdba524179');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'e1f6333bf02250fe04f05a2cbb1ff577', '********************************', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'e1f6333bf02250fe04f05a2cbb1ff577');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'e3d2b08f1be80bbca45b39d8b5c499e6', '987d71c11d370c7cc5a3a40289150e15', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'e3d2b08f1be80bbca45b39d8b5c499e6');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'e519624967d0cf71152994b53d94db69', '44bdc7b1abd201a8490f36b246cf630c', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'e519624967d0cf71152994b53d94db69');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'e6172e15e5b4f1b409f85f3fd9139b72', 'bc7711c210c864be42f0c43af05a1803', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'e6172e15e5b4f1b409f85f3fd9139b72');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'e6923ab2cd146c88891b0b451a390431', 'fcdfd962bc93512c1cdee056daad37ef', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'e6923ab2cd146c88891b0b451a390431');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'e76173f190f507ff6fee01a690d59442', '3d03c2717ab93cfe9205740c60455bd9', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'e76173f190f507ff6fee01a690d59442');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'e7ce75459c4ab1fe541f715e2f21a7af', '44bdc7b1abd201a8490f36b246cf630c', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'e7ce75459c4ab1fe541f715e2f21a7af');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'e81767320177e3ecf0956e73d92cef7b', '61d125a65df54296d6ed381976585cab', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'e81767320177e3ecf0956e73d92cef7b');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'e8bc775cef96041afe3dbd91de2411e1', 'd0a0296989d0b46f0a7399568df05064', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'e8bc775cef96041afe3dbd91de2411e1');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'e8be810ba8ede2e7bd7437d8fb22db30', '890826492a92e7db24d8865c778a8e38', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'e8be810ba8ede2e7bd7437d8fb22db30');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'e98f32a29563231992f41be2f0c391da', '7d83120a78a67a76fda51b94adec4801', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'e98f32a29563231992f41be2f0c391da');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'eab147314995f9ab174e60dec4a1d41d', 'a8cbf9ee2898e2b2979824d4d6098be2', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'eab147314995f9ab174e60dec4a1d41d');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'f1a48b7b48fe21ed211f407edcb77c49', 'e87f3a8553c2792982e1ea15d2a0a54c', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'f1a48b7b48fe21ed211f407edcb77c49');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'f328aa257174f27c3b0bbbca824cb18e', 'aab2dc93c2c0a57e6efa0235b5cdc696', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'f328aa257174f27c3b0bbbca824cb18e');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'f363952e495cf2d8eab0040fb303a515', '10cda0983f13804014ca186b9b612c0a', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'f363952e495cf2d8eab0040fb303a515');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'f406e174b4d537e8f324bbae1b12f85f', '4b115b2d2e7f4b3c027164c2542f764c', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'f406e174b4d537e8f324bbae1b12f85f');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'f410f2b7d705d829344567f4de9401f4', '890826492a92e7db24d8865c778a8e38', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'f410f2b7d705d829344567f4de9401f4');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'f459439fe4bd099b24ef8a2bb6a2f55e', '018c2c7192e42695c0306b5dc1756ec4', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'f459439fe4bd099b24ef8a2bb6a2f55e');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'f5f76b5ab254576d94b28f688538fa2a', 'd0a0296989d0b46f0a7399568df05064', '5a5508808dc641bd83f2ca62ac641ee9' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'f5f76b5ab254576d94b28f688538fa2a');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'f6955636e920ddcaf8127a7a4df2e3c8', 'cfd7708b7d3f2b38ef67fdf187dcfdf1', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'f6955636e920ddcaf8127a7a4df2e3c8');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'f80d19eb8974cea756a88de1bea009ef', 'ef25f0a4bd4b8221f81636047d1e2351', '22dc7fd7a67e11eb9db98257b6a5d0d7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'f80d19eb8974cea756a88de1bea009ef');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'f8a2ce9af52fd2d2734a82e0f16e68d1', '252c150ee420f8ef83d349f3d293722d', '94d25a5fa44b4e02b46f87fba31e1af1' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'f8a2ce9af52fd2d2734a82e0f16e68d1');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'f9b27290079c1f2081b515a8fce553f6', '2f29666b441d9f10b193ae5db1687345', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'f9b27290079c1f2081b515a8fce553f6');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'fa7fa4982c3f76f010fbaa8679f20706', '199e95b1f85d781cd4d04e2cfc08a2ea', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'fa7fa4982c3f76f010fbaa8679f20706');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'fa88f1954e83cb032de203e98de2f979', '5930d39ee1c3b19b0160723ff40c3779', 'c16c9944ca324aa693d3da6f7e7484f7' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'fa88f1954e83cb032de203e98de2f979');
INSERT INTO ram_policy_role (`uuid`,`policy_id`, `ram_role_id`) SELECT 'fe072b819151346de7d7fdaf98b1f462', '2baf4e8462592ad4201df36edd6da1a4', '23546c7ab11e11ecbde1083a88902c92' FROM DUAL WHERE NOT EXISTS(SELECT uuid FROM ram_policy_role WHERE uuid = 'fe072b819151346de7d7fdaf98b1f462');

