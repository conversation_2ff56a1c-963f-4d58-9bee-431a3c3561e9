SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for assignment
-- ----------------------------
CREATE TABLE IF NOT EXISTS `assignment` (
  `type` enum('UserProject','GroupProject','UserDomain','GroupDomain') NOT NULL,
  `actor_id` varchar(64) NOT NULL,
  `target_id` varchar(64) NOT NULL,
  `role_id` varchar(64) NOT NULL,
  `inherited` tinyint(1) NOT NULL,
  PRIMARY KEY (`type`,`actor_id`,`target_id`,`role_id`,`inherited`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Records of assignment
-- ----------------------------
INSERT INTO `assignment` VALUES ('UserProject', '0d2bbb018e8b44b985a169647379f413', 'edcc24059c8b46969a37260dc9ab2329', '8e5bd5e03d2243319887d650976200b9', '0');
INSERT INTO `assignment` VALUES ('UserProject', '28c87a1b4479ba14d34cdf0be96b6fc4', '94743c3f9dd440649476fcbfa7c46538', '8e5bd5e03d2243319887d650976200b9', '0');
INSERT INTO `assignment` VALUES ('UserProject', '431c439e563384116d79d413a87cfb12', '94743c3f9dd440649476fcbfa7c46538', '8e5bd5e03d2243319887d650976200b9', '0');
INSERT INTO `assignment` VALUES ('UserProject', '4855cf8d94a559e0cb2ec9995535b3d8', '94743c3f9dd440649476fcbfa7c46538', '8e5bd5e03d2243319887d650976200b9', '0');
INSERT INTO `assignment` VALUES ('UserProject', '75c39b6bca2e01e0b942931a7afca4dc', '94743c3f9dd440649476fcbfa7c46538', '8e5bd5e03d2243319887d650976200b9', '0');
INSERT INTO `assignment` VALUES ('UserProject', '8423f64d57e1283fd2d59320cd1ff981', '824e39c5e2c54b3e986fa6e0e39d948a', '8e5bd5e03d2243319887d650976200b9', '0');
INSERT INTO `assignment` VALUES ('UserProject', '8581f65885ccd39dcc381f3b5e0799b3', '94743c3f9dd440649476fcbfa7c46538', '8e5bd5e03d2243319887d650976200b9', '0');
INSERT INTO `assignment` VALUES ('UserProject', 'a617dde9ad1441618c9c7376ee2d44fc', '94743c3f9dd440649476fcbfa7c46538', '8e5bd5e03d2243319887d650976200b9', '0');
INSERT INTO `assignment` VALUES ('UserProject', 'b3f26a5e99a74a4894fa245ecd90ab38', '94743c3f9dd440649476fcbfa7c46538', '8e5bd5e03d2243319887d650976200b9', '0');
INSERT INTO `assignment` VALUES ('UserProject', 'b666c4ac44a84039bbd8f26262e85bc5', 'edcc24059c8b46969a37260dc9ab2329', '1c4e4b874b99470eaf114a5bba457c1b', '0');
INSERT INTO `assignment` VALUES ('UserProject', 'b666c4ac44a84039bbd8f26262e85bc5', 'edcc24059c8b46969a37260dc9ab2329', '8e5bd5e03d2243319887d650976200b9', '0');
INSERT INTO `assignment` VALUES ('UserProject', 'bbf7ad5382928cf99420e40a8ef9fef6', '94743c3f9dd440649476fcbfa7c46538', '8e5bd5e03d2243319887d650976200b9', '0');
INSERT INTO `assignment` VALUES ('UserProject', 'beacb53308824d179d7c579f29db61d3', '94743c3f9dd440649476fcbfa7c46538', '8e5bd5e03d2243319887d650976200b9', '0');
INSERT INTO `assignment` VALUES ('UserProject', 'c54eed6d25c14b73a3e551afe85a46de', '94743c3f9dd440649476fcbfa7c46538', '8e5bd5e03d2243319887d650976200b9', '0');
INSERT INTO `assignment` VALUES ('UserProject', 'c5f3d02010be0c843762bf05b3120b3a', '94743c3f9dd440649476fcbfa7c46538', '8e5bd5e03d2243319887d650976200b9', '0');
INSERT INTO `assignment` VALUES ('UserProject', 'e31964dde5b84d3883d61095d982cb70', '94743c3f9dd440649476fcbfa7c46538', '8e5bd5e03d2243319887d650976200b9', '0');
INSERT INTO `assignment` VALUES ('UserProject', 'eb1dcd73a651e4f3b82da8214a580fbc', '94743c3f9dd440649476fcbfa7c46538', '8e5bd5e03d2243319887d650976200b9', '0');
INSERT INTO `assignment` VALUES ('UserDomain', 'b666c4ac44a84039bbd8f26262e85bc5', 'default', '8e5bd5e03d2243319887d650976200b9', '0');
INSERT INTO `assignment` VALUES ('UserDomain', 'c042989bab15f86aeda223cfe7398941', '82ff77249d582f6cd3c5c0d2773351e5', '8e5bd5e03d2243319887d650976200b9', '0');

-- ----------------------------
-- Table structure for credential
-- ----------------------------
CREATE TABLE IF NOT EXISTS `credential` (
  `id` varchar(64) NOT NULL,
  `user_id` varchar(64) NOT NULL,
  `project_id` varchar(64) DEFAULT NULL,
  `type` varchar(255) NOT NULL,
  `extra` text DEFAULT NULL,
  `key_hash` varchar(64) NOT NULL,
  `encrypted_blob` text NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Records of credential
-- ----------------------------

-- ----------------------------
-- Table structure for department
-- ----------------------------
CREATE TABLE IF NOT EXISTS `department` (
  `id` varchar(64) NOT NULL COMMENT '主键ID',
  `name` varchar(255) NOT NULL COMMENT '名称',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
  `parent_id` varchar(64) DEFAULT NULL COMMENT '父级部门id',
  `domain_id` varchar(64) NOT NULL COMMENT 'project中is_domain=1的id',
  `level` int(11) NOT NULL DEFAULT 0 COMMENT '层级',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='组织部门表';

-- ----------------------------
-- Records of department
-- ----------------------------

-- ----------------------------
-- Table structure for endpoint
-- ----------------------------
CREATE TABLE IF NOT EXISTS `endpoint` (
  `id` varchar(64) NOT NULL,
  `legacy_endpoint_id` varchar(64) DEFAULT NULL,
  `interface` varchar(8) NOT NULL,
  `service_id` varchar(64) NOT NULL,
  `url` text NOT NULL,
  `extra` text DEFAULT NULL,
  `enabled` tinyint(1) NOT NULL DEFAULT 1,
  `region_id` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Records of endpoint
-- ----------------------------
INSERT INTO `endpoint` VALUES ('01e1e46634ea44279d3993bd4f453efb', null, 'admin', '7caace0dd34142b7a210b4bf2e7fa227', 'http://cinder-api.openstack.svc.cluster.local:8776/v1/%(tenant_id)s', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('112c3a18eb774dfeb05bef74729b924a', null, 'public', '5fadfbcdf60648a18704dc80267521d8', 'http://octavia.openstack.svc.cluster.local/', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('11a63241a473476f8e05ba2e39a67613', null, 'public', 'a00193a6bd2440259dc6d069b222b9be', 'http://glance.openstack.svc.cluster.local/', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('170a55b83c7747eb86ce7b60ce30e3f0', null, 'public', '4b1493829dc5471f85841e7b3061e2db', 'http://senlin.openstack.svc.cluster.local/', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('222aa1151ee04af282d99676b8aac157', null, 'admin', '9c9d9e851f7f479c996a6d6a55f1c8bf', 'http://cinder-api.openstack.svc.cluster.local:8776/v3/%(tenant_id)s', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('22bca9dc53c24d1eb0bc074d34b5f153', null, 'internal', '4b1493829dc5471f85841e7b3061e2db', 'http://senlin-api.openstack.svc.cluster.local:8778/', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('2517428326fc48bd903c612b08b9cdca', null, 'admin', '5fadfbcdf60648a18704dc80267521d8', 'http://octavia-api.openstack.svc.cluster.local:9876/', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('29a2865d572b4f4eb9d04b11eeb47d70', null, 'public', '66ac98a42e1043c498a16a070e1435ba', 'http://neutron.openstack.svc.cluster.local/', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('29a797cf60504aeba0bcd38abe5e3505', null, 'internal', 'ff3e8340eeb44a22977c83668dd9b45e', 'http://placement-api.openstack.svc.cluster.local:8778/', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('31c9a1df8e4b4412b8e1e424c72da5f1', null, 'public', '1814df1a91df41b19a19ad7da7f92a26', 'http://ironic-inspect.openstack.svc.cluster.local', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('38384f6a0c624ffda46dea6cb7b37647', null, 'public', '3c97fc690d494f1091b2c0c3f0109017', 'http://cloudformation.openstack.svc.cluster.local/v1', '{}', '1', null);
INSERT INTO `endpoint` VALUES ('3e08a29db7d948299aa0c0e956a10231', null, 'internal', '66ac98a42e1043c498a16a070e1435ba', 'http://neutron-server.openstack.svc.cluster.local:9696/', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('3eb7e31e7c154375bb113ae31a378c60', null, 'internal', 'd8e68ac1ca434a1f98b91df59565fa43', 'http://cinder-api.openstack.svc.cluster.local:8776/v2/%(tenant_id)s', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('4129e46894594e509f208bd52a8c47d6', null, 'admin', 'd8e68ac1ca434a1f98b91df59565fa43', 'http://cinder-api.openstack.svc.cluster.local:8776/v2/%(tenant_id)s', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('494e5298006f493499fce5b247c1de43', null, 'public', '85e081b2e9f343bb9d914c58173db71a', 'http://watcher.openstack.svc.cluster.local/', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('550fb74c0e7544ea90c59f2d4093cd46', null, 'internal', '7caace0dd34142b7a210b4bf2e7fa227', 'http://cinder-api.openstack.svc.cluster.local:8776/v1/%(tenant_id)s', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('56d1a7436f134797b53f39a913d4da79', null, 'internal', '5fadfbcdf60648a18704dc80267521d8', 'http://octavia-api.openstack.svc.cluster.local:9876/', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('61cfc1376e8c45938be41e211e9b213a', null, 'public', '1e481cd120fd4f8ea2ebe78fc644247c', 'http://sugoncloud-iam-api-http.micro-service.svc.cluster.local:8080/v3', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('63a5dd5d5cc34651a370eb0eee747399', null, 'internal', '8e6ed97ab2804de88270b48c7949a045', 'http://heat-api.openstack.svc.cluster.local:8004/v1/%(project_id)s', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('6fb14ec644eb4dd486ad97561e1f0aae', null, 'internal', '1814df1a91df41b19a19ad7da7f92a26', 'http://ironic-inspect-api.openstack.svc.cluster.local:5050', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('78d3bd36c4f4431a8da1e09f73fcfdef', null, 'admin', 'b1736bda62424b1d915947c7cdd5544d', 'http://ironic-api.openstack.svc.cluster.local:6385/', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('797a5272d05c41bd86ec9a503e09988f', null, 'admin', '3c97fc690d494f1091b2c0c3f0109017', 'http://heat-cfn.openstack.svc.cluster.local:8000/v1', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('80da29dd32914e4684f80aa6e401ee91', null, 'admin', '2916cfccf2dc434d955cda3f2d39107c', 'http://barbican-api.openstack.svc.cluster.local:9311/', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('840e4c8da073470b97a09dfdadd37d69', null, 'public', '21ca49b7864b4e758fd1228f5c4693ef', 'http://nova.openstack.svc.cluster.local/v2.1/%(tenant_id)s', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('850dccca30d3477a8a830063b82e390d', null, 'internal', '3c97fc690d494f1091b2c0c3f0109017', 'http://heat-cfn.openstack.svc.cluster.local:8000/v1', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('85e054dd2e8a4a0bac7856fb6a12178c', null, 'admin', 'ff3e8340eeb44a22977c83668dd9b45e', 'http://placement-api.openstack.svc.cluster.local:8778/', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('881d46938cf549b69044e2d0acfdc26d', null, 'internal', '9c9d9e851f7f479c996a6d6a55f1c8bf', 'http://cinder-api.openstack.svc.cluster.local:8776/v3/%(tenant_id)s', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('883c1fb93c924217bc25cc9051e34299', null, 'internal', '85e081b2e9f343bb9d914c58173db71a', 'http://watcher-api.openstack.svc.cluster.local:9322', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('980b2d3f8a264652a5b44f55b5bf7741', null, 'admin', '8e6ed97ab2804de88270b48c7949a045', 'http://heat-api.openstack.svc.cluster.local:8004/v1/%(project_id)s', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('9ddaf56ce99842be8a7d93741fa87d12', null, 'admin', '66ac98a42e1043c498a16a070e1435ba', 'http://neutron-server.openstack.svc.cluster.local:9696/', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('a291737250f448efbda1b873aa0b6478', null, 'admin', '85e081b2e9f343bb9d914c58173db71a', 'http://watcher-api.openstack.svc.cluster.local:9322', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('a55dc50fbeb64f88be470e2ad95d264f', null, 'admin', '1e481cd120fd4f8ea2ebe78fc644247c', 'http://sugoncloud-iam-api-http.micro-service.svc.cluster.local:8080/v3', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('aa2d57ed0aef4ac6a9cda184376fd463', null, 'public', '2916cfccf2dc434d955cda3f2d39107c', 'http://barbican.openstack.svc.cluster.local/', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('abc6e169464049799e7ab0539fa838d7', null, 'internal', 'b1736bda62424b1d915947c7cdd5544d', 'http://ironic-api.openstack.svc.cluster.local:6385/', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('ad504dca79934bcfadac7965dea594e8', null, 'admin', '21ca49b7864b4e758fd1228f5c4693ef', 'http://nova-api.openstack.svc.cluster.local:8774/v2.1/%(tenant_id)s', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('b4464ad6395e4e0c8a4e583c8cc6392c', null, 'public', 'd8e68ac1ca434a1f98b91df59565fa43', 'http://cinder.openstack.svc.cluster.local/v2/%(tenant_id)s', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('c39cc3f7f70d4ab2b2eee3926b794cbd', null, 'public', 'b1736bda62424b1d915947c7cdd5544d', 'http://ironic.openstack.svc.cluster.local/', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('c3a37769029b4a3083c3d3d02a074e80', null, 'admin', '4b1493829dc5471f85841e7b3061e2db', 'http://senlin-api.openstack.svc.cluster.local:8778/', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('d10e4150f34b4578b28a5920d1256b52', null, 'public', '7caace0dd34142b7a210b4bf2e7fa227', 'http://cinder.openstack.svc.cluster.local/v1/%(tenant_id)s', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('d6f9dcf812bb49c58ac80ae1b679ccd0', null, 'internal', '1e481cd120fd4f8ea2ebe78fc644247c', 'http://sugoncloud-iam-api-http.micro-service.svc.cluster.local:8080/v3', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('ddd148e6d057480fbae6b946afe3d941', null, 'public', '9c9d9e851f7f479c996a6d6a55f1c8bf', 'http://cinder.openstack.svc.cluster.local/v3/%(tenant_id)s', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('e080702b78f849818c3ce96347e3821f', null, 'admin', '1814df1a91df41b19a19ad7da7f92a26', 'http://ironic-inspect-api.openstack.svc.cluster.local:5050', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('e61443b82b594b988a236229750bfb3c', null, 'internal', 'a00193a6bd2440259dc6d069b222b9be', 'http://glance-api.openstack.svc.cluster.local:9292/', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('e6cd75e9b8564a6d9a7f368aa1805306', null, 'internal', '21ca49b7864b4e758fd1228f5c4693ef', 'http://nova-api.openstack.svc.cluster.local:8774/v2.1/%(tenant_id)s', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('e88522ed40f5403eaf7b86b2b7262d74', null, 'public', '8e6ed97ab2804de88270b48c7949a045', 'http://heat.openstack.svc.cluster.local/v1/%(project_id)s', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('ec1543abe05040dbbce851d2dd9e3912', null, 'admin', 'a00193a6bd2440259dc6d069b222b9be', 'http://glance-api.openstack.svc.cluster.local:9292/', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('ef7b71398ba74efabc7dd9d91a1628f5', null, 'public', 'ff3e8340eeb44a22977c83668dd9b45e', 'http://placement.openstack.svc.cluster.local/', '{}', '1', 'RegionOne');
INSERT INTO `endpoint` VALUES ('ff6b4428f296453f9c8412b17ddf97fa', null, 'internal', '2916cfccf2dc434d955cda3f2d39107c', 'http://barbican-api.openstack.svc.cluster.local:9311/', '{}', '1', 'RegionOne');

-- ----------------------------
-- Table structure for globalsettings
-- ----------------------------
CREATE TABLE IF NOT EXISTS `globalsettings` (
  `uuid` varchar(64) NOT NULL COMMENT '唯一标识',
  `policy_name` varchar(255) NOT NULL COMMENT '策略名称',
  `policy_document` mediumtext NOT NULL COMMENT '策略内容',
  `policy_type` varchar(255) NOT NULL COMMENT '策略类型',
  `retained_field` varchar(255) DEFAULT NULL COMMENT '保留字段',
  `policy_display_name` varchar(255) DEFAULT NULL COMMENT '显示名称',
  PRIMARY KEY (`uuid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='全局设置';

-- ----------------------------
-- Records of globalsettings
-- ----------------------------
INSERT INTO `globalsettings` VALUES ('091ec8d8236fdba65c5040e03a266bfa', 'password', '[\\s\\S]{8,}', 'user', '密码不能少于8位', '密码策略');
INSERT INTO `globalsettings` VALUES ('0936f47f4360377704cb7fa70412933b', 'status', 'false', 'user', '用户初始化状态 值为true激活', '用户初始化状态');
INSERT INTO `globalsettings` VALUES ('100', 'switch', 'false', 'ukey', '双因子认证', '双因子认证');
INSERT INTO `globalsettings` VALUES ('11', 'user_expired', 'true', 'user', '是否开启用户过期验证', '用户过期验证');
INSERT INTO `globalsettings` VALUES ('1124431', 'error_time_interval', '10', 'password', '多少分钟内连续输密码错误次数达到设定值，触发锁定', '用户登录密码错误配置');
INSERT INTO `globalsettings` VALUES ('1231235', 'change_password_interval', '90', 'password', '强制修改密码的时间间隔（天），最长90', '强制修改密码时长');
INSERT INTO `globalsettings` VALUES ('123456', 'ipv6', '2001:2001:2001', 'v6', 'IPv6内置前段', 'IPv6内置前段');
INSERT INTO `globalsettings` VALUES ('22', 'long_time_no_login', '10', 'user', '用户多少天未登录验证', '自动冻结时长');
INSERT INTO `globalsettings` VALUES ('33', 'one_place_login', 'false', 'user', '是否只允许用户同时只在一个地方登录', '用户多会话连接');
INSERT INTO `globalsettings` VALUES ('34f8b9f8a4b2d1a3cde47a60548c7840', 'log_reserve_time', '100', 'log', '日志保留时间（天）', '日志保留时间');
INSERT INTO `globalsettings` VALUES ('3c611f1cc8174f6682f27634954930aa', 'sendTo', 'null', 'email', '接收者', '系统收件人邮箱');
INSERT INTO `globalsettings` VALUES ('44', 'token_expired', '3600', 'token', 'token有效时间（秒）', '会话超时时长');
INSERT INTO `globalsettings` VALUES ('4997ef5e18948eb0271ec1186d9922ac', 'web_lock_time', '180', 'web', 'web界面锁屏时间(分钟)', '锁屏超时时长');
INSERT INTO `globalsettings` VALUES ('56', 'error_times', '10', 'password', '密码输入错误次数', '用户登录密码错误配置');
INSERT INTO `globalsettings` VALUES ('57', 'lock_minute', '1', 'password', '账号锁定的时间（分钟）', '用户登录密码错误配置');
INSERT INTO `globalsettings` VALUES ('60edfcb0227e16219abf9f94f9791ac7', 'password', 'null', 'email', '邮箱授权码', '系统发件人邮箱服务器');
INSERT INTO `globalsettings` VALUES ('68ca5f77362537431fff4c7ac1733c5d', 'update_password_first', 'false', 'user', '注册第一次登录是否需要修改密码', '密码安全性设置');
INSERT INTO `globalsettings` VALUES ('76157bfe01814d49964b80f48aa6c2e5', 'port', '25', 'email', '端口', '系统发件人邮箱服务器');
INSERT INTO `globalsettings` VALUES ('8c2cea755571035578a91463171a9149', 'send', 'null', 'email', '发送者', '系统发件人邮箱服务器');
INSERT INTO `globalsettings` VALUES ('9f500092de7a2a7552bf150ec0d220fc', 'neutron_min_hoaggest', 'nova', 'neutron', '网络路由所用可用域', '网络路由所用可用域');
INSERT INTO `globalsettings` VALUES ('b6f3b9e1be46c4f68aa8c544be6b83cb', 'status', 'false', 'email', '是否启用email 值为true启用', '是否开启邮件');
INSERT INTO `globalsettings` VALUES ('bdffc9e2d6c8445caab5-d153bea49b78', 'alert', '界面告警', 'email', '系统告警通知方式', '系统告警通知方式');
INSERT INTO `globalsettings` VALUES ('db0fcd4c937436f0b45beeb0b239c53e', 'flavor_min_max', '2,256,2048,131072,1,2048,0,2048,0,102400', 'ecs', '规格最小值与最大值设置', '规格设置');
INSERT INTO `globalsettings` VALUES ('f6b0c917ce3aca4d7902588168639cba', 'host', 'smtp.qq.com', 'email', '邮箱服务器地址', '系统发件人邮箱服务器');

-- ----------------------------
-- Table structure for iam_role
-- ----------------------------
CREATE TABLE IF NOT EXISTS `iam_role` (
  `id` varchar(64) NOT NULL,
  `name` varchar(64) NOT NULL,
  `dept_id` varchar(64) DEFAULT NULL,
  `description` varchar(64) DEFAULT NULL,
  `type` varchar(64) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Records of iam_role
-- ----------------------------
INSERT INTO `iam_role` VALUES ('22dc7fd7a67e11eb9db98257b6a5d0d7', 'department_master内置角色', null, 'department_master(部门管理员)内置角色', 'department_master', '2021-04-28 01:26:21', '2021-04-28 01:26:21');
INSERT INTO `iam_role` VALUES ('5a5508808dc641bd83f2ca62ac641ee9', 'security内置角色', null, 'security(安全)内置角色', 'security', '2021-04-28 01:26:21', '2021-04-28 01:26:21');
INSERT INTO `iam_role` VALUES ('6d4372436aa34fd5a015550c22ba605c', 'audit内置角色', null, 'audit(日志)内置角色', 'audit', '2021-04-28 01:26:21', '2021-04-28 01:26:21');
INSERT INTO `iam_role` VALUES ('94d25a5fa44b4e02b46f87fba31e1af1', 'master内置角色', null, 'master(公司管理员)内置角色', 'master', '2021-04-28 01:26:21', '2021-04-28 01:26:21');
INSERT INTO `iam_role` VALUES ('c16c9944ca324aa693d3da6f7e7484f7', 'admin内置角色', null, 'admin内置角色', 'admin', '2021-04-28 01:26:21', '2021-04-28 01:26:21');

-- ----------------------------
-- Table structure for implied_role
-- ----------------------------
CREATE TABLE IF NOT EXISTS `implied_role` (
  `prior_role_id` varchar(64) NOT NULL,
  `implied_role_id` varchar(64) NOT NULL,
  PRIMARY KEY (`prior_role_id`,`implied_role_id`) USING BTREE,
  KEY `implied_role_implied_role_id_fkey` (`implied_role_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Records of implied_role
-- ----------------------------
INSERT INTO `implied_role` VALUES ('638ea8b19d3e481cb0564febddc4d210', '4a1232e054664d10b29761cabc7db297');
INSERT INTO `implied_role` VALUES ('e82e891ba3674a4e93c475965ce69922', '638ea8b19d3e481cb0564febddc4d210');

-- ----------------------------
-- Table structure for micro_service
-- ----------------------------
CREATE TABLE IF NOT EXISTS `micro_service` (
  `id` varchar(64) NOT NULL COMMENT '主键ID',
  `name` varchar(255) NOT NULL COMMENT '微服务名称',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `service_id` varchar(255) NOT NULL COMMENT '微服务id',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
  `link` varchar(255) DEFAULT NULL COMMENT '连接地址',
  `category_id` varchar(64) DEFAULT NULL COMMENT '分类',
  `nav_hidden` tinyint(1) NOT NULL DEFAULT 0 COMMENT '导航栏是否隐藏',
  `latest_request_time` datetime DEFAULT NULL COMMENT '最近一次的访问',
  `collect_time` datetime DEFAULT NULL COMMENT '收藏时间：为空表示未被收藏',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Records of micro_service
-- ----------------------------
INSERT INTO `micro_service` VALUES ('161f2333cb454c32a745778df2566bcb', '弹性云服务器 ECS', '弹性云服务器 ECS', 'sugoncloud-ecs-api', '2021-04-15 15:41:16', '2021-05-24 14:42:47', 'http://**********:30506', '4afee060a36111eb8230f20ed01ddf38', '0', null, '2021-05-24 14:42:47');
INSERT INTO `micro_service` VALUES ('21365f9f42b2494296c5fcc74b5ae6c9', '专有网络 VPC', '专有网络 VPC', 'sugoncloud-vpc-api', '2021-04-15 15:45:00', '2021-05-24 14:42:36', 'http://**********:30509', 'ea209553-6177-441b-981a-9c5e40c62ea8', '0', null, null);
INSERT INTO `micro_service` VALUES ('4e9dfd85fdd84b10a8e269dd09e4b535', '裸金属 BMS', '裸金属 BMS', 'sugoncloud-bms-api', '2021-04-15 14:25:51', '2021-05-17 14:33:10', 'http://**********:30520', '4afee060a36111eb8230f20ed01ddf38', '0', null, '2021-05-17 14:33:25');
INSERT INTO `micro_service` VALUES ('76b08502a59d11eb8230f20ed01ddf38', '云容器引擎 CCE', '云容器引擎 CCE', 'sugoncloud-cce-api', '2021-04-25 16:09:18', '2021-05-17 14:40:20', 'http://**********:30517', '44c1f40ba59d11eb8230f20ed01ddf38', '0', null, '2021-05-17 14:40:20');
INSERT INTO `micro_service` VALUES ('ac988dbe28ec428893304640df20087f', '统一身份认证IAM', '统一身份认证IAM', 'sugoncloud-iam-api', '2021-04-15 14:11:27', '2021-05-17 19:07:45', 'http://**********:30513', '4afee060a36111eb8230f20ed01ddf38', '0', null, null);
INSERT INTO `micro_service` VALUES ('e455f304d2e34f2eb448d1d3182c3c67', '云硬盘 EVS', '云硬盘 EVS', 'sugoncloud-evs-api', '2021-04-15 15:43:44', '2021-05-24 15:59:06', 'http://**********:30508', '13a38073a36111eb8230f20ed01ddf38', '0', null, '2021-05-24 15:59:06');
INSERT INTO `micro_service` VALUES ('f72db00f-4914-4bdf-9161-7cc5977edf7d', 'E-MapReduce', 'E-MapReduce', 'sugoncloud-emr-api', '2021-05-03 10:24:56', '2021-05-17 14:33:09', 'http://**********:30522', '54df68e1a36111eb8230f20ed01ddf38', '0', null, '2021-05-17 14:33:25');

-- ----------------------------
-- Table structure for micro_service_category
-- ----------------------------
CREATE TABLE IF NOT EXISTS `micro_service_category` (
  `id` varchar(64) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `modify_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Records of micro_service_category
-- ----------------------------
INSERT INTO `micro_service_category` VALUES ('13a38073a36111eb8230f20ed01ddf38', '存储', '存储', '2021-04-22 11:51:30', '2021-04-22 11:51:30');
INSERT INTO `micro_service_category` VALUES ('44c1f40ba59d11eb8230f20ed01ddf38', '容器服务', '容器服务', '2021-04-25 16:07:01', '2021-04-25 16:07:05');
INSERT INTO `micro_service_category` VALUES ('4afee060a36111eb8230f20ed01ddf38', '计算', '计算', '2021-04-22 11:51:30', '2021-04-22 11:51:30');
INSERT INTO `micro_service_category` VALUES ('54df68e1a36111eb8230f20ed01ddf38', '大数据', '大数据', '2021-04-22 11:51:30', '2021-04-22 11:51:30');
INSERT INTO `micro_service_category` VALUES ('ea209553-6177-441b-981a-9c5e40c62ea8', '网络', '网络', '2021-05-08 10:33:34', '2021-05-08 10:33:37');

-- ----------------------------
-- Table structure for migrate_version
-- ----------------------------
CREATE TABLE IF NOT EXISTS `migrate_version` (
  `repository_id` varchar(250) NOT NULL,
  `repository_path` text DEFAULT NULL,
  `version` int(11) DEFAULT NULL,
  PRIMARY KEY (`repository_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Records of migrate_version
-- ----------------------------
INSERT INTO `migrate_version` VALUES ('keystone', '/usr/lib/python2.7/site-packages/keystone/common/sql/migrate_repo', '109');
INSERT INTO `migrate_version` VALUES ('keystone_contract', '/usr/lib/python2.7/site-packages/keystone/common/sql/contract_repo', '52');
INSERT INTO `migrate_version` VALUES ('keystone_data_migrate', '/usr/lib/python2.7/site-packages/keystone/common/sql/data_migration_repo', '52');
INSERT INTO `migrate_version` VALUES ('keystone_expand', '/usr/lib/python2.7/site-packages/keystone/common/sql/expand_repo', '52');

-- ----------------------------
-- Table structure for project
-- ----------------------------
CREATE TABLE IF NOT EXISTS `project` (
  `id` varchar(64) NOT NULL,
  `name` varchar(64) NOT NULL,
  `extra` text DEFAULT NULL,
  `description` text DEFAULT NULL,
  `enabled` tinyint(1) DEFAULT NULL,
  `domain_id` varchar(64) NOT NULL,
  `parent_id` varchar(64) DEFAULT NULL,
  `is_domain` tinyint(1) NOT NULL DEFAULT 0,
  `dept_id` varchar(64) DEFAULT NULL,
  `alias` varchar(64) DEFAULT NULL COMMENT '别名',
  `create_time` datetime DEFAULT NULL,
  `modify_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ixu_project_name_domain_id` (`domain_id`,`name`) USING BTREE,
  KEY `project_parent_id_fkey` (`parent_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Records of project
-- ----------------------------
INSERT INTO `project` VALUES ('824e39c5e2c54b3e986fa6e0e39d948a', 'service', '{}', 'Domain for RegionOne/service', '1', 'default', 'default', '1', null, null, null, null);
INSERT INTO `project` VALUES ('82ff77249d582f6cd3c5c0d2773351e5', 'heat', null, null, '1', '<<keystone.domain.root>>', null, '1', null, null, '2021-05-06 15:03:19', '2021-05-06 15:03:19');
INSERT INTO `project` VALUES ('94743c3f9dd440649476fcbfa7c46538', 'service', '{}', 'Service Project for RegionOne/service', '1', '824e39c5e2c54b3e986fa6e0e39d948a', '824e39c5e2c54b3e986fa6e0e39d948a', '0', null, null, null, null);
INSERT INTO `project` VALUES ('<<keystone.domain.root>>', '<<keystone.domain.root>>', '{}', '', '0', '<<keystone.domain.root>>', null, '1', null, null, null, null);
INSERT INTO `project` VALUES ('default', 'Default', '{}', 'The default domain', '1', '<<keystone.domain.root>>', null, '1', null, null, null, null);
INSERT INTO `project` VALUES ('ec40423849384bfb92a98ec9aff38909', 'internal_cinder', '{}', '', '1', 'default', 'default', '0', null, null, null, null);
INSERT INTO `project` VALUES ('edcc24059c8b46969a37260dc9ab2329', 'admin', '{}', 'Bootstrap project for initializing the cloud.', '1', 'default', 'default', '0', null, null, '2021-04-15 15:54:58', '2024-11-15 15:55:01');

-- ----------------------------
-- Table structure for quota_department_value
-- ----------------------------
CREATE TABLE IF NOT EXISTS `quota_department_value` (
  `metric_name` varchar(64) NOT NULL,
  `department_id` varchar(64) NOT NULL,
  `total_value` bigint(20) DEFAULT NULL,
  `used_value` bigint(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Records of quota_department_value
-- ----------------------------

-- ----------------------------
-- Table structure for quota_metric
-- ----------------------------
CREATE TABLE IF NOT EXISTS `quota_metric` (
  `name` varchar(64) NOT NULL,
  `type_name` varchar(64) NOT NULL,
  `total_name` varchar(64) NOT NULL,
  `used_name` varchar(64) NOT NULL,
  PRIMARY KEY (`name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Records of quota_metric
-- ----------------------------
INSERT INTO `quota_metric` VALUES ('cce_cpu', 'cce', 'cpu总量（个）', 'cpu使用量（个）');
INSERT INTO `quota_metric` VALUES ('cce_ram', 'cce', '内存总量（M）', '内存使用量（M）');
INSERT INTO `quota_metric` VALUES ('ecs_cpu', 'ecs', 'cpu总量（个）', 'cpu使用量（个）');
INSERT INTO `quota_metric` VALUES ('ecs_ram', 'ecs', '内存总量（M）', '内存使用量（M）');
INSERT INTO `quota_metric` VALUES ('emr_cpu', 'emr', 'cpu总量（个）', 'cpu使用量（个）');
INSERT INTO `quota_metric` VALUES ('emr_ram', 'emr', '内存总量（M）', '内存使用量（M）');
INSERT INTO `quota_metric` VALUES ('evs_capacity', 'evs', '容量总量（G）', '容量使用量（G）');
INSERT INTO `quota_metric` VALUES ('evs_snapshot', 'evs', '快照总量（G）', '快照使用量（G）');

-- ----------------------------
-- Table structure for quota_project_value
-- ----------------------------
CREATE TABLE IF NOT EXISTS `quota_project_value` (
  `metric_name` varchar(64) NOT NULL,
  `project_id` varchar(64) NOT NULL,
  `total_value` bigint(20) DEFAULT NULL,
  `used_value` bigint(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Records of quota_project_value
-- ----------------------------

-- ----------------------------
-- Table structure for quota_type
-- ----------------------------
CREATE TABLE IF NOT EXISTS `quota_type` (
  `name` varchar(64) NOT NULL,
  `description` varchar(255) NOT NULL,
  PRIMARY KEY (`name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Records of quota_type
-- ----------------------------
INSERT INTO `quota_type` VALUES ('cce', '云容器引擎CCE配额');
INSERT INTO `quota_type` VALUES ('ecs', '云服务器ECS配额');
INSERT INTO `quota_type` VALUES ('emr', '大数据治理EMR配额');
INSERT INTO `quota_type` VALUES ('evs', '云硬盘EVS配额');

-- ----------------------------
-- Table structure for ram_policy
-- ----------------------------
CREATE TABLE IF NOT EXISTS `ram_policy` (
  `uuid` varchar(64) NOT NULL DEFAULT '1' COMMENT '唯一标识',
  `policy_name` varchar(255) NOT NULL COMMENT '策略名称',
  `policy_document` mediumtext NOT NULL COMMENT '策略内容',
  `action` varchar(16) DEFAULT NULL COMMENT '方法',
  `class_name` varchar(255) DEFAULT NULL COMMENT '类名',
  `method_name` varchar(255) DEFAULT NULL COMMENT '方法名',
  `policy_type` varchar(255) DEFAULT NULL COMMENT '策略类型: 0: 菜单类型 | 1：非菜单类型',
  `comments` varchar(255) DEFAULT NULL COMMENT '描述',
  `owner_id` varchar(64) DEFAULT NULL COMMENT '主账号ID',
  `effect` varchar(16) NOT NULL COMMENT '用户系统：0，云平台：1， 大数据：2',
  `parent_id` varchar(64) NOT NULL COMMENT '父ID',
  `method_url` varchar(255) DEFAULT NULL COMMENT '方法请求路径',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_at` timestamp NULL DEFAULT current_timestamp() COMMENT '创建时间',
  `index` varchar(64) DEFAULT NULL COMMENT '前端菜单路由',
  `icon` varchar(255) DEFAULT NULL COMMENT '前端菜单图标',
  `link_id` varchar(64) DEFAULT NULL COMMENT '前端权限联动',
  `resource_link_id` varchar(64) DEFAULT NULL COMMENT '资源策略关联到策略的id',
  `resource_policy_flag` varchar(4) DEFAULT NULL COMMENT '是否为资源，1为资源，0非资源，null为用户不能查看',
  `can_delete` varchar(4) DEFAULT '1' COMMENT '能否删除（0：不能删除，1：能删除）',
  PRIMARY KEY (`uuid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='RAM策略';

-- ----------------------------
-- Records of ram_policy
-- ----------------------------
INSERT INTO `ram_policy` VALUES ('018c2c7192e42695c0306b5dc1756ec4', '修改用户状态', '组织管理-用户管理-操作-修改用户状态', 'null', '', '', '1', '', null, '1', '7d83120a78a67a76fda51b94adec4801', '', '7', '', '2021-04-23 00:00:00', '', '', '', null, '', '1');
INSERT INTO `ram_policy` VALUES ('0afb7e979297c8046dfd7e5839aad8ea', '修改', '策略管理-修改', 'PUT', 'com.sugon.cloud.policy.api.controller.CommonPolicyController', 'editPolicy', '1', '', null, '1', 'bda8f353d61b50ae676b02fb62741ca1', '/api/common/ram/policy/{id}', '2', '', '2021-04-23 00:00:00', '', '', '', null, '', '1');
INSERT INTO `ram_policy` VALUES ('0bac8a03f16bcce0af6d58c161459e6c', '修改配额', '组织管理-项目管理-操作-修改配额', 'PUT', 'QuotaController', 'updateProjectQuota', '2', '', null, '1', '2ad1a10cdd55777e0acc760e26fc4259', '/api/quotas/project/{project_id}', '2', '', '2021-05-06 00:00:00', '/departmentManage/modifyQuota', '', '', null, '2', '1');
INSERT INTO `ram_policy` VALUES ('1087f2f01567c61ac1baa20a10510c34', '新建', '组织管理-角色管理-新建', 'POST', 'com.sugon.cloud.iam.api.controller.IamRoleController', 'createRole', '1', '', null, '1', 'fe3a21e5bffa710d207343632197ae40', '/api/roles', '0', '', '2021-04-23 00:00:00', '', '', '', null, '', '1');
INSERT INTO `ram_policy` VALUES ('170e6e421f685fd9ef1e517f363972fa', '新建', '组织管理-项目管理-新建', 'POST', 'com.sugon.cloud.iam.api.controller.ProjectController', 'create', '1', '', null, '1', '199e95b1f85d781cd4d04e2cfc08a2ea', '/api/projects', '0', '', '2021-04-23 00:00:00', '', '', '', null, '', '1');
INSERT INTO `ram_policy` VALUES ('199e95b1f85d781cd4d04e2cfc08a2ea', '项目管理', '组织管理-项目管理-列表', 'GET', 'com.sugon.cloud.iam.api.controller.DepartmentController', 'pageList', '1', '', null, '1', 'f79da1ccd2add999e2491cce36c89290', '/api/departments/{id}/projects', '1', '', '2021-04-23 00:00:00', '', '', '', null, '2', '1');
INSERT INTO `ram_policy` VALUES ('1b48776def1fb454bfc8523d4f983f4f', '角色管理', '组织管理-用户管理-操作-角色管理', 'DELETE', 'com.sugon.cloud.iam.api.controller.UserController', 'batchAssignmentRoles', '1', '', null, '1', '7d83120a78a67a76fda51b94adec4801', '/api/users/{user_id}/bind-roles', '2', '', '2021-04-23 00:00:00', '', '', '', null, '', '1');
INSERT INTO `ram_policy` VALUES ('252c150ee420f8ef83d349f3d293722d', 'IP控制', '组织管理-用户管理-操作-IP控制', 'null', '', '', '1', '', null, '1', '7d83120a78a67a76fda51b94adec4801', '', '6', '', '2021-04-23 00:00:00', '', '', '', null, '', '1');
INSERT INTO `ram_policy` VALUES ('2ad1a10cdd55777e0acc760e26fc4259', '操作', '组织管理-项目管理-操作', 'null', '', '', '1', '', null, '1', '199e95b1f85d781cd4d04e2cfc08a2ea', '', '2', '', '2021-04-23 00:00:00', '', '', '', null, '2', '1');
INSERT INTO `ram_policy` VALUES ('2f563fdc6b3c716115a57fda77b06789', '修改角色', '组织管理-角色管理-操作-修改角色', 'PUT', 'com.sugon.cloud.iam.api.controller.IamRoleController', 'update', '1', '', null, '1', 'd92db5112c6c67cf11a8e90d9ca79335', '/api/roles', '1', '', '2021-04-23 00:00:00', '', '', '', null, '', '1');
INSERT INTO `ram_policy` VALUES ('376b911d000734db8418a6f00e712d1a', '全局配置', '全局配置', 'null', '', '', null, '', null, '1', 'de5b8265a3f911eb8230f20ed01ddf38', '', '0', '', '2021-04-27 00:00:00', '/global', '', '', null, '0', '1');
INSERT INTO `ram_policy` VALUES ('3928a382a51cbe0b9dd96754709e3638', '删除', '策略管理-删除', 'DELETE', 'com.sugon.cloud.policy.api.controller.CommonPolicyController', 'deletePolicy', '1', '', null, '1', 'bda8f353d61b50ae676b02fb62741ca1', '/api/common/ram/policy/{id}', '3', '', '2021-04-23 00:00:00', '', '', '', null, '', '1');
INSERT INTO `ram_policy` VALUES ('4b115b2d2e7f4b3c027164c2542f764c', '修改用户', '组织管理-用户管理-操作-修改用户', 'PUT', 'com.sugon.cloud.iam.api.controller.UserController', 'update', '1', '', null, '1', '7d83120a78a67a76fda51b94adec4801', '/api/users', '0', '', '2021-04-23 00:00:00', '', '', '', null, '', '1');
INSERT INTO `ram_policy` VALUES ('5930d39ee1c3b19b0160723ff40c3779', '删除角色', '组织管理-角色管理-操作-删除角色', 'DELETE', 'com.sugon.cloud.iam.api.controller.IamRoleController', 'deleteRole', '1', '', null, '1', 'd92db5112c6c67cf11a8e90d9ca79335', '/api/roles/{role_id}', '0', '', '2021-04-23 00:00:00', '', '', 'a72680e8bc494c93715698a887740f78', null, '', '1');
INSERT INTO `ram_policy` VALUES ('61d125a65df54296d6ed381976585cab', '添加', '策略管理-添加', 'POST', 'com.sugon.cloud.policy.api.controller.CommonPolicyController', 'createPolicy', '1', '', null, '1', 'bda8f353d61b50ae676b02fb62741ca1', '/api/common/ram/policy', '1', '', '2021-04-23 00:00:00', '', '', '', null, '', '1');
INSERT INTO `ram_policy` VALUES ('630c2949797479f246573339f4218e57', '创建用户', '组织管理-用户管理-创建用户', 'POST', 'com.sugon.cloud.iam.api.controller.UserController', 'createSub', '1', '', null, '1', 'c1fd2cce952da1093f3aa062e8ca8d9e', '/api/users/sub', '1', '', '2021-04-23 00:00:00', '', '', '', null, '', '1');
INSERT INTO `ram_policy` VALUES ('6f5562a40b28f22788015a32d5e4e1e5', '编辑', '组织管理-项目管理-操作-编辑', 'PUT', 'com.sugon.cloud.iam.api.controller.ProjectController', 'update', '1', '', null, '1', '2ad1a10cdd55777e0acc760e26fc4259', '/api/projects/{id}', '0', '', '2021-04-23 00:00:00', '', '', '', null, '', '1');
INSERT INTO `ram_policy` VALUES ('6f7a9d24602f6e528039800c1862e620', '设置用户过期时间', '组织管理-用户管理-操作-设置用户过期时间', 'null', '', '', '1', '', null, '1', '7d83120a78a67a76fda51b94adec4801', '', '5', '', '2021-04-23 00:00:00', '', '', '', null, '', '1');
INSERT INTO `ram_policy` VALUES ('70f15b48260a078ff3188591b1bbc9fc', '删除用户', '组织管理-用户管理-删除用户', 'DELETE', 'com.sugon.cloud.iam.api.controller.UserController', 'delete', '1', '', null, '1', 'c1fd2cce952da1093f3aa062e8ca8d9e', '/api/users/{user_id}', '1', '', '2021-04-23 00:00:00', '', '', '70f15b48260a078ff3188591b1bbc9fc', null, '', '1');
INSERT INTO `ram_policy` VALUES ('7d83120a78a67a76fda51b94adec4801', '操作', '组织管理-用户管理-操作', 'null', '', '', '1', '', null, '1', 'c1fd2cce952da1093f3aa062e8ca8d9e', '', '2', '', '2021-04-23 00:00:00', '', '', '', null, '', '1');
INSERT INTO `ram_policy` VALUES ('880d73ee0a2390331cee75bec070e9ca', '删除', '组织管理-项目管理-删除', 'DELETE', 'com.sugon.cloud.iam.api.controller.ProjectController', 'delete', '1', '', null, '1', '199e95b1f85d781cd4d04e2cfc08a2ea', '/api/projects/{id}', '1', '', '2021-04-23 00:00:00', '', '', '880d73ee0a2390331cee75bec070e9ca', null, '', '1');
INSERT INTO `ram_policy` VALUES ('890826492a92e7db24d8865c778a8e38', '项目管理', '组织管理-用户管理-操作-项目管理', 'POST', 'com.sugon.cloud.iam.api.controller.UserController', 'batchAssignmentProjects', '1', '', null, '1', '7d83120a78a67a76fda51b94adec4801', '/api/users/{user_id}/bind-projects', '3', '', '2021-04-23 00:00:00', '', '', '', null, '', '1');
INSERT INTO `ram_policy` VALUES ('a72680e8bc494c93715698a887740f78', '删除', '组织管理-角色管理-删除', 'DELETE', 'com.sugon.cloud.iam.api.controller.IamRoleController', 'deleteRole', '1', '', null, '1', 'fe3a21e5bffa710d207343632197ae40', '/api/roles/{role_id}', '1', '', '2021-04-23 00:00:00', '', '', 'a72680e8bc494c93715698a887740f78', null, '', '1');
INSERT INTO `ram_policy` VALUES ('a8cbf9ee2898e2b2979824d4d6098be2', '删除', '组织管理-用户管理-操作-删除', 'DELETE', 'com.sugon.cloud.iam.api.controller.UserController', 'delete', '1', '', null, '1', '7d83120a78a67a76fda51b94adec4801', '/api/users/{user_id}', '8', '', '2021-04-23 00:00:00', '', '', '70f15b48260a078ff3188591b1bbc9fc', null, '', '1');
INSERT INTO `ram_policy` VALUES ('aab2dc93c2c0a57e6efa0235b5cdc696', '概览', '用户管理IAM-概览', 'GET', '', '', '1', '', null, '1', 'de5b8265a3f911eb8230f20ed01ddf38', '', '0', '', '2021-04-23 00:00:00', '/overview', '', '', null, '0', '1');
INSERT INTO `ram_policy` VALUES ('b1ff673ebc7dee772da81fd3e68a5607', '列表', '组织管理-列表', 'GET', 'com.sugon.cloud.iam.api.controller.DepartmentController', 'findByUserId', null, '', null, '1', 'f79da1ccd2add999e2491cce36c89290', '/api/departments', '0', '', '2021-04-27 00:00:00', '', '', '', null, '', '1');
INSERT INTO `ram_policy` VALUES ('bc0e2aa5b071a67599e4d3a8221750da', '列表', '策略管理-列表', 'GET', 'com.sugon.cloud.policy.api.controller.CommonPolicyController', 'getAllPolicyByTree', '1', '', null, '1', 'bda8f353d61b50ae676b02fb62741ca1', '/api/common/ram/policy/tree', '0', '', '2021-04-23 00:00:00', '', '', '', null, '', '1');
INSERT INTO `ram_policy` VALUES ('bda8f353d61b50ae676b02fb62741ca1', '策略管理', '用户管理IAM-策略管理', 'null', '', '', '0', '', null, '1', 'de5b8265a3f911eb8230f20ed01ddf38', '', '2', '', '2021-04-23 00:00:00', '/strategy', '', '', null, '0', '1');
INSERT INTO `ram_policy` VALUES ('c1fd2cce952da1093f3aa062e8ca8d9e', '用户管理', '组织管理-用户管理-列表', 'GET', 'com.sugon.cloud.iam.api.controller.UserController', 'userListByDeptId', '1', '', null, '1', 'f79da1ccd2add999e2491cce36c89290', '/api/users/dept/{dept_id}/list', '0', '', '2021-04-23 00:00:00', '', '', '', null, '', '1');
INSERT INTO `ram_policy` VALUES ('cfd7708b7d3f2b38ef67fdf187dcfdf1', '删除', '组织管理-项目管理-操作-删除', 'DELETE', 'com.sugon.cloud.iam.api.controller.ProjectController', 'delete', '1', '', null, '1', '2ad1a10cdd55777e0acc760e26fc4259', '/api/projects/{id}', '1', '', '2021-04-23 00:00:00', '', '', '880d73ee0a2390331cee75bec070e9ca', null, '', '1');
INSERT INTO `ram_policy` VALUES ('d92db5112c6c67cf11a8e90d9ca79335', '操作', '组织管理-角色管理-操作', 'null', '', '', '1', '', null, '1', 'fe3a21e5bffa710d207343632197ae40', '', '2', '', '2021-04-23 00:00:00', '', '', '', null, '', '1');
INSERT INTO `ram_policy` VALUES ('de5b8265a3f911eb8230f20ed01ddf38', '用户管理IAM', '用户管理IAM顶级菜单', 'null', '', '', '1', null, null, '1', '0000000000', '', '2', null, '2020-04-12 00:00:00', '/Vpc', 'icon-ico-33', '', null, '0', '0');
INSERT INTO `ram_policy` VALUES ('f78146c95201acef6690ecf30a19a03d', '重置密码', '组织管理-用户管理-操作-重置密码', 'null', 'com.sugon.cloud.iam.api.controller.UserController', 'updatePassword', '1', '', null, '1', '7d83120a78a67a76fda51b94adec4801', '/api/users/{user_id}/password/{password}/{old_password}', '4', '', '2021-04-23 00:00:00', '', '', '', null, '', '1');
INSERT INTO `ram_policy` VALUES ('f79da1ccd2add999e2491cce36c89290', '组织管理', '用户管理IAM-部门管理', 'null', '', '', '0', '', null, '1', 'de5b8265a3f911eb8230f20ed01ddf38', '', '0', '', '2021-04-23 00:00:00', '/departmentManage', '', '', null, '0', '1');
INSERT INTO `ram_policy` VALUES ('fcdfd962bc93512c1cdee056daad37ef', '编辑权限', '组织管理-角色管理-操作-编辑权限', 'null', '', '', '1', '', null, '1', 'd92db5112c6c67cf11a8e90d9ca79335', '', '2', '', '2021-04-23 00:00:00', '', '', '', null, '', '1');
INSERT INTO `ram_policy` VALUES ('fe3a21e5bffa710d207343632197ae40', '角色管理', '组织管理-角色管理-列表', 'GET', 'com.sugon.cloud.iam.api.controller.IamRoleController', 'listByDept', '1', '', null, '1', 'f79da1ccd2add999e2491cce36c89290', '/api/roles/dept/{dept_id}', '2', '', '2021-04-23 00:00:00', '', '', '', null, '', '1');

-- ----------------------------
-- Table structure for ram_policy_role
-- ----------------------------
CREATE TABLE IF NOT EXISTS `ram_policy_role` (
  `uuid` varchar(64) NOT NULL COMMENT '唯一标识',
  `policy_id` varchar(64) NOT NULL COMMENT '策略ID',
  `ram_role_id` varchar(64) NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`uuid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='RAM策略对角色';

-- ----------------------------
-- Records of ram_policy_role
-- ----------------------------

-- ----------------------------
-- Table structure for region
-- ----------------------------
CREATE TABLE IF NOT EXISTS `region` (
  `id` varchar(255) NOT NULL,
  `description` varchar(255) NOT NULL,
  `parent_region_id` varchar(255) DEFAULT NULL,
  `extra` text DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Records of region
-- ----------------------------
INSERT INTO `region` VALUES ('RegionOne', 'RegionOne', null, '{}');

-- ----------------------------
-- Table structure for revocation_event
-- ----------------------------
CREATE TABLE IF NOT EXISTS `revocation_event` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `domain_id` varchar(64) DEFAULT NULL,
  `project_id` varchar(64) DEFAULT NULL,
  `user_id` varchar(64) DEFAULT NULL,
  `role_id` varchar(64) DEFAULT NULL,
  `trust_id` varchar(64) DEFAULT NULL,
  `consumer_id` varchar(64) DEFAULT NULL,
  `access_token_id` varchar(64) DEFAULT NULL,
  `issued_before` datetime NOT NULL,
  `expires_at` datetime DEFAULT NULL,
  `revoked_at` datetime NOT NULL,
  `audit_id` varchar(32) DEFAULT NULL,
  `audit_chain_id` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_revocation_event_new_revoked_at` (`revoked_at`) USING BTREE,
  KEY `ix_revocation_event_issued_before` (`issued_before`) USING BTREE,
  KEY `ix_revocation_event_project_id_issued_before` (`project_id`,`issued_before`) USING BTREE,
  KEY `ix_revocation_event_user_id_issued_before` (`user_id`,`issued_before`) USING BTREE,
  KEY `ix_revocation_event_audit_id_issued_before` (`audit_id`,`issued_before`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Records of revocation_event
-- ----------------------------

-- ----------------------------
-- Table structure for role
-- ----------------------------
CREATE TABLE IF NOT EXISTS `role` (
  `id` varchar(64) NOT NULL,
  `name` varchar(255) NOT NULL,
  `extra` text DEFAULT NULL,
  `domain_id` varchar(64) NOT NULL DEFAULT '<<null>>',
  `description` text DEFAULT NULL,
  `dept_id` varchar(64) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ixu_role_name_domain_id` (`name`,`domain_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Records of role
-- ----------------------------
INSERT INTO `role` VALUES ('0ef7277f9d99445e9919d513213d2b8b', 'load-balancer_member', '{}', '<<null>>', null, null);
INSERT INTO `role` VALUES ('1c4e4b874b99470eaf114a5bba457c1b', 'member', '{}', '<<null>>', null, null);
INSERT INTO `role` VALUES ('2be8479f0f764be8b4cd59aeec3f779b', 'load-balancer_observer', '{}', '<<null>>', null, null);
INSERT INTO `role` VALUES ('73ac11bdd8dd43898b8bef393d1a0e59', 'heat_stack_user', '{}', '<<null>>', null, null);
INSERT INTO `role` VALUES ('8e5bd5e03d2243319887d650976200b9', 'admin', '{}', '<<null>>', null, null);
INSERT INTO `role` VALUES ('bbacda980f6940008977e6c70cfe1d3c', 'load-balancer_admin', '{}', '<<null>>', null, null);
INSERT INTO `role` VALUES ('c1c8625cd29841e8a2c8d1452872291a', 'load-balancer_quota_admin', '{}', '<<null>>', null, null);
INSERT INTO `role` VALUES ('dbb0afce5e6646c295dfcaa3ae53fbee', 'load-balancer_global_observer', '{}', '<<null>>', null, null);
INSERT INTO `role` VALUES ('e4fea202a27b43daa0e415e1641d8b64', 'reader', '{}', '<<null>>', null, null);

-- ----------------------------
-- Table structure for service
-- ----------------------------
CREATE TABLE IF NOT EXISTS `service` (
  `id` varchar(64) NOT NULL,
  `type` varchar(255) DEFAULT NULL,
  `enabled` tinyint(1) NOT NULL DEFAULT 1,
  `extra` text DEFAULT NULL,
  `description` text DEFAULT NULL,
  `name` varchar(64) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Records of service
-- ----------------------------
INSERT INTO `service` VALUES ('1814df1a91df41b19a19ad7da7f92a26', 'ironic-inspect', '1', '{\"name\":\"baremetal-introspection\",\"description\":\"openstack ironic inspect\"}', 'openstack ironic inspect', 'baremetal-introspection');
INSERT INTO `service` VALUES ('1e481cd120fd4f8ea2ebe78fc644247c', 'identity', '1', '{\"name\": \"keystone\"}', null, 'keystone');
INSERT INTO `service` VALUES ('21ca49b7864b4e758fd1228f5c4693ef', 'compute', '1', '{\"name\": \"nova\", \"description\": \"RegionOne: nova (compute) service\"}', null, 'nova');
INSERT INTO `service` VALUES ('2916cfccf2dc434d955cda3f2d39107c', 'key-manager', '1', '{\"name\":\"barbican\",\"description\":\"RegionOne: barbican (key-manager) service\"}', 'RegionOne: barbican (key-manager) service', 'barbican');
INSERT INTO `service` VALUES ('3c97fc690d494f1091b2c0c3f0109017', 'cloudformation', '1', '{\"name\":\"heat-cfn\",\"description\":\"RegionOne: heat-cfn (cloudformation) service\"}', 'RegionOne: heat-cfn (cloudformation) service', 'heat-cfn');
INSERT INTO `service` VALUES ('4b1493829dc5471f85841e7b3061e2db', 'clustering', '1', '{\"name\":\"senlin\",\"description\":\"RegionOne: senlin (clustering) service\"}', 'RegionOne: senlin (clustering) service', 'senlin');
INSERT INTO `service` VALUES ('5fadfbcdf60648a18704dc80267521d8', 'load-balancer', '1', '{\"name\":\"octavia\",\"description\":\"RegionOne: octavia (load-balancer) service\"}', 'RegionOne: octavia (load-balancer) service', 'octavia');
INSERT INTO `service` VALUES ('66ac98a42e1043c498a16a070e1435ba', 'network', '1', '{\"name\": \"neutron\", \"description\": \"RegionOne: neutron (network) service\"}', null, 'neutron');
INSERT INTO `service` VALUES ('7caace0dd34142b7a210b4bf2e7fa227', 'volume', '1', '{\"name\": \"cinder\", \"description\": \"RegionOne: cinder (volume) service\"}', null, 'cinder');
INSERT INTO `service` VALUES ('85e081b2e9f343bb9d914c58173db71a', 'infra-optim', '1', '{\"name\":\"watcher\",\"description\":\"Infrastructure Optimization\"}', 'Infrastructure Optimization', 'watcher');
INSERT INTO `service` VALUES ('8e6ed97ab2804de88270b48c7949a045', 'orchestration', '1', '{\"name\":\"heat\",\"description\":\"RegionOne: heat (orchestration) service\"}', 'RegionOne: heat (orchestration) service', 'heat');
INSERT INTO `service` VALUES ('9c9d9e851f7f479c996a6d6a55f1c8bf', 'volumev3', '1', '{\"name\": \"cinderv3\", \"description\": \"RegionOne: cinderv3 (volumev3) service\"}', null, 'cinderv3');
INSERT INTO `service` VALUES ('a00193a6bd2440259dc6d069b222b9be', 'image', '1', '{\"name\": \"glance\", \"description\": \"RegionOne: glance (image) service\"}', null, 'glance');
INSERT INTO `service` VALUES ('b1736bda62424b1d915947c7cdd5544d', 'baremetal', '1', '{\"name\":\"ironic\",\"description\":\"RegionOne: ironic (baremetal) service\"}', 'RegionOne: ironic (baremetal) service', 'ironic');
INSERT INTO `service` VALUES ('d8e68ac1ca434a1f98b91df59565fa43', 'volumev2', '1', '{\"name\": \"cinderv2\", \"description\": \"RegionOne: cinderv2 (volumev2) service\"}', null, 'cinderv2');
INSERT INTO `service` VALUES ('ff3e8340eeb44a22977c83668dd9b45e', 'placement', '1', '{\"name\": \"placement\", \"description\": \"RegionOne: placement (placement) service\"}', null, 'placement');

-- ----------------------------
-- Table structure for trust
-- ----------------------------
CREATE TABLE IF NOT EXISTS `trust` (
  `id` varchar(64) NOT NULL,
  `trustor_user_id` varchar(64) NOT NULL,
  `trustee_user_id` varchar(64) NOT NULL,
  `project_id` varchar(64) DEFAULT NULL,
  `impersonation` tinyint(1) NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `expires_at` datetime DEFAULT NULL,
  `remaining_uses` int(11) DEFAULT NULL,
  `extra` text DEFAULT NULL,
  `expires_at_int` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `duplicate_trust_constraint_expanded` (`trustor_user_id`,`trustee_user_id`,`project_id`,`impersonation`,`expires_at`,`expires_at_int`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Records of trust
-- ----------------------------
INSERT INTO `trust` VALUES ('0e30708aa4603799b237891d5ce3a155', '0d2bbb018e8b44b985a169647379f413', '8581f65885ccd39dcc381f3b5e0799b3', 'edcc24059c8b46969a37260dc9ab2329', '0', null, null, '0', null, '0');
INSERT INTO `trust` VALUES ('152338b4cb1e4c88f584d46373edb5d0', '0d2bbb018e8b44b985a169647379f413', '8581f65885ccd39dcc381f3b5e0799b3', 'edcc24059c8b46969a37260dc9ab2329', '0', null, null, '0', null, '0');
INSERT INTO `trust` VALUES ('27a4f014c772de364f96accbf4e7ce7d', '0d2bbb018e8b44b985a169647379f413', '8581f65885ccd39dcc381f3b5e0799b3', 'edcc24059c8b46969a37260dc9ab2329', '0', null, null, '0', null, '0');
INSERT INTO `trust` VALUES ('60f95aba8002af14c789445c47313aef', '0d2bbb018e8b44b985a169647379f413', '8581f65885ccd39dcc381f3b5e0799b3', 'edcc24059c8b46969a37260dc9ab2329', '0', null, null, '0', null, '0');
INSERT INTO `trust` VALUES ('61ba7ccfcbaa74f7a948e8c25a491c01', '0d2bbb018e8b44b985a169647379f413', '8581f65885ccd39dcc381f3b5e0799b3', 'edcc24059c8b46969a37260dc9ab2329', '0', null, null, '0', null, '0');
INSERT INTO `trust` VALUES ('7955b12d35dacd553d02f366e73b4b91', '0d2bbb018e8b44b985a169647379f413', '8581f65885ccd39dcc381f3b5e0799b3', 'edcc24059c8b46969a37260dc9ab2329', '0', null, null, '0', null, '0');
INSERT INTO `trust` VALUES ('a6c012c8e40743e30602364076896116', '0d2bbb018e8b44b985a169647379f413', '8581f65885ccd39dcc381f3b5e0799b3', 'edcc24059c8b46969a37260dc9ab2329', '0', null, null, '0', null, '0');
INSERT INTO `trust` VALUES ('bcb310624ebea56b22f668cec44e6b84', '0d2bbb018e8b44b985a169647379f413', '8581f65885ccd39dcc381f3b5e0799b3', 'edcc24059c8b46969a37260dc9ab2329', '0', null, null, '0', null, '0');
INSERT INTO `trust` VALUES ('bf316d1f1fa2e687de8f607f7276995b', '0d2bbb018e8b44b985a169647379f413', '8581f65885ccd39dcc381f3b5e0799b3', 'edcc24059c8b46969a37260dc9ab2329', '0', null, null, '0', null, '0');
INSERT INTO `trust` VALUES ('fddcb5930cc2787cf4d16311726d2f5a', '0d2bbb018e8b44b985a169647379f413', '8581f65885ccd39dcc381f3b5e0799b3', 'edcc24059c8b46969a37260dc9ab2329', '0', null, null, '0', null, '0');

-- ----------------------------
-- Table structure for trust_role
-- ----------------------------
CREATE TABLE IF NOT EXISTS `trust_role` (
  `trust_id` varchar(64) NOT NULL,
  `role_id` varchar(64) NOT NULL,
  PRIMARY KEY (`trust_id`,`role_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Records of trust_role
-- ----------------------------
INSERT INTO `trust_role` VALUES ('0e30708aa4603799b237891d5ce3a155', '8e5bd5e03d2243319887d650976200b9');
INSERT INTO `trust_role` VALUES ('152338b4cb1e4c88f584d46373edb5d0', '8e5bd5e03d2243319887d650976200b9');
INSERT INTO `trust_role` VALUES ('27a4f014c772de364f96accbf4e7ce7d', '8e5bd5e03d2243319887d650976200b9');
INSERT INTO `trust_role` VALUES ('60f95aba8002af14c789445c47313aef', '8e5bd5e03d2243319887d650976200b9');
INSERT INTO `trust_role` VALUES ('61ba7ccfcbaa74f7a948e8c25a491c01', '8e5bd5e03d2243319887d650976200b9');
INSERT INTO `trust_role` VALUES ('7955b12d35dacd553d02f366e73b4b91', '8e5bd5e03d2243319887d650976200b9');
INSERT INTO `trust_role` VALUES ('a6c012c8e40743e30602364076896116', '8e5bd5e03d2243319887d650976200b9');
INSERT INTO `trust_role` VALUES ('bcb310624ebea56b22f668cec44e6b84', '8e5bd5e03d2243319887d650976200b9');
INSERT INTO `trust_role` VALUES ('bf316d1f1fa2e687de8f607f7276995b', '8e5bd5e03d2243319887d650976200b9');
INSERT INTO `trust_role` VALUES ('fddcb5930cc2787cf4d16311726d2f5a', '8e5bd5e03d2243319887d650976200b9');

-- ----------------------------
-- Table structure for user
-- ----------------------------
CREATE TABLE IF NOT EXISTS `user` (
  `id` varchar(64) NOT NULL,
  `name` varchar(64) DEFAULT '',
  `extra` text DEFAULT NULL,
  `enabled` tinyint(1) DEFAULT NULL,
  `default_project_id` varchar(64) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `last_active_at` date DEFAULT NULL,
  `domain_id` varchar(64) NOT NULL,
  `password_expires_at` date DEFAULT NULL,
  `password` varchar(128) DEFAULT NULL,
  `email` varchar(32) DEFAULT NULL,
  `phone` varchar(16) DEFAULT NULL,
  `type` varchar(32) DEFAULT NULL,
  `dept_id` varchar(64) DEFAULT NULL,
  `expired` datetime DEFAULT NULL,
  `allow_ip` varchar(32) DEFAULT NULL,
  `last_login_ip` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ixu_user_id_domain_id` (`id`,`domain_id`) USING BTREE,
  KEY `domain_id` (`domain_id`) USING BTREE,
  KEY `ix_default_project_id` (`default_project_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Records of user
-- ----------------------------
INSERT INTO `user` VALUES ('003f67adc2d4465da6e2f716a621307a', 'securityadmin', '{}', '1', 'c954407e943c49fc8be65ec86cb95c28', '2021-01-09 07:36:46', '2021-05-13', 'default', null, '$2a$10$wBXCI8Tw1/UHQhl3T9TUfOKRzNsJmsfTZIz7SPxGctuhgJNOfJHlK', null, '', 'security', null, null, null, '***********');
INSERT INTO `user` VALUES ('0334d3358df0452fa6d08c5999672412', 'internal_cinder', '{\"description\":\"\"}', '1', '824e39c5e2c54b3e986fa6e0e39d948a', '2021-04-21 10:03:51', '2025-04-21', 'default', '2025-04-21', '$2a$10$hfi0xTZn.69zIbKfuQBucuv1pckUZ.mmDzMf4i3pcGBX27GfWhgiq', null, null, null, null, null, null, null);
INSERT INTO `user` VALUES ('0d2bbb018e8b44b985a169647379f413', 'admin', '{}', '1', 'edcc24059c8b46969a37260dc9ab2329', '2021-01-09 07:36:39', '2021-05-21', 'default', null, '$2a$10$7WUjpZfTPYvzcRZfFC9Nc.JJ.3e2.sTr/hNZo.9PwzXO.raXovOaq', '<EMAIL>', '18888888888', 'admin', '2', null, null, '***********');
INSERT INTO `user` VALUES ('28c87a1b4479ba14d34cdf0be96b6fc4', 'octavia', '{\"description\":\"Service User for RegionOne/service/octavia\"}', '1', '94743c3f9dd440649476fcbfa7c46538', '2021-05-06 15:17:02', null, '824e39c5e2c54b3e986fa6e0e39d948a', null, '$2a$10$WIefmLXYvSHVPY4lrZ7moOlmK/MJq3u5dhmLz7q3cWyeR7XLD/0Wy', null, null, null, null, null, null, null);
INSERT INTO `user` VALUES ('431c439e563384116d79d413a87cfb12', 'barbican', '{\"description\":\"Service User for RegionOne/service/barbican\"}', '1', '94743c3f9dd440649476fcbfa7c46538', '2021-05-06 15:13:10', null, '824e39c5e2c54b3e986fa6e0e39d948a', null, '$2a$10$2NFm9NKKiOeSuNG8qpGeauHPSa29oRkpUIlKVFECQC80gsUNBKd7S', null, null, null, null, null, null, null);
INSERT INTO `user` VALUES ('4855cf8d94a559e0cb2ec9995535b3d8', 'senlin', '{\"description\":\"Service User for RegionOne/service/senlin\"}', '1', '94743c3f9dd440649476fcbfa7c46538', '2021-05-06 14:59:36', null, '824e39c5e2c54b3e986fa6e0e39d948a', null, '$2a$10$mSl1A1lKMIDJUuRTRjiAuuaAkI6LzpOkGz8kSaLD2jHusJY2ugMTO', null, null, null, null, null, null, null);
INSERT INTO `user` VALUES ('75c39b6bca2e01e0b942931a7afca4dc', 'ironic', '{\"description\":\"Service User for RegionOne/service/ironic\"}', '1', '94743c3f9dd440649476fcbfa7c46538', '2021-05-06 15:30:34', '2021-05-18', '824e39c5e2c54b3e986fa6e0e39d948a', null, '$2a$10$nAD1MM7Svyru2tlzXPCN/.jNE/QXVdGY/UZ06oOoNUUrePYiskLVy', null, null, null, null, null, null, '***********');
INSERT INTO `user` VALUES ('8423f64d57e1283fd2d59320cd1ff981', 'prometheus-openstack-exporter', '{\"description\":\"Service User for RegionOne/default/prometheus-openstack-exporter\"}', '1', '824e39c5e2c54b3e986fa6e0e39d948a', '2021-04-25 17:48:18', '2021-05-18', 'default', null, '$2a$10$shyWAj7eQe0XwLpcNJMKg.pgyXYvIbL39CWQC1QfwMd77oAW87oiy', null, null, null, null, null, null, '***********');
INSERT INTO `user` VALUES ('8581f65885ccd39dcc381f3b5e0799b3', 'heat-trust', '{\"description\":\"Service User for RegionOne/service/heat\"}', '1', '94743c3f9dd440649476fcbfa7c46538', '2021-05-06 15:03:23', null, '824e39c5e2c54b3e986fa6e0e39d948a', null, '$2a$10$I3gUe.OksLvwkkyKpv0xaOpr71tkQu/zgFwdfHq8VPbPVofCYzydO', null, null, null, null, null, null, null);
INSERT INTO `user` VALUES ('a617dde9ad1441618c9c7376ee2d44fc', 'nova', '{\"description\": null, \"email\": null}', '1', '824e39c5e2c54b3e986fa6e0e39d948a', '2021-01-09 07:40:37', '2021-05-18', 'default', null, '$2a$10$yjvgekgkRbDUaDRvlUnWH.3dtpafV3gUZ6Y49To/KRA5rTClCWZTe', null, null, null, null, null, null, '***********');
INSERT INTO `user` VALUES ('b3f26a5e99a74a4894fa245ecd90ab38', 'neutron', '{\"description\": null, \"email\": null}', '1', '824e39c5e2c54b3e986fa6e0e39d948a', '2021-01-09 07:46:44', '2021-05-18', 'default', null, '$2a$10$hfi0xTZn.69zIbKfuQBucuv1pckUZ.mmDzMf4i3pcGBX27GfWhgiq', null, null, null, null, null, null, '***********');
INSERT INTO `user` VALUES ('bbf7ad5382928cf99420e40a8ef9fef6', 'watcher', '{}', '1', '94743c3f9dd440649476fcbfa7c46538', '2021-05-17 14:32:43', '2021-05-18', 'default', null, '$2a$10$pGbN6Ac59Jx93KHYj3.Y8euvE4bmKCRM0owx5fcakVKEAkYUb2oeK', null, null, null, null, null, null, '***********');
INSERT INTO `user` VALUES ('beacb53308824d179d7c579f29db61d3', 'glance', '{\"description\": null, \"email\": null}', '1', '824e39c5e2c54b3e986fa6e0e39d948a', '2021-01-09 07:38:27', null, 'default', null, '$2a$10$hfi0xTZn.69zIbKfuQBucuv1pckUZ.mmDzMf4i3pcGBX27GfWhgiq', null, null, null, null, null, null, null);
INSERT INTO `user` VALUES ('c042989bab15f86aeda223cfe7398941', 'heat-domain', '{\"description\":\"Service User for RegionOne/heat\"}', '1', null, '2021-05-06 15:22:31', null, '82ff77249d582f6cd3c5c0d2773351e5', null, '$2a$10$UM5aJ1Mh1fyQGv7cY0aD4eGiLlseJ8MPxYQARhB1S44VKD9MkV4/u', null, null, null, null, null, null, null);
INSERT INTO `user` VALUES ('c54eed6d25c14b73a3e551afe85a46de', 'placement', '{\"description\": null, \"email\": null}', '1', '824e39c5e2c54b3e986fa6e0e39d948a', '2021-01-09 07:40:41', '2021-05-18', 'default', null, '$2a$10$glLmVdVeODubVbPrVrnapuQagzH4v5DloMqk3jtxI6WDMhet28DC.', null, null, null, null, null, null, '***********');
INSERT INTO `user` VALUES ('c5f3d02010be0c843762bf05b3120b3a', 'heat', '{\"description\":\"Service User for RegionOne/service/heat\"}', '1', '94743c3f9dd440649476fcbfa7c46538', '2021-05-06 15:03:23', null, '824e39c5e2c54b3e986fa6e0e39d948a', null, '$2a$10$3ghOxD3YfeCKowCOlwZ1EueFxEIQ72W0xv606IYF9fPqfvKYOxgoe', null, null, null, null, null, null, null);
INSERT INTO `user` VALUES ('e31964dde5b84d3883d61095d982cb70', 'cinder', '{\"description\": null, \"email\": null}', '1', '824e39c5e2c54b3e986fa6e0e39d948a', '2021-01-09 07:48:48', '2021-05-18', 'default', null, '$2a$10$yaJW/RSEqun/cqZ0WOfNfuXigGuTmEiMN0XpPwFVZF3RVP823hrz6', null, null, null, null, null, null, '***********');
INSERT INTO `user` VALUES ('e8fbcd008be84419abf9e371d7fa3753', 'auditadmin', '{}', '1', '0cd7ea8585b94468a1a00acb7f2a0d3a', '2021-01-09 07:36:49', null, 'default', null, '$2a$10$wBXCI8Tw1/UHQhl3T9TUfOKRzNsJmsfTZIz7SPxGctuhgJNOfJHlK', null, '', 'audit', null, null, null, null);
INSERT INTO `user` VALUES ('eb1dcd73a651e4f3b82da8214a580fbc', 'ironic-inspect', '{}', '1', null, '2021-05-17 13:50:58', null, 'default', null, '$2a$10$ldjswHIznJhH1R3s5auNjOXVD0lLeI/G.CkPxb4rJKn9g2P41vO6S', null, null, null, null, null, null, null);

-- ----------------------------
-- Table structure for user_role_project_mapping
-- ----------------------------
CREATE TABLE IF NOT EXISTS `user_role_project_mapping` (
  `id` varchar(64) NOT NULL,
  `user_id` varchar(64) DEFAULT NULL COMMENT '用户ID',
  `role_id` varchar(64) DEFAULT NULL COMMENT '角色ID',
  `dept_id` varchar(64) DEFAULT NULL COMMENT '部门ID',
  `project_id` varchar(64) DEFAULT NULL COMMENT '项目ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='用户角色关联表';

-- ----------------------------
-- Records of user_role_project_mapping
-- ----------------------------
INSERT INTO `user_role_project_mapping` VALUES ('7baaff60-fc6c-440d-bc12-1008cec8d590', '0d2bbb018e8b44b985a169647379f413', 'c16c9944ca324aa693d3da6f7e7484f7', null, null);
INSERT INTO `user_role_project_mapping` VALUES ('e5c53d44-4388-4c1a-90c3-15d8d66c850c', 'e8fbcd008be84419abf9e371d7fa3753', '6d4372436aa34fd5a015550c22ba605c', null, null);
INSERT INTO `user_role_project_mapping` VALUES ('f2821b49a5b211eb8230f20ed01ddf38', '003f67adc2d4465da6e2f716a621307a', '5a5508808dc641bd83f2ca62ac641ee9', null, null);
